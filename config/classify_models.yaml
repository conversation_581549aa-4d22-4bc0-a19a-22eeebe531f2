classify_models:
- name: 身份证（中国内地）
  model_id: 4395
  data_tag: T_ID_CARD_CH
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 18
    - regex_match:
      - patterns:
        - ^[1-9]\d{5}(19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$
    - function_match:
      - function_name: ID_CARD
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*身份证.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 4
        - 20
    - regex_match:
      - patterns:
        - \b((id|identity)_?(card)_?(no|number)?|身份证(号|号码)?)\b
- name: 储蓄卡
  model_id: 3107
  data_tag: T_DEBIT_CARD
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_range:
        - 16
        - 19
    - regex_match:
      - patterns:
        - ^(?:620200|620202|620203|622202|622208|622210|622211|622212|622213|622230|622235|622238|622250|622253)\d{10,13}$
        - ^(?:622840|622841|622844|622845|622846|622847|622848|622860|622861|622862|622863)\d{10,13}$
        - ^(?:622750|622751|622752|622753|622754|622755|622756|622757|622758|622759|622760)\d{10,13}$
        - ^(?:622280|622700|622701|622702|622703|622704|622705|622706|622707|622708|622709)\d{10,13}$
        - ^(?:622260|622261|622262|622263|622264|622265|622266|622267|622268|622269)\d{10,13}$
        - ^(?:621599|622581|622582|622583|622584|622585|622586|622587|622588|622598|622599)\d{10,13}$
        - ^(?:622575|622576|622577|622578|622579|622580|621286|620520|621483|621485|621486)\d{10,13}$
        - ^(?:622386|622387|622388|622389|622390|622391|622392)\d{10,13}$
        - ^(?:622615|622616|622617|622618|622619|622620|622621|622622|622623|622624|622625|622626|622627|622628)\d{10,13}$
        - ^(?:622650|622651|622652|622653|622654|622655|622656|622657|622658|622659|622660|622661|622662)\d{10,13}$
        - ^(?:622680|622681|622682|622683|622684|622685|622686|622687|622688|622689|622690|622691|622692)\d{10,13}$
        - ^(?:621977|621979|621980|621981|621982|622520|622521|622522|622523|622524|622527|622528|622529|622530)\d{10,13}$
        - ^(?:622630|622631|622632|622633|622634|622635|622636|622637|622638|622639|622640|622641|622642)\d{10,13}$
        - ^(?:621626|622535|622536|622538|622539|622983|623221|623229|623231|623233|623234|623235|623236)\d{10,13}$
        - ^(?:622555|622556|622557|622558|622559|622560|622561|622562|622563|622564|622565|622566|622567)\d{10,13}$
    - function_match:
      - function_name: LUHN
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*储蓄卡.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 8
        - 16
    - regex_match:
      - patterns:
        - \b(bank_?(card_?(id|no|number)?|acct|account)|(银行|储蓄|借记)卡号)\b
- name: 姓名（简体中文）
  model_id: 3336
  data_tag: T_FULL_NAME_CH
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 6
        - 9
        - 12
        - 15
        - 18
        - 21
    - dict_match:
      - dict_ids:
        - D_XING_CH
      - match_type: prefix
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*姓名.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 6
        - 13
    - regex_match:
      - patterns:
        - \b(full_?name|person_?name|user_?name|customer_?name|real_?name|姓名|名字|称呼)\b
- name: 地址（中国内地）
  model_id: 7699
  data_tag: T_ADDRESS_CH
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - regex_match:
      - patterns:
        - ^.*(省|市|区|县|自治区|自治州|特别行政区|街道|镇|乡|路|街|巷|弄|村|社区|小区|大厦|公寓|号楼|单元|室|幢|座|栋|层|号|户).+$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*地址.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 4
        - 18
    - regex_match:
      - patterns:
        - \b((home|residence|living|permanent|temporary|mailing|shipping|billing|contact)?_?(address|addr|location)|地址|住址|所在地)\b
- name: 手机号（中国内地）
  model_id: 4285
  data_tag: T_MOBILE_PHONE_NUMBER_CH
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 11
    - regex_match:
      - patterns:
        - ^1[3-9]\d{9}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*手机号.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 6
        - 14
    - regex_match:
      - patterns:
        - \b((mobile|cell|phone|contact)_?(number|no)|手机(号|号码)?)\b
- name: 邮箱
  model_id: 5512
  data_tag: T_EMAIL
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_range:
        - 6
        - 50
    - regex_match:
      - patterns:
        - ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*邮箱.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 4
        - 14
    - regex_match:
      - patterns:
        - \b((email|e_?mail|mail)_?(address|addr|id)?|邮箱|电子邮件)\b
- name: 护照号（中国内地）
  model_id: 4468
  data_tag: T_PASSPORT_CH
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 8
        - 9
    - regex_match:
      - patterns:
        - ^([E]\d{8}|[G]\d{8}|[P]\d{7}|[D]\d{7}|[HK]\d{7,8}|[M]\d{7})$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*护照号.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 6
        - 21
    - regex_match:
      - patterns:
        - \b((passport|travel_?document|intl_?)_?(number|no|code|id)|护照(号|号码)?)\b
- name: 港澳通行证
  model_id: 1743
  data_tag: T_PASSPORT_HK_MACAO
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 9
    - regex_match:
      - patterns:
        - ^([C][A-HJ-NP-Z0-9]\d{7}|[HM]\d{8})$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*港澳通行证.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 11
        - 34
    - regex_match:
      - patterns:
        - \b((hk_?macau|hongkong_?macau)_?(travel_permit_)?(number|no|code|id)|港澳通行证(号|号码)?)\b
- name: 车牌号（中国内地）
  model_id: 7968
  data_tag: T_VEHICLE_PLATE_NUMBER_CH
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 9
        - 10
    - regex_match:
      - patterns:
        - ^(?:京|津|沪|渝|冀|豫|云|辽|黑|湘|皖|鲁|新|苏|浙|赣|鄂|桂|甘|晋|蒙|陕|吉|闽|贵|粤|青|藏|川|宁|琼)[A-Z](?:[0-9A-Z]{5}|[DF][0-9A-Z]{5})$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*车牌号.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 5
        - 19
    - regex_match:
      - patterns:
        - \b((car|license_?plate)_?(number|no|id)|车牌(号|号码)?)\b
- name: 电话号码（中国内地）
  model_id: 10279
  data_tag: T_PHONE_NUMBER_CH
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 11
        - 10
        - 12
        - 15
        - 16
        - 17
        - 18
    - regex_match:
      - patterns:
        - ^(?:1[3-9]\d{9}|0\d{2,3}-?\d{7,8}(-\d{3,4})?)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*电话号码.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 6
        - 16
    - regex_match:
      - patterns:
        - \b((phone|telephone|fixed_?line|landline|contact)_?(number|no|num|tel)|电话(号|号码)?)\b
- name: 军官证
  model_id: 6997
  data_tag: T_OFFICER_CARD
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 16
        - 20
    - regex_match:
      - patterns:
        - ^((军|兵|士|文|职)字第([0-9a-zA-Z]{4,8})号)|(军字第\d{7}号)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 9
        - 19
    - regex_match:
      - patterns:
        - .*军官证.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((military|officer|soldier)_?(id|card|certificate|number|no)|军官证(号|号码)?)\b
- name: 性别
  model_id: 10386
  data_tag: T_GENDER
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 3
    - enum_match:
      - enum:
        - 男
        - 女
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*性别.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 3
        - 11
    - regex_match:
      - patterns:
        - \b((gender|sex|male_?female)|性别)\b
- name: 民族
  model_id: 8508
  data_tag: T_ETHNIC
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - dict_match:
      - dict_ids:
        - D_ETHNIC
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*民族.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 6
        - 20
    - regex_match:
      - patterns:
        - \b((ethnic|nationality|race|ethnicity)_?(group|type|category)|民族)\b
- name: 省份（中国内地）
  model_id: 8885
  data_tag: T_PROVINCE_CH
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - dict_match:
      - dict_ids:
        - D_PROVINCE_CH
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*省份.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 4
        - 8
    - regex_match:
      - patterns:
        - \b((province|prov|state|region)|省份|行政区)\b
- name: 城市（中国内地）
  model_id: 3957
  data_tag: T_CITY_CH
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - dict_match:
      - dict_ids:
        - D_CITY_CH
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*城市.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 4
        - 10
    - regex_match:
      - patterns:
        - \b((city|municipal|urban|town|prefecture)|城市|市区|县市)\b
- name: 身份证（中国香港）
  model_id: 10640
  data_tag: T_ID_CARD_HK
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 7
        - 11
    - regex_match:
      - patterns:
        - ^[A-Z]{1,2}[0-9]{6}\(?[0-9A-Z]\)?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*身份证.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 4
        - 17
    - regex_match:
      - patterns:
        - \b((hk|hongkong)_?(id|identity)(_?card)?_?(number|no)?|香港身份證(號|號碼)?|香港身份证(号|号码)?)\b
- name: 姓名（繁体中文）
  model_id: 3845
  data_tag: T_FULL_NAME_HK
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 6
        - 9
        - 12
        - 15
        - 18
        - 21
    - dict_match:
      - dict_ids:
        - D_XING_HK
      - match_type: prefix
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*姓名.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 6
        - 21
    - regex_match:
      - patterns:
        - \b((full_?|person_?|user_?|customer_?|real_?)?name_?zh_?hant|稱呼|繁体中文姓名|繁体中文名字|繁体中文称呼|繁体中文稱呼)\b
- name: 姓名（英文）
  model_id: 5713
  data_tag: T_FULL_NAME_UK
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_range:
        - 3
        - 30
    - regex_match:
      - patterns:
        - ^[A-Z][a-z]+(?:(\s+|-)(?:(?:Mac|Mc)?[A-Z][a-z]+|(?:van|de|la|von|der|den|St\.|Le|Da|O'|D')\s+[A-Z][a-z]+))*$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*姓名.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 7
        - 12
    - regex_match:
      - patterns:
        - \b((english_?name|en_?name|name_?en|foreign_?name|given_?name|surname|last_?name|first_?name)|英文姓名|英文名(字)?)\b
- name: 信用卡
  model_id: 7716
  data_tag: T_CREDIT_CARD
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_range:
        - 13
        - 19
    - regex_match:
      - patterns:
        - ^(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|3[47][0-9]{13}|6(?:011|4[4-9][0-9]|5[0-9]{2})[0-9]{12}|62[0-9]{14,17}|35(?:28|29|[3-8][0-9]|91)[0-9]{12}|3[68-9][0-9]{12,17})$
    - function_match:
      - function_name: LUHN
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*信用卡.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 10
        - 21
    - regex_match:
      - patterns:
        - \b(credit_?(card|acct|account)_?(number|no)?|信用卡(号|号码|號|號碼)?)\b
- name: SWIFT Code
  model_id: 6043
  data_tag: T_SWIFT_CODE
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_range:
        - 8
        - 11
    - regex_match:
      - patterns:
        - ^[A-Z]{4}[A-Z0-9]{2}[A-Z0-9]{2}(?:[A-Z0-9]{3})?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*SWIFT Code.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 5
        - 20
    - regex_match:
      - patterns:
        - \b((swift|bic|bank_?(identifier|id|code))_?(code|number|no)|国际银行代码|SWIFT(码|碼)|國際銀行代碼)\b
- name: 宗教信仰
  model_id: 5199
  data_tag: T_RELIGION
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 6
        - 9
        - 12
    - dict_match:
      - dict_ids:
        - D_RELIGION
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*宗教信仰.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 5
        - 9
    - regex_match:
      - patterns:
        - \b((religion|faith|belief|spiritual)|宗教(信仰)?|信仰)
- name: 微信UnionID
  model_id: 4970
  data_tag: T_WECHAT_UNIONID
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 28
    - regex_match:
      - patterns:
        - ^[a-zA-Z0-9_-]{28}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*微信UnionID.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((wechat|wx)_?(union_?(id|identifier)|uid)|微信(Union|联盟|联合)?ID)\b
- name: 微信OpenID
  model_id: 6628
  data_tag: T_WECHAT_OPENID
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - regex_match:
      - patterns:
        - ^[a-zA-Z0-9_-]{28}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*微信OpenID.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((wechat|wx)_?(open_?(id|identifier)|oid)|微信OpenID)\b
- name: 姓名（拼音）
  model_id: 4529
  data_tag: T_PINYIN_NAME
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_range:
        - 4
        - 42
    - dict_match:
      - dict_ids:
        - D_PINYIN_NAME
      - match_type: prefix
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*姓名.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((pinyin_?name|name_?pinyin|romanized_?name|chinese_?pinyin)|拼音(姓名|名字))\b
- name: IP地址
  model_id: 5182
  data_tag: T_IP
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 7
        - 15
    - regex_match:
      - patterns:
        - ^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*IP地址.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((ip|ipv4|network|host|server|client)_?(address|addr)|(ip|ipv4)(地址)?)\b
- name: MAC地址
  model_id: 4161
  data_tag: T_MAC
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 12
        - 17
    - regex_match:
      - patterns:
        - ^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$|^([0-9A-Fa-f]{4}[.]){2}([0-9A-Fa-f]{4})$|^[0-9A-Fa-f]{12}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*MAC地址.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((mac|hardware|device|physical)_?(address|addr|id)|mac(地址)?)\b
- name: JDBC连接串
  model_id: 5945
  data_tag: T_JDBC
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - ^jdbc:([a-zA-Z0-9:]+)://([^:/]+)(?::(\d+))?(?:/([^?;]+))?(?:[?;](.*))?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*JDBC连接串.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((jdbc|database)_?(url|string|uri|link)|jdbc(连接串)?)\b
- name: PEM证书
  model_id: 10835
  data_tag: T_PEM_CERT
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 256
        - 4096
    - regex_match:
      - patterns:
        - ^-----BEGIN(?:\s+CERTIFICATE)-----(?:[\r\n]?[A-Za-z0-9+/]{64})*[\r\n]?[A-Za-z0-9+/=\r\n]{256,}[=]{0,2}[\r\n]?-----END(?:\s+CERTIFICATE)-----$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*PEM证书.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(pem_?(file|content|data)|pem(证书|文件)?)\b
- name: KEY私钥
  model_id: 3178
  data_tag: T_PRIVATE_KEY
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 256
        - 4096
    - regex_match:
      - patterns:
        - ^-----BEGIN(?:\s(?:RSA|EC|OPENSSH))(?:\s+PRIVATE)(?:\s+KEY)-----[\r\n]?[A-Za-z0-9+/=\r\n]{256,}[\r\n]?-----END(?:\s(?:RSA|EC|OPENSSH))(?:\s+PRIVATE)(?:\s+KEY)-----$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*KEY私钥.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((private|priv)_?key|私钥|密钥)\b
- name: AccessKeyId
  model_id: 10757
  data_tag: T_ACCESS_KEY_ID
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 20
        - 23
    - regex_match:
      - patterns:
        - ^(LTAI5t[a-zA-Z0-9]{17}|AKIA[0-9A-Z]{16})$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*AccessKeyId.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((access|auth|credential)_?(key_?id|id)|AccessKeyId|访问密钥ID)\b
- name: AccessKeySecret
  model_id: 1515
  data_tag: T_ACCESS_KEY_SECRET
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 30
    - regex_match:
      - patterns:
        - ^[a-zA-Z0-9+/]{30}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*AccessKeySecret.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((access|auth|credential)_?(key_?secret|secret)|AccessKeySecret|访问密钥)\b
- name: IPv6地址
  model_id: 7226
  data_tag: T_IPV6
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 2
        - 39
    - regex_match:
      - patterns:
        - ^(?:[0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4}$
        - ^(?:(?:[0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})?::(?:(?:[0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})?$
        - ^::ffff:(?:(?:25[0-5]|2[0-4][0-9]|[0-1]?[0-9]{1,2})\.){3}(?:25[0-5]|2[0-4][0-9]|[0-1]?[0-9]{1,2})$
        - ^(?:[0-9A-Fa-f:]+)(?:%[^%\s]+)?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*IPv6地址.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(ipv6_?(address|addr)|ipv6(地址)?)\b
- name: GPS位置
  model_id: 1572
  data_tag: T_GPS
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - regex_match:
      - patterns:
        - ^-?(?:[0-8]?\d(?:\.\d{4,})|90(?:\.0{4,})?),\s*-?(?:(?:1[0-7]\d|[0-9]?\d)(?:\.\d{4,})|180(?:\.0{4,})?)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*GPS位置.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((gps|geo)_?(coord|coordinate|location)|gps(地址|位置|坐标)?|定位)\b
- name: 日期
  model_id: 6960
  data_tag: T_DATE
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - regex_match:
      - patterns:
        - ^\d{4}(?:[-_](?:[1-9]|0[1-9]|1[0-2])[-_](?:[1-9]|0[1-9]|[12]\d|3[01])|(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01]))$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*日期.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((birth|start|end|due|issue|expire|create|update|modify|publish|record|entry|register|event|transaction|order|payment)_?(date|time|day)|(出生)?(日期|时间))\b
- name: IMEI
  model_id: 6845
  data_tag: T_IMEI
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_range:
        - 15
        - 17
    - regex_match:
      - patterns:
        - ^(01|35|86)\d{13,15}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*IMEI.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(international_?mobile_?equipment_?(id|identifier|number|no)|imei)\b
- name: MEID
  model_id: 2869
  data_tag: T_MEID
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 14
    - regex_match:
      - patterns:
        - ^[A-Fa-f0-9]{14}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*MEID.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(mobile_?equipment_?(id|identifier|number|no)|meid)\b
- name: URL链接
  model_id: 10419
  data_tag: T_URL
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - regex_match:
      - patterns:
        - ^(https?|ftp|file|mailto|tel|data|ssh|sftp|ldap|ws|wss)://([a-zA-Z0-9-]+\.)+[a-zA-Z0-9-]{2,}(/[\w.\-]*)*/?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*URL链接.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((web|site|page|resource|media|image|file|download|upload|api|service|endpoint)_?(url|link|address)|url|网址|链接)\b
- name: 营业执照号码
  model_id: 8530
  data_tag: T_BUSINESS_LICENSE
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 15
        - 18
    - regex_match:
      - patterns:
        - ^(\d{15}||[0-9A-HJ-NP-RT-UW-Y]{18})$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*营业执照号码.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(business_?(license|licence|cert|certificate)_?(number|no|code|id)|营业执照(号|号码))\b
- name: 税务登记证号码
  model_id: 7910
  data_tag: T_TAX_REGISTRATION
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 15
        - 18
    - regex_match:
      - patterns:
        - ^([0-9A-Z]{15}|[0-9A-HJ-NP-RT-UW-Y]{18})$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*税务登记证号码.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(tax_?(registration|reg|register)?_?(cert|certificate)_?(number|no|code|id)|税务登记(证)?号(码)?)\b
- name: 组织机构代码
  model_id: 10114
  data_tag: T_ORGANIZATION_CODE
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 9
    - regex_match:
      - patterns:
        - ^[0-9A-Z]{9}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*组织机构代码.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((org|organization|entity)_?(code|number|no|id|identifier)|组织机构代码)\b
- name: 统一社会信用代码
  model_id: 5927
  data_tag: T_UNIFIED_SOCIAL_CREDIT_CODE
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 18
    - regex_match:
      - patterns:
        - ^[0-9A-HJ-NP-RT-UW-Y]{18}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*统一社会信用代码.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(unified_?social_?credit_?(code|number|no|id)|uscc|usci|统一社会信用代码)\b
- name: 车辆识别代码
  model_id: 10440
  data_tag: T_VEHICLE_ID
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 17
    - regex_match:
      - patterns:
        - ^[A-HJ-NPR-Z0-9]{17}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*车辆识别代码.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(vehicle_?(id|identification_?(number|no)|identifier|number|no|code)|vin|车辆识别代码)\b
- name: 职位
  model_id: 4721
  data_tag: T_JOB_TITLE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - dict_match:
      - dict_ids:
        - D_JOB_TITLE
      - match_type: suffix
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*职位.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((job|employee|staff|candidate|member|person)_?(position|title|role)|job|职位|头衔)\b
- name: 还款方式
  model_id: 4819
  data_tag: T_REPAYMENT_MODE
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - dict_match:
      - dict_ids:
        - D_REPAYMENT_MODE
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*还款方式.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((loan|debt|credit|mortgage)?_?repayment_?(method|way|type|mode)|还款(方式|类型))\b
- name: 证件类型
  model_id: 9890
  data_tag: T_ID_DOCUMENT_TYPE
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - dict_match:
      - dict_ids:
        - D_ID_DOCUMENT_TYPE
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*证件类型.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((id|identification|credential|document|cert|certificate)_?type|证件类型|证件名称)\b
- name: 支付方式
  model_id: 2702
  data_tag: T_PAYMENT_MODE
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - dict_match:
      - dict_ids:
        - D_PAYMENT_MODE
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*支付方式.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((transaction|order|purchase|buy|sale)?_?payment_?(method|way|type|mode)|支付(方式|类型))\b
- name: 家庭关系
  model_id: 10621
  data_tag: T_FAMILIY_RELATIONSHIP
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - dict_match:
      - dict_ids:
        - D_FAMILY_RELATIONSHIP
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*家庭关系.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((family|household|kin|relative)_?(relation|relationship|tie|link)|家庭关系)\b
- name: 体重
  model_id: 4918
  data_tag: T_WEIGHT
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 3
        - 12
    - regex_match:
      - patterns:
        - ^(?:[1-9]\d{0,2}(?:\.\d{1,2})?|0\.\d{1,2})\s*(?:kg|公斤|斤|lbs)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*体重.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((body|physical|person)?_?weight|体重|重量)\b
- name: 身高
  model_id: 1981
  data_tag: T_HEIGHT
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 2
        - 12
    - regex_match:
      - patterns:
        - ^(?:(?:(?:[5-9]\d|1\d{2}|2[0-4]\d)(?:\.\d{1,2})?)\s*(?:cm|厘米)|(?:0\.[5-9]\d|1\.\d{1,2}|2\.[0-4]\d)\s*m)$
        - ^[3-7]\'\s*(?:[0-9]|1[01])(?:\\.\\d)?\s*\"$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*身高.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((body|physical|person)?_?(height|stature)|身高|高度)\b
- name: 收款记录
  model_id: 10083
  data_tag: T_COLLECTION_RECORD
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - regex_match:
      - patterns:
        - \d{4}-\d{2}-\d{2}\s+[一-龥]{2,8}\s+[+-]?\d+\.\d{2}
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*收款记录.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((income|revenue|collection|receivable)_?record|收款记录)\b
- name: 发票代码
  model_id: 10219
  data_tag: T_INVOCE_CODE
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 10
        - 12
    - regex_match:
      - patterns:
        - ^\d{10}(\d{2})?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*发票代码.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((bill|receipt|statement|tax|vat)?_?invoice_?(code|number|no|id)|发票(代码|号(码)?))\b
- name: 性取向
  model_id: 10077
  data_tag: T_SEXUAL_ORIENTATION
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - dict_match:
      - dict_ids:
        - D_SEXUAL_ORIENTATION
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*性取向.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 6
        - 17
    - regex_match:
      - patterns:
        - \b(sexual_?(orientation|preference|identity)|性(取向|偏好|身份))\b
- name: 星座
  model_id: 5994
  data_tag: T_ASTROLOGICAL
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 9
    - dict_match:
      - dict_ids:
        - D_ASTROLOGICAL
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*星座.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 6
        - 12
    - regex_match:
      - patterns:
        - \b(zodiac|constellation|horoscope|星座)\b
- name: IDFA
  model_id: 2268
  data_tag: T_IDFA
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 36
    - regex_match:
      - patterns:
        - ^[A-F0-9]{8}(-[A-F0-9]{4}){3}-[A-F0-9]{12}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*IDFA.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 4
        - 14
    - regex_match:
      - patterns:
        - \b(idfa|advertising_?id|ad_?id|广告(标识|ID))\b
- name: GUID
  model_id: 5631
  data_tag: T_GUID
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 36
    - regex_match:
      - patterns:
        - ^[A-F0-9]{8}(-[A-F0-9]{4}){3}-[A-F0-9]{12}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*GUID.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 4
        - 15
    - regex_match:
      - patterns:
        - \b(guid|global_?unique_?id|全局唯一(标识|ID))\b
- name: IMSI
  model_id: 2687
  data_tag: T_IMSI
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 14
        - 15
    - regex_match:
      - patterns:
        - ^\d{14,15}$
    - dict_match:
      - dict_ids:
        - D_IMSI
      - match_type: prefix
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*IMSI.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 4
        - 37
    - regex_match:
      - patterns:
        - \b(imsi|international_?mobile_?subscriber_?identity|国际移动用户(标识|身份))\b
- name: 区县
  model_id: 1029
  data_tag: T_COUNTRY_CH
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 9
        - 12
        - 15
    - regex_match:
      - patterns:
        - ^[一-龥]{2,4}(?:区|县)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*区县.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 4
        - 8
    - regex_match:
      - patterns:
        - \b(district|county|area|区县|地区)\b
- name: 年节及纪念日
  model_id: 1139
  data_tag: T_FESTIVAL_ANNIVERSARY
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 6
        - 9
    - dict_match:
      - dict_ids:
        - D_FESTIVAL_ANNIVERSARY
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*年节及纪念日.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 6
        - 20
    - regex_match:
      - patterns:
        - \b((annual|commemorative|memorial)_?(festival|day|date)|年节(及)?(纪念日)?|纪念日|节日)\b
- name: 生肖
  model_id: 9594
  data_tag: T_ZODIAC_CH
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 3
    - dict_match:
      - dict_ids:
        - D_ZODIAC_CH
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*生肖.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 6
        - 14
    - regex_match:
      - patterns:
        - \b((chinese_?)zodiac|sheng_?xiao|生肖)\b
- name: 二十四节气
  model_id: 1007
  data_tag: T_SOLAR_TERM
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 6
    - dict_match:
      - dict_ids:
        - D_SOLAR_TERM
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*二十四节气.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 6
        - 15
    - regex_match:
      - patterns:
        - \b(solar_?term(s)?|24_?solar_?terms|(二十四)?节气)\b
- name: 传统节日
  model_id: 4611
  data_tag: T_FESTIVAL_CH
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 6
        - 9
    - dict_match:
      - dict_ids:
        - D_FESTIVAL_CH
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*传统节日.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 12
        - 18
    - regex_match:
      - patterns:
        - \b(traditional_?(festival|holiday)|传统(节日|节庆))\b
- name: 农历日期
  model_id: 9151
  data_tag: T_LUNAR_DATE
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 12
        - 15
    - regex_match:
      - patterns:
        - ^(正|二|三|四|五|六|七|八|九|十|冬|腊)月(初[一二三四五六七八九十]|[二十][十]|十[一二三四五六七八九]|二十[一二三四五六七八九]|三十|廿[一二三四五六七八九])$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*农历日期.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 8
        - 13
    - regex_match:
      - patterns:
        - \b(lunar_?(date|calendar|day)|农历(日期|日))\b
- name: 农历年份
  model_id: 3563
  data_tag: T_LUNAR_YEAR
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 9
    - regex_match:
      - patterns:
        - ^[甲乙丙丁戊己庚辛壬癸][子丑寅卯辰巳午未申酉戌亥]年$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*农历年份.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 7
        - 12
    - regex_match:
      - patterns:
        - \b(lunar_?(year|yr)|农历(年|年份))\b
- name: 币种
  model_id: 8669
  data_tag: T_CURRENCY_TYPE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - dict_match:
      - dict_ids:
        - D_CURRENCY_TYPE
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*币种.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 6
        - 12
    - regex_match:
      - patterns:
        - \b(currency|money_?type|币种|货币(类型)?)\b
- name: 手机型号
  model_id: 9610
  data_tag: T_MOBILE_PHONE_MODEL
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - ^(iPhone)\s*\d{1,2}(?:\s*(?:Pro|Max|Plus|SE))?$
        - ^(Samsung)\s*(?:Galaxy\s*(?:S|Note|A|Z|M)\s*\d{1,2}(?:\s*(?:Ultra|FE|5G))?)$
        - ^(Huawei)\s*(?:Mate|P|Nova)\s*\d{1,3}(?:\s*(?:Pro|Ultra|E|Art))?$
        - ^(Xiaomi|Redmi)\s*(?:Mix\s*Fold\s*\d{1,3}|\d{1,3}|Note\s*\d{1,3}|K\d{1,3})(?:\s*(?:Pro|Ultra|A|Turbo))?$
        - ^(Oppo)\s*(?:Reno|Find\s*(?:X|N))\s*\d{1,3}(?:\s*(?:Pro|Lite))?$
        - ^(Vivo)\s*(?:X|S|iQOO\s*Neo)\s*\d{1,3}(?:\s*(?:Pro|\+))?$
        - ^(Honor)\s*(?:Magic)\s*\d{1,3}(?:\s*(?:Pro|GT))?$
        - ^(OnePlus)\s*(?:\d{1,2}|Ace\s*\d{1,2})(?:\s*(?:Pro|T))?$
        - ^(Google\s*)?Pixel\s*\d{1,2}(?:\s*(?:Pro|XL|a))?$
        - ^(Realme)\s*(?:GT\s*Neo\s*\d{1,3}|\d{1,2})(?:\s*Pro)?(?:\s*\+)?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*手机型号.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 10
        - 14
    - regex_match:
      - patterns:
        - \b(mobile_?(model|type|version)|手机(型号|类型|版本))\b
- name: 温度
  model_id: 10709
  data_tag: T_TEMPERATURE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 3
        - 8
    - regex_match:
      - patterns:
        - ^(-?\d{1,3}(?:\.\d{1,2})?)\s*(?:°C|℃|°F|℉|度)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*温度.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 6
        - 11
    - regex_match:
      - patterns:
        - \b(temperature|气温|温度)\b
- name: 计量单位
  model_id: 1625
  data_tag: T_PHY_UNIT
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_range:
        - 3
        - 10
    - dict_match:
      - dict_ids:
        - D_PHY_UNIT
      - match_type: suffix
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*计量单位.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 4
        - 12
    - regex_match:
      - patterns:
        - \b(unit|measurement|计量单位|量度)\b
- name: 区域编码
  model_id: 2413
  data_tag: T_AREA_CODE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 3
        - 4
        - 6
        - 9
        - 12
    - regex_match:
      - patterns:
        - ^(?:0\d{2,3}|\d{6}(\d{3}){0,2})$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*区域编码.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 6
        - 12
    - regex_match:
      - patterns:
        - \b(area_?(code|id|number)|区域(编码|代码|编号))\b
- name: 文件名称
  model_id: 2404
  data_tag: T_FILENAME
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - ^[^\\/:*?"<>|\r\n]+\.(?:txt|pdf|docx?|xlsx?|pptx?|rtf|csv|jpe?g|png|gif|bmp|tif?f|webp|mp[34]|wav|aac|avi|mov|wmv|mkv|flv|zip|rar|7z|tar|gz|bz2|html?|css|js|xml|json|yaml|py|java|c|cpp|h|cs|php|sql|log|md|ini|cfg|bat|sh|exe|dll|app|dmg|iso)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*文件名称.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 8
        - 15
    - regex_match:
      - patterns:
        - \b(file_?(name|title|identifier)|文件(名|名称|标识))\b
- name: 月份
  model_id: 1229
  data_tag: T_MONTH
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 6
        - 9
    - regex_match:
      - patterns:
        - ^(?:一|二|三|四|五|六|七|八|九|十|十一|十二)月$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*月份.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 3
        - 6
    - regex_match:
      - patterns:
        - \b(month|mon|月份)\b
- name: 季度
  model_id: 7160
  data_tag: T_QUARTER
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 2
        - 6
        - 12
    - regex_match:
      - patterns:
        - ^(?:[Qq][1-4]|(?:第一|第二|第三|第四)季度|[春夏秋冬]季)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*季度.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 3
        - 7
    - regex_match:
      - patterns:
        - \b(quarter|qtr|季度)\b
- name: 年份
  model_id: 4050
  data_tag: T_YEAR
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_range:
        - 7
        - 24
    - regex_match:
      - patterns:
        - ^(?:公元[前后]?)?(?:[〇零一二三四五六七八九十百千]{1,4}|\d{1,4})年$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*年份.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 2
        - 4
        - 6
    - regex_match:
      - patterns:
        - \b(year|yr|年份)\b
- name: 小时
  model_id: 6703
  data_tag: T_HOUR
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_range:
        - 2
        - 8
    - regex_match:
      - patterns:
        - ^(?:[01]?\d|2[0-3])\s*(h|hour|hours|时|小时)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*小时.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 2
        - 4
        - 6
    - regex_match:
      - patterns:
        - \b(hour|hr|小时)\b
- name: 芝麻ID
  model_id: 4493
  data_tag: T_ZHIMA_ID
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 16
    - regex_match:
      - patterns:
        - ^2088\d{12}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*芝麻ID.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 7
        - 9
    - regex_match:
      - patterns:
        - \b(zhima|sesame|芝麻)_?id\b
- name: 2088ID
  model_id: 4935
  data_tag: T_2088_ID
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 16
    - regex_match:
      - patterns:
        - ^2088\d{12}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*2088ID.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 6
        - 12
    - regex_match:
      - patterns:
        - \b(2088|alipay|支付宝)_?id\b
- name: 网商银行ID
  model_id: 3838
  data_tag: T_COMMERCE_BANK_ID
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 13
    - regex_match:
      - patterns:
        - ^[A-Z]\d{12}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*网商银行ID.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 6
        - 7
        - 14
        - 15
    - regex_match:
      - patterns:
        - \b(mybank|网商银行)_?id\b
- name: 保单号
  model_id: 1250
  data_tag: T_COMMERCIAL_ID
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 20
        - 22
    - regex_match:
      - patterns:
        - ^((?:PDAA|PDAB|PDZB|PDZB)\d{18}|\d{20})$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*保单号.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 8
        - 16
    - regex_match:
      - patterns:
        - \b(insurance|policy)_?(number|no|id)|保单(号|号码)\b
- name: 澳门特别行政区护照号码
  model_id: 5740
  data_tag: T_PASSPORT_ID_MACAO
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 9
    - regex_match:
      - patterns:
        - ^(?:MA)\d{7}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*澳门特别行政区护照号码.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 15
        - 21
    - regex_match:
      - patterns:
        - \b(macau_?passport_?(number|no|id)|澳门护照号(码)?)\b
- name: 香港特别行政区护照号码
  model_id: 8408
  data_tag: T_PASSPORT_ID_HK
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 9
    - regex_match:
      - patterns:
        - ^(?:K)\d{8}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*香港特别行政区护照号码.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 12
        - 24
    - regex_match:
      - patterns:
        - \b((hk|hongkong)_?passport_?(number|no|id)|香港护照号(码)?)\b
- name: 房屋所有权证书号码
  model_id: 3026
  data_tag: T_PROPERTY_OWNERSHIP_CERT_ID
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 33
        - 34
    - regex_match:
      - patterns:
        - ^(X)?[一-龥]房权证[一-龥]字第\d{9}号
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*房屋所有权证书号码.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 20
        - 37
    - regex_match:
      - patterns:
        - \b(house|property)_?ownership_?(certificate|cert)_?(number|no|id)|房屋所有权证(书)?(号|号码)\b
- name: 不动产权证书号码
  model_id: 4144
  data_tag: T_REAL_ESTATE_OWNERSHIP_CERT_ID
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_range:
        - 43
        - 67
    - regex_match:
      - patterns:
        - ^[一-龥]\((?:19|20)\d{2}\)[一-龥]{2,10}市不动产权第\d{7}号$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*不动产权证书号码.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 21
        - 40
    - regex_match:
      - patterns:
        - \b(real_?estate_?ownership_?(certificate|cert)_?(number|no|id)|不动产权证(书)?(号|号码))\b
- name: 结婚证号码
  model_id: 10305
  data_tag: T_MARRIAGE_CERT_ID
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 19
        - 20
    - regex_match:
      - patterns:
        - ^(B)?J\d{6}\-(?:19|20)\d{2}\-\d{6}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*结婚证号码.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(marriage_?(cert(ificate)?|id)_?(number|no)?|结婚证(号码|号)?)\b
- name: 离婚证号码
  model_id: 3795
  data_tag: T_DIVORCE_CERT_ID
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 19
        - 20
    - regex_match:
      - patterns:
        - ^(B)?L\d{6}\-(?:19|20)\d{2}\-\d{6}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*离婚证号码.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(divorce_?(cert(ificate)?|id)_?(number|no)?|离婚证(号码|号)?)\b
- name: 住房公积金账号
  model_id: 4189
  data_tag: T_HOUSING_FUND_ACCOUNT
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 12
    - regex_match:
      - patterns:
        - ^[A-Za-z0-9]{12}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*住房公积金账号.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(housing_?fund_?account|hpf_?(account|no|number)|(住房)?公积金账号)\b
- name: 国际银行账号
  model_id: 6729
  data_tag: T_IBAN
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_range:
        - 15
        - 32
    - regex_match:
      - patterns:
        - ^[A-Z]{2}\d{2}[A-Z0-9]{11,28}
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*国际银行账号.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(iban|international_?bank_?account_?(number|no)|国际银行账号)\b
- name: 台湾座机号码
  model_id: 9238
  data_tag: T_LANDLINE_NUMBER_TW
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 9
        - 10
        - 11
        - 12
    - regex_match:
      - patterns:
        - ^0\d{1,2}-?\d{7,8}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*台湾座机号码.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((tw|taiwan)_?landline_?(number|no)|台湾座机号(码)?|台湾固话)\b
- name: 台湾手机号码
  model_id: 9774
  data_tag: T_MOBILE_PHONE_NUMBER_TW
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 10
    - regex_match:
      - patterns:
        - ^09\d{8}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*台湾手机号码.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((tw|taiwan)_?mobile_?(number|no)|台湾手机号(码)?)\b
- name: 台湾护照号码
  model_id: 8600
  data_tag: T_PASSPORT_ID_TW
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 9
    - regex_match:
      - patterns:
        - ^3[0-9]{8}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*台湾护照号码.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(tw_?passport_?(number|no)|台湾护照号(码)?)\b
- name: 澳门电话号码
  model_id: 4480
  data_tag: T_PHONE_NUMBER_MACAO
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 8
    - regex_match:
      - patterns:
        - ^(?:6|8)\d{7}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*澳门电话号码.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(macau_?(phone|telephone)_?(number|no)|澳门电话号码|澳门电话)\b
- name: 澳门身份证号码
  model_id: 8631
  data_tag: T_ID_CARD_MACAO
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 10
    - regex_match:
      - patterns:
        - ^[1-8]\d{6}\(\d\)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*澳门身份证号码.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(macau_?(id|identity)(_?card)?_?(number|no)?|澳门身份证(号|号码)?)\b
- name: ONEID
  model_id: 2797
  data_tag: T_ONEID
  rules:
  - rule_type: column_value
    rule_score: 10
    matches:
    - length_match:
      - length_enum:
        - 16
    - regex_match:
      - patterns:
        - ^[0-9A-F]{32}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*ONEID.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(one_?id)\b
- name: SessionID
  model_id: 9325
  data_tag: T_SESSION_ID
  rules:
  - rule_type: column_value
    rule_score: 10
    matches:
    - length_match:
      - length_enum:
        - 32
        - 64
    - regex_match:
      - patterns:
        - ^([0-9A-Za-z]{32}|[0-9A-Za-z]{64})$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*SessionID.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(session_?id)\b
- name: TokenID
  model_id: 4313
  data_tag: T_TOKEN_ID
  rules:
  - rule_type: column_value
    rule_score: 10
    matches:
    - length_match:
      - length_enum:
        - 256
    - regex_match:
      - patterns:
        - ^([a-zA-Z0-9\\-_=]{128}|[a-zA-Z0-9\\-_=]{256})$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*TokenID.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(token_?id)\b
- name: UTDID
  model_id: 7916
  data_tag: T_UTD_ID
  rules:
  - rule_type: column_value
    rule_score: 10
    matches:
    - length_match:
      - length_enum:
        - 24
    - regex_match:
      - patterns:
        - '[A-Za-z0-9+/]{24}$'
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*UTDID.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(utd_?id)\b
- name: USBKey
  model_id: 1364
  data_tag: T_USBKEY_ID
  rules:
  - rule_type: column_value
    rule_score: 10
    matches:
    - length_match:
      - length_enum:
        - 20
    - regex_match:
      - patterns:
        - ^[0-9]{20}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*USBKey.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(usb_?key)\b
- name: 会员等级
  model_id: 9620
  data_tag: T_MEMBERSHIP_LEVEL
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 4
        - 5
        - 6
        - 12
    - dict_match:
      - dict_ids:
        - D_MEMBERSHIP_LEVEL
      - match_type: prefix
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*会员等级.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(vip|member(ship)?_?(level|grade))\b
- name: 淘宝账号
  model_id: 8932
  data_tag: T_TAOBAO_ACCOUNT
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_range:
        - 5
        - 25
    - regex_match:
      - patterns:
        - ^[a-zA-Z0-9_一-龥]{5,25}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*淘宝账号.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((taobao|tb)_?(account|id))\b
- name: 医疗保险信息
  model_id: 1307
  data_tag: T_MEDICAL_INSURANCE_INFORMATION
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 16
    - regex_match:
      - patterns:
        - ^[0-9]{16}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*医疗保险信息.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((medical|health)_?insurance_?(info|information))\b
- name: 安全问题
  model_id: 3142
  data_tag: T_SECURITY_QUESTION
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - dict_match:
      - dict_ids:
        - D_SECURITY_QUESTION
      - match_type: prefix
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*安全问题.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((security|safety)_?question)\b
- name: 基金持有信息
  model_id: 8780
  data_tag: T_FUND_HOLDING_INFORMATION
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 6
    - regex_match:
      - patterns:
        - ^(50|51|52|53|58|15|16|18|30)\d{4}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*基金持有信息.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(fund_?holding_?(info|information))\b
- name: 基金收益信息
  model_id: 3786
  data_tag: T_FUND_RETURN_INFORMATION
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_range:
        - 4
        - 8
    - regex_match:
      - patterns:
        - ^[+-]?\d{1,3}(\.\d{1,2})?%$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*基金收益信息.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(fund_?(income|profit)_?(info|information))\b
- name: 银行业金融机构法人
  model_id: 1986
  data_tag: T_BANKING_LEGAL_PERSON
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - dict_match:
      - dict_ids:
        - D_BANKING_LEGAL_PERSON
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*银行业金融机构法人.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((bank|banking)_?(legal_?entity|institution))\b
- name: 公募基金管理机构
  model_id: 6361
  data_tag: T_PUBLIC_FUND_MANAGEMENT_INSTITUTION
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - dict_match:
      - dict_ids:
        - D_PUBLIC_FUND_MANAGEMENT_INSTITUTION
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*公募基金管理机构.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(public_?fund_?management_?institution)\b
- name: 期货公司
  model_id: 6349
  data_tag: T_FUTURES_COMPANY
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - dict_match:
      - dict_ids:
        - D_FUTURES_COMPANY
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*期货公司.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(futures_?(company|firm))\b
- name: 保险机构法人名单
  model_id: 10110
  data_tag: T_INSURANCE_LEGAL_PERSON
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - dict_match:
      - dict_ids:
        - D_INSURANCE_LEGAL_PERSON
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*保险机构法人名单.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(insurance_?(legal_?entity|institution)_?list)\b
- name: 证券公司
  model_id: 7045
  data_tag: T_SECURITIES_COMPANY
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - dict_match:
      - dict_ids:
        - D_SECURITIES_COMPANY
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*证券公司.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(securities_?(company|firm|broker)|stock_?exchange|证券(公司|交易所))\b
- name: 验证码（内容）
  model_id: 7080
  data_tag: T_VERIFICATION_CODE
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_range:
        - 4
        - 8
    - regex_match:
      - patterns:
        - ^[a-zA-Z0-9]{4,8}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*验证码.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(verification_?code|auth_?code|captcha|验证码)\b
- name: OSS产品敏感命令行
  model_id: 10456
  data_tag: T_OSS_SENSITIVE_COMMAND
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - regex_match:
      - patterns:
        - ^ossutil\s+(?:rm|delete|set-acl|modify)\s+oss://bucket/[a-zA-Z0-9._-]+\s+(?:--force|--recursive|--acl\s+public-read)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*OSS产品敏感命令行.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(oss_?(sensitive_?)?(cmd|command)|oss敏感命令(行)?)\b
- name: MSE产品敏感命令行
  model_id: 4034
  data_tag: T_MSE_SENSITIVE_COMMAND
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - regex_match:
      - patterns:
        - ^(?:mse-client|mse-admin)\s+(?:deleteConfig|modifyService|updateRouteRule)\s+(?:configs/|services/|routes/)[a-zA-Z0-9._-]+\s*$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*MSE产品敏感命令行.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(mse_?(sensitive_?)?(cmd|command)|mse敏感命令(行)?)\b
- name: PAI产品EAS敏感命令行
  model_id: 3994
  data_tag: T_PAI_EAS_SENSITIVE_COMMAND
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - regex_match:
      - patterns:
        - ^pai-eas\s+(?:delete|update|reset)\s+(?:eas-service-|model-deploy-|batch-job-)[a-zA-Z0-9._-]+\s*$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*PAI产品EAS敏感命令行.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(pai_?eas_?(sensitive_?)?(cmd|command)|eas_?(sensitive_?)?cmd|eas敏感命令(行)?)\b
- name: PAI产品DLC敏感命令行
  model_id: 10920
  data_tag: T_PAI_DLC_SENSITIVE_COMMAND
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - regex_match:
      - patterns:
        - ^dlc\s+(?:delete job|modify cluster|stop task)\s+[a-zA-Z0-9._-]+\s*$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*PAI产品DLC敏感命令行.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(pai_?dlc_?(sensitive_?)?(cmd|command)|dlc_?(sensitive_?)?cmd|dlc敏感命令(行)?)\b
- name: ClickHouse产品敏感命令行
  model_id: 4075
  data_tag: T_CLICKHOUSE_SENSITIVE_COMMAND
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - regex_match:
      - patterns:
        - ^(DROP\s+(TABLE|DATABASE)|ALTER\s+TABLE|TRUNCATE\s+TABLE)\s+[a-zA-Z0-9_]+(\.[a-zA-Z0-9_]+)?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*ClickHouse产品敏感命令行.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((ck|clickhouse)_?(sensitive_?)?(cmd|command)|(ck|clickhouse)敏感命令(行)?)\b
- name: 腾讯云AK
  model_id: 8035
  data_tag: T_TENCENT_CLOUD_AK
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_range:
        - 17
        - 24
    - regex_match:
      - patterns:
        - ^AKID[A-Za-z0-9]{13,20}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*腾讯云AK.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(tencent_?(cloud_?)?(ak|access_?key)|腾讯云(AK|访问密钥))\b
- name: Google Cloud Platform AK
  model_id: 1304
  data_tag: T_GOOGLE_CLOUD_AK
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_range:
        - 14
        - 34
    - regex_match:
      - patterns:
        - ^GOOG[\w\W]{10,30}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*Google Cloud Platform AK.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(gcp_?(ak|access_?key)|google_?(cloud_?)?(ak|access_?key))\b
- name: Microsoft Azure AK
  model_id: 7783
  data_tag: T_MICROSOFT_AZURE_AK
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_range:
        - 36
        - 42
    - regex_match:
      - patterns:
        - ^AZ[A-Za-z0-9]{34,40}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*Microsoft Azure AK.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((ms_?|microsoft_?)?azure_?(ak|access_?key))\b
- name: Amazon Web Services AK
  model_id: 1916
  data_tag: T_AMAZON_WEB_SERVICE_AK
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 20
    - regex_match:
      - patterns:
        - ^AKIA[A-Za-z0-9]{16}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*Amazon Web Services AK.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(aws|amazon_?web_?service(s)?)_?(ak|access_?key)\b
- name: IBM Cloud AK
  model_id: 1662
  data_tag: T_IBM_CLOUD_AK
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_range:
        - 13
        - 43
    - regex_match:
      - patterns:
        - ^IBM[A-Za-z0-9]{10,40}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*IBM Cloud AK.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(ibm_?cloud_?(ak|access_?key))\b
- name: Oracle Cloud AK
  model_id: 8547
  data_tag: T_ORACLE_CLOUD_AK
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_range:
        - 14
        - 44
    - regex_match:
      - patterns:
        - ^OCID[A-Za-z0-9]{10,40}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*Oracle Cloud AK.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(oracle_?cloud_?(ak|access_?key))\b
- name: 京东云AK
  model_id: 1309
  data_tag: T_JD_CLOUD_AK
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_range:
        - 32
        - 36
    - regex_match:
      - patterns:
        - ^JDC_[A-Z0-9]{28,32}
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*京东云AK.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((jd|jingdong)_?cloud_?(ak|access_?key)|京东云(AK|访问密钥))\b
- name: 阿里云AK
  model_id: 4167
  data_tag: T_ALI_CLOUD_AK
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_range:
        - 16
        - 24
    - regex_match:
      - patterns:
        - ^LTAI[A-Za-z0-9]{12,20}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*阿里云AK.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((aliyun|ali_?cloud)_?(ak|access_?key)|阿里云(AK|访问密钥))\b
- name: 职业
  model_id: 5764
  data_tag: T_OCCUPATION
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - dict_match:
      - dict_ids:
        - D_OCCUPATION
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*职业.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(profession|occupation|职业)\b
- name: 工作单位
  model_id: 6568
  data_tag: T_EMPLOYER
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 6
        - 36
    - regex_match:
      - patterns:
        - ^.*?(公司|医院|大学|集团|局|所|中心)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*工作单位.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(work_?unit|employer|工作单位)\b
- name: 教育经历
  model_id: 6372
  data_tag: T_EDUCATION_EXPERIENCE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - ^\d{4}-(\d{4}|至今)\s+[一-龥]{2,10}(小学|附小|中学|附中|初中|高中|大学|中专|大专|技校|卫校)\s+(本科|硕士|博士)?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*教育经历.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((edu|education)_?(history|background)|教育经历)\b
- name: 工作经历
  model_id: 6702
  data_tag: T_PROFESSIONAL_EXPERIENCE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - ^\d{4}-(\d{4}|至今)\s+[一-龥]{2,10}(公司|集团)?\s+[一-龥]{2,10}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*工作经历.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((work(ing)?|profession(al)?)_?(history|experience)|工作经历)\b
- name: 传染病史
  model_id: 1951
  data_tag: T_INFECTIOUS_DISEASE_HISTORY
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - dict_match:
      - dict_ids:
        - D_INFECTIOUS_DISEASE_HISTORY
      - match_type: contain
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*传染病史.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(infectious_?disease_?history|传染病史)\b
- name: 家族病史
  model_id: 5812
  data_tag: T_FAMILIY_MEDICAL_HISTORY
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 12
        - 15
        - 18
        - 21
        - 24
    - regex_match:
      - patterns:
        - ^(?:家族|父母|祖父|祖母)[曾]?[有|患|患有]?(?:糖尿病|高血压|癌症|心脏病|精神疾病)(?:史|记录)?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*家族病史.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(family_?medical_?history|家族病史)\b
- name: 以往病史
  model_id: 7544
  data_tag: T_PAST_MEDICAL_HISTORY
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - regex_match:
      - patterns:
        - ^(?:曾患|既往|患有)?(?:[一-龥]{1,10})(高血压|癌症|炎|病|手术|骨折)(?:史|记录)?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*以往病史.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(medical_?history|past_?medical_?history|以往病史)\b
- name: 数据库账密信息
  model_id: 6082
  data_tag: T_DATABASE_CREDENTIAL
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - regex_match:
      - patterns:
        - ^(user(name)?|u)[=:]\s*[\w\-]+[\s;&].*(pass(word)?|pwd)[=:]\s*[^\s;&]+$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*数据库账密信息.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((db|database)_?(account|cred(ential)?(s)?|user_?(name|id)|password|pwd)_?(info|information)?|数据库(账号|账密|密码)(信息)?)\b
- name: MYSQL数据库连接命令
  model_id: 7490
  data_tag: T_MYSQL_CONNECTION_COMMAND
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - ^mysql\s+(?:--user=|(?:-u))\s*[\w-]+\s+(?:--password=|(?:-p))[^\s]+$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*MYSQL数据库连接命令.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(mysql_?conn(ect)?(ion)?_?(cmd|command)|mysql数据库连接命令)\b
- name: 数据库密码
  model_id: 1685
  data_tag: T_DATABASE_PASSWORD
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - regex_match:
      - patterns:
        - ^(?:password|pwd|pass(word)?|passwd)[=:]\s*[^\s;&]+$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*数据库密码.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(db_?(password|pwd)|数据库密码)\b
- name: 国籍
  model_id: 10299
  data_tag: T_NATIONALITY
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - dict_match:
      - dict_ids:
        - D_NATIONALITY
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*国籍.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(nationality|citizenship|国籍)\b
- name: 婚姻状况
  model_id: 3937
  data_tag: T_MARITAL_STATUS
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 6
    - dict_match:
      - dict_ids:
        - D_MARITAL_STATUS
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*婚姻状况.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((marriage|marital)_?status|婚姻状况)\b
- name: 微信号
  model_id: 1873
  data_tag: T_WECHAT_ID
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_range:
        - 6
        - 20
    - regex_match:
      - patterns:
        - ^[a-zA-Z][a-zA-Z0-9_-]{5,19}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*微信号.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((wx|wechat)_?(id|account)|微信(号|账号))\b
- name: 吸烟史
  model_id: 2301
  data_tag: T_SMOKING_HISTORY
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 6
        - 9
        - 12
        - 15
        - 18
        - 21
    - dict_match:
      - dict_ids:
        - D_SMOKING_HISTORY
      - match_type: contain
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*吸烟史.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((smoke|smoking)?_?history|吸烟史)\b
- name: 学校
  model_id: 1566
  data_tag: T_SCHOOL_NAME
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 12
        - 15
        - 18
        - 21
        - 24
        - 27
        - 30
    - dict_match:
      - dict_ids:
        - D_SCHOOL_NAME
      - match_type: suffix
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*学校.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(school|institution|academy|学校)\b
- name: 学院
  model_id: 7068
  data_tag: T_COLLEGE_NAME
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - dict_match:
      - dict_ids:
        - D_COLLEGE_NAME
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*学院.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(college|faculty|department|学院|院系)\b
- name: 学历
  model_id: 3195
  data_tag: T_EDUCATION_LEVEL
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - dict_match:
      - dict_ids:
        - D_EDUCATION_LEVEL
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*学历.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((edu|education)_?(level|qualification)|学历)\b
- name: 学位
  model_id: 5766
  data_tag: T_DEGREE
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - dict_match:
      - dict_ids:
        - D_DEGREE
      - match_type: suffix
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*学位.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(degree|academic_?degree|学位)\b
- name: 资质证书编号
  model_id: 7296
  data_tag: T_QUALIFICATION_CERT_ID
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 12
        - 26
    - regex_match:
      - patterns:
        - ^\d{12}$
        - ^APJ\-\([京|津|沪|渝|冀|豫|云|辽|黑|湘|皖|鲁|新|苏|浙|赣|鄂|桂|甘|晋|蒙|陕|吉|闽|贵|粤|青|藏|川|宁|琼](●煤)?\)\-\d{3}$
        - ^自资规甲字\d{8}$
        - ^[京|津|沪|渝|冀|豫|云|辽|黑|湘|皖|鲁|新|苏|浙|赣|鄂|桂|甘|晋|蒙|陕|吉|闽|贵|粤|青|藏|川|宁|琼]自资规乙字\d{8}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*资质证书编号.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(qualification_?cert(ificate)?_?(number|no|id)|资质号|资质证书(编号|号(码)?))\b
- name: 机构名称
  model_id: 5803
  data_tag: T_ORGANIZATION_NAME
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_range:
        - 8
        - 64
    - dict_match:
      - dict_ids:
        - D_ORGANIZATION_NAME
      - match_type: suffix
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*机构名称.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b((org|institution)_?name|机构(名称|名)?)\b
- name: 党派
  model_id: 3433
  data_tag: T_POLITICAL_PARTY
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - dict_match:
      - dict_ids:
        - D_POLITICAL_PARTY
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*党派.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(political_?party|party_?affiliation|党派)\b
- name: CVN2
  model_id: 1025
  data_tag: T_CVN2
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 3
    - regex_match:
      - patterns:
        - ^\d{3}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*CVN2.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(cvn2|card_?verification_?(number|no|code))\b
- name: 动态口令
  model_id: 1004
  data_tag: T_DYNAMIC_PASSWORD
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 6
        - 8
    - regex_match:
      - patterns:
        - ^\d{6}|\d{8}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*动态口令.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(dynamic_?(password|pwd|token)|动态(口令|密码))\b
- name: 区域
  model_id: 3972
  data_tag: T_REGION
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 9
        - 12
        - 15
        - 18
        - 21
        - 24
        - 27
    - regex_match:
      - patterns:
        - ^[一-龥]{2,8}(省|市|区|县)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*区域.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(region|area|区域)\b
- name: 街道
  model_id: 6915
  data_tag: T_STREET
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 9
        - 12
        - 15
        - 18
        - 21
        - 24
        - 27
    - regex_match:
      - patterns:
        - ^[一-龥]{2,8}(街|路|巷|道)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*街道.*
  - rule_type: column_name
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - \b(street|road|avenue|街道)\b
- name: 血缘关系
  model_id: 9225
  data_tag: T_BLOOD_RELATIONSHIP
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 6
        - 9
    - dict_match:
      - dict_ids:
        - D_BLOOD_RELATIONSHIP
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*血缘关系.*
- name: 社交关系
  model_id: 5841
  data_tag: T_SOCIAL_RELATIONSHIP
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 6
        - 9
        - 12
    - dict_match:
      - dict_ids:
        - D_SOCIAL_RELATIONSHIP
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*社交关系.*
- name: 企业名称
  model_id: 10370
  data_tag: T_ENTERPRISE_NAME
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_range:
        - 8
        - 78
    - regex_match:
      - patterns:
        - ^[一-龥A-Za-z0-9]{2,20}(有限公司|集团|股份公司|有限责任公司)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*企业名称.*
- name: 经营范围
  model_id: 9956
  data_tag: T_BUSINESS_SCOPE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - dict_match:
      - dict_ids:
        - D_BUSINESS_SCOPE
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*经营范围.*
- name: 行业分类
  model_id: 5298
  data_tag: T_INDUSTRY_CLASSIFICATION
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - dict_match:
      - dict_ids:
        - D_INDUSTRY_CLASSIFICATION
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*行业分类.*
- name: 企业类型
  model_id: 1378
  data_tag: T_ENTERPRISE_TYPE
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 12
        - 15
        - 18
        - 21
        - 24
    - dict_match:
      - dict_ids:
        - D_ENTERPRISE_TYPE
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*企业类型.*
- name: 存款业务种类
  model_id: 2632
  data_tag: T_DEPOSIT_BUSINESS_TYPE
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 12
        - 15
    - dict_match:
      - dict_ids:
        - D_DEPOSIT_BUSINESS_TYPE
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*存款业务种类.*
- name: 合同种类
  model_id: 10002
  data_tag: T_CONTRACT_TYPE
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 6
        - 12
        - 18
        - 24
    - dict_match:
      - dict_ids:
        - D_CONTRACT_TYPE
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*合同种类.*
- name: 合同状态
  model_id: 7921
  data_tag: T_CONTRACT_STATUS
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 6
        - 9
    - dict_match:
      - dict_ids:
        - D_CONTRACT_STATUS
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*合同状态.*
- name: 合同编号
  model_id: 1008
  data_tag: T_CONTRACT_ID
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 20
        - 24
    - regex_match:
      - patterns:
        - ^[A-Z]{2}\-[A-Z]{2}\-[A-Z]{2}\-[A-Z]{2}\-\d{8}(\(\d{2}\))?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*合同编号.*
- name: 开户机构编号
  model_id: 7646
  data_tag: T_FINANCIAL_INSTITUTION_ID
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 12
    - regex_match:
      - patterns:
        - ^\d{12}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*开户机构编号.*
- name: 账户状态
  model_id: 3744
  data_tag: T_ACCOUNT_STATUS
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 6
    - dict_match:
      - dict_ids:
        - D_ACCOUNT_STATUS
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*账户状态.*
- name: 账户类型
  model_id: 6381
  data_tag: T_ACCOUNT_TYPE
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 6
        - 9
        - 12
    - dict_match:
      - dict_ids:
        - D_ACCOUNT_TYPE
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*账户类型.*
- name: 冻结类型
  model_id: 10458
  data_tag: T_FREEZE_TYPE
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 12
        - 15
        - 18
    - dict_match:
      - dict_ids:
        - D_FREEZE_TYPE
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*冻结类型.*
- name: 贷款类型
  model_id: 2962
  data_tag: T_LOAN_TYPE
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 15
    - dict_match:
      - dict_ids:
        - D_LOAN_TYPE
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*贷款类型.*
- name: 贷款用途
  model_id: 3981
  data_tag: T_LOAN_PURPOSE
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 6
        - 12
    - dict_match:
      - dict_ids:
        - D_LOAN_PURPOSE
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*贷款用途.*
- name: 发动机号码
  model_id: 10901
  data_tag: T_ENGINE_NUMBER
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_range:
        - 10
        - 17
    - regex_match:
      - patterns:
        - ^[A-HJ-NPR-Z0-9]{10,17}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*发动机号码.*
- name: 再保业务种类
  model_id: 1603
  data_tag: T_REINSURANCE_BUSINESS_TYPE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - dict_match:
      - dict_ids:
        - D_REINSURANCE_BUSINESS_TYPE
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*再保业务种类.*
- name: 再保险方式
  model_id: 6406
  data_tag: T_REINSURANCE_METHOD
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - dict_match:
      - dict_ids:
        - D_REINSURANCE_METHOD
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*再保险方式.*
- name: 假币收缴来源
  model_id: 1268
  data_tag: T_COUNTERFEIT_COLLECTION_SOURCE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - dict_match:
      - dict_ids:
        - D_COUNTERFEIT_COLLECTION_SOURCE
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*假币收缴来源.*
- name: 再贷款的贷款方式
  model_id: 5817
  data_tag: T_RELENDING_METHOD
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - dict_match:
      - dict_ids:
        - D_RELENDING_METHOD
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*再贷款的贷款方式.*
- name: LEI机构验证状态
  model_id: 1417
  data_tag: T_INSTITUTION_VERIFICATION_STATUS
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - dict_match:
      - dict_ids:
        - D_INSTITUTION_VERIFICATION_STATUS
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*LEI机构验证状态.*
- name: 金额
  model_id: 3997
  data_tag: T_AMOUNT
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - ^[+-]?(\d{1,3}(,\d{3})*|\d+)(\.\d{2})?(\s+)?((十|百|千|万|亿)+)?(元|\$|￥|¥|€|£|₽|₹|₩|₫|₴|₦|₱)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*金额.*
- name: 对手类型
  model_id: 9434
  data_tag: T_COUNTERPARTY_TYPE
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - dict_match:
      - dict_ids:
        - D_COUNTERPARTY_TYPE
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*对手类型.*
- name: 门店名称
  model_id: 1616
  data_tag: T_STORE_NAME
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 9
        - 12
        - 15
        - 18
        - 21
        - 24
        - 27
    - regex_match:
      - patterns:
        - ^[一-龥]{2,8}(店|铺)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*门店名称.*
- name: 柜台名称
  model_id: 3855
  data_tag: T_COUNTER_NAME
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 8
        - 24
    - regex_match:
      - patterns:
        - ^[A-Za-z0-9一-龥]{2,6}(柜台)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*柜台名称.*
- name: 设备类型
  model_id: 5784
  data_tag: T_DEVICE_TYPE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - dict_match:
      - dict_ids:
        - D_DEVICE_TYPE
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*设备类型.*
- name: 客户端类型
  model_id: 4163
  data_tag: T_CLIENT_TYPE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - dict_match:
      - dict_ids:
        - D_CLIENT_TYPE
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*客户端类型.*
- name: 单位类型
  model_id: 2807
  data_tag: T_UNIT_TYPE
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - dict_match:
      - dict_ids:
        - D_UNIT_TYPE
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*单位类型.*
- name: 贷款分类
  model_id: 4358
  data_tag: T_LOAN_CLASSIFY
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - dict_match:
      - dict_ids:
        - D_LOAN_CLASSIFY
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*贷款分类.*
- name: 代码
  model_id: 1415
  data_tag: T_CODE_CONTENT
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - regex_match:
      - patterns:
        - ^#include(\s)?[<"][a-zA-Z.]+[>"](\s+)?\n.*
        - ^import(\s)+[a-z\.\*A-Z]+(\s+)?\n.*
        - ^package(\s)+[a-z\.\*A-Z]+(\s+)?\n.*
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*代码.*
- name: 存放路径
  model_id: 9757
  data_tag: T_SAVE_PATH
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_range:
        - 1
        - 4096
    - regex_match:
      - patterns:
        - ^a-zA-Z:\(?:[^\/:?"<>|\r\n]+\)[^\/:?"<>|\r\n]$
        - ^\/(\w+\/?)+$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*存放路径.*
- name: shell脚本
  model_id: 3435
  data_tag: T_SHELL_SCRIPT
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - ^#!\s*/bin/(ba|k|z)?sh\b
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*shell脚本.*
- name: sql脚本
  model_id: 9062
  data_tag: T_SQL_SCRIPT
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - regex_match:
      - patterns:
        - ^(?:(SELECT|select))\s+.*\s+(FROM|from)\s+.*;$
        - ^(?:(CREATE|create))\s+(?:TABLE|VIEW|SCEHMA|DATABASE|FUNCTION|PROCEDURE|EVENT|TRIGGER|table|view|schema|database|function|procedure|event|trigger)\s+.*;$
        - ^(?:(DROP|drop))\s+(?:TABLE|VIEW|SCEHMA|DATABASE|FUNCTION|PROCEDURE|EVENT|TRIGGER|table|view|schema|database|function|procedure|event|trigger)\s+.*;$
        - ^(?:(TRUNCATE|truncate))\s+(?:TABLE|table)\s+.*;$
        - ^(?:(CREATE|create))\s+(INDEX|index)\s+.*\s+(ON|on)\s+.*?;$
        - ^(?:(INSERT|insert))\s+(INTO|into)\s+.*\s+.*;$
        - ^(?:(UPDATE|update))\s+.*\s+(SET|set)\s+.*;$
        - ^(?:(DELETE|delete))\s+(FROM|from)\s+.*;$
        - ^(?:(BEGIN|begin))\s+.*\s+(END|end);?$
        - ^(?:(DECLARE|declare))\s+.*$
        - ^(?:(ALTER|alter))\s+(?:TABLE|table)\s+.*;$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*sql脚本.*
- name: 户名（企业客户）
  model_id: 6097
  data_tag: T_ENTERPRISE_CUSTOMER
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - regex_match:
      - patterns:
        - ^.*?(公司|医院|大学|银行|集团|局|所|中心|人寿|协会|联通|移动)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*户名.*
- name: 网站账户
  model_id: 1624
  data_tag: T_WEB_ACCOUNT
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_range:
        - 3
        - 50
    - regex_match:
      - patterns:
        - ^[a-zA-Z0-9_.-]{3,50}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*网站账户.*
- name: QQ号
  model_id: 3763
  data_tag: T_QQ_NUMBER
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_range:
        - 5
        - 11
    - regex_match:
      - patterns:
        - ^[1-9]\d{4,10}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*QQ号.*
- name: 台湾通行证（台胞证号）
  model_id: 8066
  data_tag: T_TRAVEL_PERMIT_NUMBER_TW
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 9
    - regex_match:
      - patterns:
        - ^[LT]\d{8}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*台湾通行证.*
- name: 邮编
  model_id: 4952
  data_tag: T_POSTAL_CODE
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 6
    - regex_match:
      - patterns:
        - ^\d{6}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*邮编.*
- name: 比特币账号
  model_id: 3171
  data_tag: T_BITCOIN_ACCOUNT
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_range:
        - 26
        - 35
    - regex_match:
      - patterns:
        - ^[13][a-km-zA-HJ-NP-Z1-9]{25,34}
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*比特币账号.*
- name: ABA ROUTING
  model_id: 4183
  data_tag: T_ABA_ROUTING
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 9
    - regex_match:
      - patterns:
        - ^\d{9}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*ABA ROUTING.*
- name: 房产用途（内容）
  model_id: 1854
  data_tag: T_REAL_ESTATE_USAGE
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 12
    - dict_match:
      - dict_ids:
        - D_REAL_ESTATE_USAGE
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*房产用途.*
- name: 信贷记录
  model_id: 3398
  data_tag: T_CREDIT_RECORD
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 6
        - 24
    - regex_match:
      - patterns:
        - ^(逾期|不良记录|信用评分).*$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*信贷记录.*
- name: 普通贷款种类
  model_id: 8123
  data_tag: T_NORMAL_LOAN_TYPE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - dict_match:
      - dict_ids:
        - D_NORMAL_LOAN_TYPE
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*普通贷款种类.*
- name: 贷款产品类别
  model_id: 5671
  data_tag: T_LOAN_PRODUCT_TYPE
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 12
        - 15
        - 18
        - 21
        - 24
    - regex_match:
      - patterns:
        - ^[一-龥]{2,5}(快贷|融易贷|消费贷)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*贷款产品类别.*
- name: 征信信息
  model_id: 2007
  data_tag: T_CREDIT_INFORMATION
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - regex_match:
      - patterns:
        - ^(?:查询机构|信贷账户数|未结清账户)\s*[:：]\s*[\d]+$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*征信信息.*
- name: 交易和消费记录（内容）
  model_id: 8854
  data_tag: T_TRADE_CONSUMPTION_RECORD
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - regex_match:
      - patterns:
        - \d{4}-\d{2}-\d{2}\s+[一-龥]{2,8}\s+[+-]?\d+\.\d{2}
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*交易和消费记录.*
- name: 流水记录
  model_id: 6186
  data_tag: T_TRANSCATION_STATEMENT_RECORD
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - regex_match:
      - patterns:
        - \d{4}-\d{2}-\d{2}\s+[一-龥]{2,8}\s+[+-]?\d+\.\d{2}
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*流水记录.*
- name: 虚拟财产信息
  model_id: 8211
  data_tag: T_VIRTUAL_PROPERTY_INFORMATION
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 13
    - regex_match:
      - patterns:
        - ^[A-Z]{2}-\d{10}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*虚拟财产信息.*
- name: 虚拟交易
  model_id: 2881
  data_tag: T_VIRTUAL_TRANSCATION
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 14
    - regex_match:
      - patterns:
        - ^TX\d{8}\d{4}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*虚拟交易.*
- name: 住院志（内容）
  model_id: 5180
  data_tag: T_HOSTPITALIZATION_RECORD
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - dict_match:
      - dict_ids:
        - D_HOSTPITALIZATION_RECORD
      - match_type: contain
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*住院志.*
- name: 医嘱单
  model_id: 8268
  data_tag: T_MEDICAL_ORDER_SHEET
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 16
    - regex_match:
      - patterns:
        - ^YZ-\d{6}-\d{6}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*医嘱单.*
- name: 医嘱信息
  model_id: 9038
  data_tag: T_MEDICAL_ORDER_INFORMATION
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 16
    - regex_match:
      - patterns:
        - ^YZ-\d{6}-\d{6}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*医嘱信息.*
- name: 检验报告（内容）
  model_id: 2115
  data_tag: T_LAB_REPORT_CONTENT
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 16
    - regex_match:
      - patterns:
        - ^LAB-\d{8}-[A-Z]{3}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*检验报告.*
- name: 麻醉术前访视记录
  model_id: 8408
  data_tag: T_PREOPERATIVE_ANESTHESIA_VISIT_RECORD
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_range:
        - 11
        - 13
    - regex_match:
      - patterns:
        - ^MA-\d{6}-(I{1,3}|IV)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*麻醉术前访视记录.*
- name: 麻醉记录
  model_id: 4149
  data_tag: T_ANESTHESIA_RECORD
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_range:
        - 11
        - 13
    - regex_match:
      - patterns:
        - ^MA-\d{6}-(I{1,3}|IV)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*麻醉记录.*
- name: 手术记录
  model_id: 9012
  data_tag: T_SURGICAL_RECORD
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 16
    - regex_match:
      - patterns:
        - ^SR-\d{8}-\d{4}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*手术记录.*
- name: 护理记录
  model_id: 6005
  data_tag: T_NURSING_RECORD
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 12
        - 13
    - regex_match:
      - patterns:
        - ^NR-[A-Z]{2,3}-\d{6}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*护理记录.*
- name: 用药记录
  model_id: 10153
  data_tag: T_MEDICATION_RECORD
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - dict_match:
      - dict_ids:
        - D_MEDICATION_RECORD
      - match_type: suffix
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*用药记录.*
- name: 生育状况（内容）
  model_id: 6030
  data_tag: T_FERTILITY_STATUS
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_enum:
        - 12
    - dict_match:
      - dict_ids:
        - D_FERTILITY_STATUS
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*生育状况.*
- name: 血型（内容）
  model_id: 3117
  data_tag: T_BLOOD_TYPE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 1
        - 13
    - regex_match:
      - patterns:
        - ^(A|B|AB|O)型(Rh)?(\+|阳性|-|阴性)?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*血型.*
- name: 基因
  model_id: 9070
  data_tag: T_GENETIC_INFORMATION
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_range:
        - 3
        - 14
    - regex_match:
      - patterns:
        - ^rs\d{1,12}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*基因.*
- name: 授信种类
  model_id: 2989
  data_tag: T_CREDIT_GRANT_TYPE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_range:
        - 12
        - 21
    - regex_match:
      - patterns:
        - ^(个人|企业|公司)?(贷款|授信|额度|信用卡|抵押|消费|经营性|专项|综合)(额度|贷款|授信)?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*授信种类.*
- name: 授信用途
  model_id: 6056
  data_tag: T_CREDIT_GRANT_USAGE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - ^(购房|购车|装修|教育|生产|经营|消费|旅游|医疗|投资|综合)(支出|用途|贷款|资金)?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*授信用途.*
- name: 担保方式
  model_id: 7674
  data_tag: T_GUARANTEE_METHOD
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - ^(抵押|质押|保证|信用|连带责任|第三方|一般|最高额|全额|有限)(担保|保证|抵押|质押)?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*担保方式.*
- name: 违约种类
  model_id: 7069
  data_tag: T_BREACH_TYPE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - ^(逾期|未履行|提前终止|未支付|违反|未按时|拖欠)(还款|合同义务|合同|利息|担保条款|交货|租金)?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*违约种类.*
- name: 催收方式
  model_id: 2615
  data_tag: T_COLLECTION_METHOD
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - ^(电话|短信|上门|律师函|法律|邮件|第三方)(催收|提醒|诉讼|通知|委外)?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*催收方式.*
- name: 中间业务类型
  model_id: 4930
  data_tag: T_INTERMEDIATE_BUSINESS_TYPE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - ^(支付|代销|代理|财务|信用证|担保|托管)(结算|基金|保险|顾问|开立|业务)?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*中间业务类型.*
- name: 资金交易类型
  model_id: 2801
  data_tag: T_FUND_TRANSACTION_TYPE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - ^(转账|现金|代扣|投资|理财|基金)(汇款|存取|代缴|入金|申购|赎回)?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*资金交易类型.*
- name: 资金来源
  model_id: 2503
  data_tag: T_FUND_SOURCE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - ^(工资|投资|贷款|赠与|经营|父母)(收入|收益|资金|所得|赠与)?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*资金来源.*
- name: 车牌种类
  model_id: 6184
  data_tag: T_VEHICLE_PLATE_TYPE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - ^(小型|大型|新能|挂|摩托|教练)(汽车|车|源车)?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*车牌种类.*
- name: 车辆种类
  model_id: 2806
  data_tag: T_VEHICLE_TYPE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - ^(轿|货|摩托|客|挂|电动)(车)?$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*车辆种类.*
- name: 厂牌车型
  model_id: 7498
  data_tag: T_VEHICLE_BRAND_MODEL
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - dict_match:
      - dict_ids:
        - D_VEHICLE_BRAND
      - match_type: prefix
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*厂牌车型.*
- name: 驾驶档案编号
  model_id: 8044
  data_tag: T_DRIVER_LICENSE_FILE_NUMBER
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 12
    - regex_match:
      - patterns:
        - ^[A-Z][A-Z0-9]{11}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*驾驶档案编号.*
- name: 准驾车型
  model_id: 3285
  data_tag: T_DRIVER_LICENSE_CLASS
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - dict_match:
      - dict_ids:
        - D_DRIVER_LICENSE_CLASS
      - match_type: exact
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*准驾车型.*
- name: 工作证（内容）
  model_id: 9515
  data_tag: T_EMPLOYEE_ID
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - regex_match:
      - patterns:
        - ^(工号|员工号|工作证号)[:：][A-Za-z0-9]{6,12}$
        - ^(EmpID:|BadgeNo:)?([A-Z]{2,5})([-/_])([A-Z0-9]{2,5})([-/_])\d{2,6}$
        - ^\d{7,10}[-_]\d$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*工作证.*
- name: 未公开的违法犯罪记录
  model_id: 10590
  data_tag: T_UNPUB_CRIME_RECORD
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - '[一-龥]{2,10}(罪|判决)'
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*未公开的违法犯罪记录.*
- name: 社会保障卡
  model_id: 1050
  data_tag: T_SOCIAL_CARD_ID
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 9
    - regex_match:
      - patterns:
        - \d{9}
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*社会保障卡.*
- name: 社会保障卡号（内容）
  model_id: 6506
  data_tag: T_SOCIAL_CARD_ID_CONTENT
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_enum:
        - 9
    - regex_match:
      - patterns:
        - \d{9}
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*社会保障卡号.*
- name: 居住证（内容）
  model_id: 3780
  data_tag: T_RESIDENCE_PERMIT_ID
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 18
    - regex_match:
      - patterns:
        - ^(810000|820000|830000)(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dX]$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*居住证.*
- name: 行踪轨迹
  model_id: 9951
  data_tag: T_MOVEMENT_TRACE
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - length_match:
      - length_range:
        - 12
        - 128
    - regex_match:
      - patterns:
        - ^(?:步行至|驾驶|乘坐).*$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*行踪轨迹.*
- name: 网页浏览记录
  model_id: 5247
  data_tag: T_WEB_BROWSING_HISTORY
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - regex_match:
      - patterns:
        - ^(?:https?://[\w.-]+(?:/[\w.-]*)*)\s+(?:\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}:\d{2}:\d{2})$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*网页浏览记录.*
- name: 住宿信息
  model_id: 5519
  data_tag: T_ACCOMMODATION_INFORMATION
  rules:
  - rule_type: column_value
    rule_score: 30
    matches:
    - dict_match:
      - dict_ids:
        - D_ACCOMMODATION_INFORMATION
      - match_type: contain
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*住宿信息.*
- name: 精准定位信息
  model_id: 4865
  data_tag: T_PRECISE_LOCATION
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_range:
        - 13
        - 21
    - regex_match:
      - patterns:
        - ^-?(?:[0-8]?\d(?:\.\d{4,})|90(?:\.0{4,})?),\s*-?(?:(?:1[0-7]\d|[0-9]?\d)(?:\.\d{4,})|180(?:\.0{4,})?)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*精准定位信息.*
- name: 年龄（内容）
  model_id: 5884
  data_tag: T_AGE
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - length_match:
      - length_range:
        - 4
        - 9
    - regex_match:
      - patterns:
        - ^([1-9]|[1-9]\d|100)(岁|周岁)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*年龄.*
- name: 出入证
  model_id: 9494
  data_tag: T_PASSPORT_ID
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 9
    - regex_match:
      - patterns:
        - ^([C][A-HJ-NP-Z0-9]\d{7}|[HM]\d{8})$
        - ^[LT]\d{8}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*出入证.*
- name: ICCID
  model_id: 6502
  data_tag: T_ICCID
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 19
        - 20
    - regex_match:
      - patterns:
        - ^89\d{17,18}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*ICCID.*
- name: 台湾身份证
  model_id: 10755
  data_tag: T_ID_CARD_TW
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 10
    - regex_match:
      - patterns:
        - ^[A-Z][1-2]\d{8}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*台湾身份证.*
- name: 经度
  model_id: 7372
  data_tag: T_LONGITUDE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - ^-?(?:(?:1[0-7]\d|[0-9]?\d)(?:\.\d{4,})|180(?:\.0{4,})?)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*经度.*
- name: 纬度
  model_id: 9462
  data_tag: T_LATITUDE
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - ^-?(?:[0-8]?\d(?:\.\d{4,})|90(?:\.0{4,})?)$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*纬度.*
- name: 香港电话号码
  model_id: 8152
  data_tag: T_PHONE_NUMBER_HK
  rules:
  - rule_type: column_value
    rule_score: 60
    matches:
    - length_match:
      - length_enum:
        - 8
    - regex_match:
      - patterns:
        - ^[2-9]\d{7}$
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*香港电话号码.*
- name: 医疗机构名称
  model_id: 4282
  data_tag: T_MEDICAL_INSTITUTION_NAME
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - dict_match:
      - dict_ids:
        - D_MEDICAL_INSTITUTION_NAME
      - match_type: suffix
  - rule_type: column_comment
    rule_score: 60
    matches:
    - regex_match:
      - patterns:
        - .*医疗机构名称.*
- name: CVE编号
  model_id: 5093
  data_tag: T_CVE
  rules:
  - rule_type: column_value
    rule_score: 80
    matches:
    - regex_match:
      - patterns:
        - ^CVE-\d{4}-\d{4,7}$
