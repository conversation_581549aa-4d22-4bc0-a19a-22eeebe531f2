discovery_models:
  - name: "身份证 (中国内地)"
    model_id: 100
    data_tag: "T_ID_CARD_CH"   
    rules:
    - rule_id: 1001
      rule_params:
        - max_context_distance: 30

      detect:
      - regex_search:
        - patterns: ["^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9Xx]$"]         

      filter:
      - function_match:
        - function_name: "ID_CARD"

      context:
      - enum_match:
        - enum: ["idcard", "idnumber", "identificationnumber", "identitycard", "citizenid", "nationalid"]