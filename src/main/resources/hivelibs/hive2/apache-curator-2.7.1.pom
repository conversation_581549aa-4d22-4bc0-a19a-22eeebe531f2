<?xml version="1.0" encoding="UTF-8"?><!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.apache</groupId>
        <artifactId>apache</artifactId>
        <version>14</version>
    </parent>

    <groupId>org.apache.curator</groupId>
    <artifactId>apache-curator</artifactId>
    <version>2.7.1</version>
    <packaging>pom</packaging>

    <name>Apache Curator</name>
    <description>
        Curator is a set of Java libraries that make using Apache ZooKeeper much easier.
    </description>
    <url>http://curator.apache.org</url>
    <inceptionYear>2011</inceptionYear>

    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <organization>
        <name>The Apache Software Foundation</name>
        <url>http://www.apache.org/</url>
    </organization>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.build.resourceEncoding>UTF-8</project.build.resourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <jdk-version>1.6</jdk-version>

        <surefire-forkcount>1</surefire-forkcount>

        <!-- versions -->
        <maven-project-info-reports-plugin-version>2.7</maven-project-info-reports-plugin-version>
        <maven-javadoc-plugin-version>2.9.1</maven-javadoc-plugin-version>
        <maven-dependency-plugin-version>2.8</maven-dependency-plugin-version>
        <maven-install-plugin-version>2.5.1</maven-install-plugin-version>
        <maven-compiler-plugin-version>3.1</maven-compiler-plugin-version>
        <maven-bundle-plugin-version>2.3.7</maven-bundle-plugin-version>
        <maven-surefire-plugin-version>2.17</maven-surefire-plugin-version>
        <maven-site-plugin-version>3.3</maven-site-plugin-version>
        <doxia-module-confluence-version>1.5</doxia-module-confluence-version>
        <maven-scm-publish-plugin-version>1.0</maven-scm-publish-plugin-version>
        <maven-license-plugin-version>1.9.0</maven-license-plugin-version>
        <maven-release-plugin-version>2.5</maven-release-plugin-version>
        <apache-rat-plugin-version>0.10</apache-rat-plugin-version>
        <javassist-version>3.18.1-GA</javassist-version>
        <commons-math-version>2.2</commons-math-version>
        <jackson-mapper-asl-version>1.9.13</jackson-mapper-asl-version>
        <jersey-version>1.18.1</jersey-version>
        <jsr311-api-version>1.1.1</jsr311-api-version>
        <jetty-version>6.1.26</jetty-version>
        <scannotation-version>1.0.2</scannotation-version>
        <resteasy-jaxrs-version>2.3.0.GA</resteasy-jaxrs-version>
        <zookeeper-version>3.4.6</zookeeper-version>
        <guava-version>16.0.1</guava-version>
        <testng-version>6.8.8</testng-version>
        <swift-version>0.12.0</swift-version>
        <dropwizard-version>0.7.0</dropwizard-version>
        <maven-shade-plugin-version>2.3</maven-shade-plugin-version>
        <slf4j-version>1.7.6</slf4j-version>

        <!-- OSGi Properties -->
        <osgi.export.package />
        <osgi.import.package />
        <osgi.private.package />
        <osgi.dynamic.import />
        <osgi.require.bundle />
        <osgi.export.service />
        <osgi.activator />
    </properties>

    <scm>
        <url>https://github.com/apache/curator.git</url>
        <connection>scm:git:https://git-wip-us.apache.org/repos/asf/curator.git</connection>
        <developerConnection>scm:git:https://git-wip-us.apache.org/repos/asf/curator.git
        </developerConnection>
        <tag>apache-curator-2.7.1</tag>
    </scm>

    <issueManagement>
        <system>JIRA</system>
        <url>http://issues.apache.org/jira/browse/CURATOR</url>
    </issueManagement>

    <ciManagement>
        <system>Jenkins</system>
        <url>https://builds.apache.org/job/Curator/</url>
        <notifiers>
            <notifier>
                <type>mail</type>
                <sendOnError>true</sendOnError>
                <sendOnFailure>true</sendOnFailure>
                <sendOnSuccess>false</sendOnSuccess>
                <sendOnWarning>false</sendOnWarning>
                <configuration>
                    <address><EMAIL></address>
                </configuration>
            </notifier>
        </notifiers>
    </ciManagement>

    <distributionManagement>
        <site>
            <id>apache.website.svnpub</id>
            <url>scm:svn:https://svn.apache.org/repos/asf/curator/site/trunk</url>
        </site>
    </distributionManagement>

    <mailingLists>
        <mailingList>
            <name>Users</name>
            <post><EMAIL></post>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
            <archive>http://mail-archives.apache.org/mod_mbox/curator-user/</archive>
        </mailingList>
        <mailingList>
            <name>Development</name>
            <post><EMAIL></post>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
            <archive>http://mail-archives.apache.org/mod_mbox/curator-dev/</archive>
        </mailingList>
        <mailingList>
            <name>Commits</name>
            <post><EMAIL></post>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
            <archive>http://mail-archives.apache.org/mod_mbox/curator-commits/</archive>
        </mailingList>
    </mailingLists>

    <developers>
        <developer>
            <id>randgalt</id>
            <name>Jordan Zimmerman</name>
            <email><EMAIL></email>
            <timezone>-5</timezone>
            <roles>
                <role>Committer</role>
                <role>PMC Chair</role>
            </roles>
            <url>https://people.apache.org/~randgalt</url>
        </developer>

        <developer>
            <id>zarfide</id>
            <name>Jay Zarfoss</name>
            <email><EMAIL></email>
            <timezone>-8</timezone>
            <roles>
                <role>Committer</role>
                <role>PMC Member</role>
            </roles>
            <url>http://www.linkedin.com/pub/jay-zarfoss/34/56/a19</url>
        </developer>

        <developer>
            <id>cheddar</id>
            <name>Eric Tschetter</name>
            <email><EMAIL></email>
            <timezone>-6</timezone>
            <roles>
                <role>Committer</role>
                <role>PMC Member</role>
                <role>ChedHeader</role>
            </roles>
        </developer>
        <developer>
            <id>iocanel</id>
            <name>Ioannis Canellos</name>
            <email><EMAIL></email>
            <timezone>+2</timezone>
            <roles>
                <role>Committer</role>
                <role>PMC Member</role>
            </roles>
            <url>http://iocanel.blogspot.com</url>
        </developer>

        <developer>
            <id>cammckenzie</id>
            <name>Cameron McKenzie</name>
            <email><EMAIL></email>
            <timezone>+10</timezone>
            <roles>
                <role>Committer</role>
                <role>PMC Member</role>
            </roles>
            <url>https://people.apache.org/~cammckenzie</url>
        </developer>

        <developer>
            <id>dragonsinth</id>
            <name>Scott Blum</name>
            <email><EMAIL></email>
            <timezone>-5</timezone>
            <roles>
                <role>Committer</role>
                <role>PMC Member</role>
            </roles>
            <url>http://github.com/dragonsinth</url>
        </developer>

        <developer>
            <name>Patrick Hunt</name>
            <email><EMAIL></email>
            <roles>
                <role>PMC Member</role>
            </roles>
            <timezone>-8</timezone>
            <url>http://www.linkedin.com/pub/patrick-hunt/2/5b2/24a</url>
        </developer>

        <developer>
            <name>Mahadev Konar</name>
            <email><EMAIL></email>
            <roles>
                <role>PMC Member</role>
            </roles>
            <timezone>-8</timezone>
            <url>http://www.linkedin.com/in/mahadevkonar</url>
        </developer>

        <developer>
            <name>Luciano Resende</name>
            <email><EMAIL></email>
            <roles>
                <role>PMC Member</role>
            </roles>
            <timezone>-8</timezone>
            <url>https://people.apache.org/~lresende</url>
        </developer>

        <developer>
            <name>Enis Söztutar</name>
            <email><EMAIL></email>
            <roles>
                <role>PMC Member</role>
            </roles>
            <timezone>-8</timezone>
            <url>https://people.apache.org/~enis</url>
        </developer>
    </developers>

    <modules>
        <module>curator-client</module>
        <module>curator-test</module>
        <module>curator-framework</module>
        <module>curator-recipes</module>
        <module>curator-examples</module>
        <module>curator-x-discovery</module>
        <module>curator-x-discovery-server</module>
        <module>curator-x-rpc</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j-version}</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>${slf4j-version}</version>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>1.9.5</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-client</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-test</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-x-discovery</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-x-discovery-server</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>${javassist-version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-math</artifactId>
                <version>${commons-math-version}</version>
            </dependency>

            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-mapper-asl</artifactId>
                <version>${jackson-mapper-asl-version}</version>
            </dependency>

            <dependency>
                <groupId>com.sun.jersey</groupId>
                <artifactId>jersey-server</artifactId>
                <version>${jersey-version}</version>
            </dependency>

            <dependency>
                <groupId>com.sun.jersey</groupId>
                <artifactId>jersey-servlet</artifactId>
                <version>${jersey-version}</version>
            </dependency>

            <dependency>
                <groupId>com.sun.jersey</groupId>
                <artifactId>jersey-client</artifactId>
                <version>${jersey-version}</version>
            </dependency>

            <dependency>
                <groupId>com.sun.jersey</groupId>
                <artifactId>jersey-core</artifactId>
                <version>${jersey-version}</version>
            </dependency>

            <dependency>
                <groupId>javax.ws.rs</groupId>
                <artifactId>jsr311-api</artifactId>
                <version>${jsr311-api-version}</version>
            </dependency>

            <dependency>
                <groupId>org.mortbay.jetty</groupId>
                <artifactId>jetty</artifactId>
                <version>${jetty-version}</version>
            </dependency>

            <dependency>
                <groupId>net.sf.scannotation</groupId>
                <artifactId>scannotation</artifactId>
                <version>${scannotation-version}</version>
            </dependency>

            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-jaxrs</artifactId>
                <version>${resteasy-jaxrs-version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.scannotation</groupId>
                        <artifactId>scannotation</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>${zookeeper-version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sun.jmx</groupId>
                        <artifactId>jmxri</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sun.jdmk</groupId>
                        <artifactId>jmxtools</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.jms</groupId>
                        <artifactId>jms</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>junit</groupId>
                        <artifactId>junit</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava-version}</version>
            </dependency>

            <dependency>
                <groupId>org.testng</groupId>
                <artifactId>testng</artifactId>
                <version>${testng-version}</version>
            </dependency>

            <dependency>
                <groupId>com.facebook.swift</groupId>
                <artifactId>swift-codec</artifactId>
                <version>${swift-version}</version>
            </dependency>

            <dependency>
                <groupId>com.facebook.swift</groupId>
                <artifactId>swift-service</artifactId>
                <version>${swift-version}</version>
            </dependency>

            <dependency>
                <groupId>io.dropwizard</groupId>
                <artifactId>dropwizard-configuration</artifactId>
                <version>${dropwizard-version}</version>
            </dependency>

            <dependency>
                <groupId>io.dropwizard</groupId>
                <artifactId>dropwizard-logging</artifactId>
                <version>${dropwizard-version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-project-info-reports-plugin</artifactId>
                <version>${maven-project-info-reports-plugin-version}</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>${maven-javadoc-plugin-version}</version>
                <configuration>
                    <aggregate>true</aggregate>
                    <additionalJOptions>
                        <additionalJOption>-J-Xmx1g</additionalJOption>
                    </additionalJOptions>
                    <failOnError>false</failOnError>
                </configuration>
            </plugin>
        </plugins>
    </reporting>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>${maven-dependency-plugin-version}</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>${maven-install-plugin-version}</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin-version}</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>${maven-bundle-plugin-version}</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin-version}</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>${maven-javadoc-plugin-version}</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>${maven-site-plugin-version}</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-scm-publish-plugin</artifactId>
                    <version>${maven-scm-publish-plugin-version}</version>
                </plugin>

                <plugin>
                    <groupId>com.mycila.maven-license-plugin</groupId>
                    <artifactId>maven-license-plugin</artifactId>
                    <version>${maven-license-plugin-version}</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>${maven-release-plugin-version}</version>
                    <configuration>
                        <autoVersionSubmodules>true</autoVersionSubmodules>
                        <tagNameFormat>${project.artifactId}-${project.version}</tagNameFormat>
                        <pushChanges>false</pushChanges>
                        <localCheckout>true</localCheckout>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.rat</groupId>
                    <artifactId>apache-rat-plugin</artifactId>
                    <version>${apache-rat-plugin-version}</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>${maven-shade-plugin-version}</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <resources>
            <resource>
                <directory>${basedir}</directory>
                <targetPath>META-INF</targetPath>
                <includes>
                    <include>DISCLAIMER</include>
                    <include>LICENSE</include>
                    <include>NOTICE</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <configuration>
                    <createChecksum>true</createChecksum>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${jdk-version}</source>
                    <target>${jdk-version}</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <extensions>true</extensions>
                <inherited>true</inherited>
                <configuration>
                    <instructions>
                        <Bundle-Name>${project.name}</Bundle-Name>
                        <Bundle-SymbolicName>${project.artifactId}</Bundle-SymbolicName>
                        <Export-Package>${osgi.export.package}</Export-Package>
                        <Import-Package>${osgi.import.package}</Import-Package>
                        <DynamicImport-Package>${osgi.dynamic.import}</DynamicImport-Package>
                        <Private-Package>${osgi.private.package}</Private-Package>
                        <Require-Bundle>${osgi.require.bundle}</Require-Bundle>
                        <Bundle-Activator>${osgi.activator}</Bundle-Activator>
                        <Export-Service>${osgi.export.service}</Export-Service>
                    </instructions>
                    <supportedProjectTypes>
                        <supportedProjectType>jar</supportedProjectType>
                        <supportedProjectType>war</supportedProjectType>
                        <supportedProjectType>bundle</supportedProjectType>
                    </supportedProjectTypes>
                    <unpackBundle>true</unpackBundle>
                </configuration>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <forkCount>${surefire-forkcount}</forkCount>
                    <reuseForks>false</reuseForks>
                    <redirectTestOutputToFile>true</redirectTestOutputToFile>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <aggregate>true</aggregate>
                    <additionalJOptions>
                        <additionalJOption>-J-Xmx1g</additionalJOption>
                    </additionalJOptions>
                    <failOnError>false</failOnError>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-site-plugin</artifactId>
                <configuration>
                    <locales>en</locales>
                    <skipDeploy>true</skipDeploy>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.apache.maven.doxia</groupId>
                        <artifactId>doxia-module-confluence</artifactId>
                        <version>${doxia-module-confluence-version}</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <phase>site</phase>
                        <goals>
                            <goal>site</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-scm-publish-plugin</artifactId>
                <inherited>false</inherited>
                <configuration>
                    <checkinComment>Curator website deployment</checkinComment>
                    <!-- define curator-website-checkout-path in settings.xml -->
                    <checkoutDirectory>${curator-website-checkout-path}</checkoutDirectory>
                </configuration>
                <executions>
                    <execution>
                        <id>scm-publish</id>
                        <phase>site-deploy</phase>
                        <goals>
                            <goal>publish-scm</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>com.mycila.maven-license-plugin</groupId>
                <artifactId>maven-license-plugin</artifactId>
                <configuration>
                    <header>src/etc/header.txt</header>
                    <excludes>
                        <exclude>**/*.confluence</exclude>
                        <exclude>**/help.txt</exclude>
                        <exclude>**/*.rdf</exclude>
                        <exclude>**/.gitignore</exclude>
                        <exclude>**/*.thrift</exclude>
                        <exclude>**/*.json</exclude>
                        <exclude>**/.idea/**</exclude>
                        <exclude>**/DISCLAIMER</exclude>
                        <exclude>**/DEPENDENCIES</exclude>
                        <exclude>**/KEYS</exclude>
                        <exclude>**/LICENSE</exclude>
                        <exclude>**/NOTICE</exclude>
                        <exclude>**/README</exclude>
                        <exclude>**/CHANGES</exclude>
                        <exclude>**/RELEASE-NOTES</exclude>
                        <exclude>**/generated/**</exclude>
                    </excludes>
                    <strictCheck>true</strictCheck>
                </configuration>
                <executions>
                    <execution>
                        <id>license</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <configuration>
                    <arguments>-Dmaven.test.skip=true</arguments>
                    <mavenExecutorId>forked-path</mavenExecutorId>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.rat</groupId>
                <artifactId>apache-rat-plugin</artifactId>
                <configuration>
                    <numUnapprovedLicenses>0</numUnapprovedLicenses>
                    <excludeSubProjects>false</excludeSubProjects>
                    <excludes>
                        <exclude>**/*.confluence</exclude>
                        <exclude>**/*.rdf</exclude>
                        <exclude>**/help.txt</exclude>
                        <exclude>**/.gitignore</exclude>
                        <exclude>**/*.thrift</exclude>
                        <exclude>**/*.json</exclude>
                        <exclude>**/.idea/**</exclude>
                        <exclude>**/DISCLAIMER</exclude>
                        <exclude>**/DEPENDENCIES</exclude>
                        <exclude>**/KEYS</exclude>
                        <exclude>**/LICENSE</exclude>
                        <exclude>**/NOTICE</exclude>
                        <exclude>**/README</exclude>
                        <exclude>**/CHANGES</exclude>
                        <exclude>**/RELEASE-NOTES</exclude>
                        <exclude>**/generated/**</exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
