discovery_models:
  - name: 日期
    model_id: 1352
    data_tag: T_DATE
    safe_level: C2
    sensitive_level: S2
    rules:
      - rule_id: 26
        rule_params:
          - max_context_distance: 40
        detect:
          - regex_search:
              - patterns:
                  - ((\d{4}(-|_|年))(([1-9]|0[1-9]|1[0-2])(-|_|月))([1-9]|0[1-9]|[12]\d|3[01]日?)(\s*(00|0?[1-9]|1[0-9]|2[0-4]):[0-5][0-9](:[0-5][0-9])?)?)
        context:
          - enum_match:
              - enum:
                  - 日期
                  - 时间
                  - date
                  - time
  - name: sql脚本
    model_id: 6558
    data_tag: T_SQL_SCRIPT
    safe_level: C3
    sensitive_level: S3
    rules:
      - rule_id: 102
        rule_params:
          - max_context_distance: 40
        detect:
          - regex_search:
              - patterns:
                  - (((SELECT|select))\s+.*\s+(FROM|from)\s+.*;)
                  - (((CREATE|create))\s+(TABLE|VIEW|SCEHMA|DATABASE|FUNCTION|PROCEDURE|EVENT|TRIGGER|table|view|schema|database|function|procedure|event|trigger)\s+.*;)
                  - (((DROP|drop))\s+(TABLE|VIEW|SCEHMA|DATABASE|FUNCTION|PROCEDURE|EVENT|TRIGGER|table|view|schema|database|function|procedure|event|trigger)\s+.*;)
                  - (((TRUNCATE|truncate))\s+(TABLE|table)\s+.*;)
                  - (((CREATE|create))\s+(INDEX|index)\s+.*\s+(ON|on)\s+.*?;)
                  - (((INSERT|insert))\s+(INTO|into)\s+.*\s+.*;)
                  - (((UPDATE|update))\s+.*\s+(SET|set)\s+.*;)
                  - (((DELETE|delete))\s+(FROM|from)\s+.*;)
                  - (((BEGIN|begin))\s+.*\s+(END|end);?)
                  - (((DECLARE|declare))\s+.*)
                  - (((ALTER|alter))\s+(TABLE|table)\s+.*;)
        context:
          - enum_match:
              - enum:
                  - sql
                  - 脚本