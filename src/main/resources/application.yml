server:
  port: 8081
spring:
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
  main:
    allow-circular-references: true
  application:
    name: DCAP Probe Client
    active: dcap
#  datasource:
#    druid:
#      conf:
#        driver-class-name: com.mysql.cj.jdbc.Driver
#        url: ****************************************************************************************************************************************************
#        username: ENC[4c4c439f590a87981694ab321091042c]
#        password: ENC[a6f6812df5181ef1fccdf7ac463b0494]
#        max-active: 100
#        initial-size: 1
#        max-wait: 60000
#        min-idle: 1
#        time-between-eviction-runs-millis: 60000
#        min-evictable-idle-time-millis: 300000
#        validation-query: select 'x'
#        test-while-idle: true
#        test-on-borrow: false
#        test-on-return: false
#        pool-prepared-statements: true
#        max-open-prepared-statements: 50
#        max-pool-prepared-statement-per-connection-size: 20
  kafka:
    bootstrap-servers: 172.27.253.29:32666
#    bootstrap-servers: app-alpha.yuandiansec.net:30060
    properties:
      security:
        protocol: SASL_PLAINTEXT
      sasl:
        mechanism: PLAIN
        jaas:
          config: 'org.apache.kafka.common.security.scram.ScramLoginModule required username="user1" password="j4J3F6LpuN";'
    consumer:
      enable-auto-commit: true
      auto-commit-interval-ms: 5000
      auto-offset-reset: earliest
      heartbeat-interval-ms: 3000
      session-timeout-ms: 45000
      max-poll-interval-ms: 30000
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    producer:
      request-timeout-ms: 1000
      acks: all
      retries: 3
      batch-size: 16384
      linger-ms: 100
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
grpc:
  enabled: true
  client:
    dcapProbeService:
      maxInboundMessageSize: 50MB # 10MB
      address: dns:///************:9098
      negotiationType: TLS
      security:
        clientAuthEnabled: true
        certificateChain: file:certificates/client.crt
        privateKey: file:certificates/client.key
        authorityOverride: app.yuandiansec.net
        trustCertCollection: file:certificates/trusted-server.crt.collection

totalItemsIgnoreZero: true
## personal config
custom:
  mq:
    enabled: true
    topicPartitions: 10
    #support 3 scenes: dcap,dspm-datascan,dspm-snapshot
    groupSuffix: datascan
    task-topic: xzw-task-topic
    task-state-topic: xzw-task-state
    task-data-topic: xzw-task-data
