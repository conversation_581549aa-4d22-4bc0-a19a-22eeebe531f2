server:
  port: 8081
ebean:
  databasePlatformName: mysql #mysql #postgres
spring:
  application:
    name: DCAP Probe Client
  main:
    allow-circular-references: true
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
  kafka:
    bootstrap-servers: kafka:9092
    consumer:
      enable-auto-commit: true
      auto-commit-interval-ms: 5000
      auto-offset-reset: latest
      heartbeat-interval-ms: 5000
      session-timeout-ms: 45000
      max-poll-interval-ms: 30000
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    producer:
      request-timeout-ms: 1000
      acks: all
      retries: 3
      batch-size: 16384
      linger-ms: 100
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer

grpc:
  client:
    dcapProbeService:
      maxInboundMessageSize: 50MB
      address: dns:///dcap-probe-server:9098
      negotiationType: TLS
      security:
        clientAuthEnabled: true
        certificateChain: file:certificates/client.crt
        privateKey: file:certificates/client.key
        authorityOverride: app.yuandiansec.net
        trustCertCollection: file:certificates/trusted-server.crt.collection

totalItemsIgnoreZero: true

custom:
  mq:
    enabled:    true
    sdi-scan-result-record-as-table: true
    groupSuffix: sdi-data-scan
    task-topic: sdi-scan-task-topic
    task-state-topic: sdi-scan-task-state
    task-data-topic: sdi-scan-task-data

    general-task-topic: general-task
    general-task-state-topic: general-task-state
    general-task-data-topic: general-task-data