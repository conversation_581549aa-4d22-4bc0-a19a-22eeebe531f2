<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE xml>
<Configuration status="warn" monitorInterval="30" strict="true">
    <Properties>
        <Property name="LOG_HOME" value="logs"/>
        <Property name="PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS}-[%p][%t][%logger{1}] :%m %n" />
        <Property name="LOG_LEVEL" value="info"/>
        <Property name="RETENTION_SIZE" value="50M"/>
        <Property name="RETENTION_TIME" value="7d"/>
        <Property name="LOG_FILE_SIZE" value="20"/>
    </Properties>

    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${PATTERN}"/>
        </Console>

        <RollingFile name="info" immediateFlush="true" fileName="${LOG_HOME}/info.log" filePattern="${LOG_HOME}/info-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${PATTERN}"/>
            <Policies>
                <OnStartupTriggeringPolicy/>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="${RETENTION_SIZE}"/>
            </Policies>
            <DefaultRolloverStrategy max="${LOG_FILE_SIZE}">
                <Delete basePath="${LOG_HOME}/">
                    <IfFileName glob="info-*.log.gz"/>
                    <IfLastModified age="${RETENTION_TIME}"/>
                </Delete>
            </DefaultRolloverStrategy>
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
        </RollingFile>

        <RollingFile name="debug" immediateFlush="true" fileName="${LOG_HOME}/debug.log" filePattern="${LOG_HOME}/debug-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${PATTERN}"/>
            <Policies>
                <OnStartupTriggeringPolicy/>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="${RETENTION_SIZE}"/>
            </Policies>
            <DefaultRolloverStrategy max="${LOG_FILE_SIZE}">
                <Delete basePath="${LOG_HOME}/">
                    <IfFileName glob="bug-*.log.gz"/>
                    <IfLastModified age="${RETENTION_TIME}"/>
                </Delete>
            </DefaultRolloverStrategy>
            <ThresholdFilter level="DEBUG" onMatch="ACCEPT" onMismatch="DENY"/>
        </RollingFile>

        <RollingFile name="error" immediateFlush="true" fileName="${LOG_HOME}/error.log" filePattern="${LOG_HOME}/error-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${PATTERN}"/>
            <Policies>
                <OnStartupTriggeringPolicy/>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="${RETENTION_SIZE}"/>
            </Policies>
            <DefaultRolloverStrategy max="${LOG_FILE_SIZE}">
                <Delete basePath="${LOG_HOME}/">
                    <IfFileName glob="error-*.log.gz"/>
                    <IfLastModified age="${RETENTION_TIME}"/>
                </Delete>
            </DefaultRolloverStrategy>
            <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
        </RollingFile>

        <!-- systrm.parameter -->
        <File name="sys" fileName="${LOG_HOME}/start.log" append="false">
            <PatternLayout pattern="${PATTERN}"/>
        </File>
    </Appenders>

    <Loggers>
        <Root level="${env:YD_LOG_LEVEL:-info}" includeLocation="true">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="info"/>
            <AppenderRef ref="debug"/>
            <AppenderRef ref="error"/>
        </Root>

        <logger name="org.apache.kafka" level="warn"></logger>
<!--        <AsyncLogger name="com.yd.dcap.classifier.ClassifierService" level="debug" additivity="false" includeLocation="true">-->
<!--            <AppenderRef ref="Console"/>-->
<!--            <AppenderRef ref="info"/>-->
<!--            <AppenderRef ref="debug"/>-->
<!--            <AppenderRef ref="error"/>-->
<!--        </AsyncLogger>-->

    </Loggers>
</Configuration>