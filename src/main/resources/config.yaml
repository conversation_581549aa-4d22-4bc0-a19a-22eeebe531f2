layers:
  enumerate:
    type: origin
    queue: xform
    plugins:
      - cloud.aliyun.discovery
  transform-sql-parser:
    type: intermediate
    dequeue: xform
    queue: default
    plugins:
      - cloud.sql.parser
  output:
    type: terminal
    dequeue: default
    plugins:
      - cloud.csv.out
fifos:
  default:
    type: local
  xform:
    type: local
  csvch:
    type: local
plugins:
  cloud.aliyun.discovery:
    enabled: true
    config:
      ramUser:
        AccessKeyID: xxxx
        AccessKeySecret: xxxx
      stsUser:
        AccessKeyID: xxxx
        AccessKeySecret: xxxx
        assumedRoles:
          - acs:ram::1103802118874618:role/ramrdstest
      services:
        - rds_audit_log
      regions:
        - cn-qingdao
        - cn-beijing
      ignoredRegions:
        - fips.*
        - us-gov.*
  cloud.sql.parser:
    enabled: true
    config:
  cloud.csv.entry.gen:
    enabled: true
    config:
  cloud.csv.out:
    enabled: true
    config:
      reportFilePath: /var/log/dcap/
