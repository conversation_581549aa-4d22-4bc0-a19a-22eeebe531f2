layers:
  enumerate:
    type: origin
    queue: from
    plugins:
      - cloud.aliyun.discovery
  transform-sql-parser:
    type: intermediate
    dequeue: from
    queue: default
    plugins:
      - cloud.sql.parser
  output:
    type: terminal
    dequeue: default
    plugins:
      - cloud.csv.out
fifos:
  default:
    type: local
  from:
    type: local
  csvch:
    type: local
plugins:
  cloud.aliyun.discovery:
    enabled: true
    config:
  cloud.sql.parser:
    enabled: true
    config:
  cloud.csv.entry.gen:
    enabled: true
    config:
  cloud.csv.out:
    enabled: true
    config:
      reportFilePath: /var/log/dcap/
