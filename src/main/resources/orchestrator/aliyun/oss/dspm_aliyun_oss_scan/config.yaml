layers:
  list-oss:
    type: origin
    queue: file-q
    plugins:
      - cloud.aliyun.discovery
  extract-content:
    type: intermediate
    dequeue: file-q
    queue: identify-q
    plugins:
      - oss.extract.content.from.path
      - out.metadata.to.kafka
  identify-content:
    type: intermediate
    dequeue: identify-q
    queue: result-q
    plugins:
      - oss.identify.content
  output:
    type: terminal
    dequeue: result-q
    plugins:
      - cloud.mq.out
fifos:
  file-q:
    type: local
  identify-q:
    type: local
  result-q:
    type: local
plugins:
  cloud.aliyun.discovery:
    enabled: true
    config:
  oss.extract.content.from.path:
    enabled: true
    config:
  oss.identify.content:
    enabled: true
    config:
  cloud.mq.out:
    enabled: true
    config:
