layers:
  enumerate:
    type: origin
    queue: inst-list
    plugins:
      - cloud.aliyun.discovery
  db-discovery:
    type: intermediate
    dequeue: inst-list
    queue: db-list
    plugins:
      - cloud.ecs.database.discovery
  output:
    type: terminal
    dequeue: db-list
    plugins:
      - cloud.db.out
fifos:
  inst-list:
    type: local
  db-list:
    type: local
plugins:
  cloud.aliyun.discovery:
    enabled: true
    config:
  cloud.ecs.database.discovery:
    enabled: true
    config:
      port.scan.range: 22-10500
      port.scan.threads: 1
      port.scan.timeout.milliseconds: 100
      test.host.threads: 100
      test.one.host.timeout.milliseconds: 200
  cloud.db.out:
    enabled: true
    config:
