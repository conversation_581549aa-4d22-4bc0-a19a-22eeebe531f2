# VFS 扫描流水线配置 (单一 Orchestrator + 同层分支)
layers:
  vfs-metadata-origin: # Origin Layer: 扫描 VFS 并提取元数据
    type: origin
    queue: metadata-processing-q # 输出到此队列
    plugins:
      - file-service-vfs-discovery # 引用下面的插件定义
  metadata-processing: # Intermediate Layer 1: 分支处理层
    type: intermediate
    dequeue: metadata-processing-q # 从元数据队列读取
    queue: identification-input-q # 内容提取插件的输出队列
    plugins:
      - metadata-kafka-sender # 插件 1: 发送元数据到 Kafka (不发射到下游队列)
      - vfs-content-extractor     # 插件 2: 提取内容 (发射到下游队列 identification-input-q)
  content-identification: # Intermediate Layer 2: 内容识别
    type: intermediate
    dequeue: identification-input-q # 从内容提取队列读取
    queue: result-output-q        # 识别插件的输出队列
    plugins:
      - vfs-file-content-identifier # 引用下面的插件定义
  result-output: # Terminal Layer: 发送最终结果
    type: terminal
    dequeue: result-output-q # 从识别队列读取
    plugins:
      - file-identity-result-kafka-sender # 引用下面的插件定义

fifos:
  metadata-processing-q:
    type: local # 使用内存队列
    # capacity: 1000 # 可选：队列容量
  identification-input-q:
    type: local
  result-output-q:
    type: local

plugins:
  file-service-vfs-discovery:
    enabled: true
    config:
      s3UseHttps: false
  metadata-kafka-sender: # MetadataKafkaSenderPlugin 的配置
    enabled: true
  vfs-content-extractor: # ContentExtractionPlugin 的配置
    enabled: true
    config:
      s3AccessKey: "placeholder_access_key"
      s3SecretKey: "placeholder_secret_key"
      s3UseHttps: false
  vfs-file-content-identifier: # IdentificationPlugin 的配置
    enabled: true
  file-identity-result-kafka-sender: # ResultKafkaSenderPlugin 的配置
    enabled: true