package org.apache.commons.vfs2.provider.smb;

import jcifs.CIFSContext;
import jcifs.CIFSException;
import jcifs.context.SingletonContext;
import jcifs.smb.*;
import org.apache.commons.vfs2.*;
import org.apache.commons.vfs2.provider.AbstractFileName;
import org.apache.commons.vfs2.provider.AbstractFileObject;
import org.apache.commons.vfs2.provider.UriParser;
import org.apache.commons.vfs2.util.RandomAccessMode;
import org.apache.commons.vfs2.util.UserAuthenticatorUtils;

import java.io.InputStream;
import java.io.OutputStream;
import java.net.MalformedURLException;

/**
 * A file in an SMB file system.
 */
public class SmbFileObject extends AbstractFileObject<SmbFileSystem> {
    // private final String fileName;
    private SmbFile file;

    /**
     * Constructs a new instance.
     *
     * @param fileName the file name.
     * @param fileSystem the file system.
     * @throws FileSystemException not thrown.
     */
    protected SmbFileObject(final AbstractFileName fileName, final SmbFileSystem fileSystem) throws FileSystemException {
        super(fileName, fileSystem);
        // this.fileName = UriParser.decode(name.getURI());
    }

    private SmbFile createSmbFile(final FileName fileName)
            throws MalformedURLException, SmbException, FileSystemException {
        final SmbFileName smbFileName = (SmbFileName) fileName;

        final String path = smbFileName.getUriWithoutAuth();

        UserAuthenticationData authData = null;
        SmbFile file;
        try {
            authData = UserAuthenticatorUtils.authenticate(getFileSystem().getFileSystemOptions(),
                    SmbFileProvider.AUTHENTICATOR_TYPES);

            CIFSContext context;
            if (authData != null) {
                // 提取认证信息
                String domain = UserAuthenticatorUtils.toString(UserAuthenticatorUtils.getData(authData,
                        UserAuthenticationData.DOMAIN, UserAuthenticatorUtils.toChar(smbFileName.getDomain())));
                String username = UserAuthenticatorUtils.toString(UserAuthenticatorUtils.getData(authData, 
                        UserAuthenticationData.USERNAME, UserAuthenticatorUtils.toChar(smbFileName.getUserName())));
                String password = UserAuthenticatorUtils.toString(UserAuthenticatorUtils.getData(authData, 
                        UserAuthenticationData.PASSWORD, UserAuthenticatorUtils.toChar(smbFileName.getPassword())));
                // 使用jcifs-ng的正确API创建认证
                NtlmPasswordAuthenticator auth = new NtlmPasswordAuthenticator(domain, username, password);
                context = SingletonContext.getInstance().withCredentials(auth);
            } else {
                // 使用匿名访问
                context = SingletonContext.getInstance();
            }

            // 使用新的jcifs-ng API创建SmbFile
            file = new SmbFile(path, context);

            if (file.isDirectory() && !file.toString().endsWith("/")) {
                file = new SmbFile(path + "/", context);
            }
            return file;
        } catch (CIFSException e) {
            throw new SmbException(e.getMessage(), e);
        } finally {
            UserAuthenticatorUtils.cleanup(authData); // might be null
        }
    }

    /**
     * Attaches this file object to its file resource.
     */
    @Override
    protected void doAttach() throws Exception {
        // Defer creation of the SmbFile to here
        if (file == null) {
            file = createSmbFile(getName());
        }
    }

    /**
     * Creates this file as a folder.
     */
    @Override
    protected void doCreateFolder() throws Exception {
        file.mkdir();
        file = createSmbFile(getName());
    }

    /**
     * Deletes the file.
     */
    @Override
    protected void doDelete() throws Exception {
        file.delete();
    }

    @Override
    protected void doDetach() throws Exception {
        // file closed through content-streams
        file = null;
    }

    /**
     * Returns the size of the file content (in bytes).
     */
    @Override
    protected long doGetContentSize() throws Exception {
        return file.length();
    }

    /**
     * Creates an input stream to read the file content from.
     */
    @Override
    protected InputStream doGetInputStream(final int bufferSize) throws Exception {
        try {
            return new SmbFileInputStream(file);
        } catch (final SmbException e) {
            if (e.getNtStatus() == NtStatus.NT_STATUS_NO_SUCH_FILE) {
                throw new org.apache.commons.vfs2.FileNotFoundException(getName());
            }
            if (file.isDirectory()) {
                throw new FileTypeHasNoContentException(getName());
            }

            throw e;
        }
    }

    /**
     * Returns the last modified time of this file.
     */
    @Override
    protected long doGetLastModifiedTime() throws Exception {
        return file.getLastModified();
    }

    /**
     * Creates an output stream to write the file content to.
     */
    @Override
    protected OutputStream doGetOutputStream(final boolean bAppend) throws Exception {
        return new SmbFileOutputStream(file, bAppend);
    }

    /**
     * random access
     */
    @Override
    protected RandomAccessContent doGetRandomAccessContent(final RandomAccessMode mode) throws Exception {
        return new SmbFileRandomAccessContent(file, mode);
    }

    /**
     * Determines the type of the file, returns null if the file does not exist.
     */
    @Override
    protected FileType doGetType() throws Exception {
        if (!file.exists()) {
            return FileType.IMAGINARY;
        }
        if (file.isDirectory()) {
            return FileType.FOLDER;
        }
        if (file.isFile()) {
            return FileType.FILE;
        }

        throw new FileSystemException("vfs.provider.smb/get-type.error", getName());
    }

    /**
     * Determines if this file is hidden.
     */
    @Override
    protected boolean doIsHidden() throws Exception {
        return file.isHidden();
    }

    /**
     * Lists the children of the file. Is only called if {@link #doGetType} returns {@link FileType#FOLDER}.
     */
    @Override
    protected String[] doListChildren() throws Exception {
        // VFS-210: do not try to get listing for anything else than directories
        if (!file.isDirectory()) {
            return null;
        }

        return UriParser.encode(file.list());
    }

    @Override
    protected void doRename(final FileObject newfile) throws Exception {
        file.renameTo(createSmbFile(newfile.getName()));
    }

    @Override
    protected boolean doSetLastModifiedTime(final long modtime) throws Exception {
        file.setLastModified(modtime);
        return true;
    }
}
