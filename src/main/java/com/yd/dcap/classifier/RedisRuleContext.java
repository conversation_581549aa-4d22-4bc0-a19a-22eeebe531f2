package com.yd.dcap.classifier;

import com.dcap.classifier.access.DataSampler;
import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.RuleContext;
import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.TaskGlobalDataHolder;
import com.yd.dcap.classifier.taskreport.ScanDbReport;

import java.sql.Connection;
import java.util.*;
import java.util.stream.Collectors;

public class RedisRuleContext extends RuleContext {

   public RedisRuleContext(ClassifierDataSource dataSource, TaskGlobalDataHolder globalDataHolder) throws ConnectionException {
      super(dataSource, globalDataHolder);
   }

   @Override
   public void setConnection(Connection connection) {
      this.hasMoreTables();
   }

   @Override
   protected List<String> findCatalogs() {
      this.resetBrowse();
      RedisDatasource redisDatasource = (RedisDatasource) this.getDatasource();
      List<String> catalogs = redisDatasource.getCatalogs();
      Set<String> selectedDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedDatabase();
      Set<String> excludedDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedDatabase();
      for (String catalog : catalogs) {
         // 如果包含就跳过
         if (excludedDatabase.contains(catalog.toLowerCase())){
            continue;
         }
         if (selectedDatabase.isEmpty() || selectedDatabase.contains(catalog.toLowerCase())) {
            this.catalogs.add(catalog);
         }
      }
      return this.catalogs;
   }

   @Override
   protected List<ContextTable> findCatalogTables(String catalog) {
      if (this.tables != null) {
         this.tables.clear();
      } else {
         this.tables = new ArrayList<>();
      }
      // todo: redis 也没有什么数据库和 schema 的概念。
      this.getTaskGlobalDataHolder().getProbeClientTaskContext().recordField(ScanDbReport.FIELD_DATASOURCE_COUNT, 1);

      this.currentTable = null;

      Set<String> selectedTables = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedTable();
      Set<String> excludedTables = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedTable();
      RedisDatasource redisDatasource = (RedisDatasource) this.getDatasource();
      List<String> tables = redisDatasource.getTables(Integer.parseInt(catalog));
      for(String tableName: tables){
         ContextTable contextTable = this.createContextTable(catalog, null, tableName, "TABLE",null);
         if ( contextTable == null){
            continue;
         }

         String tableIdentityName = contextTable.getIdentityName().toLowerCase();
         if (excludedTables.contains(tableIdentityName)){
            continue;
         }
         if ((selectedTables.isEmpty() || selectedTables.contains(tableIdentityName))){
            this.addTable(contextTable);
         }
      }

      this.getTaskGlobalDataHolder().getProbeClientTaskContext().recordField(ScanDbReport.FIELD_TABLE_COUNT, this.tables.size()).sendToServer();
      return this.listCatalogTables();
   }

   protected int addTable(ContextTable table) {
      if (table == null) {
         return this.tables.size();
      }
      if (this.tables == null) {
         this.tables = new ArrayList<>();
      }
      this.tables.add(table);
      return this.tables.size();
   }

   @Override
   protected List<ContextColumn> findTableColumns() {
      ContextTable currentTable = this.getCurrentTable();
      String table = currentTable.getTableName();

      List<ContextColumn> contextColumns = new ArrayList<>();
      Set<String> addedColumns = new HashSet<>();
      // todo: cache the rows that will be used by data based rules
      RedisDatasource redisDatasource = (RedisDatasource) this.getDatasource();

      for (Map<String, Object> row : redisDatasource.getRows(Integer.parseInt(currentTable.getCatalog()), table, 10)) {
         for (Map.Entry<String, Object> entry : row.entrySet()) {
            String column = entry.getKey();
            // todo: column size set to 20
            contextColumns.add(this.createContextColumn(currentTable, column, 1, "", 20, 0,null,0,false));
            addedColumns.add(column);
         }
      }
      return contextColumns;
   }


   @Override
   public DataSampler getSamplerFor(String table) {
      String catalog = getCurrentTable().getCatalog();
      int sampleCount = getTaskGlobalDataHolder().getTaskParam().getSampleCount();
      return new DataSampler(this) {
         @Override
         public Map<String, List<String>> sample(List<ContextColumn> columns) {
            Map<String, List<String>> result = new HashMap<>();

            if (columns == null || columns.isEmpty()){
               return result;
            }
            int index = Integer.parseInt(catalog);
            Set<String> columnNames = columns.stream().map(ContextColumn::getColumnName).collect(Collectors.toSet());
            RedisDatasource redisDatasource = (RedisDatasource) RedisRuleContext.this.getDatasource();
            for (Map<String, Object> row : redisDatasource.getRows(index, table, sampleCount)) {
               for (Map.Entry<String, Object> entry : row.entrySet()) {
                  String column = entry.getKey();
                  String value = entry.getValue() == null ? null : entry.getValue().toString();

                  if (value != null && columnNames.contains(column)) {
                     result.compute(column, (k, v) -> {
                        if (v == null) {
                           v = new ArrayList<>();
                        }
                        v.add(value);
                        return v;
                     });
                  }
               }
            }
            return result;
         }
      };
   }
}
