package com.yd.dcap.classifier;

import com.dcap.classifier.access.SQLDataPager;
import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.RuleContext;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class ClickhouseDataPager extends SQLDataPager {

   private static final transient String SQL_PAGE_LIMIT = " LIMIT ${SUB_FETCH} OFFSET ${SUB_START} ";

   public ClickhouseDataPager(RuleContext ruleContext) {
      super(ruleContext);
   }

   public String createSql(List<ContextColumn> columns, long start, long rowLimit, boolean useRandomSampling) {
      String qualifiedName = this.getContext().getQualifiedName(columns);
      StringBuilder sql = new StringBuilder("select ");
      sql.append(qualifiedName).append(this.getFrom());
      if (useRandomSampling) {
         sql.append(this.substituteRange(SQL_PAGE_LIMIT, Math.max(start - 1L, 0L), rowLimit));
      } else if (this.context.getTaskGlobalDataHolder().getTaskParam().getSamplingReverseOrder()){
         String order = getOrder();
         if (StringUtils.isNotBlank(order)) {
            sql.append(order).append(" desc");
         }
         sql.append(" LIMIT ").append(rowLimit);
      } else {
         sql.append(" LIMIT ").append(rowLimit);
      }
      return sql.toString();
   }

   @Override
   public String createSpecifyTableSql(String tableName, List<String> columns, long start, long rowLimit, boolean useRandomSampling) {
      String qualifiedName = String.join(",", columns);
      StringBuilder sql = new StringBuilder("select ");
      sql.append(qualifiedName).append(this.getFrom(tableName));
      if (useRandomSampling) {
         sql.append(this.substituteRange(SQL_PAGE_LIMIT, Math.max(start - 1L, 0L), rowLimit));
      } else {
         sql.append(" LIMIT ").append(rowLimit);
      }
      return sql.toString();
   }

   @Override
   public String createSubsamplingSql(String tableName, String column, long start, long rowLimit, boolean useRandomSampling) {
      StringBuilder sql = new StringBuilder("select ");
      sql.append(column).append(this.getFrom(tableName));
      sql.append(" where ").append(this.appendExcludeEmptyCondition(column));
      if (useRandomSampling) {
         sql.append(this.substituteRange(SQL_PAGE_LIMIT, Math.max(start - 1L, 0L), rowLimit));
      } else {
         sql.append(" LIMIT ").append(rowLimit);
      }
      return sql.toString();
   }
}
