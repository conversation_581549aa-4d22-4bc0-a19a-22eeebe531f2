package com.yd.dcap.classifier;

import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.DataSourceType;
import com.dcap.datalayer.DatasourceDriver;
import com.dcap.utils.UtilDB;
import com.google.common.collect.ImmutableSet;
import com.yd.dcap.probe.client.TaskConfig;
import lombok.Builder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.Set;

@Builder
public class Gbase8aDatasource extends ClassifierDataSource {
    private static final Logger LOG = LoggerFactory.getLogger(Gbase8aDatasource.class);
    private Long tenantId;
    private String id;
    private String name;
    private String host;
    private String port;
    private String username;
    private String password;
    private String extraCfg;
    private String url;

    private static final DatasourceDriver DEFAULT_DRIVER = new DatasourceDriver() {
        @Override
        public String[] adjustTableTypes(String[] tableTypes, boolean excludeInternalTables, Connection connection) {
            return tableTypes;
        }

        @Override
        public String getExceptionDetails(String sql, SQLException sqlException) {
            return sql;
        }
    };

    @Override
    public Long getTenantId() {
        return tenantId;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getDescription() {
        return "Gbase8a DataSource";
    }

    @Override
    public String getAttributes() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getAttributes()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public String getDbName() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getDbName()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public String getInstanceName() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getInstanceName()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public String getConnectionDescriptor() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getConnectionDescriptor()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public Connection connect() throws ConnectionException {
        DriverManager.setLoginTimeout(30);
        Connection connection;
        try {
            connection = UtilDB.buildDatabaseConnection("gbase8a",
                    this.host, Integer.parseInt(this.port), this.username, this.password, this.extraCfg);
            url = connection.getMetaData().getURL();
            LOG.debug("success connect to sinodb ["+connection.getMetaData().getURL()+"],user["+username+"]");
        } catch (SQLException e) {
            throw new ConnectionException("Can not connect to database: " + url, e);
        }

        return connection;
    }

    @Override
    public DatasourceDriver getDriverInterface() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getDriverInterface()" + " @@@@@@@@@@@@@@@@@");
        return DEFAULT_DRIVER;
    }

    @Override
    public boolean isCatalog() {
        return true;
    }

    @Override
    public Set<String> getEmptyCatalogs() {
        return ImmutableSet.of("gclusterdb", "information_schema", "mysql", "performance_schema");
    }

    @Override
    public boolean supportedQueryTimeout() {
        return true;
    }

    @Override
    public DataSourceType getType() {
        return DataSourceType.GBASE8A;
    }

    public static List<ClassifierDataSource> buildDatasource(Long tenantId, TaskConfig.DataSource dsConfig) {
        return Collections.singletonList(Gbase8aDatasource.builder()
                .tenantId(tenantId)
                .id(dsConfig.getId())
                .name(dsConfig.getName())
                .host(dsConfig.getHost())
                .port(dsConfig.getPort())
                .extraCfg(dsConfig.getExtraCfg())
                .username(dsConfig.getAuthCfg().getUsername())
                .password(dsConfig.getAuthCfg().getPassword())
                .build());
    }
}
