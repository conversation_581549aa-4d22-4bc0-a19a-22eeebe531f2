package com.yd.dcap.classifier;

import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.DataSourceType;
import com.dcap.datalayer.DatasourceDriver;
import com.dcap.utils.JSON;
import com.google.common.collect.ImmutableSet;
import com.yd.dcap.probe.client.TaskConfig;
import lombok.Builder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

@Builder
public class Kingbase8Datasource extends ClassifierDataSource {

    private static final Logger LOG = LoggerFactory.getLogger(Kingbase8Datasource.class);

    private Long tenantId;
    private String id;
    private String name;
    private String host;
    private String port;
    private String username;
    private String password;
    private String dbName;
    private String url;

    private static final Set<String> excludeCatalog = ImmutableSet.of(
            "template0", "template1", "omm", "pg_catalog", "pg_namespace", "PG_CATALOG", "PG_NAMESPACE",
            "pg_toast","PG_TOAST", "pg_bitmapindex", "PG_BITMAPINDEX",
            "pg_temp_1", "PG_TEMP_1", "pg_toast_temp_1", "PG_TOAST_TEMP_1",
            "information_schema", "INFORMATION_SCHEMA", "xlog_record_read", "XLOG_RECORD_READ",
            "anon","ANON"
    ).stream().map(String::toUpperCase).collect(Collectors.toSet());

    private static final DatasourceDriver DEFAULT_DRIVER = new DatasourceDriver() {
        @Override
        public String[] adjustTableTypes(String[] tableTypes, boolean excludeInternalTables, Connection connection) {
            return tableTypes;
        }

        @Override
        public String getExceptionDetails(String sql, SQLException sqlException) {
            return sql;
        }
    };

    @Override
    public Long getTenantId() {
        return tenantId;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getDescription() {
        return "Kingbase DataSource";
    }

    @Override
    public String getAttributes() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getAttributes()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public String getDbName() {
        if (dbName.contains("?")){
            return dbName.substring(0,dbName.indexOf("?"));
        }
        return dbName;
    }

    @Override
    public String getInstanceName() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getInstanceName()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public String getConnectionDescriptor() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getConnectionDescriptor()" + " @@@@@@@@@@@@@@@@@");
        return url;
    }

    @Override
    public Connection connect() throws ConnectionException {
        if (StringUtils.isBlank(this.username) || StringUtils.isBlank(this.password)){
            throw new IllegalArgumentException("username ["+this.username+"] or password ["+password+"] is blank ");
        }

        String drvName = "com.kingbase8.Driver";
        Driver dbDriver;
        try {
            dbDriver = (Driver) Class.forName(drvName).newInstance();
        } catch (Exception e) {
            throw new ConnectionException("Can not initialize the driver class: " + drvName, e);
        }

        Properties props = new Properties();
        url = "jdbc:kingbase8://" + host + ":" + port + "/" + dbName;
        props.setProperty("user", username);
        props.setProperty("password", password);

        Connection connection;
        try {
            connection = dbDriver.connect(url, props);
            LOG.info("success connect to pgsql[{}]", url);
        } catch (SQLException e) {
            throw new ConnectionException("Can not connect to database: " + url, e);
        }

        return connection;
    }

    private static Connection connect(String host, String port, String dbName, String username, String password) throws ConnectionException {
        DriverManager.setLoginTimeout(30);
//        String drvName = "org.postgresql.Driver";
//        Driver dbDriver;
//        try {
//            dbDriver = (Driver) Class.forName(drvName).newInstance();
//            DriverManager.registerDriver(dbDriver);
//        } catch (Exception e) {
//            throw new ConnectionException("Can not initialize the driver class: " + drvName, e);
//        }

        String url = "jdbc:kingbase8://" + host + ":" + port + "/" + dbName;
        Properties props = new Properties();
        props.setProperty("user", username);
        props.setProperty("password", password);

        Connection connection;
        try {
            connection = DriverManager.getConnection(url, props);
            LOG.debug("success connect to kingbase[{}]", url);
        } catch (SQLException e) {
            throw new ConnectionException("Can not connect to database: " + url, e);
        }

        return connection;
    }


    @Override
    public DatasourceDriver getDriverInterface() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getDriverInterface()" + " @@@@@@@@@@@@@@@@@");
        return DEFAULT_DRIVER;
    }

    @Override
    public boolean isCatalog() {
        return true;
    }

    @Override
    public Set<String> getEmptyCatalogs() {
        return excludeCatalog;
    }

    @Override
    public boolean supportedQueryTimeout() {
        return true;
    }

    @Override
    public DataSourceType getType() {
        return DataSourceType.POSTGRESQL;
    }

    public static List<ClassifierDataSource> buildDatasource(Long tenantId, TaskConfig.DataSource dsConfig) throws SQLException, ConnectionException {
        try {
            Map map = JSON.from(dsConfig.getExtraCfg()).toObject(Map.class);
            if (map != null && !map.isEmpty()) {
                dsConfig.setExtraCfg("");
            }
        } catch (Exception ignored) {
        }
        if (StringUtils.isBlank(dsConfig.getExtraCfg()) || dsConfig.getExtraCfg().startsWith("?")) {
            String extraCfg = dsConfig.getExtraCfg();
            if (StringUtils.isNotBlank(extraCfg) && dsConfig.getExtraCfg().startsWith("?") ){
                dsConfig.setExtraCfg("postgres"+extraCfg);
            } else {
                dsConfig.setExtraCfg("postgres");
            }
            try (Connection connect = connect(dsConfig.getHost(), dsConfig.getPort(), dsConfig.getExtraCfg(), dsConfig.getAuthCfg().getUsername(), dsConfig.getAuthCfg().getPassword());
                 PreparedStatement preparedStatement = connect.prepareStatement("SELECT datname FROM pg_database", ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
                 ResultSet resultSet = preparedStatement.executeQuery()) {
                List<ClassifierDataSource> classifierDataSourceList = new ArrayList<>();
                while (resultSet.next()) {
                    String catalogName = resultSet.getString("datname");
                    if (!excludeCatalog.isEmpty() && excludeCatalog.contains(catalogName.toUpperCase())) {
                        continue;
                    }
                    Kingbase8Datasource pgsqlDatasource = Kingbase8Datasource.builder()
                            .tenantId(tenantId)
                            .id(dsConfig.getId())
                            .name(dsConfig.getName())
                            .host(dsConfig.getHost())
                            .port(dsConfig.getPort())
                            .dbName(catalogName)
                            .username(dsConfig.getAuthCfg().getUsername())
                            .password(dsConfig.getAuthCfg().getPassword())
                            .build();
                    classifierDataSourceList.add(pgsqlDatasource);
                }
                return classifierDataSourceList;
            } catch (Exception e) {
                throw e;
            }
        } else {
            List<ClassifierDataSource> classifierDataSourceList = Arrays.stream(dsConfig.getExtraCfg().split(","))
                    .filter(StringUtils::isNotBlank)
                    .map(db -> Kingbase8Datasource.builder()
                            .tenantId(tenantId)
                            .id(dsConfig.getId())
                            .name(dsConfig.getName())
                            .host(dsConfig.getHost())
                            .port(dsConfig.getPort())
                            .dbName(db)
                            .username(dsConfig.getAuthCfg().getUsername())
                            .password(dsConfig.getAuthCfg().getPassword())
                            .build())
                    .collect(Collectors.toList());
            if (classifierDataSourceList.isEmpty()) {
                throw new RuntimeException("DB list is empty");
            }
            return classifierDataSourceList;
        }
    }

}
