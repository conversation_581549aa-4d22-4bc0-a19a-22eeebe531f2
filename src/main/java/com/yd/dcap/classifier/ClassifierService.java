package com.yd.dcap.classifier;

import com.dcap.classifier.context.RuleContext;
import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.TaskGlobalDataHolder;
import com.dcap.utils.JSON;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.probe.client.ProbeClientTaskUtil;
import com.yd.dcap.probe.client.TaskConfig;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@DependsOn("springContextUtil")
public class ClassifierService {

    private static final Logger log = LoggerFactory.getLogger(ClassifierService.class);


    public Map<String, Object> scan(String taskType, TaskConfig taskConfig) throws IOException {
        String message = "Start of task; scan job history id: " + taskConfig.getScanJobHistoryId()+", task id: "+ taskConfig.getTaskId();
        log.info(message);
        log.info(JSON.from(taskConfig.getDatasource()).toString());
        ProbeClientTaskContext probeClientTaskContext = ProbeClientTaskUtil.getInstance().getProbeClientTaskContext(taskType, taskConfig.getTaskId());
        probeClientTaskContext.reportExecuting(StatusRecord.Position.Scan,
                "scan job history id [" + taskConfig.getScanJobHistoryId() + "], task id ["+taskConfig.getTaskId()+"]", message)
                .sendToServer();
        Map<String, String> tablesResult = null;
        try (TaskGlobalDataHolder globalDataHolder = new TaskGlobalDataHolder(probeClientTaskContext, taskConfig)) {
            for (ClassifierDataSource classifierDataSource : getDataSourceList(taskConfig)) {
                if (globalDataHolder.getProbeClientTaskContext().checkInterrupt()) {
                    break;
                }
                RuleContext ruleContext = null;
                try {
                    log.debug("Create RuleContext");
                    ruleContext = RuleContext.createRuleContext(classifierDataSource, globalDataHolder);
                    log.debug("evaluate Rules");
                    tablesResult = ruleContext.evaluateRules();
//                    // 记录用户和权限
//                    try {
//                        ruleContext.recordUsersAndPrivileges();
//                    } catch (Exception e){
//                        if (Objects.equals(probeClientTaskContext.getTaskType(), ProbeClientTask.TASK_TYPE_SCAN)) {
//                            log.error(e.getMessage());
//                        } else {
//                            throw e;
//                        }
//                    }
                } catch (ConnectionException e) {
                    log.error(e.getMessage(), e);
                    // 如果是连接错误就认为任务失败。
                    return probeClientTaskContext.reportFailed(StatusRecord.Position.RuleContext,
                            "scan job history id [" + taskConfig.getScanJobHistoryId() + "], task id ["+taskConfig.getTaskId()+"]",
                            e
                    ).toMap();
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    probeClientTaskContext.reportErrorOccurredExecuting(StatusRecord.Position.RuleContext,
                            "scan job history id [" + taskConfig.getScanJobHistoryId() + "], task id ["+taskConfig.getTaskId()+"]",
                            null, e
                    );
                } finally {
                    if (ruleContext != null){
                        // 释放资源
                        ruleContext.release();
                    }
                }
            }
            String endTaskMsg = "End of task; scan job history id: " + taskConfig.getScanJobHistoryId()+", task id:"+taskConfig.getTaskId();
            Map<String,Object> reportMap = null;
            if (!probeClientTaskContext.isInterrupted()){
                reportMap = probeClientTaskContext.reportSuccess(StatusRecord.Position.Scan,
                        "scan job history id [" + taskConfig.getScanJobHistoryId() + "], task id ["+taskConfig.getTaskId()+"]",
                        endTaskMsg).toMap();
            } else {
                reportMap = probeClientTaskContext.reportInterrupted(StatusRecord.Position.Scan, endTaskMsg).toMap();
            }

            // 此时计算应该清理的表
            Set<String> remaining = globalDataHolder.getHashTables().keySet();
            TaskConfig.TaskParam.ScanRange scanRange = globalDataHolder.getTaskParam().getScanRange();
            List<String> cleanedTables = calculateTablesForCleaned(remaining, scanRange);
            reportMap.put("cleanedTables", cleanedTables);
            String reportText = JSON.from(reportMap).toString();
            reportMap.remove("cleanedTables");// 序列化之后，就删除，因为返回任务结果的位置，用不上。
            log.info("{}; {}", endTaskMsg, reportText);
            // 任务完成后，将任务执行结果，也写入一条记录到数据队列，这样在消费端，可以确认整个任务已经完全消费结束。
            globalDataHolder.getInventoryWriter().writeEndRecordToDataTopic(probeClientTaskContext.getExecStatus(), reportText);

            // 扫描完成后，将表的 hash 结果传回。
            if (tablesResult == null){
                tablesResult = new HashMap<>();
            }
            reportMap.put("hashTables", tablesResult);
            return reportMap;
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            Map<String,Object> reportMap = probeClientTaskContext.reportFailed(StatusRecord.Position.Scan, null, ex).toMap();
            String reportText = JSON.from(reportMap).toString();
            log.error("Error of task; scan job history id: {}, task id: {}; {}", taskConfig.getScanJobHistoryId(), taskConfig.getTaskId(), reportText);
            return reportMap;
        }
    }
    private List<String> calculateTablesForCleaned(Collection<String> remaining, TaskConfig.TaskParam.ScanRange scanRange){
        Set<String> selectedDatabase = scanRange.getSelectedDatabase();
        Set<String> excludedDatabase = scanRange.getExcludedDatabase();
        Set<String> selectedSchema = scanRange.getSelectedSchema();
        Set<String> excludedSchema = scanRange.getExcludedSchema();
        Set<String> selectedTable = scanRange.getSelectedTable();
        Set<String> excludedTable = scanRange.getExcludedTable();
        Set<String> selectedView = scanRange.getSelectedView();
        Set<String> excludedView = scanRange.getExcludedView();
        Set<String> selectedSynonym = scanRange.getSelectedSynonym();
        Set<String> excludedSynonym = scanRange.getExcludedSynonym();
        Stream<String> stream = remaining.stream();
        if (selectedDatabase != null && !selectedDatabase.isEmpty()){
            stream = stream.filter(tableIdentityName->{
                for (String databaseName : selectedDatabase) {
                    if (tableIdentityName.startsWith(databaseName)) {
                        return true;
                    }
                }
                return false;
            });
        }
        if (excludedDatabase != null && !excludedDatabase.isEmpty()){
            stream = stream.filter(tableIdentityName->{
                for (String databaseName : excludedDatabase) {
                    if (!tableIdentityName.startsWith(databaseName)) {
                        return true;
                    }
                }
                return false;
            });
        }
        if (selectedSchema != null && !selectedSchema.isEmpty()){
            stream = stream.filter(tableIdentityName->{
                for (String schemaName : selectedSchema) {
                    if (tableIdentityName.startsWith(schemaName)) {
                        return true;
                    }
                }
                return false;
            });
        }
        if (excludedSchema != null && !excludedSchema.isEmpty()){
            stream = stream.filter(tableIdentityName->{
                for (String schemaName : excludedSchema) {
                    if (!tableIdentityName.startsWith(schemaName)) {
                        return true;
                    }
                }
                return false;
            });
        }
        if (selectedTable != null && !selectedTable.isEmpty()){
            stream = stream.filter(tableIdentityName->{
                for (String tableName : selectedTable) {
                    if (tableIdentityName.equals(tableName)) {
                        return true;
                    }
                }
                return false;
            });
        }
        if (excludedTable != null && !excludedTable.isEmpty()){
            stream = stream.filter(tableIdentityName->{
                for (String tableName : excludedTable) {
                    if (!tableIdentityName.equals(tableName)) {
                        return true;
                    }
                }
                return false;
            });
        }
        if (selectedView != null && !selectedView.isEmpty()){
            stream = stream.filter(tableIdentityName->{
                for (String viewName : selectedView) {
                    if (tableIdentityName.equals(viewName)) {
                        return true;
                    }
                }
                return false;
            });
        }
        if (excludedView != null && !excludedView.isEmpty()){
            stream = stream.filter(tableIdentityName->{
                for (String viewName : excludedView) {
                    if (!tableIdentityName.equals(viewName)) {
                        return true;
                    }
                }
                return false;
            });
        }
        if (selectedSynonym != null && !selectedSynonym.isEmpty()){
            stream = stream.filter(tableIdentityName->{
                for (String synonymName : selectedSynonym) {
                    if (tableIdentityName.equals(synonymName)) {
                        return true;
                    }
                }
                return false;
            });
        }
        if (excludedSynonym != null && !excludedSynonym.isEmpty()){
            stream = stream.filter(tableIdentityName->{
                for (String synonymName : excludedSynonym) {
                    if (!tableIdentityName.equals(synonymName)) {
                        return true;
                    }
                }
                return false;
            });
        }
        return stream.collect(Collectors.toList());
    }

    private List<ClassifierDataSource> getDataSourceList(TaskConfig taskConfig) throws SQLException, ConnectionException {
        List<ClassifierDataSource> classifierDataSourceList = new ArrayList<>();
        List<ClassifierDataSource> classifierDataSources = createDataSource(taskConfig.getTenantId(), taskConfig.getDatasource());
        classifierDataSourceList.addAll(classifierDataSources);
        return classifierDataSourceList;
    }

    private List<ClassifierDataSource> createDataSource(Long tenantId, TaskConfig.DataSource dsConfig) throws SQLException, ConnectionException {
        switch (dsConfig.getSourceType()) {
            case "mysql":
            case "tdsql_mysql":
            case "starrocks":
            case "mariadb":
            case "polardb_mysql":
            case "analyticdb_mysql":
            case "tidb":
            case "ob":
                return MysqlDatasource.buildDatasource(tenantId, dsConfig);
            case "doris":
                return DorisDatasource.buildDatasource(tenantId, dsConfig);
            case "mssql":
                return SqlServerDatasource.buildDatasource(tenantId, dsConfig);
            case "kingbase":
                return Kingbase8Datasource.buildDatasource(tenantId, dsConfig);
            case "pgsql":
            case "vastbase":
            case "gplum":
            case "polardb_pg":
            case "hologres":
            case "oushudb":
            case "analyticdb_pg":
            case "tdsql_pg":
                return PgsqlDatasource.buildDatasource(tenantId, dsConfig);
            case "redshift":
                return RedshiftDatasource.buildDatasource(tenantId, dsConfig);
            case "highgo":
                return HighGoDatasource.buildDatasource(tenantId, dsConfig);
            case "gbase8c":
            case "opengauss":
            case "gaussdb":
                return Gbase8cDatasource.buildDatasource(tenantId, dsConfig);
            case "oceanbase_oracle":
                return OceanbaseOracleDatasource.buildDatasource(tenantId, dsConfig);
            case "oracle":
                return OracleDatasource.buildDatasource(tenantId, dsConfig);
            case "mongo":
                return MongoDatasource.buildDatasource(tenantId, dsConfig);
            case "transwarp_inceptor":
                return InceptorDatasource.buildDatasource(tenantId, dsConfig);
            case "transwarp_argodb":
                return InceptorArgoDatasource.buildDatasource(tenantId, dsConfig);
            case "sparksql":
                return SparkSQLDatasource.buildDatasource(tenantId, dsConfig);
            case "hive":
                return HiveDatasource.buildDatasource(tenantId, dsConfig);
            case "dameng":
                return DaMengDatasource.buildDatasource(tenantId, dsConfig);
            case "hana":
                return HanaDatasource.buildDatasource(tenantId, dsConfig);
            case "db2":
                return Db2Datasource.buildDatasource(tenantId, dsConfig);
            case "maxcompute":
                return MaxComputeDatasource.buildDatasource(tenantId, dsConfig);
            case "impala":
                return ImpalaDatasource.buildDatasource(tenantId, dsConfig);
            case "dremio":
                return DremioDatasource.buildDatasource(tenantId, dsConfig);
            case "sinodb":
                return SinodbDatasource.buildDatasource(tenantId, dsConfig);
            case "gbase8s":
                return Gbase8sDatasource.buildDatasource(tenantId, dsConfig);
            case "elasticsearch":
                return ElasticsearchDatasource.buildDatasource(tenantId, dsConfig);
            case "redis":
                return RedisDatasource.buildDatasource(tenantId, dsConfig);
            case "clickhouse":
                return ClickhouseDatasource.buildDatasource(tenantId, dsConfig);
            case "gbase8a":
                return Gbase8aDatasource.buildDatasource(tenantId, dsConfig);
            case "dynamodb":
                return DynamoDatasource.buildDatasource(tenantId, dsConfig);
            case "trino":
                return TrinoDatasource.buildDatasource(tenantId, dsConfig);
            case "prestodb":
            case "prestosql":
                return PrestoDatasource.buildDatasource(tenantId, dsConfig);
            case "cache":
                return CacheDatasource.buildDatasource(tenantId, dsConfig);
            case "sybase":
                return SybaseDatasource.buildDatasource(tenantId, dsConfig);
            default:
                throw new RuntimeException("Not supported DB type: " + dsConfig.getSourceType());
        }
    }
}
