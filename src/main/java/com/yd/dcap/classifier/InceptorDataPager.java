package com.yd.dcap.classifier;

import com.dcap.classifier.InitializationException;
import com.dcap.classifier.access.SQLDataPager;
import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.RuleContext;
import org.apache.commons.lang3.StringUtils;

import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

public class InceptorDataPager extends SQLDataPager {

   private static final transient String SQL_PAGE_LIMIT = " limit ${SUB_START}, ${SUB_FETCH} ";

   public InceptorDataPager(RuleContext ruleContext) {
      super(ruleContext);
   }

   @Override
   public String createSql(List<ContextColumn> columns, long start, long rowLimit, boolean useRandomSampling) {
      String qualifiedName = columns.stream()
              .map(column -> "`" + column.getColumnName() + "`")
              .collect(Collectors.joining(","));
      StringBuilder sql = new StringBuilder("select ");
      sql.append(qualifiedName).append(this.getFrom());
      if (useRandomSampling) {
         sql.append(this.substituteRange(SQL_PAGE_LIMIT, Math.max(start - 1L, 0L), rowLimit));
      } else if (this.context.getTaskGlobalDataHolder().getTaskParam().getSamplingReverseOrder()){
         String order = getOrder();
         if (StringUtils.isNotBlank(order)) {
            sql.append(order).append(" desc");
         }
         sql.append(" LIMIT ").append(rowLimit);
      } else {
         sql.append(" LIMIT ").append(rowLimit);
      }
      return sql.toString();
   }

   @Override
   public String createSpecifyTableSql(String tableName, List<String> columns, long start, long rowLimit, boolean useRandomSampling) {
      String qualifiedName = columns.stream()
              .map(column -> "`" + column + "`")
              .collect(Collectors.joining(","));
      StringBuilder sql = new StringBuilder("select ");
      sql.append(qualifiedName).append(this.getFrom(tableName));
      if (useRandomSampling) {
         sql.append(this.substituteRange(SQL_PAGE_LIMIT, Math.max(start - 1L, 0L), rowLimit));
      } else {
         sql.append(" LIMIT ").append(rowLimit);
      }
      return sql.toString();
   }

   @Override
   public String createSubsamplingSql(String tableName, String column, long start, long rowLimit, boolean useRandomSampling) {
      String qualifiedName = "`" + column + "`";
      StringBuilder sql = new StringBuilder("select ");
      sql.append(qualifiedName).append(this.getFrom(tableName));
      sql.append(" where ").append(this.appendExcludeEmptyCondition(qualifiedName));
      if (useRandomSampling) {
         sql.append(this.substituteRange(SQL_PAGE_LIMIT, Math.max(start - 1L, 0L), rowLimit));
      } else {
         sql.append(" LIMIT ").append(rowLimit);
      }
      return sql.toString();
   }

   @Override
   protected String getFrom() {
      return this.getFrom(this.context.getCurrentTableName());
   }

   protected String getOrder() {
      String order = null;
      try {
         List<ContextColumn> columns = this.getContext().getPrimaryKeyColumns();
         if (columns != null && !columns.isEmpty()) {
            StringBuilder orderBuilder = new StringBuilder(SQL_ORDER);
            Iterator<ContextColumn> columnIterator = columns.iterator();
            while (columnIterator.hasNext()) {
               ContextColumn column = columnIterator.next();
               orderBuilder.append("`").append(column.getColumnName()).append("`");
               if (columnIterator.hasNext()) {
                  orderBuilder.append(",");
               }
            }
            order = orderBuilder.toString();
         }
      } catch (InitializationException var7) {
         if (LOG.isDebugEnabled()) {
            LOG.debug("Could not get primary key for: " + this.getContext().getCurrentTableName(), var7);
         }
      }

      if (order == null) {
         try {
            for (ContextColumn column : this.getContext().getTableColumns()) {
               if (column.isOfType(ContextColumn.DATE)) {
                  order = " order by `" + column.getColumnName() + "`";
                  break;
               }
            }
         } catch (InitializationException var6) {
            if (LOG.isDebugEnabled()) {
               LOG.debug("Could not get date column for: " + this.getContext().getCurrentTableName(), var6);
            }
         }
      }

      if (order == null) {
         try {
            ContextColumn column = this.getContext().getTableColumns().get(0);
            order = " order by `" + column.getColumnName() +"`";
         } catch (InitializationException initializationException) {
            LOG.debug("Could not find an order for: {}", this.getContext().getCurrentTableName(), initializationException);
         }
      }

      if (order == null) {
         order = "";
      }

      return order;
   }
}
