package com.yd.dcap.classifier;

import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.DataSourceType;
import com.dcap.datalayer.DatasourceDriver;
import com.google.common.collect.ImmutableSet;
import com.yd.dcap.probe.client.TaskConfig;
import lombok.Builder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.Properties;
import java.util.Set;

@Builder
public class SqlServerDatasource extends ClassifierDataSource {

    private static final Logger LOG = LoggerFactory.getLogger(SqlServerDatasource.class);

    private Long tenantId;
    private String id;
    private String name;
    private String host;
    private String port;
    private String username;
    private String password;

    private static final DatasourceDriver DEFAULT_DRIVER = new DatasourceDriver() {
        @Override
        public String[] adjustTableTypes(String[] tableTypes, boolean excludeInternalTables, Connection connection) {
            return tableTypes;
        }

        @Override
        public String getExceptionDetails(String sql, SQLException sqlException) {
            return sql;
        }
    };

    @Override
    public Long getTenantId() {
        return tenantId;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getDescription() {
        return "SqlServer DataSource";
    }

    @Override
    public String getAttributes() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getAttributes()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public String getDbName() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getDbName()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public String getInstanceName() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getInstanceName()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public String getConnectionDescriptor() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getConnectionDescriptor()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public Connection connect() throws ConnectionException {
        if (StringUtils.isBlank(this.username) || StringUtils.isBlank(this.password)){
            throw new IllegalArgumentException("username ["+this.username+"] or password ["+password+"] is blank ");
        }
        try {
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        } catch (ClassNotFoundException e) {
            throw new RuntimeException(e);
        }
        DriverManager.setLoginTimeout(30);
//        String drvName = "com.ibm.guardium.jdbc.sqlserver.SQLServerDriver";
//        String drvName = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
//        Driver dbDriver;
//        try {
//            dbDriver = (Driver) Class.forName(drvName).newInstance();
//            DriverManager.registerDriver(dbDriver);
//        } catch (Exception e) {
//            throw new ConnectionException("Can not initialize the driver class: " + drvName, e);
//        }

        Properties props = new Properties();
        String url = "jdbc:sqlserver://" + host + ":" + port;
        props.setProperty("user", username);
        props.setProperty("password", password);

        Connection connection;
        try {
            connection = DriverManager.getConnection(url, props);
            LOG.debug("success connect to SqlServer["+url+"],user["+username+"]");
        } catch (SQLException e) {
            throw new ConnectionException("Can not connect to database: " + url, e);
        }

        return connection;
    }

    @Override
    public DatasourceDriver getDriverInterface() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getDriverInterface()" + " @@@@@@@@@@@@@@@@@");
        return DEFAULT_DRIVER;
    }

    @Override
    public boolean isCatalog() {
        return true;
    }

    @Override
    public DataSourceType getType() {
        return DataSourceType.MSSQL;
    }

    @Override
    public Set<String> getEmptyCatalogs() {
        return ImmutableSet.of("master", "msdb", "tempdb");
    }

    @Override
    public boolean supportedQueryTimeout() {
        return true;
    }

    public static List<ClassifierDataSource> buildDatasource(Long tenantId, TaskConfig.DataSource dsConfig) {
        return Collections.singletonList(SqlServerDatasource.builder()
                .tenantId(tenantId)
                .id(dsConfig.getId())
                .name(dsConfig.getName())
                .host(dsConfig.getHost())
                .port(dsConfig.getPort())
                .username(dsConfig.getAuthCfg().getUsername())
                .password(dsConfig.getAuthCfg().getPassword())
                .build());
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
