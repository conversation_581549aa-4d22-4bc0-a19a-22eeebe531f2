package com.yd.dcap.classifier;

import com.dcap.classifier.ClassifierException;
import com.dcap.classifier.InitializationException;
import com.dcap.classifier.access.DataSampler;
import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.RuleContext;
import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.TaskGlobalDataHolder;
import com.yd.dcap.classifier.taskreport.ScanDbReport;
import org.apache.commons.lang3.StringUtils;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;

import java.sql.Connection;
import java.util.*;
import java.util.stream.Collectors;

public class DynamoRuleContext extends RuleContext {

   public DynamoRuleContext(ClassifierDataSource dataSource, TaskGlobalDataHolder globalDataHolder) throws ConnectionException {
      super(dataSource, globalDataHolder);
   }

   @Override
   public void setConnection(Connection connection) {
      this.hasMoreTables();
   }

   @Override
   protected List<String> findCatalogs() {
      this.resetBrowse();
      DynamoDatasource datasource = (DynamoDatasource) this.getDatasource();
      List<String> catalogs = datasource.getCatalogs();
      Set<String> selectedDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedDatabase();
      Set<String> excludedDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedDatabase();
      for (String catalog : catalogs) {
         // 如果包含就跳过
         if (excludedDatabase.contains(catalog.toLowerCase())){
            continue;
         }
         if (selectedDatabase.isEmpty() || selectedDatabase.contains(catalog.toLowerCase())) {
            this.catalogs.add(catalog);
         }
      }
      return this.catalogs;
   }

   @Override
   protected List<ContextTable> findCatalogTables(String catalog) {
      if (this.tables != null) {
         this.tables.clear();
      } else {
         this.tables = new ArrayList<>();
      }
      Set<String> excludedSchema = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedSchema();
      if (excludedSchema.contains(catalog.toLowerCase())){
         return listCatalogTables();
      }

      Set<String> selectedSchema = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedSchema();
      if (!selectedSchema.isEmpty()  && !selectedSchema.contains(catalog.toLowerCase())){
         return listCatalogTables();
      }
      this.getTaskGlobalDataHolder().getProbeClientTaskContext().recordField(ScanDbReport.FIELD_DATASOURCE_COUNT, 1);

      this.currentTable = null;

      Set<String> selectedTables = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedTable();
      Set<String> excludedTables = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedTable();
      DynamoDatasource datasource = (DynamoDatasource) this.getDatasource();
      List<String> tables = datasource.getTables(catalog);
      for(String tableName: tables){
         ContextTable contextTable = this.createContextTable(catalog, null, tableName, "TABLE",null);
         if ( contextTable == null){
            continue;
         }

         String tableIdentityName = contextTable.getIdentityName().toLowerCase();
         if (excludedTables.contains(tableIdentityName)){
            continue;
         }
         if ((selectedTables.isEmpty() || selectedTables.contains(tableIdentityName))){
            this.addTable(contextTable);
         }
      }

      this.getTaskGlobalDataHolder().getProbeClientTaskContext().recordField(ScanDbReport.FIELD_TABLE_COUNT, this.tables.size()).sendToServer();
      return this.listCatalogTables();
   }

   protected int addTable(ContextTable table) {
      if (table == null) {
         return this.tables.size();
      }
      if (this.tables == null) {
         this.tables = new ArrayList<>();
      }
      this.tables.add(table);
      return this.tables.size();
   }

   @Override
   protected List<ContextColumn> findTableColumns() throws InitializationException {
      ContextTable currentTable = this.getCurrentTable();

      String catalog = currentTable.getCatalog();
      String table = currentTable.getTableName();

      List<ContextColumn> contextColumns = new ArrayList<>();
      Set<String> addedColumns = new HashSet<>();
      DynamoDatasource datasource = (DynamoDatasource) this.getDatasource();
      for (Set<Map.Entry<String, Object>> row : datasource.getRows(catalog, table)) {
         for (Map.Entry<String, Object> entry : row) {
            String column = entry.getKey();
            // todo: column size set to 20
            addedColumns.add(column);
            contextColumns.add(this.createContextColumn(currentTable, column, 1, "", 20, 0, null,0, false));
            addNested(contextColumns, addedColumns, currentTable, column, entry.getValue());
         }
      }
      return contextColumns.stream().map(column->addedColumns.remove(column.getColumnName())?column:null)
              .filter(Objects::nonNull).collect(Collectors.toList());
   }
   private void addNested(List<ContextColumn> contextColumns, Set<String> addedColumns, ContextTable currentTable,
                                String currentColumn, Object value){
      if (value instanceof Collection){
         Collection<Object> items = (Collection<Object>) value;
         if (items.isEmpty()){
            return;
         }
         for (Object item : items) {
            if (!(item instanceof Map)){
               return;
            }
         }

         for (Object column : items) {
            addNestedDocumentColumn(contextColumns, addedColumns, currentTable, currentColumn, column);
         }
      } else if (value instanceof Map){
         addNestedDocumentColumn(contextColumns, addedColumns, currentTable, currentColumn, value);
      } else if (value instanceof AttributeValue){
         AttributeValue attributeValue = ((AttributeValue) value);
         AttributeValue.Type type = attributeValue.type();
         value = attributeValue.getValueForField(type.name(), Object.class).orElse(null);
         addNestedDocumentColumn(contextColumns, addedColumns, currentTable, currentColumn, value);
      }
   }
   private void addNestedDocumentColumn(List<ContextColumn> contextColumns, Set<String> addedColumns, ContextTable currentTable,
                                  String currentColumn, Object row){
      if(!(row instanceof Map)){
         return;
      }
      Map<String,Object> record = (Map<String, Object>) row;
      for (Map.Entry<String, Object> entry : record.entrySet()) {
         String column = StringUtils.joinWith(".",currentColumn, entry.getKey());
         contextColumns.add(this.createContextColumn(currentTable, column, 1, "", 20, 0,null,0, false));
         addedColumns.add(column);
         addNested(contextColumns, addedColumns, currentTable, column, entry.getValue());
      }
   }

   @Override
   public DataSampler getSamplerFor(String table) throws ClassifierException {
      String catalog = getCurrentTable().getCatalog();
      return new DataSampler(this) {
         @Override
         public Map<String, List<String>> sample(List<ContextColumn> columns) {
            Map<String, List<String>> result = new HashMap<>();

            Set<String> columnNames = columns.stream().map(ContextColumn::getColumnName).collect(Collectors.toSet());
            DynamoDatasource datasource = (DynamoDatasource) DynamoRuleContext.this.getDatasource();
            for (Set<Map.Entry<String, Object>> row : datasource.getRows(catalog, table)) {
               for (Map.Entry<String, Object> entry : row) {
                  String column = entry.getKey();
                  Object value = entry.getValue() == null ? null : entry.getValue();
                  if (value == null){
                     continue;
                  }
                  addSampleResult(result, columnNames, column, value);
               }
               row.forEach(entry -> {
                  String column = entry.getKey();
                  String value = entry.getValue() == null ? null : entry.getValue().toString();

                  if (value != null && columnNames.contains(column)) {
                     result.compute(column, (k, v) -> {
                        if (v == null) {
                           v = new ArrayList<>();
                        }
                        v.add(value);
                        return v;
                     });
                  }
               });
            }
            return result;
         }

         private void addSampleResult(Map<String, List<String>> result, Set<String> columnNames,
                                            String currentColumn, Object value){
            if (value instanceof Collection){
               Collection<Object> items = (Collection<Object>) value;
               if (items.isEmpty()){
                  return;
               }
               for (Object item : items) {
                  if (!(item instanceof Map)){
                     return;
                  }
               }

               for (Object columnValue : items) {
                  Map<String,Object> columnValueMap = (Map<String, Object>) columnValue;
                  for (Map.Entry<String, Object> entry : columnValueMap.entrySet()) {
                     Object v = entry.getValue();
                     if (v == null){
                        continue;
                     }
                     String column = StringUtils.joinWith(".", currentColumn, entry.getKey());
                     if (!columnNames.contains(column)){
                        continue;
                     }
                     this.addSampleResult(result, columnNames, column, v);
                  }
               }
            } else if (value instanceof AttributeValue){
               AttributeValue attributeValue = ((AttributeValue) value);
               AttributeValue.Type type = attributeValue.type();
               value = attributeValue.getValueForField(type.name(), Object.class).orElse(null);
            }
            if (value == null){
               return;
            }

            if(!(value instanceof Map)){
               String text = value.toString();
               result.compute(currentColumn, (k, vs) -> {
                  if (vs == null) {
                     vs = new ArrayList<>();
                  }
                  vs.add(text);
                  return vs;
               });
               return;
            }
            Map<String,Object> columnValue = (Map<String, Object>) value;
            for (Map.Entry<String, Object> entry : columnValue.entrySet()) {
               Object v = entry.getValue();
               if (v == null){
                  continue;
               }
               String column = StringUtils.joinWith(".", currentColumn, entry.getKey());
               if (!columnNames.contains(column)){
                  continue;
               }
               this.addSampleResult(result, columnNames, column, v);
            }
         }
      };
   }
}
