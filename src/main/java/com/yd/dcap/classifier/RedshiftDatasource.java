package com.yd.dcap.classifier;

import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.DataSourceType;
import com.dcap.datalayer.DatasourceDriver;
import com.dcap.utils.JSON;
import com.dcap.utils.UtilDB;
import com.google.common.collect.ImmutableSet;
import com.yd.dcap.probe.client.TaskConfig;
import lombok.Builder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

@Builder
public class RedshiftDatasource extends ClassifierDataSource {

    private static final Logger LOG = LoggerFactory.getLogger(RedshiftDatasource.class);

    private Long tenantId;
    private String id;
    private String name;
    private String host;
    private String port;
    private String username;
    private String password;
    private String dbName;
    private String url;

    private static final Set<String> excludeCatalog = ImmutableSet.of("awsdatacatalog", "padb_harvest", "sys:internal", "template1", "template0")
            .stream().map(String::toUpperCase).collect(Collectors.toSet());

    private static final DatasourceDriver DEFAULT_DRIVER = new DatasourceDriver() {
        @Override
        public String[] adjustTableTypes(String[] tableTypes, boolean excludeInternalTables, Connection connection) {
            return tableTypes;
        }

        @Override
        public String getExceptionDetails(String sql, SQLException sqlException) {
            return sql;
        }
    };

    @Override
    public Long getTenantId() {
        return tenantId;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getDescription() {
        return "Redshift DataSource";
    }

    @Override
    public String getAttributes() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getAttributes()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public String getDbName() {
        if (dbName.contains("?")){
            return dbName.substring(0,dbName.indexOf("?"));
        }
        return dbName;
    }

    @Override
    public String getInstanceName() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getInstanceName()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public String getConnectionDescriptor() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getConnectionDescriptor()" + " @@@@@@@@@@@@@@@@@");
        return url;
    }

    @Override
    public Connection connect() throws ConnectionException {
        if (StringUtils.isBlank(this.username) || StringUtils.isBlank(this.password)){
            throw new IllegalArgumentException("username ["+this.username+"] or password ["+password+"] is blank ");
        }

        String drvName = "com.amazon.redshift.jdbc42.Driver";
        Driver dbDriver;
        try {
            dbDriver = (Driver) Class.forName(drvName).newInstance();
        } catch (Exception e) {
            throw new ConnectionException("Can not initialize the driver class: " + drvName, e);
        }

        Properties props = new Properties();
        url = "jdbc:redshift://" + host + ":" + port + "/" + dbName;
        props.setProperty("user", username);
        props.setProperty("password", password);

        Connection connection;
        try {
            connection = dbDriver.connect(url, props);
            LOG.info("success connect to redshift[{}]", url);
        } catch (SQLException e) {
            throw new ConnectionException("Can not connect to database: " + url, e);
        }

        return connection;
    }

    private static Connection connect(String host, String port, String dbName, String username, String password) throws ConnectionException {
        DriverManager.setLoginTimeout(30);
        Connection redshift = UtilDB.buildDatabaseConnection("redshift", host, Integer.parseInt(port), username, password, dbName);
        LOG.debug("success connect to redshift[{}]", host);
        return redshift;
    }


    @Override
    public DatasourceDriver getDriverInterface() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getDriverInterface()" + " @@@@@@@@@@@@@@@@@");
        return DEFAULT_DRIVER;
    }

    @Override
    public boolean isCatalog() {
        return true;
    }

    @Override
    public Set<String> getEmptyCatalogs() {
        return excludeCatalog;
    }

    @Override
    public boolean supportedQueryTimeout() {
        return true;
    }

    @Override
    public DataSourceType getType() {
        return DataSourceType.REDSHIFT;
    }

    public static List<ClassifierDataSource> buildDatasource(Long tenantId, TaskConfig.DataSource dsConfig) throws SQLException, ConnectionException {
        try {
            Map map = JSON.from(dsConfig.getExtraCfg()).toObject(Map.class);
            if (map != null && !map.isEmpty()) {
                dsConfig.setExtraCfg("");
            }
        } catch (Exception ignored) {
        }
        if (StringUtils.isBlank(dsConfig.getExtraCfg()) || dsConfig.getExtraCfg().startsWith("?")) {
            String extraCfg = dsConfig.getExtraCfg();
            if (StringUtils.isNotBlank(extraCfg) && dsConfig.getExtraCfg().startsWith("?")){
                dsConfig.setExtraCfg("dev"+extraCfg);
            } else {
                dsConfig.setExtraCfg("dev");
            }
            try (Connection connect = connect(dsConfig.getHost(), dsConfig.getPort(), dsConfig.getExtraCfg(), dsConfig.getAuthCfg().getUsername(), dsConfig.getAuthCfg().getPassword());
                 PreparedStatement preparedStatement = connect.prepareStatement("SELECT datname FROM pg_database", ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
                 ResultSet resultSet = preparedStatement.executeQuery()) {
                List<ClassifierDataSource> classifierDataSourceList = new ArrayList<>();
                while (resultSet.next()) {
                    String catalogName = resultSet.getString("datname");
                    if (!excludeCatalog.isEmpty() && excludeCatalog.contains(catalogName.toUpperCase())) {
                        continue;
                    }
                    RedshiftDatasource pgsqlDatasource = RedshiftDatasource.builder()
                            .tenantId(tenantId)
                            .id(dsConfig.getId())
                            .name(dsConfig.getName())
                            .host(dsConfig.getHost())
                            .port(dsConfig.getPort())
                            .dbName(catalogName)
                            .username(dsConfig.getAuthCfg().getUsername())
                            .password(dsConfig.getAuthCfg().getPassword())
                            .build();
                    classifierDataSourceList.add(pgsqlDatasource);
                }
                return classifierDataSourceList;
            } catch (Exception e) {
                throw e;
            }
        } else {
            List<ClassifierDataSource> classifierDataSourceList = Arrays.stream(dsConfig.getExtraCfg().split(","))
                    .filter(StringUtils::isNotBlank)
                    .map(db -> RedshiftDatasource.builder()
                            .tenantId(tenantId)
                            .id(dsConfig.getId())
                            .name(dsConfig.getName())
                            .host(dsConfig.getHost())
                            .port(dsConfig.getPort())
                            .dbName(db)
                            .username(dsConfig.getAuthCfg().getUsername())
                            .password(dsConfig.getAuthCfg().getPassword())
                            .build())
                    .collect(Collectors.toList());
            if (classifierDataSourceList.isEmpty()) {
                throw new RuntimeException("DB list is empty");
            }
            return classifierDataSourceList;
        }
    }

}
