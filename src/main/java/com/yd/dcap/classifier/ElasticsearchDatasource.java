package com.yd.dcap.classifier;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.indices.*;
import co.elastic.clients.elasticsearch.indices.get_alias.IndexAliases;
import co.elastic.clients.json.JsonData;
import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.DataSourceType;
import com.dcap.datalayer.DatasourceDriver;
import com.dcap.utils.UtilDB;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.yd.dcap.probe.client.TaskConfig;
import lombok.Builder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

@Builder
public class ElasticsearchDatasource extends ClassifierDataSource {
    private static final Logger LOG = LoggerFactory.getLogger(ElasticsearchDatasource.class);
    private Long tenantId;
    private String id;
    private String name;
    private String host;
    private String port;
    private String username;
    private String password;
    private String extraCfg;
    private String url;

    private ElasticsearchClient esClient;
    private static final DatasourceDriver DEFAULT_DRIVER = new DatasourceDriver() {
        @Override
        public String[] adjustTableTypes(String[] tableTypes, boolean excludeInternalTables, Connection connection) {
            return tableTypes;
        }

        @Override
        public String getExceptionDetails(String sql, SQLException sqlException) {
            return sqlException.getMessage();
        }
    };

    @Override
    public Long getTenantId() {
        return tenantId;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getDescription() {
        return "Elasticsearch DataSource";
    }



    @Override
    public String getAttributes() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getAttributes()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public String getDbName() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getDbName()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public String getInstanceName() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getInstanceName()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public String getConnectionDescriptor() {
        return this.url;
    }

    @Override
    public Connection connect() throws ConnectionException {
        return null;
    }

    @Override
    public DatasourceDriver getDriverInterface() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getDriverInterface()" + " @@@@@@@@@@@@@@@@@");
        return DEFAULT_DRIVER;
    }

    @Override
    public boolean isCatalog() {
        return true;
    }

    @Override
    public Set<String> getEmptyCatalogs() {
        return ImmutableSet.of("");
    }

    @Override
    public DataSourceType getType() {
        return DataSourceType.ELASTIC_SEARCH;
    }

    public static List<ClassifierDataSource> buildDatasource(Long tenantId, TaskConfig.DataSource dsConfig) {
        return Collections.singletonList(
                ElasticsearchDatasource.builder()
                .tenantId(tenantId)
                .id(dsConfig.getId())
                .name(dsConfig.getName())
                .host(dsConfig.getHost())
                .port(dsConfig.getPort())
                .extraCfg(dsConfig.getExtraCfg())
                .username(dsConfig.getAuthCfg().getUsername())
                .password(dsConfig.getAuthCfg().getPassword())
                .build()
        );
    }

    /**
     * 使用 elasticsearchClient 获取全部的索引名称
     *
     * @return
     */
    public List<String> getCatalogs() {
        List<String> catalogs = new ArrayList<>();
        ElasticsearchClient  elasticsearchClient = getElasticsearchClient();
        try {
            catalogs.add(elasticsearchClient.info().clusterName());
        } catch (IOException e) {
            e.printStackTrace();
        }
        return catalogs;
    }

    public List<String> getTables(String catalog) {
        Set<String> tables = new HashSet<>();
        ElasticsearchClient  elasticsearchClient = getElasticsearchClient();
        try {
            // 获取所有的索引
            GetIndexResponse getIndexResponse = elasticsearchClient.indices()
                    .get(new GetIndexRequest.Builder().index("_all").build());

            // 遍历所有的索引
            for (Map.Entry<String,IndexState> entry : getIndexResponse.result().entrySet()) {
                String indexName = entry.getKey();
                if (indexName.startsWith(".")) {
                    continue;
                }
                tables.add(indexName);
                // 获取索引的别名
                GetAliasResponse getAliasResponse = elasticsearchClient.indices()
                        .getAlias(new GetAliasRequest.Builder().index(indexName).build());
                Map<String, IndexAliases> result = getAliasResponse.result();
                for (Map.Entry<String, IndexAliases> indexAliasesEntry : result.entrySet()) {
                    IndexAliases indexAliases = indexAliasesEntry.getValue();
                    // 将别名添加到列表中
                    tables.addAll(indexAliases.aliases().keySet());
                }
            }
        } catch (Exception e){
            throw new RuntimeException(e);
        }
        return Lists.newArrayList(tables.iterator()).stream().sorted().collect(Collectors.toList());
    }

    public List<Map<String, Object>> getRows(String table,int rowLimit) {
        List<Map<String, Object>> rows = new ArrayList<>();
        ElasticsearchClient client = getElasticsearchClient();
        try {
            SearchResponse<Map> searchResponse = client.search(new SearchRequest.Builder().index(table).size(rowLimit).build(), Map.class);
            for (Map.Entry<String, JsonData> fieldEntry : searchResponse.fields().entrySet()) {
                System.out.println(fieldEntry.getKey());
                System.out.println(fieldEntry.getValue());
            }
            for (Hit<Map> hit : searchResponse.hits().hits()) {
                rows.add(hit.source());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return rows;
    }


    private ElasticsearchClient getElasticsearchClient() {
        // 请实现单例模式获取该连接
        // 使用单例模式获取连接
        if (esClient == null) {
            synchronized (this) {
                if (esClient == null) {
                    esClient = UtilDB.buildElasticsearchClient(this.host, Integer.parseInt(this.port),this.username,this.password);
                }
            }
        }
        return esClient;
    }

    @Override
    public void release(){
        if (this.esClient != null){
            this.esClient = null;
        }
    }

    @Override
    public boolean supportedQueryTimeout() {
        return false;
    }
}
