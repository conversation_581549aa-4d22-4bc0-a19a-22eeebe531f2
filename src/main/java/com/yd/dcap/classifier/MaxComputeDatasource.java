package com.yd.dcap.classifier;

import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.DataSourceType;
import com.dcap.datalayer.DatasourceDriver;
import com.google.common.collect.ImmutableSet;
import com.yd.dcap.probe.client.TaskConfig;
import lombok.Builder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.Properties;
import java.util.Set;

@Builder
public class MaxComputeDatasource extends ClassifierDataSource {

    private static final Logger LOG = LoggerFactory.getLogger(MaxComputeDatasource.class);

    private Long tenantId;
    private String id;
    private String name;
    private String host;
    private String port;
    private String username;
    private String password;
    private String dbName;

    private String instanceName;

    private static final DatasourceDriver DEFAULT_DRIVER = new DatasourceDriver() {
        @Override
        public String[] adjustTableTypes(String[] tableTypes, boolean excludeInternalTables, Connection connection) {
            return tableTypes;
        }

        @Override
        public String getExceptionDetails(String sql, SQLException sqlException) {
            return sql;
        }
    };
    @Override
    public Long getTenantId() {
        return tenantId;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getDescription() {
        return "Oracle DataSource";
    }

    @Override
    public String getAttributes() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getAttributes()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public String getDbName() {
        return null;
//        return username.toUpperCase();
    }

    @Override
    public String getInstanceName() {
//        LOG.info("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getInstanceName()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public String getConnectionDescriptor() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getConnectionDescriptor()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public Connection connect() throws ConnectionException {
        DriverManager.setLoginTimeout(30);

        Properties props = new Properties();
        // notesReporting
        String url = "jdbc:odps:http://"+host+":"+port+"/api?project=" + dbName;
        props.setProperty("user", username);
        props.setProperty("password", password);

        Connection connection;
        try {
            connection = DriverManager.getConnection(url,props);// dbDriver.connect(url, props);
            LOG.debug("success connect to oracle["+url+"]");
        } catch (SQLException e) {
            throw new ConnectionException("Can not connect to database: " + url, e);
        }
        return connection;
    }

    @Override
    public DatasourceDriver getDriverInterface() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getDriverInterface()" + " @@@@@@@@@@@@@@@@@");
        return DEFAULT_DRIVER;
    }

    @Override
    public boolean isCatalog() {
        return true;
    }

    @Override
    public DataSourceType getType() {
        return DataSourceType.MAXCOMPUTE;
    }

    @Override
    public Set<String> getEmptyCatalogs() {
        return ImmutableSet.of(
                "MAXCOMPUTE_PUBLIC_DATA"
        );
    }

    @Override
    public boolean supportedQueryTimeout() {
        return false;
    }

    public static List<ClassifierDataSource> buildDatasource(Long tenantId, TaskConfig.DataSource dsConfig) {
        if (StringUtils.isBlank(dsConfig.getExtraCfg())) {
            throw new RuntimeException("DB list is empty");
        }
        List<ClassifierDataSource> classifierDataSourceList = Collections.singletonList(MaxComputeDatasource.builder()
                        .tenantId(tenantId)
                        .id(dsConfig.getId())
                        .name(dsConfig.getName())
                        .host(dsConfig.getHost())
                        .port(dsConfig.getPort())
                        .dbName(dsConfig.getExtraCfg())
                        .username(dsConfig.getAuthCfg().getUsername())
                        .password(dsConfig.getAuthCfg().getPassword())
                        .build());
//        if (classifierDataSourceList.isEmpty()) {
//            throw new RuntimeException("DB list is empty");
//        }
        return classifierDataSourceList;
    }

}
