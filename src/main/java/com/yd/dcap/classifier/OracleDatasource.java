package com.yd.dcap.classifier;

import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.DataSourceType;
import com.dcap.datalayer.DatasourceDriver;
import com.google.common.collect.ImmutableSet;
import com.yd.dcap.probe.client.TaskConfig;
import lombok.Builder;
import lombok.Getter;
import oracle.jdbc.driver.OracleConnection;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.*;

@Builder
public class OracleDatasource extends ClassifierDataSource {

    private static final Logger LOG = LoggerFactory.getLogger(OracleDatasource.class);

    private Long tenantId;
    private String id;
    private String name;
    private String host;
    private String port;
    private String username;
    private String password;
    private String dbName;

    private String instanceName;
    @Getter
    private String fetchDbNameType;

    private static final DatasourceDriver DEFAULT_DRIVER = new DatasourceDriver() {
        @Override
        public String[] adjustTableTypes(String[] tableTypes, boolean excludeInternalTables, Connection connection) {
            return tableTypes;
        }

        @Override
        public String getExceptionDetails(String sql, SQLException sqlException) {
            return sql;
        }
    };

    @Override
    public Long getTenantId() {
        return tenantId;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getDescription() {
        return "Oracle DataSource";
    }

    @Override
    public String getAttributes() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getAttributes()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public String getDbName() {
        return null;
//        return username.toUpperCase();
    }

    @Override
    public String getInventoryDbName() {
        return dbName;
    }

    @Override
    public String getInstanceName() {
//        LOG.info("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getInstanceName()" + " @@@@@@@@@@@@@@@@@");
        return instanceName;
    }

    @Override
    public String getConnectionDescriptor() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getConnectionDescriptor()" + " @@@@@@@@@@@@@@@@@");
        return "jdbc:oracle:thin:@//" + host + ":" + port + "/" + dbName;
    }

    @Override
    public Connection connect() throws ConnectionException {
        if (StringUtils.isBlank(this.username) || StringUtils.isBlank(this.password)){
            throw new IllegalArgumentException("username ["+this.username+"] or password ["+password+"] is blank ");
        }
        DriverManager.setLoginTimeout(30);
//        String drvName = "oracle.jdbc.driver.OracleDriver";
//        Driver dbDriver;
//        try {
//            dbDriver = (Driver) Class.forName(drvName).newInstance();
//            DriverManager.registerDriver(dbDriver);
//        } catch (Exception e) {
//            throw new ConnectionException("Can not initialize the driver class: " + drvName, e);
//        }
        System.setProperty("oracle.net.CONNECT_TIMEOUT",String.valueOf(60000 * 60 * 30));
        System.setProperty("oracle.jdbc.ReadTimeout",String.valueOf(60000 * 60 * 30));
        System.setProperty("oracle.net.READ_TIMEOUT",String.valueOf(60000 * 60 * 30));
        Properties props = new Properties();
        // notesReporting
        String url = "jdbc:oracle:thin:@//" + host + ":" + port + "/" + dbName;
        props.setProperty("user", username);
        props.setProperty("password", password);
        props.setProperty("oracle.net.CONNECT_TIMEOUT",String.valueOf(60000 * 60 * 30));
        props.setProperty("oracle.jdbc.ReadTimeout", String.valueOf(60000 * 60 * 30));
        props.setProperty("oracle.net.READ_TIMEOUT", String.valueOf(60000 * 60 * 30));


        Connection connection;
        try {
            connection = DriverManager.getConnection(url,props);// dbDriver.connect(url, props);
            OracleConnection oraCon = (OracleConnection)connection;
            oraCon.setRemarksReporting(true);
            LOG.debug("success connect to oracle["+url+"]");
        } catch (SQLException e) {
            throw new ConnectionException("Can not connect to database: " + url, e);
        }

        return connection;
    }

    @Override
    public DatasourceDriver getDriverInterface() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getDriverInterface()" + " @@@@@@@@@@@@@@@@@");
        return DEFAULT_DRIVER;
    }

    @Override
    public boolean isCatalog() {
        return false;
    }

    @Override
    public DataSourceType getType() {
        return DataSourceType.ORACLE;
    }

    @Override
    public Set<String> getEmptyCatalogs() {
        return ImmutableSet.of(
                // For oracle 11g
                "anonymous",
                "apex_030200",
                "apex_050000",
                "apex_public_user",
                "appqossys",
                "bi",
                "ctxsys",
                "dbsnmp",
                "dip",
                "exfsys",
                "flows_files",
                "hr",
                "ix",
                "mddata",
                "mdsys",
                "mgmt_view",
                "oe",
                "olapsys",
                "oracle_ocm",
                "orddata",
                "ordplugins",
                "ordsys",
                "outln",
                "owbsys",
                "owbsys_audit",
                "pm",
                "sh",
                "si_informtn_schema",
                "spatial_csw_admin_usr",
                "spatial_wfs_admin_usr",
                "sys",
                "sysman",
                "system",
                "wmsys",
                "xdb",
                "xs$null",
                // For oracle 19c
                "audsys",
                "dbsfwuser",
                "dvf",
                "dvsys",
                "ggsys",
                "gsmadmin_internal",
                "gsmcatuser",
                "gsmrootuser",
                "gsmuser",
                "lbacsys",
                "ojvmsys",
                "remote_scheduler_agent",
                "sys$umf",
                "sysbackup",
                "sysdg",
                "syskm",
                "sysrac",
                // other
//                "sysadm",
                "PUBLIC"
        );
    }

    @Override
    public boolean supportedQueryTimeout() {
        return true;
    }

    public static List<ClassifierDataSource> buildDatasource(Long tenantId, TaskConfig.DataSource dsConfig) {
        if (StringUtils.isBlank(dsConfig.getExtraCfg())) {
            throw new RuntimeException("DB list is empty");
        }
        String dbNameType;
        int dbnameType = Integer.parseInt(Objects.toString(dsConfig.getDbNameType(),"2"));
        if (dbnameType == 1) {
            dbNameType = "DB_UNIQUE_NAME";
        } else if (dbnameType == 2){
            dbNameType = "INSTANCE_NAME";
        } else {
            dbNameType = "DB_NAME";
        }
        List<ClassifierDataSource> classifierDataSourceList = Collections.singletonList(OracleDatasource.builder()
                        .tenantId(tenantId)
                        .id(dsConfig.getId())
                        .name(dsConfig.getName())
                        .host(dsConfig.getHost())
                        .port(dsConfig.getPort())
                        .dbName(dsConfig.getExtraCfg())
                        .username(dsConfig.getAuthCfg().getUsername())
                        .password(dsConfig.getAuthCfg().getPassword())
                        .fetchDbNameType(dbNameType)
                        .build());
//        if (classifierDataSourceList.isEmpty()) {
//            throw new RuntimeException("DB list is empty");
//        }
        return classifierDataSourceList;
    }

}
