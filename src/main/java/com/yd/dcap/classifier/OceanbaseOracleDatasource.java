package com.yd.dcap.classifier;

import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.DataSourceType;
import com.dcap.datalayer.DatasourceDriver;
import com.dcap.utils.UtilDB;
import com.google.common.collect.ImmutableSet;
import com.yd.dcap.probe.client.TaskConfig;
import lombok.Builder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.Set;

@Builder
public class OceanbaseOracleDatasource extends ClassifierDataSource {

    private static final Logger LOG = LoggerFactory.getLogger(OceanbaseOracleDatasource.class);

    private Long tenantId;
    private String id;
    private String name;
    private String host;
    private String port;
    private String username;
    private String password;
    private String extraCfg;


    private static final DatasourceDriver DEFAULT_DRIVER = new DatasourceDriver() {
        @Override
        public String[] adjustTableTypes(String[] tableTypes, boolean excludeInternalTables, Connection connection) {
            return tableTypes;
        }

        @Override
        public String getExceptionDetails(String sql, SQLException sqlException) {
            return sql;
        }
    };

    @Override
    public Long getTenantId() {
        return tenantId;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getDescription() {
        return "Oceanbase Oracle DataSource";
    }

    @Override
    public String getAttributes() {
        return null;
    }

    @Override
    public String getDbName() {
        return null;
    }

    @Override
    public String getInstanceName() {
        return "";
    }


    @Override
    public String getConnectionDescriptor() {
        return "jdbc:oceanbase://" + host + ":" + port + "/" + extraCfg;
    }

    @Override
    public Connection connect() throws ConnectionException {
        if (StringUtils.isBlank(this.username) || StringUtils.isBlank(this.password)){
            throw new IllegalArgumentException("username ["+this.username+"] or password ["+password+"] is blank ");
        }
        DriverManager.setLoginTimeout(30);
        Connection connection;
        try {
            connection = UtilDB.buildDatabaseConnection("oceanbase_oracle", this.host, Integer.parseInt(this.port),
                    this.username, this.password, extraCfg);
            LOG.debug("success connect to oceanbase oracle[{}]", connection.getMetaData().getURL());
        } catch (Exception e) {
            throw new ConnectionException("Can not connect to database: "+ getConnectionDescriptor(), e);
        }

        return connection;
    }

    @Override
    public DatasourceDriver getDriverInterface() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getDriverInterface()" + " @@@@@@@@@@@@@@@@@");
        return DEFAULT_DRIVER;
    }

    @Override
    public boolean isCatalog() {
        return false;
    }

    @Override
    public DataSourceType getType() {
        return DataSourceType.OCEANBASE_ORACLE;
    }

    @Override
    public Set<String> getEmptyCatalogs() {
        return ImmutableSet.of(
            "SYS"
        );
    }

    @Override
    public boolean supportedQueryTimeout() {
        return false;
    }

    public static List<ClassifierDataSource> buildDatasource(Long tenantId, TaskConfig.DataSource dsConfig) {
        List<ClassifierDataSource> classifierDataSourceList = Collections.singletonList(OceanbaseOracleDatasource.builder()
                        .tenantId(tenantId)
                        .id(dsConfig.getId())
                        .name(dsConfig.getName())
                        .host(dsConfig.getHost())
                        .port(dsConfig.getPort())
                        .username(dsConfig.getAuthCfg().getUsername())
                        .password(dsConfig.getAuthCfg().getPassword())
                        .extraCfg(dsConfig.getExtraCfg())
                        .build());
        return classifierDataSourceList;
    }

}
