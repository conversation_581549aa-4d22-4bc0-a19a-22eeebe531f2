package com.yd.dcap.classifier;

import com.dcap.utils.HiveJDBCUtil;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.util.Base64;

public class InceptorDatasourceTest {

    public static void main(String[] args) throws SQLException, IOException {
        String url = "jdbc:transwarp2://************:10000/default";
        String userName = "admin";
        String authMethod = "KERBEROS";
        String krb5Text = FileUtils.readFileToString(new File("C:\\Users\\<USER>\\Desktop\\krb5.conf"), "UTF-8");
        String krb5conf = Base64.getEncoder().encodeToString(krb5Text.getBytes());
        String password = Base64.getEncoder().encodeToString(FileUtils.readFileToByteArray(new File("C:\\Users\\<USER>\\Desktop\\admin.keytab")));
        String servicePrincipal = "hive/node";
        Connection transwarpInceptorConnection = HiveJDBCUtil.getTranswarpInceptorArgoConnection(url, userName, password, authMethod, krb5conf, servicePrincipal);
        DatabaseMetaData metaData = transwarpInceptorConnection.getMetaData();

    }

}
