package com.yd.dcap.classifier;

import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.DataSourceType;
import com.dcap.datalayer.DatasourceDriver;
import com.dcap.utils.HiveJDBCUtil;
import com.google.common.collect.ImmutableSet;
import com.yd.dcap.probe.client.TaskConfig;
import lombok.Builder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.Set;

@Builder
public class InceptorDatasource extends ClassifierDataSource {

    private static final Logger LOG = LoggerFactory.getLogger(InceptorDatasource.class);

    private Long tenantId;
    private String id;
    private String name;
    private String host;
    private String port;
    private String username;
    private String password;
    private String extraUrl;
    private TaskConfig.AuthCfg authCfg;

    private static final DatasourceDriver DEFAULT_DRIVER = new DatasourceDriver() {
        @Override
        public String[] adjustTableTypes(String[] tableTypes, boolean excludeInternalTables, Connection connection) {
            return tableTypes;
        }

        @Override
        public String getExceptionDetails(String sql, SQLException sqlException) {
            return sql;
        }
    };

    @Override
    public Long getTenantId() {
        return tenantId;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getDescription() {
        return "transwarp_inceptor";
    }

    @Override
    public String getAttributes() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getAttributes()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public String getDbName() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getDbName()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public String getInstanceName() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getInstanceName()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public String getConnectionDescriptor() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getConnectionDescriptor()" + " @@@@@@@@@@@@@@@@@");
        return null;
    }

    @Override
    public Connection connect() throws ConnectionException {
        String extCfg = "?transaction.type=inceptor";
        StringBuilder url = new StringBuilder("jdbc:hive2://" + host + ":" + port);
        if (StringUtils.isNotBlank(this.extraUrl)){
            if (this.extraUrl.startsWith("?")){
                url.append(this.extraUrl);
            } else {
                if (this.extraUrl.startsWith("/")){
                    url.append(this.extraUrl);
                } else {
                    url.append("/").append(this.extraUrl);
                }
            }
        } else{
            url.append(extCfg);
        }

        Connection connection;
        try {
            connection = HiveJDBCUtil.getTranswarpInceptorConnection(url.toString(), this.authCfg.getUsername(),
                    this.authCfg.getPassword(), this.authCfg.getAuthMethod(), this.authCfg.getKrb5conf(), this.authCfg.getServicePrincipal());
            LOG.debug("success connect to inceptor [{}],user[{}]", url, username);
        } catch (Exception e) {
            throw new ConnectionException("Can not connect to database: " + url, e);
        }
        return connection;
    }

    @Override
    public DatasourceDriver getDriverInterface() {
        LOG.debug("@@@@@@@@@@@@@@@@@ " + "ClassifierDataSource.getDriverInterface()" + " @@@@@@@@@@@@@@@@@");
        return DEFAULT_DRIVER;
    }

    @Override
    public boolean isCatalog() {
        return false;
        // todo: return false because getting databases from schema
    }

    @Override
    public Set<String> getEmptyCatalogs() {
        return ImmutableSet.of("system");
    }

    @Override
    public boolean supportedQueryTimeout() {
        return false;
    }

    @Override
    public DataSourceType getType() {
        return DataSourceType.TRANSWARP_INCEPTOR;
    }

    public static List<ClassifierDataSource> buildDatasource(Long tenantId, TaskConfig.DataSource dsConfig) {
        return Collections.singletonList(
            InceptorDatasource.builder()
                .tenantId(tenantId)
                .id(dsConfig.getId())
                .name(dsConfig.getName())
                .host(dsConfig.getHost())
                .port(dsConfig.getPort())
                .extraUrl(dsConfig.getExtraCfg())
                .username(dsConfig.getAuthCfg().getUsername())
                .password(dsConfig.getAuthCfg().getPassword())
                .authCfg(dsConfig.getAuthCfg())
                .build()
        );
    }
}
