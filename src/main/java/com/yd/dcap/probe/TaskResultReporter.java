package com.yd.dcap.probe;

import com.dcap.classifier.writer.RecordTypeEnum;
import com.dcap.utils.JSON;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.yd.dcap.probe.entities.ProbeClientJobResult;
import com.yd.dcap.probe.utils.JsonUtils;
import com.yd.dspm.config.DspmMsgType;
import com.yd.dspm.model.conf.ProbeTaskJobStatus;

/**
 * 任务结果上报工具
 */
public interface TaskResultReporter {

    /**
     * 当前只用于数据库扫描任务的结果上报
     * @param scanJobHistoryId
     * @param status
     * @param message
     */
    default void updateDatabaseScanTaskStatus(long scanJobHistoryId, int status, String message) {
        // update task status
        ObjectNode node = JsonUtils.createObjectNode();
        node.put("id", scanJobHistoryId);
        node.put("status", status);
        node.put("message", message);
        sendMessageData((long) status, scanJobHistoryId, RecordTypeEnum.DB_SCAN_STATUS.name(), node.toString());
    }

    /**
     * 这个只用于使用 probe client 任务的机制，比如目前的连接测试和采样
     */
    default void updateProbeClientTaskStatus(ProbeClientJobResult jobResult) {
        try {
            String result = JSON.from(jobResult).toString();
            sendMessageData(Long.valueOf(jobResult.getTaskStatus()), jobResult.getExecId(), RecordTypeEnum.PROBE_CLIENT_STATUS.name(), result);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    default void updateDspmProbeClientJobResult(ProbeClientJobResult jobResult) {
        ProbeTaskJobStatus status = ProbeTaskJobStatus.getStatusByCode(jobResult.getExecStatus());
        Object taskResult = jobResult.getTaskResult();
        clientReportScanState(jobResult.getExecId(), DspmMsgType.SCAN_JOB_STATE, status, taskResult);
    }

    /**
     * 给 server 上报信息。
     * 针对 probe-server 来说,此处包括：
     * 1、心跳消息；
     * 2、任务执行进度状态上报。因此 result 对应的是 ProbeMessage.body
     */
    default void sendMessageData(Long status, Long jobId, String recordType, String result) {
        throw new RuntimeException("ProbeClient Must override sendMessageData method");
    }

    /**
     * 主动给server上报扫描进度结果等状态信息
     * 针对mq来说，此处只包括：任务执行进度状态上报，因此stateMessage对应的是TaskStateBody.msg
     */
    default void clientReportScanState(Long jobId, DspmMsgType msgType, ProbeTaskJobStatus status, Object taskResult) {
        throw new RuntimeException("Mq Client Must override clientReportScanState method");
    }

    /**
     * 被动给 server上报信息,因为 server下发了状态询问消息
     */
    default void clientReportAskedState(Long jobId, ProbeTaskJobStatus status) {
        throw new RuntimeException("Mq Client Must override clientReportAskedState method");
    }

    /**
     * 上报扫描结果数据记录
     * 扫描器应主动调用本方法输出数据结果
     */
    default void clientReportData(Long jobId, RecordTypeEnum recordType, String dataContent) {
        throw new RuntimeException("Mq Client Must override clientReportScanData method");
    }
}
