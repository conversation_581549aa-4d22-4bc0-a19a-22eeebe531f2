package com.yd.dcap.probe;

import com.dcap.utils.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.dcap.probe.client.ProbeClientProcessor;
import com.yd.dcap.probe.client.ProbeClientTaskUtil;
import com.yd.dcap.probe.client.TaskConfig;
import com.yd.dcap.probe.config.DcapConstants;
import com.yd.dcap.probe.entities.ProbeClientJobResult;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import com.yd.dcap.probe.utils.JsonUtils;
import com.yd.dspm.config.DspmMsgType;
import com.yd.dspm.model.conf.ProbeClientDspmJobHistory;
import com.yd.dspm.model.conf.ProbeTaskJobStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;

/**
 * 新任务处理器
 */
public class JobHandler {
    private final Logger logger = LoggerFactory.getLogger(JobHandler.class);

    private final TaskResultReporter resultReporter;
    /**
     * 是否支持某个dspm的job的判定器
     * 针对dcap的job不会生效
     */
    private final Predicate<ProbeClientDspmJobHistory> effectDspmJobPredicate;

    public JobHandler(TaskResultReporter resultReporter, Predicate<ProbeClientDspmJobHistory> effectDspmJobPredicate) {
        this.resultReporter = resultReporter;
        this.effectDspmJobPredicate = effectDspmJobPredicate;
    }

    //处理接收到的任务消息
    public void onNext(int msgType, String msgText) {
        if (DcapConstants.MSG_TYPE_SCAN_TASK == msgType) {
            // 扫描任务
            JsonNode node = JsonUtils.readJson(msgText);
            if (!node.hasNonNull("scanJobHistoryId")) {
                logger.warn("Receive not valid message data: {}", msgText);
            }
            try {
                ProbeClientTaskContext probeClientTaskContext = new ProbeClientTaskContext(
                        JSON.from(msgText).toObject(TaskConfig.class), resultReporter
                );
                ProbeClientProcessor probeClientProcessor = ProbeClientTaskUtil.getInstance().createTaskProcessor(probeClientTaskContext);
                probeClientProcessor.doWork(null);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else if (DcapConstants.MSG_TYPE_INTERRUPT_TASK == msgType) {
            JsonNode node = JsonUtils.readJson(msgText);
            if (!node.hasNonNull("scanJobHistoryId")) {
                logger.warn("Receive not valid message data: {}", msgText);
            }
            long scanJobHistoryId = node.get("scanJobHistoryId").asLong();
            long taskId = node.get("taskId").asLong();
            ProbeClientProcessor probeClientProcessor = ProbeClientTaskUtil.getInstance()
                    .fetchRunningTask("SCAN", taskId);
            if (probeClientProcessor != null) {
                probeClientProcessor.doInterrupt();
            } else {
                logger.warn("收到中断请求,taskType[{}],taskId[{}],scanJobHistoryId[{}] but the task is no running",
                        "SCAN", taskId, scanJobHistoryId);
                // 此时尝试恢复 server 并重置状态
                ProbeClientTaskContext.clean("SCAN", taskId, scanJobHistoryId);
                resultReporter.updateDatabaseScanTaskStatus(scanJobHistoryId, 9, "{\"errMsg\":\"Task is no running\"}");
            }
        } else if (DcapConstants.MSG_TYPE_PROBE_CLIENT_TASK_START == msgType) {
            try {
                Map<String,Object> probeClientTaskHistory = JSON.from(msgText).toObject(Map.class);
                ProbeClientTaskContext probeClientTaskContext = new ProbeClientTaskContext(probeClientTaskHistory, resultReporter);
                ProbeClientProcessor probeClientProcessor = ProbeClientTaskUtil.getInstance().createTaskProcessor(probeClientTaskContext);
                probeClientProcessor.doWork(resultReporter::updateProbeClientTaskStatus);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else if (DcapConstants.MSG_TYPE_PROBE_CLIENT_TASK_INTERRUPT == msgType) {
            try {
                Map<String,Object> probeClientTaskHistory = JSON.from(msgText).toObject(Map.class);
                ProbeClientTaskContext probeClientTaskContext = new ProbeClientTaskContext(probeClientTaskHistory, resultReporter);
                ProbeClientProcessor probeClientProcessor = ProbeClientTaskUtil.getInstance()
                        .fetchRunningTask(probeClientTaskContext.getTaskType(), probeClientTaskContext.getTaskId());
                if (probeClientProcessor != null) {
                    probeClientProcessor.doInterrupt();
                } else {
                    logger.warn("收到中断请求,taskType[{}],taskId[{}],scanJobHistoryId[{}] but the task is no running",
                            probeClientTaskContext.getTaskType(), probeClientTaskContext.getTaskId(), probeClientTaskContext.getExecId());
                    // 此时尝试恢复 server 并重置状态
                    ProbeClientTaskContext.clean(probeClientTaskContext.getTaskType(),
                            probeClientTaskContext.getTaskId(), probeClientTaskContext.getExecId());
                    probeClientTaskContext.changeToSuccessStatus();
                    resultReporter.updateProbeClientTaskStatus(
                            new ProbeClientJobResult(
                                    probeClientTaskContext.getExecId(), probeClientTaskContext.getTaskId(),
                                    probeClientTaskContext.getTaskType(), probeClientTaskContext.getTaskStatusDict().success(),
                                    probeClientTaskContext.getTaskStatusDict().success(), UtilMisc.toMap("result", "任务被中断"))
                    );
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else if (DspmMsgType.CLIENT_JOB_START.getCode() == msgType) {
            List<ProbeClientDspmJobHistory> probeClientTaskList = parseProbeClientDspmJobHistory(msgText, true);
            for (ProbeClientDspmJobHistory record : probeClientTaskList) {
                ProbeClientTaskContext probeClientTaskContext = new ProbeClientTaskContext(record, resultReporter);
                logger.info("receive a new dspm scan job.execId:{}", probeClientTaskContext.getExecId());
                ProbeClientProcessor probeClientProcessor = ProbeClientTaskUtil.getInstance()
                        .createTaskProcessor(probeClientTaskContext);
                probeClientProcessor.doWork(resultReporter::updateDspmProbeClientJobResult);
            }
        } else if (DspmMsgType.CLIENT_JOB_INTERRUPT.getCode() == msgType) {
            List<ProbeClientDspmJobHistory> probeClientTaskList = parseProbeClientDspmJobHistory(msgText, true);
            for (ProbeClientDspmJobHistory record : probeClientTaskList) {
                ProbeClientTaskContext probeClientTaskContext = new ProbeClientTaskContext(record, resultReporter);
                logger.info("receive a new dspm interrupt job.execId:{}", probeClientTaskContext.getExecId());
                ProbeClientProcessor probeClientProcessor = ProbeClientTaskUtil.getInstance()
                        .fetchRunningTask(probeClientTaskContext.getTaskType(), probeClientTaskContext.getTaskId());
                if (probeClientProcessor != null) {
                    probeClientProcessor.doInterrupt();
                } else {
                    //任务中断时，发现任务不在运行中，那么有两种可能性：1、任务还在排队还没运行；2、任务刚好那一瞬间执行完毕了
                    logger.warn("[DSPM]收到中断请求,taskType[{}],taskId[{}],scanJobHistoryId[{}] but the task is no running",
                            probeClientTaskContext.getTaskType(), probeClientTaskContext.getTaskId(), probeClientTaskContext.getExecId());
                    //在 mq 场景中: 有可能任务还在排队,但是中断消息先收到了
                    probeClientProcessor = ProbeClientTaskUtil.getInstance().createTaskProcessor(probeClientTaskContext);
                    ProbeClientTaskUtil.getInstance().recordWaitInterruptTask(probeClientTaskContext, probeClientProcessor);
                }
            }
        } else if (DspmMsgType.JOB_STATE.getCode() == msgType) {
            // 服务器主动询问 job 状态
            List<ProbeClientDspmJobHistory> probeClientTaskList = parseProbeClientDspmJobHistory(msgText, false);
            for (ProbeClientDspmJobHistory record : probeClientTaskList) {
                ProbeClientTaskContext probeClientTaskContext = new ProbeClientTaskContext(record, resultReporter);
                logger.info("receive a new state job.execId:{}", probeClientTaskContext.getExecId());
                processScanJobState(probeClientTaskContext);
            }
        } else if (DcapConstants.MSG_TYPE_RESULT == msgType) {
            logger.info("The dcap task {} update status was successful", msgText);
        } else {
            logger.error("Unsupported message type[{}]", msgType);
        }
    }

    /**
     * 反序列化任务
     *
     * @param msgText
     * @return
     */
    public List<ProbeClientDspmJobHistory> parseProbeClientDspmJobHistory(String msgText, boolean filterByPredicate) {
        List<Map<String, Object>> probeClientTaskList = null;
        List<ProbeClientDspmJobHistory> jobList = null;
        try {
            if (msgText.startsWith("[")) {//多条任务发送时是个数组,probe server
                probeClientTaskList = JSON.from(msgText).toObject(ArrayList.class);
            } else {//单条任务时,mq
                Map<String, Object> map = JSON.from(msgText).toObject(Map.class);
                probeClientTaskList = Arrays.asList(map);
            }
            jobList = new ArrayList<>(probeClientTaskList.size());
            for (Map<String, Object> record : probeClientTaskList) {
                ProbeClientDspmJobHistory job = JSON.from(record).toObject(ProbeClientDspmJobHistory.class);
                if (filterByPredicate) {
                    if (effectDspmJobPredicate.test(job)) {
                        jobList.add(job);
                    } else {
                        logger.info("ignore dspm job {} with taskId {} and taskType:{}", job.getId(), job.getTaskId(), job.getTaskType());
                    }
                } else {
                    jobList.add(job);
                }
            }
        } catch (Exception e) {
            logger.error("parseProbeClientDspmJobHistory msgText error", e);
            throw new RuntimeException(e);
        }
        return jobList;
    }

    /**
     * 获取任务状态的询问消息，反馈状态结果
     *
     * @param context
     */
    private void processScanJobState(ProbeClientTaskContext context) {
        long jobId = context.getExecId();
        ProbeTaskJobStatus status = null;

        if (ProbeClientTaskUtil.getInstance().checkTaskRunning(context)) {
            ProbeClientProcessor runningProcessor = ProbeClientTaskUtil.getInstance()
                    .fetchRunningTask(context.getTaskType(), context.getTaskId());
            ProbeClientTaskContext realContext = runningProcessor.getTaskContext();
            if (realContext.isScanning()) {
                status = ProbeTaskJobStatus.SCAN_RUNNING;
            } else {
                status = ProbeTaskJobStatus.WAIT_SCAN;
            }
        } else if (ProbeClientTaskUtil.getInstance().checkTaskWaitInterrupt(context)) {
            status = ProbeTaskJobStatus.SCAN_INTERRUPTING;
        } else {
            status = ProbeTaskJobStatus.NO_EXISTS;
        }
        logger.info("处理接收到的状态获取任务:{},反馈结果为:{}", jobId, status);
        resultReporter.clientReportAskedState(jobId, status);
    }
}
