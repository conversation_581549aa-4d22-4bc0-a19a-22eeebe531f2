package com.yd.dcap.probe.client;


import com.dcap.utils.JSON;
import com.yd.dcap.classifier.ClassifierService;
import com.yd.dcap.classifier.config.SpringContextUtil;
import com.yd.dcap.classifier.taskreport.ScanDbReport;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.dcap.probe.TaskResultReporter;
import com.yd.dcap.probe.entities.ProbeClientJobResult;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;

import java.util.Map;
import java.util.function.Consumer;

@TaskType("SCAN")
public class DatabaseScanTask extends AbstractProbeClientProcessor {

    public DatabaseScanTask(ProbeClientTaskContext probeClientTaskContext) {
        super(probeClientTaskContext);
        TaskResultReporter taskResultReporter = probeClientTaskContext.getTaskResultReporter();
        probeClientTaskContext.registerTaskReport(new ScanDbReport(taskResultReporter, probeClientTaskContext));
    }


    @Override
    protected Map<String, Object> doWork(long execId, long taskId, String taskType, long tenantId,
                                         Map<String, Object> taskParam, Consumer<ProbeClientJobResult> callback) throws Exception {
        ClassifierService classifierService = SpringContextUtil.getBean(ClassifierService.class);
        try {
            TaskConfig taskConfig = (TaskConfig) taskParam.get("taskConfig");
            Map<String, Object> taskReportMap = classifierService.scan("SCAN", taskConfig);
            if (probeClientTaskContext.getTaskResultReporter() == null){
                logger.warn("TaskResultReporter is null, no results could be reported for this task. " +
                        "TaskType: {}, TaskId: {}, ExecId: {}",
                        taskType, taskId, execId);
                return null;
            }
            probeClientTaskContext.getTaskResultReporter().updateDatabaseScanTaskStatus(probeClientTaskContext.getExecId(),
                    probeClientTaskContext.getTaskStatus(), JSON.from(taskReportMap).toString()
            );
        } catch (Exception e){
            String causeMessage = getCauseMessage(e);
            if (causeMessage != null && causeMessage.contains("Unable to parse URL")) {
                probeClientTaskContext.setResult(UtilMisc.toMap("errMsg", "是否指定了数据库？" + causeMessage));
            } else if (causeMessage != null && causeMessage.contains("doesn't exist")) {
                probeClientTaskContext.setResult(UtilMisc.toMap("errMsg", causeMessage + "; 数据库是否开启了大小写敏感？"));
            } else {
                probeClientTaskContext.setResult(UtilMisc.toMap("errMsg", causeMessage));
            }
            probeClientTaskContext.reportFailed(StatusRecord.Position.Scan, null, causeMessage)
                    .sendToServer();
            throw e;
        } finally {
            probeClientTaskContext.clean();
        }
        return null;
    }
}
