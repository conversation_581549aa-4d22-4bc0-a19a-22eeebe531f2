package com.dcap.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLClassLoader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.sql.Connection;
import java.sql.Driver;
import java.util.HashSet;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;

public final class SqlServerJDBCUtil {

    private static final Logger LOG = LoggerFactory.getLogger(SqlServerJDBCUtil.class);

    private static final String [] SQLSERVER_JDBC_6_LIBS = new String[]{
            "sqljdbc42.jar"
    };

    private final URLClassLoader jdbc6ClassLoader;

    private SqlServerJDBCUtil(URLClassLoader jdbc6ClassLoader) {
        this.jdbc6ClassLoader = jdbc6ClassLoader;
    }

    private static class HiveJDBCHolder {
        private static final SqlServerJDBCUtil JDBC_UTIL_INSTANCE = createJdbcUtilInstance();
    }

    private static SqlServerJDBCUtil createJdbcUtilInstance()  {
        try {
            URLClassLoader jdbc6ClassLoader = createClassLoader("jdbc6");
            return new SqlServerJDBCUtil(jdbc6ClassLoader);
        } catch (Exception e){
            LOG.error(e.getMessage(),e);
        }
        return null;
    }

    public static SqlServerJDBCUtil getInstance(){
        return HiveJDBCHolder.JDBC_UTIL_INSTANCE;
    }

    public static Connection getConnection(String url, String userName, String password){
        SqlServerJDBCUtil instance = SqlServerJDBCUtil.getInstance();
        if (instance == null){
            throw new RuntimeException("加载 sql server jdbc util 失败");
        }
        Connection connection;
        try {
            connection = instance.getJdbc6Connection(url, userName, password);
            if (connection != null){
                LOG.info("创建 sql server jdbc 6 连接成功。");
                return connection;
            }
        } catch (Exception e){
            LOG.error("创建 sql server jdbc 6 连接失败：", e);
        }

        throw new RuntimeException("创建 sql server jdbc 连接失败。");
    }

    private Connection getJdbc6Connection(String url, String userName, String password) throws Exception{
        return buildConnection(jdbc6ClassLoader, url, userName, password);
    }

    private Connection buildConnection(ClassLoader classLoader, String url,
                                           String userName, String password) throws Exception{
        Class<?> cls = classLoader.loadClass("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        // 实例化 driver对象
        Driver driver = (Driver) cls.newInstance();
        Properties info = new Properties();
        info.setProperty("user", userName);
        info.setProperty("password",password);

        try {
            // 获取连接对象
            return driver.connect(url, info);
        } catch (Exception e) {
            LOG.error("连接 hive 失败：{}", e.getMessage());
            return null;
        }
    }



    private static URLClassLoader createClassLoader(String version) throws IOException {
        String [] libs = null;
        if (Objects.equals(version,"jdbc6")){
            libs = SQLSERVER_JDBC_6_LIBS;
        } else {
            throw new IllegalArgumentException(version);
        }

        String prefix = null;
        if (version.equals("jdbc6")) {
            prefix = "mssqllibs/jdbc6/";
        }

        ClassLoader contextClassLoader = Thread.currentThread().getContextClassLoader();
        // 创建一个 set 集合用于存储驱动文件的 url，使得 url 不能重复
        Set<URL> set = new HashSet<>();
        Path tempDir = Files.createTempDirectory(version);
        for (String hiveLib : libs) {
            URL resource = contextClassLoader.getResource(prefix + hiveLib);
            // 将 Jar 文件解压到系统临时目录中
            File nestedJarFile = new File(tempDir.toFile(), hiveLib);
            try (InputStream inputStream = resource.openStream(); FileOutputStream outputStream = new FileOutputStream(nestedJarFile)) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }
            set.add(nestedJarFile.toURI().toURL());
        }

        URL[] urls = set.toArray(new URL[0]);
        //服务器上 获取当前线程 调用 上下文类加载器 亲测 可行
        return new URLClassLoader(urls, contextClassLoader);
    }
}
