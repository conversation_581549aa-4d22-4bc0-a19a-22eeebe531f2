package com.dcap.utils;

import java.util.MissingResourceException;

public class Messages {
    private static final String bundleName = Messages.class.getPackage().getName() + ".ClassifierResources";

    public Messages() {
    }

//    public static String getString(String key) {
//        try {
//            return find(key);
//        } catch (MissingResourceException missingResourceException) {
//            return key;
//        }
//    }

//    private static String find(String key) throws MissingResourceException {
//        if (bundle != null) {
//            return bundle.getString(key);
//        } else {
//            throw bundleException;
//        }
//    }

    public static String getString(String message, String... variables) {
        try {
            return replace(message, variables);
        } catch (MissingResourceException missingResourceException) {
            return message;
        }
    }

    private static String replace(String text, String... variables) {
        if (variables.length % 2 == 1) {
            throw new IllegalArgumentException("You must pass an even sized array to the replace method (size = " + variables.length + ")");
        }
        StringBuilder result = new StringBuilder(text);
        int var3 = 0;

        while (true) {
            var3 = result.indexOf("${", var3);
            if (var3 < 0) {
                break;
            }

            int var4 = var3 + 2;
            int var5 = result.indexOf("}", var4);
            if (var5 < 0) {
                break;
            }

            if (var5 > var4) {
                String var6 = result.substring(var4, var5);
                for (int var7 = 0; var7 < variables.length - 1; var7 += 2) {
                    String var8 = variables[var7];
                    if (var6.equals(var8)) {
                        String var9 = variables[var7 + 1];
                        if (var9 != null) {
                            result.replace(var3, var5 + 1, var9);
                            var5 = var3 + var9.length();
                        }
                        break;
                    }
                }
            }

            var3 = var5 + 1;
        }

        text = result.toString();

        return text;
    }
}
