package com.dcap.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.io.*;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class Utils {
   private static String pId = null;
   private static final String CASE_INSENSITIVE = "1";
   private static final String EXCLUDED_CHARACTERS = "[\\\\$|&;'\"()!]";
   private static final String EXCLUDED_DIR_CHARACTERS = "[$|&;'\"()! ]";
   public static final String XSS_SQL_EXCLUDED_CHARACTERS = "[<>;'\"]";
   private static final String GUARD_CONTROL_STUB = "guard-control_stub";
   public static final Pattern nameSpecialCharacters;
   public static final Pattern DatetimePeriodSpecialCharacters;
   private static final SimpleDateFormat sortableDateFormat;
   private static final SimpleDateFormat filenameDateFormat;
   public static final SimpleDateFormat screenDataFormat;
   public static final SimpleDateFormat screenDataFormatSimple;
   public static final SimpleDateFormat simpleDateFormat;
   public static final SimpleDateFormat numDateFormat;
   static Long pre;

   public Utils() {
   }

   public static String formatFilenameFriendlyDateString(Date var0) {
      return filenameDateFormat.format(var0);
   }

   public static String formatSortFriendlyDateString(Date var0) {
      return sortableDateFormat.format(var0);
   }

   public static Date parseFilenameFriendlyDateString(String var0) throws ParseException {
      return filenameDateFormat.parse(var0);
   }

   public static Date parseSortFriendlyDateString(String var0) throws ParseException {
      return sortableDateFormat.parse(var0);
   }

//   public static void encryptAndSaveRemote(String var0, String var1, String var2, String var3) throws Exception {
//      Connection var4 = null;
//      Statement var5 = null;
//      var0 = escapeSQL(var0);
//      var3 = var3 == null ? "" : var3;
//      if (!var3.equals("")) {
//         var3 = " WHERE " + var3;
//      }
//
//      if (!Check.isEmpty(var0) && !Check.isEmpty(var1) && !Check.isEmpty(var2)) {
//         String var6 = "UPDATE " + var2 + " SET " + var1 + " = AES_ENCRYPT('" + var0 + "','" + GlobalProperties.getGlobalProperties().getSharedSecret() + "') " + var3;
//
//         try {
//            var4 = Torque.getConnection("guard_remote");
//            var5 = var4.createStatement();
//            int var7 = var5.executeUpdate(var6);
//            if (var7 == 0) {
//               throw new Exception("encryptAndSave nothing updated on statement '" + var6 + "'");
//            }
//         } catch (Exception var11) {
//            var11.printStackTrace();
//            throw new Exception("error execute encryptAndSave");
//         } finally {
//            if (var5 != null) {
//               Check.disposal(var5);
//            }
//
//            if (var4 != null) {
//               Check.disposal(var4);
//            }
//
//         }
//
//      } else {
//         throw new Exception("Unable to encrypt and save  value: " + var0 + " column: " + var1 + " table: " + var2);
//      }
//   }

//   public static void encryptAndSave(String var0, String var1, String var2, String var3) throws Exception {
//      Connection var4 = null;
//      Statement var5 = null;
//      var3 = var3 == null ? "" : var3;
//      var0 = escapeSQL(var0);
//      if (!var3.equals("")) {
//         var3 = " WHERE " + var3;
//      }
//
//      if (!Check.isEmpty(var0) && !Check.isEmpty(var1) && !Check.isEmpty(var2)) {
//         String var6 = "UPDATE " + var2 + " SET " + var1 + " = AES_ENCRYPT('" + var0 + "','" + GlobalProperties.getGlobalProperties().getSharedSecret() + "') " + var3;
//
//         try {
//            var4 = Torque.getConnection("guard_local");
//            var5 = var4.createStatement();
//            int var7 = var5.executeUpdate(var6);
//            if (var7 == 0) {
//               throw new Exception("encryptAndSave nothing updated on statement '" + var6 + "'");
//            }
//         } finally {
//            if (var5 != null) {
//               Check.disposal(var5);
//            }
//
//            if (var4 != null) {
//               Check.disposal(var4);
//            }
//
//         }
//
//      } else {
//         throw new Exception("Unable to encrypt and save  value: " + var0 + " column: " + var1 + " table: " + var2);
//      }
//   }

   public static boolean jRegExSimpleCheck(String var0, String var1, String var2) throws Exception {
      boolean var3 = true;
      boolean var4 = false;
      int var5 = 32;
      if (var2 != null && "1".equals(var2)) {
         var3 = false;
         var5 |= 2;
      }

      Pattern var6 = Pattern.compile(var0, var5);
      Matcher var7 = var6.matcher(var1);
      var4 = var7.find();
      return var4;
   }

//   public static String fetchAndDecrypt(String var0, String var1, String var2) throws Exception {
//      String var3 = "";
//      Connection var4 = null;
//      Statement var5 = null;
//      ResultSet var6 = null;
//      var2 = var2 == null ? "" : var2;
//      if (!var2.equals("")) {
//         var2 = " WHERE " + var2 + " and " + var0 + " <> ''";
//      } else {
//         var2 = " WHERE " + var0 + " <> ''";
//      }
//
//      if (var0 != null && !var0.equals("") && var1 != null && !var1.equals("")) {
//         String var7 = "SELECT AES_DECRYPT(" + var0 + ",'";
//         var7 = var7 + GlobalProperties.getGlobalProperties().getSharedSecret() + "') FROM " + var1 + " " + var2;
//
//         try {
//            var4 = Torque.getConnection("guard_local");
//            var5 = var4.createStatement();
//            var6 = var5.executeQuery(var7);
//            if (var6.next()) {
//               var3 = var6.getString(1);
//            } else {
//               var3 = "";
//            }
//         } catch (Exception var12) {
//            throw new Exception("error execute fetchDecrypted");
//         } finally {
//            if (var5 != null) {
//               Check.disposal(var5);
//            }
//
//            if (var4 != null) {
//               Check.disposal(var4);
//            }
//
//         }
//      }
//
//      return var3;
//   }

//   public static String fetchAndDecryptRemote(String var0, String var1, String var2) throws Exception {
//      String var3 = "";
//      Connection var4 = null;
//      Statement var5 = null;
//      ResultSet var6 = null;
//      var2 = var2 == null ? "" : var2;
//      if (!var2.equals("")) {
//         var2 = " WHERE " + var2;
//      }
//
//      if (var0 != null && !var0.equals("") && var1 != null && !var1.equals("")) {
//         String var7 = "SELECT AES_DECRYPT(" + var0 + ",'";
//         var7 = var7 + GlobalProperties.getGlobalProperties().getSharedSecret() + "') FROM " + var1 + " " + var2;
//
//         try {
//            var4 = Torque.getConnection("guard_remote");
//            var5 = var4.createStatement();
//            var6 = var5.executeQuery(var7);
//            var6.next();
//            var3 = var6.getString(1);
//         } finally {
//            if (var5 != null) {
//               Check.disposal(var5);
//            }
//
//            if (var4 != null) {
//               Check.disposal(var4);
//            }
//
//         }
//      }
//
//      return var3;
//   }
/*
   public static String decrypt(String var0) throws Exception {
      String var1 = null;
      if (!Check.isEmpty(var0)) {
         AdHocLogger.logDebug("Start decryption", 9);
         byte[] var2 = Base64.decode(var0);
         var1 = new String(var2, "UTF8");
      }

      return var1;
   }

   public static String encrypt(String var0) throws Exception {
      String var1 = "";
      if (!Check.isEmpty(var0)) {
         byte[] var2 = var0.getBytes("UTF8");
         var1 = Base64.encode(var2);
      }

      return var1;
   }
*/
//   public static IPAddress resolve(Host var0) {
//      IPAddress var1 = var0.resolve();
//      return var1;
//   }

//   public static String convertHostToIP(Host var0) {
//      IPAddress var1 = var0.resolve();
//      return var1 == null ? "" : var1.toNormalizedString();
//   }

//   public static String convertHostToIP(String var0) {
//      return var0 != null && var0.length() != 0 ? convertHostToIP(new Host(var0)) : "";
//   }

//   public static boolean isValidHost(String var0) {
//      return (new Host(var0)).isValid();
//   }

//   public static IPAddressString getIpAddress(String var0) {
//      return new IPAddressString(var0);
//   }

//   public static boolean isValidIpAddress(String var0) {
//      IPAddressString var1 = getIpAddress(var0);
//      return var1.isValidAddress();
//   }

//   public static String formatDate(Date var0, String var1) {
//      String var2 = new String();
//      if (var0 != null) {
//         SimpleDateFormat var3 = new SimpleDateFormat(var1);
//         var2 = var3.format(var0);
//      }
//
//      return var2;
//   }

//   public static Date toDate(String var0, String var1) throws ParseException {
//      Date var2 = null;
//      if (Check.isEmpty(var0)) {
//         return null;
//      } else {
//         SimpleDateFormat var3 = new SimpleDateFormat(var1);
//         var2 = var3.parse(var0);
//         return var2;
//      }
//   }

   public static String convertStrListToCommaSeparatedStr(List var0) {
      String var1 = new String();
      if (var0 != null) {
         for(int var2 = 0; var2 < var0.size(); ++var2) {
            String var3 = (String)var0.get(var2);
            if (var2 > 0) {
               var1 = var1 + ",";
            }

            var1 = var1 + var3;
         }
      }

      return var1;
   }

//   public static String converttIntegerListToCommaSeparatedStr(List<Integer> var0) {
//      String var1 = new String();
//      if (!Check.isEmpty((Collection)var0)) {
//         for(int var2 = 0; var2 < var0.size(); ++var2) {
//            Integer var3 = (Integer)var0.get(var2);
//            if (var2 > 0) {
//               var1 = var1 + ",";
//            }
//
//            var1 = var1 + var3;
//         }
//      }
//
//      return var1;
//   }

   public static String convertStrListToStr(List var0, String var1) {
      String var2 = new String();
      if (var0 != null) {
         for(int var3 = 0; var3 < var0.size(); ++var3) {
            String var4 = (String)var0.get(var3);
            if (var3 > 0) {
               var2 = var2 + var1;
            }

            var2 = var2 + var4;
         }
      }

      return var2;
   }


   public static String handleBackslash(String var0) {
      String var1 = new String();
      if (var0 != null && var0.length() > 0) {
         StringTokenizer var2 = new StringTokenizer(var0, "\\");

         while(var2.hasMoreTokens()) {
            String var3 = var2.nextToken();
            if (var3 != null) {
               if (var1.length() > 0) {
                  var1 = var1 + "\\\\";
               }

               var1 = var1 + var3;
            }
         }

         if (var1.length() == 0) {
            var1 = var0;
         }
      }

      return var1;
   }

   public static int getScreenInputSize(int var0) {
      return var0 > 0 ? (int)(Math.log((double)var0) / Math.log(10.0D)) + 1 : 1;
   }


   public static int executeUpdateSqlIgnoreErr(Connection var0, String var1, int var2) throws SQLException {
      Statement var3 = null;

      byte var5;
      try {
         try {
            var3 = var0.createStatement();
            var3.executeUpdate(var1);
            int var4 = var3.getUpdateCount();
            int var17 = var4;
            return var17;
         } catch (SQLException var15) {
            if (var15.getErrorCode() != var2) {
               throw var15;
            }
         }

         var5 = 0;
      } finally {
         if (var3 != null) {
            try {
               var3.close();
            } catch (SQLException var14) {
            }
         }

      }

      return var5;
   }

   public static int executeUpdateSql(Connection var0, String var1) throws SQLException {
      Statement var2 = null;

      int var5;
      try {
         var2 = var0.createStatement();
         StringBuffer var3 = new StringBuffer();
         var3.append(var1);
         var2.executeUpdate(var3.toString());
         int var4 = var2.getUpdateCount();
         var5 = var4;
      } catch (SQLException var14) {
         throw var14;
      } finally {
         if (var2 != null) {
            try {
               var2.close();
            } catch (SQLException var13) {
            }
         }

      }

      return var5;
   }

//   public static Object executeSqlForSingleVal(String var0, int var1, Connection var2) throws SQLException {
//      Object var3 = null;
//      Statement var4 = null;
//      ResultSet var5 = null;
//
//      Object var6;
//      try {
//         var4 = var2.createStatement();
//         var5 = var4.executeQuery(var0);
//         if (var5.next()) {
//            var3 = var5.getObject(var1);
//         }
//
//         var6 = var3;
//      } finally {
//         Check.disposal(var5);
//         Check.disposal(var4);
//      }
//
//      return var6;
//   }

   public static Long getNumber(Object var0) {
      return var0 != null ? Long.parseLong(var0.toString().trim()) : null;
   }

//   public static Long executeSqlForNumber(String var0, Connection var1) throws DataSourceConnectException, SQLException {
//      Object var2 = executeSqlForSingleVal(var0, 1, var1);
//      return getNumber(var2);
//   }

//   public static ResultSet executeSqlForResultSet(String var0, Connection var1) throws DataSourceConnectException, SQLException {
//      Statement var2 = null;
//      ResultSet var3 = null;
//      CachedRowSetImpl var4 = null;
//
//      CachedRowSetImpl var5;
//      try {
//         var2 = var1.createStatement();
//         var3 = var2.executeQuery(var0);
//         var4 = new CachedRowSetImpl();
//         if (var1.getMetaData().getDatabaseProductName().equalsIgnoreCase("oracle")) {
//            var4.populate(new OracleResultSetWrapper(var3));
//         } else {
//            var4.populate(var3);
//         }
//
//         var5 = var4;
//      } finally {
//         Check.disposal(var3);
//         Check.disposal(var2);
//      }
//
//      return var5;
//   }

   public static Vector<String> execute(String[] var0) throws IOException, InterruptedException {
      Vector var1 = new Vector();
      Process var3 = Runtime.getRuntime().exec(var0);
      BufferedReader var4 = new BufferedReader(new InputStreamReader(var3.getInputStream()));

      String var2;
      while((var2 = var4.readLine()) != null) {
         var1.add(var2.trim());
      }

      var3.waitFor();
      return var1;
   }

//   public static String executeCommand(String var0) {
//      try {
//         StringBuffer var1 = new StringBuffer();
//         StringBuffer var2 = new StringBuffer();
//         execCommand((String)var0, (String[])null, (File)null, var2, var1, (StringBuffer)null);
//         return var1.length() != 0 ? var1.toString() : var2.toString();
//      } catch (IOException var3) {
//         AdHocLogger.logException(var3);
//         return var3.getMessage();
//      } catch (InterruptedException var4) {
//         AdHocLogger.logException(var4);
//         return var4.getMessage();
//      }
//   }

//   public static String executeCommand(String[] var0) {
//      try {
//         StringBuffer var1 = new StringBuffer();
//         int var2 = execCommand((String[])var0, (String[])null, (File)null, (StringBuffer)null, var1, (StringBuffer)null);
//         if (var1.length() > 0) {
//            return var1.toString();
//         } else {
//            return var2 != 0 ? "exit value=" + var2 : null;
//         }
//      } catch (IOException var3) {
//         AdHocLogger.logException(var3);
//         return var3.getMessage();
//      } catch (InterruptedException var4) {
//         AdHocLogger.logException(var4);
//         return var4.getMessage();
//      }
//   }

//   public static int execCommand(String var0, String[] var1, File var2, StringBuffer var3, StringBuffer var4, StringBuffer var5) throws IOException, InterruptedException {
//      return execCommand(new String[]{var0}, var1, var2, var3, var4, var5);
//   }
//
//   public static int execCommand(String[] var0, String[] var1, File var2, StringBuffer var3, StringBuffer var4, StringBuffer var5) throws IOException, InterruptedException {
//      LimitedAppendable var6 = var4 == null ? null : new LimitedAppendable(var4, 5000);
//      LimitedAppendable var7 = var3 == null ? null : new LimitedAppendable(var3, 5000);
//
//      int var9;
//      try {
//         CommandExecutor var8 = var0.length == 1 ? new CommandExecutor(var0[0]) : new CommandExecutor(var0);
//         var8.setEnvironment(var1);
//         var8.setWorkingDir(var2);
//         var9 = var8.exec(var7, var6, var5);
//      } finally {
//         if (var6 != null) {
//            var6.dump();
//         }
//
//         if (var7 != null) {
//            var7.dump();
//         }
//
//      }
//
//      return var9;
//   }

   public static String getDiplayErrorMsg(Throwable var0) {
      String var1 = var0.getMessage();
      if (var1 == null) {
         return null;
      } else {
         var1 = var1.replaceAll("\"", "\\\\\"");
         var1 = var1.replaceAll("\\n", "\\\\n");
         return var1;
      }
   }

   public static String getDiplayErrorMsg(String var0) {
      if (var0 == null) {
         return null;
      } else {
         var0 = var0.replaceAll("\"", "\\\\\"");
         var0 = var0.replaceAll("\\n", "\\\\n");
         return var0;
      }
   }

   public static String getDisplayMsg(Object var0) {
      if (var0 == null) {
         return "null";
      } else {
         StringBuffer var1 = new StringBuffer();
         if (var0 instanceof String[]) {
            String[] var2 = (String[])((String[])var0);
            if (var2 != null && var2.length > 0) {
               for(int var3 = 0; var3 < var2.length; ++var3) {
                  var1.append(var0).append(',');
               }
            }
         } else if (var0 instanceof List) {
            List var5 = (List)var0;
            Iterator var6 = var5.iterator();

            while(var6.hasNext()) {
               Object var4 = var6.next();
               var1.append(var4).append(',');
            }
         }

         if (var1.length() > 0) {
            var1.deleteCharAt(var1.length() - 1);
         }

         return var1.toString();
      }
   }

//   public static String getColumnsAsString(List<String> var0, String var1) {
//      String var2 = new String();
//      if (Check.isEmpty((Collection)var0)) {
//         return var2;
//      } else {
//         String var4;
//         for(Iterator var3 = var0.iterator(); var3.hasNext(); var2 = var2 + var4) {
//            var4 = (String)var3.next();
//            if (var2.length() > 0) {
//               var2 = var2 + ",";
//            }
//
//            if (!Check.isEmpty(var1)) {
//               var2 = var2 + var1 + ".";
//            }
//         }
//
//         return var2;
//      }
//   }

   public static boolean compareString(String var0, String var1) {
      if (var0 != null && var1 != null) {
         if (!var0.equals(var1)) {
            return false;
         }
      } else if (var0 != var1) {
         return false;
      }

      return true;
   }

   public static boolean isSame(Object var0, Object var1) {
      if (var0 != null && var1 != null) {
         if (!var0.equals(var1)) {
            return false;
         }
      } else if (var0 != var1) {
         return false;
      }

      return true;
   }

   public static boolean inList(Object var0, Object[] var1) {
      if (var1 == null) {
         return false;
      } else {
         for(int var2 = 0; var2 < var1.length; ++var2) {
            if (isSame(var0, var1[var2])) {
               return true;
            }
         }

         return false;
      }
   }

   public static String getSubStringAfterLastChar(String var0, char var1) {
      if (var0 == null) {
         return null;
      } else {
         int var2 = var0.lastIndexOf(var1);
         if (var2 > -1) {
         }

         return var0.substring(var2 + 1);
      }
   }

   public static HashMap getCharactersetMap() {
      HashMap var0 = new HashMap();
      var0.put("819", "ISO_8859_1");
      var0.put("912", "ISO_8859_2");
      var0.put("57346", "ISO_8859_3");
      var0.put("57347", "ISO_ISO_8859_4");
      var0.put("915", "ISO_8859_5");
      var0.put("1089", "ISO_8859_6");
      var0.put("813", "ISO_8859_7");
      var0.put("916", "ISO_8859_8");
      var0.put("920", "ISO_8859_9");
      var0.put("364", "US-ASCII");
      var0.put("932", "SJIS");
      var0.put("57350", "SJIS");
      var0.put("57372", "UTF-8");
      var0.put("57352", "Big5");
      var0.put("1250", "Cp1250");
      var0.put("1251", "Cp1251");
      var0.put("1252", "Cp1252");
      var0.put("1253", "Cp1253");
      var0.put("1254", "Cp1254");
      var0.put("1255", "Cp1255");
      var0.put("1256", "Cp1256");
      var0.put("1257", "Cp1257");
      var0.put("57356", "Cp949");
      var0.put("57356", "Cp949");
      var0.put("57356", "Cp949");
      var0.put("57351", "EUC_JP");
      var0.put("57357", "ISO2022CN_GB");
      return var0;
   }

   public static String convertCharacterset(String var0, String var1, String var2) {
      String var3 = new String();
      if (var0 != null && var0.length() > 0) {
         var3 = var0;
         if (var1 != null && var2 != null && var1.length() > 0 && var2.length() > 0 && !var1.equalsIgnoreCase(var2)) {
            try {
               byte[] var4 = new byte[256];
               ByteArrayOutputStream var5 = new ByteArrayOutputStream();
               OutputStreamWriter var6 = new OutputStreamWriter(var5, var1);
               var6.write(var0);
               var6.close();
               var4 = var5.toByteArray();
               ByteArrayInputStream var7 = new ByteArrayInputStream(var4);
               InputStreamReader var8 = new InputStreamReader(var7, var2);
               char[] var9 = new char[101];
               var8.read(var9);
               var3 = new String(var9);
               var8.close();
            } catch (FileNotFoundException var10) {
//               AdHocLogger.logException(var10);
            } catch (UnsupportedEncodingException var11) {
//               AdHocLogger.logException(var11);
            } catch (IOException var12) {
//               AdHocLogger.logException(var12);
            }
         }
      }

      return var3;
   }

   public static String bytesToString(byte[] var0) {
      String var1 = null;
      if (var0 != null) {
         var1 = "";

         for(int var2 = 0; var2 < var0.length; ++var2) {
            var1 = var1 + (char)var0[var2];
         }
      }

      return var1;
   }

   public static String arrayToString(List var0) {
      String var1 = "";
      if (var0 == null) {
         return var1;
      } else {
         for(int var2 = 0; var2 < var0.size(); ++var2) {
            if (var1.length() > 0) {
               var1 = var1 + ",";
            }

            var1 = var1 + var0.get(var2).toString();
         }

         return var1;
      }
   }

   public static List cloneArray(List var0) {
      if (var0 == null) {
         return null;
      } else {
         ArrayList var1 = new ArrayList();
         if (var0 != null) {
            for(int var2 = 0; var2 < var0.size(); ++var2) {
               var1.add(var0.get(var2));
            }
         }

         return var1;
      }
   }

//   public static String prepareForDbUsage(String var0) {
//      if (Check.isEmpty(var0)) {
//         return var0;
//      } else {
//         var0 = var0.replace('*', '%');
//         var0 = var0.replaceAll("\\\\", "\\\\\\\\");
//         return var0;
//      }
//   }

   public static String bytesToHexaString(byte[] var0) {
      StringBuffer var1 = new StringBuffer(var0.length * 2);

      for(int var2 = 0; var2 < var0.length; ++var2) {
         int var3 = var0[var2];
         if (var3 < 0) {
            var3 += 256;
         }

         if (var3 < 16) {
            var1.append('0');
         }

         var1.append(Integer.toHexString(var3));
      }

      return var1.toString();
   }

   public static String getUniqueNameByUsingMessageDigest(String var0) {
      String var1 = new String();
      if (var0 != null && var0.length() > 0) {
         try {
            MessageDigest var2 = MessageDigest.getInstance("MD5");
            byte[] var3 = var2.digest(var0.getBytes());
            var1 = bytesToHexaString(var3);
         } catch (NoSuchAlgorithmException var6) {
            log.error(var6.getMessage());
         }
      }

      return var1;
   }

   public static String getStackTraceAsString(Throwable var0) {
      ByteArrayOutputStream var1 = new ByteArrayOutputStream();
      PrintWriter var2 = new PrintWriter(var1, true);
      var0.printStackTrace(var2);
      return var1.toString();
   }

//   public static boolean verifyNoSpecialChar(String var0) {
//      return !Regexer.matchRegex(var0, "[\\\\$|&;'\"()!]");
//   }
//
//   public static boolean verifyDirNoSpecialChar(String var0) {
//      return !Regexer.matchRegex(var0, "[$|&;'\"()! ]");
//   }
//
//   public static boolean verifyNameSpecialChar(String var0) {
//      return var0 != null && nameSpecialCharacters.matcher(var0).find();
//   }

   public static Date getCurrentTime() {
      return Calendar.getInstance().getTime();
   }


   private static String replaceBackSlashes(String var0) {
      StringBuffer var1 = new StringBuffer(var0);
      String var2 = "\\\\\\";
      if (var1.length() > 0) {
         int var3 = 0;

         while(true) {
            int var4 = var1.indexOf("\\", var3);
            if (var4 == -1) {
               break;
            }

            var1.replace(var4, var4 + 1, var2);
            var3 = var4 + var2.length() + 1;
         }
      }

      return var1.toString();
   }

   public static synchronized String getUniqueId() throws Exception {
      long var0 = System.currentTimeMillis();
      int var2 = 0;

      while(var0 == pre && var2 < 3) {
         try {
            ++var2;
            Thread.sleep(1L);
         } catch (InterruptedException var4) {
//            AdHocLogger.logException(var4);
         }
      }

      if (pre != var0) {
         pre = var0;
         return Long.toString(var0);
      } else {
         throw new Exception("This should never happen: cannot get unique id");
      }
   }

   public static String escapeSpecialChar(String var0) {
      String var1 = "\\$|&;`'\"()!";
      StringBuilder var2 = new StringBuilder("\\ ");

      for(int var3 = 0; var3 < var1.length(); ++var3) {
         var2.setCharAt(1, var1.charAt(var3));
         var0 = var0.replace(var1.subSequence(var3, var3 + 1), var2);
      }

      return var0;
   }

   public static Map<String, String> sortOnValue(Map<String, String> var0) {
      TreeMap var1 = new TreeMap(String.CASE_INSENSITIVE_ORDER);
      Iterator var2 = var0.entrySet().iterator();

      while(var2.hasNext()) {
         Entry var3 = (Entry)var2.next();
         if (var1.containsKey(var3.getValue())) {
            throw new RuntimeException("Value is not unqiue: " + (String)var3.getValue());
         }

         var1.put(var3.getValue(), var3.getKey());
      }

      LinkedHashMap var5 = new LinkedHashMap();
      Iterator var6 = var1.entrySet().iterator();

      while(var6.hasNext()) {
         Entry var4 = (Entry)var6.next();
         var5.put(var4.getValue(), var4.getKey());
      }

      return var5;
   }

//   public static int getCharCountInText(char var0, String var1) {
//      byte var2 = 0;
//      if (Check.isEmpty(var1)) {
//         return var2;
//      } else {
//         String var3 = "[^" + var0 + "]";
//         int var4 = var1.replaceAll(var3, "").length();
//         return var4;
//      }
//   }

//   public static String[][] getMetaData(String var0) throws IOException, GuardGeneralException {
//      return getMetaData(var0, 15);
//   }

//   public static String[][] getMetaData(String var0, int var1) throws IOException, GuardGeneralException {
//      String var2 = GlobalProperties.getAppRoot() + var0;
//      FileInputStream var3 = new FileInputStream(var2);
//      DataInputStream var4 = new DataInputStream(var3);
//      BufferedReader var5 = new BufferedReader(new InputStreamReader(var4));
//      ArrayList var7 = new ArrayList();
//
//      String var6;
//      while((var6 = var5.readLine()) != null) {
//         String[] var8 = ParsingUtils.parseCsvLine(var6, var1, false);
//         var7.add(var8);
//      }
//
//      String[][] var9 = new String[var7.size()][];
//      var7.toArray(var9);
//      var4.close();
//      return var9;
//   }

//   public static String getExternalizedBooleanValue(boolean var0) {
//      String var1 = "";
//      if (var0) {
//         var1 = SayAppRes.say("baseline.value.boolean.true");
//      } else {
//         var1 = SayAppRes.say("baseline.value.boolean.false");
//      }
//
//      return var1;
//   }

//   public static String getPid() {
//      if (pId != null) {
//         return pId;
//      } else {
//         String var0 = ManagementFactory.getRuntimeMXBean().getName();
//         int var1 = var0.indexOf(64);
//         pId = var0.substring(0, var1);
//         return pId;
//      }
//   }

//   public static boolean isIpAddress(String var0) {
//      return isValidIpAddress(var0);
//   }

//   public static boolean startMagenServices() throws Exception {
//      if (checkMagenFile()) {
//         startICAP();
//         startSquidService();
//         return true;
//      } else {
//         return false;
//      }
//   }

//   public static boolean checkMagenFile() throws Exception {
//      String var1 = FileUtils.getGuardHome("etc/") + ".magen";
//      if (!FileUtils.fileExists(var1)) {
//         String var2 = SayAppRes.say("file.notExists", var1);
////         AdHocLogger.logDebug(var2, 9);
//         return false;
//      } else {
//         return true;
//      }
//   }

//   public static boolean startICAP() throws Exception {
//      String var1 = FileUtils.getGuardHome("bin/") + "guard-control_stub" + " icap start";
//      return startService(var1);
//   }
//
//   public static boolean startSquidService() throws Exception {
//      String var1 = FileUtils.getGuardHome("bin/") + "guard-control_stub" + " squid start";
//      return startService(var1);
//   }
//
//   public static boolean stopICAP() throws Exception {
//      String var1 = FileUtils.getGuardHome("bin/") + "guard-control_stub" + " icap stop";
//      return startService(var1);
//   }
//
//   public static boolean restartRDSCollection() throws Exception {
//      AdHocLogger.logDebug("Utils.restartRDSCollection", 9);
//      String var1 = FileUtils.getGuardHome("bin/") + "guard-control_stub" + " rds restart";
//      return startService(var1);
//   }
//
//   public static boolean startRDSCollection() throws Exception {
//      String var1 = FileUtils.getGuardHome("bin/") + "guard-control_stub" + " rds start";
//      return startService(var1);
//   }
//
//   public static boolean stopRDSCollection() throws Exception {
//      String var1 = FileUtils.getGuardHome("bin/") + "guard-control_stub" + " rds stop";
//      return startService(var1);
//   }

//   public static void restartICAP() throws Exception {
//      stopICAP();
//      startICAP();
//   }

//   public static boolean restartApplMaskingEng() throws Exception {
//      boolean var0 = startSquidAfterReload();
//      boolean var1 = restartIcapAfterReload();
//      return var1 && var0;
//   }

//   private static boolean startSquidAfterReload() throws Exception {
//      String var0 = FileUtils.getGuardHome("bin/") + "guard-control_stub" + " squid restart";
//      return startService(var0);
//   }
//
//   private static boolean restartIcapAfterReload() throws Exception {
//      String var0 = FileUtils.getGuardHome("bin/") + "guard-control_stub" + " icap restart";
//      return startService(var0);
//   }

//   private static boolean startService(String var0) throws Exception {
//      try {
//         CommandExecutor var1 = new CommandExecutor(var0, true);
//         Process var2 = var1.runCommand();
//         BufferedReader var3 = new BufferedReader(new InputStreamReader(var2.getInputStream()));
//         String var4 = null;
//         AdHocLogger.logDebug("Start log for service: " + var0, 3);
//
//         while((var4 = var3.readLine()) != null) {
//            AdHocLogger.logDebug(var4, 3);
//         }
//
//         AdHocLogger.logDebug("End log for service: " + var0, 3);
//         var2.waitFor();
//         if (var2.exitValue() != 0) {
//            var3.close();
//            throw new Exception("Abnormal status for service with exit code " + var2.exitValue() + " : " + var0);
//         } else {
//            var3.close();
//            return true;
//         }
//      } catch (InterruptedException var5) {
//         AdHocLogger.logException(var5);
//         throw var5;
//      } catch (IOException var6) {
//         AdHocLogger.logException(var6);
//         throw var6;
//      }
//   }

//   public static boolean startJobQueueListener() throws Exception {
//      String var1 = FileUtils.getGuardHome("bin/") + "guard-control_stub" + " classifier start";
//      return startService(var1);
//   }
//
//   public static boolean stopJobQueueListener() throws Exception {
//      String var1 = FileUtils.getGuardHome("bin/") + "guard-control_stub" + " classifier stop";
//      return startService(var1);
//   }

//   public static void restartJobQueueListener() throws Exception {
//      stopJobQueueListener();
//      startJobQueueListener();
//   }

//   public static ReleaseInfo parseReleaseInfo(String var0) {
//      ReleaseInfo var1 = new ReleaseInfo(var0);
//      return var1;
//   }

   public static String escapeSQL(String var0) {
      return StringUtils.replace(StringUtils.replace(var0, "\\", "\\\\"), "'", "\\'");
   }

   public static boolean verifyDatetimePeriodSpecialCharacters(String var0) {
      return var0 != null && DatetimePeriodSpecialCharacters.matcher(var0).find();
   }

   public static String removeRegExFromString(String var0, String var1) {
      if (var1 == null) {
         return null;
      } else if (var0 == null) {
         return var1;
      } else {
         Pattern var2 = Pattern.compile(var0, 42);
         Matcher var3 = var2.matcher(var1);
         if (var3.find()) {
            var1 = var3.replaceAll("<!--ignore code-->");
         }

         return var1;
      }
   }

   public static String removeIframeTag(String var0) {
      if (var0 == null) {
         return null;
      } else {
         var0 = removeRegExFromString("(<(\\s)*?IFRAME.+?<(\\s)*?/(\\s)*?iframe(\\s)*?>)", var0);
         var0 = removeRegExFromString("(<(\\s)*?/?IFRAME.*?(\\s)*?>)", var0);
         return var0;
      }
   }

   public static String removeImgTag(String var0) {
      if (var0 == null) {
         return null;
      } else {
         var0 = removeRegExFromString("(<(\\s)*?IMG.+?<(\\s)*?/(\\s)*?img(\\s)*?>)", var0);
         var0 = removeRegExFromString("(<(\\s)*?/?IMG.*?(\\s)*?>)", var0);
         return var0;
      }
   }

   static {
      String var0 = "\"$|&\\\\;`'()!/><";
      nameSpecialCharacters = Pattern.compile("[" + var0 + "]");
      DatetimePeriodSpecialCharacters = Pattern.compile("[\"&'><]");
      sortableDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      filenameDateFormat = new SimpleDateFormat("yyyy-MM-dd_HH'h'mm'm'ss's'");
      screenDataFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      screenDataFormatSimple = new SimpleDateFormat();
      simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      numDateFormat = new SimpleDateFormat("yyMMddHHmmss");
      pre = System.currentTimeMillis();
   }

   public static boolean isTrue(String value) {
      return value != null && Boolean.parseBoolean(value.trim());
   }

   public static Integer parseInteger(String value) {
      try {
         return new Integer(value.trim());
      } catch (NumberFormatException var3) {
         return null;
      }
   }

   public static String collectionToString(Collection<?> collection) {
      return collectionToString(collection, ",");
   }

   public static String collectionToString(Collection<?> collection, String delimiter) {
      if (collection == null || collection.size() == 0) {
         return "";
      }
      return org.apache.commons.lang3.StringUtils.join(collection,delimiter);
   }

   public static <K, V> Map<String, V> toMap(Object... data) {
      if (data.length == 1 && data[0] instanceof Map) {
         return (Map)data[0];
      } else if (data.length % 2 == 1) {
         throw new IllegalArgumentException("You must pass an even sized array to the toMap method (size = " + data.length + ")");
      } else {
         Map<String, V> map = new HashMap();
         int i = 0;

         while(i < data.length) {
            map.put((String)data[i++], (V) data[i++]);
         }

         return map;
      }
   }
}
