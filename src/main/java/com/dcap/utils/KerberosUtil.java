package com.dcap.utils;

import org.apache.kerby.kerberos.kerb.client.KrbConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Base64;
import java.util.Map;
import java.util.Objects;

public final class KerberosUtil {

    private static final Logger LOG = LoggerFactory.getLogger(KerberosUtil.class);

    public static String getKrb5ConfPath(String krb5confBase64) throws IOException {
        String krb5Conf;
        byte[] data = Base64.getDecoder().decode(krb5confBase64);
        Path tempFile = Files.createTempFile("krb5_", ".conf");
        Files.write(tempFile, data);
        krb5Conf = tempFile.toFile().getAbsolutePath();
        return krb5Conf;
    }

    public static String getKeyTabPath(String username, String keyTabBase64) throws IOException {
        String keyTab;
        byte[] data = Base64.getDecoder().decode(keyTabBase64);
        Path tempFile = Files.createTempFile(username+"_", ".keytab");
        Files.write(tempFile, data);
        keyTab = tempFile.toFile().getAbsolutePath().replaceAll("\\\\","/");
        return keyTab;
    }

    public static  KrbConfig loadKrb5Conf(String krb5ConfPath) {
        System.setProperty("java.security.krb5.conf", krb5ConfPath);
        File iniFile = new File(krb5ConfPath);
        KrbConfig krbConfig = new KrbConfig();
        try {
            krbConfig.addIniConfig(iniFile);
            krbConfig.addKrb5Config(iniFile);
            String kdcRealm = krbConfig.getKdcRealm();
            LOG.info("Recognized kdc realm is [{}]", kdcRealm);

            String kdcHost = krbConfig.getKdcHost().toLowerCase();
            if (Objects.equals("localhost", kdcHost) || Objects.equals("127.0.0.1", kdcHost)) {
                LOG.info("note: Recognized kdc host is [{}], how this is incorrect, \n" +
                        "Please set the kdc_host at [libdefaults] position in the krb5.ini file ", kdcHost);
            }
            Map<String, Object> realmSection = krbConfig.getRealmSection(kdcRealm);
            if (realmSection == null) {
                throw new IllegalArgumentException("Can not find realm section for realm " + kdcRealm);
            }
            String kdcAddr = null;
            for (Map.Entry<String, Object> en : realmSection.entrySet()) {
                if (Objects.equals("kdc", en.getKey())) {
                    kdcAddr = (String) en.getValue();
                    break;
                }
            }
            LOG.info("The kdcAddr [{}] recognized for kdcRealm [{}]", kdcAddr, kdcRealm);
            return krbConfig;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
