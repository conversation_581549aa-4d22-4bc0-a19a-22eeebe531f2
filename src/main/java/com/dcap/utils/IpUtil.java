package com.dcap.utils;

public class IpUtil {

    public static String longToIpv4String(long ipAsLong) {
        return ((ipAsLong >>> 24) & 0xFF) + "." +
                ((ipAsLong >>> 16) & 0xFF) + "." +
                ((ipAsLong >>>  8) & 0xFF) + "." +
                (ipAsLong & 0xFF);
    }

    public static long ipV4ToLong(String ip) {
        String[] octets = ip.split("\\.");
        return (Long.parseLong(octets[0]) << 24) + (Integer.parseInt(octets[1]) << 16)
                + (Integer.parseInt(octets[2]) << 8) + Integer.parseInt(octets[3]);
    }
    public static boolean isIPv4Private(String ip) {
        long longIp = ipV4ToLong(ip);
        return (longIp >= ipV4ToLong("10.0.0.0") && longIp <= ipV4ToLong("**************"))
                || (longIp >= ipV4ToLong("**********") && longIp <= ipV4ToLong("**************"))
                || longIp >= ipV4ToLong("***********") && longIp <= ipV4ToLong("***************");
    }
}
