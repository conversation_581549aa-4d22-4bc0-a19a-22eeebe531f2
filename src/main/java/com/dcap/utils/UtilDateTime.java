package com.dcap.utils;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.*;

public class UtilDateTime {

    /**
     * 用于 java.sql.Date 转换的 JDBC 转义格式。
     */
    private static final String DATE_FORMAT = "yyyy-MM-dd";

    /**
     * java.sql.Timestamp 转换的 JDBC 转义格式。
     */
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";

    /**
     * java.sql.Time 转换的 JDBC 转义格式。
     */
    private static final String TIME_FORMAT = "HH:mm:ss";

    /**
     * 默认日期格式
     */
    public static final String DATE_MASK_LONG = "yyyyMMddHHmmss";

    public static final String DATE_MASK_FULL_EN = "MM/dd/yyyy HH:mm:ss";

    public static final String DATE_MASK_FULL = "yyyy-MM-dd HH:mm:ss";

    // yyyy-MM-ddTHH:mm:ssZ
    public static final String DATE_UTC = "yyyy-MM-dd'T'HH:mm:ss'Z'";

    public static final TimeZone UTC_ZONE = TimeZone.getTimeZone("UTC");

    // 默认 GMT+8
    public static final TimeZone DEFAULT_ZONE = UtilDateTime.toTimeZone(8);

    public static final Locale DEFAULT_LOCAL = Locale.SIMPLIFIED_CHINESE;

    public static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(UtilDateTime.DATE_MASK_FULL, DEFAULT_LOCAL);

    public static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(UtilDateTime.DATE_FORMAT, DEFAULT_LOCAL);

    public static final DateTimeFormatter utcDateFormatter = new DateTimeFormatterBuilder().appendPattern(DATE_UTC).toFormatter(Locale.US);

    private UtilDateTime() {}

    public static double getInterval(Date from, Date thru) {
        return thru != null ? thru.getTime() - from.getTime() : 0;
    }

    /**
     * @Description: 获取两个时间戳间隔的天数
     * @param from
     * @param thru
     * @return int
     */
    public static int getIntervalInDays(Timestamp from, Timestamp thru) {
        return thru != null ? (int) ((thru.getTime() - from.getTime()) / (24 * 60 * 60 * 1000)) : 0;
    }

    /**
     * @Description: 获取两个时间戳间隔的天数
     * @param from
     * @param thru
     * @return int
     */
    public static int getIntervalInDays(Date from, Date thru) {
        return thru != null ? (int) ((thru.getTime() - from.getTime()) / (24 * 60 * 60 * 1000)) : 0;
    }

    /**
     * @Description: 在给定时间戳的基础上，减少天数，然后将其返回
     * @param start
     * @param days
     * @return java.sql.Timestamp
     */
    public static Timestamp minusDaysToTimestamp(Timestamp start, int days) {
        return new Timestamp(start.getTime() - (24L*60L*60L*1000L*days));
    }

    /**
     * @Description: 传入开始和结束日期，返回两个日期之间间隔的所有的日期字符串（必须结束时期大于开始日期，默认是 yyyy-MM-dd 格式）
     * @param startDate 开始日期。yyyy-MM-dd 格式
     * @param endDate   结束日期。yyyy-MM-dd 格式
     * @return java.util.List<java.lang.String>
     */
    public static List<String> getIntervalDateString(String format, String startDate, String endDate) throws ParseException {
        if(format == null){
            format = "yyyy-MM-dd";
        }
        List<String> dateList = new ArrayList<>();
        dateList.add(startDate);
        long subDate = UtilDateTime.getSubDate(startDate, format, endDate);
        Timestamp startTimestamp = UtilDateTime.stringToTimeStamp(startDate, format, DEFAULT_ZONE, Locale.CHINA);
        for(int i = 0, len = (int) subDate; ++i<=len;){
            Timestamp offsetTimestamp = UtilDateTime.addDaysToTimestamp(startTimestamp, i);
            String dateString = UtilDateTime.timeStampToString(offsetTimestamp, format, DEFAULT_ZONE, Locale.CHINA);
            dateList.add(dateString);
        }
        return dateList;
    }

    /**
     * @Description: 指定的日期上面增加天数
     * @param start
     * @param days
     * @return java.util.Date
     */
    public static Date addDaysToDate(Date start, int days) {
        return new Date(start.getTime() + ((24L * 60L * 60L * 1000L * days)));
    }

    /**
     * @Description: 在给定时间戳的基础上，增加天数，然后将其返回
     * @param start
     * @param days
     * @return java.sql.Timestamp
     */
    public static Timestamp addDaysToTimestamp(Timestamp start, int days) {
        return new Timestamp(start.getTime() + (24L*60L*60L*1000L*days));
    }

    /**
     * @Description: 返回两个时间戳之间的间隔(纳秒级别)
     * @param from
     * @param thru
     * @return double
     */
    public static double getInterval(Timestamp from, Timestamp thru) {
        return thru != null ? thru.getTime() - from.getTime() + (thru.getNanos() - from.getNanos()) / 1000000 : 0;
    }


    /**
     * 立即返回当前时间戳
     */
    public static Timestamp nowTimestamp() {
        return getTimestamp(System.currentTimeMillis());
    }

    /**
     * 将毫秒值转换为时间戳。
     */
    public static Timestamp getTimestamp(long time) {
        return new Timestamp(time);
    }

    /**
     * 将毫秒值转换为时间戳。
     */
    public static Timestamp getTimestamp(String milliSecs) throws NumberFormatException {
        return new Timestamp(Long.parseLong(milliSecs));
    }

    /**
     * 以字符串形式返回 currentTimeMillis
     * @return String(currentTimeMillis)
     */
    public static String currentTimeMillisAsString() {
        return Long.toString(System.currentTimeMillis());
    }

    /**
     * 返回格式为 yyyy-MM-dd HH:mm:ss 的字符串
     * @return String formatted for right now
     */
    public static String nowDateString() {
        return dateTimeFormatter.format(LocalDateTime.now(DEFAULT_ZONE.toZoneId()));
    }

    public static Date nowDate() {
        return new Date();
    }

    public static Timestamp getDayStart(Timestamp stamp) {
        return getDayStart(stamp, 0);
    }

    public static Timestamp getDayStart(Timestamp stamp, int daysLater) {
        return getDayStart(stamp, daysLater, DEFAULT_ZONE, DEFAULT_LOCAL);
    }

    public static Timestamp getNextDayStart(Timestamp stamp) {
        return getDayStart(stamp, 1);
    }

    public static Timestamp getDayEnd(Timestamp stamp) {
        return getDayEnd(stamp, 0L);
    }

    public static Timestamp getDayEnd(Timestamp stamp, Long daysLater) {
        return getDayEnd(stamp, daysLater, DEFAULT_ZONE, DEFAULT_LOCAL);
    }

    /**
     * 返回时间戳所在的该年中的第一天的日期
     * @param stamp
     * @return java.sql.Timestamp
     */
    public static Timestamp getYearStart(Timestamp stamp) {
        return getYearStart(stamp, 0, 0, 0);
    }

    public static Timestamp getYearStart(Timestamp stamp, int daysLater) {
        return getYearStart(stamp, daysLater, 0, 0);
    }

    public static Timestamp getYearStart(Timestamp stamp, int daysLater, int yearsLater) {
        return getYearStart(stamp, daysLater, 0, yearsLater);
    }
    public static Timestamp getYearStart(Timestamp stamp, int daysLater, int monthsLater, int yearsLater) {
        return getYearStart(stamp, daysLater, monthsLater, yearsLater, DEFAULT_ZONE, DEFAULT_LOCAL);
    }
    public static Timestamp getYearStart(Timestamp stamp, Number daysLater, Number monthsLater, Number yearsLater) {
        return getYearStart(stamp, (daysLater == null ? 0 : daysLater.intValue()),
                (monthsLater == null ? 0 : monthsLater.intValue()), (yearsLater == null ? 0 : yearsLater.intValue()));
    }

    /**
     * 返回时间戳所在的该月的第一天的日期
     * @param stamp
     * @return java.sql.Timestamp
     */
    public static Timestamp getMonthStart(Timestamp stamp) {
        return getMonthStart(stamp, 0, 0);
    }

    /**
     * @Description: 返回时间戳中该月的第一天起的指定天数后的日期
     * @param stamp
     * @param daysLater
     * @return java.sql.Timestamp
     */
    public static Timestamp getMonthStart(Timestamp stamp, int daysLater) {
        return getMonthStart(stamp, daysLater, 0);
    }

    /**
     * @Description: 返回时间戳中该月的指定月数和指定天数之后的日期。
     * @param stamp
     * @param daysLater
     * @param monthsLater
     * @return java.sql.Timestamp
     */
    public static Timestamp getMonthStart(Timestamp stamp, int daysLater, int monthsLater) {
        return getMonthStart(stamp, daysLater, monthsLater, DEFAULT_ZONE, DEFAULT_LOCAL);
    }

    /**
     * 返回时间戳所表示的该周中的第一天
     *
     * @param stamp
     * @return java.sql.Timestamp
     */
    public static Timestamp getWeekStart(Timestamp stamp) {
        return getWeekStart(stamp, 0, 0);
    }

    /**
     * @Description: 返回时间戳所表示的该周中第一天起指定天数后的日期
     * @param stamp
     * @param daysLater
     * @return java.sql.Timestamp
     */
    public static Timestamp getWeekStart(Timestamp stamp, int daysLater) {
        return getWeekStart(stamp, daysLater, 0);
    }

    /**
     * @Description: 返回时间戳所表示的周中第一天起指定周数指定天数后的日期
     * @param stamp
     * @param daysLater
     * @param weeksLater
     * @return java.sql.Timestamp
     */
    public static Timestamp getWeekStart(Timestamp stamp, int daysLater, int weeksLater) {
        return getWeekStart(stamp, daysLater, weeksLater, DEFAULT_ZONE, DEFAULT_LOCAL);
    }

    /**
     * @Description: 获取时间戳所表示的周中最后一天的时间戳
     * @param stamp
     * @return java.sql.Timestamp
     */
    public static Timestamp getWeekEnd(Timestamp stamp) {
        return getWeekEnd(stamp, DEFAULT_ZONE, DEFAULT_LOCAL);
    }

    public static Calendar toCalendar(Timestamp stamp) {
        Calendar cal = Calendar.getInstance();
        if (stamp != null) {
            cal.setTimeInMillis(stamp.getTime());
        }
        return cal;
    }


    public static Timestamp toTimestamp(Date date) {
        if (date == null) {
            return null;
        }
        return new Timestamp(date.getTime());
    }


    /*
     * @Description: //TODO 获取两个日期中间的日期
     * @param start
     * @param end
     * @return java.util.Date
     * @Author: xuezhiwei
     * @Date: 2020/10/30 8:37 下午
     */
    public static Date getMiddleTimeBetween(Date start, Date end) throws ParseException {
        double interval = getInterval(start, end);
        long timeMillis = new BigDecimal(interval).divide(new BigDecimal(2)).longValue();
        return new Date(timeMillis);
    }

    /**
     * 根据 format 将 date 转为相应格式的字符串。
     * @param date The Date
     * @return 给定格式的日期字符串
     */
    public static String toDateString(Date date, String format) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat dateFormat;
        if (format != null) {
            dateFormat = new SimpleDateFormat(format);
        } else {
            dateFormat = new SimpleDateFormat();
        }
        return dateFormat.format(date);
    }

    /**
     * 将 date 转为 yyyy-MM-dd HH:mm:ss 格式的日期字符串
     *
     * @param date The Date
     * @return 一个 yyyy-MM-dd HH:mm:ss 格式的日期字符串
     */
    public static String datetimeToString(Date date) {
        return date.toInstant().atZone(DEFAULT_ZONE.toZoneId()).toLocalDateTime().format(dateTimeFormatter);
    }

    /**
     * 将 date 转为 yyyy-MM-dd 格式的日期字符串
     * @param date The Date
     * @return 一个 yyyy-MM-dd 格式的日期字符串
     */
    public static String dateToString(Date date) {
        return date.toInstant().atZone(DEFAULT_ZONE.toZoneId()).toLocalDateTime().format(dateFormatter);
    }

    /**
     * 将 date 转为 HH:MM:SS 格式的字符串。如果秒值是 0 则输出格式为 HH:MM
     *
     * @param date The Date
     * @return 一个 HH:MM:SS 或 HH:MM 格式的字符串
     */
    public static String toTimeString(Date date) {
        if (date == null) {
            return "";
        }
        Calendar calendar = Calendar.getInstance();

        calendar.setTime(date);
        return (toTimeString(calendar.get(Calendar.HOUR_OF_DAY), calendar.get(Calendar.MINUTE), calendar.get(Calendar.SECOND)));
    }

    /**
     * 从指定的 时 分 秒 返回一个 HH:MM:SS 格式的字符串。如果秒值为 0 则返回的格式为 HH:MM
     *
     * @param hour   The hour int
     * @param minute The minute int
     * @param second The second int
     * @return 一个 HH:MM:SS 或 HH:MM 格式的字符串
     */
    public static String toTimeString(int hour, int minute, int second) {
        String hourStr;
        String minuteStr;
        String secondStr;

        if (hour < 10) {
            hourStr = "0" + hour;
        } else {
            hourStr = "" + hour;
        }
        if (minute < 10) {
            minuteStr = "0" + minute;
        } else {
            minuteStr = "" + minute;
        }
        if (second < 10) {
            secondStr = "0" + second;
        } else {
            secondStr = "" + second;
        }
        if (second == 0) {
            return hourStr + ":" + minuteStr;
        }
        return hourStr + ":" + minuteStr + ":" + secondStr;
    }

    /**
     * @Description: //TODO 时间戳转换为 GMT 日期字符串
     * @param timestamp
     * @return java.lang.String
     */
    public static String toGmtTimestampString(Timestamp timestamp) {
        DateFormat df = DateFormat.getDateTimeInstance();
        df.setTimeZone(TimeZone.getTimeZone("GMT"));
        return df.format(timestamp);
    }

    /**
     * 返回一个本月初的时间戳
     * @return 本月初的时间戳
     */
    public static Timestamp monthBegin() {
        Calendar mth = Calendar.getInstance();
        mth.set(Calendar.DAY_OF_MONTH, 1);
        mth.set(Calendar.HOUR_OF_DAY, 0);
        mth.set(Calendar.MINUTE, 0);
        mth.set(Calendar.SECOND, 0);
        mth.set(Calendar.MILLISECOND, 0);
        mth.set(Calendar.AM_PM, Calendar.AM);
        return new Timestamp(mth.getTime().getTime());
    }

    /**
     * 返回输入的时间戳是今年中第几个星期
     *
     * @param input Timestamp date
     * @return 包含星期数的整数
     */
    public static int weekNumber(Timestamp input) {
        return weekNumber(input, DEFAULT_ZONE, DEFAULT_LOCAL);
    }

    /**
     * 根据时间戳返回该时间戳是一周中的第几天。
     * @param stamp Timestamp date
     * @return 包含日期数字的int（星期日= 1，星期六= 7）
     */
    public static int dayNumber(Timestamp stamp) {
        Calendar tempCal = toCalendar(stamp, DEFAULT_ZONE, DEFAULT_LOCAL);
        return tempCal.get(Calendar.DAY_OF_WEEK);
    }

    public static int weekNumber(Timestamp input, int startOfWeek) {
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(startOfWeek);

        if (startOfWeek == Calendar.MONDAY) {
            calendar.setMinimalDaysInFirstWeek(4);
        } else if (startOfWeek == Calendar.SUNDAY) {
            calendar.setMinimalDaysInFirstWeek(3);
        }

        calendar.setTime(new Date(input.getTime()));
        return calendar.get(Calendar.WEEK_OF_YEAR);
    }

    /**
     * @Description: 返回一个日历对象，需要时区和语言环境
     * @param timeZone      时区
     * @param locale        语言环境
     * @return Calendar
     */
    public static Calendar getCalendarInstance(TimeZone timeZone, Locale locale) {
        return Calendar.getInstance(timeZone, locale);
    }

    /**
     * 返回一个Calendar对象，该对象已初始化为指定的日期/时间，时区和语言环境。
     *
     * @param date date/time to use
     * @param timeZone
     * @param locale
     * @return Calendar object
     * @see Calendar
     */
    public static Calendar toCalendar(Date date, TimeZone timeZone, Locale locale) {
        Calendar cal = getCalendarInstance(timeZone, locale);
        if (date != null) {
            cal.setTime(date);
        }
        return cal;
    }

    /**
     * 在时间戳上执行日期/时间 调整。 这是跨地区和时区执行日期/时间调整的唯一准确方法。
     *
     * @param stamp 进行调整的日期/时间
     * @param adjType 要执行的调整类型。 使用java.util.Calendar字段之一。
     * @param adjQuantity 调整量。
     * @param timeZone      时区
     * @param locale        地区
     * @return adjusted Timestamp
     * @see Calendar
     */
    public static Timestamp adjustTimestamp(Timestamp stamp, int adjType, int adjQuantity, TimeZone timeZone, Locale locale) {
        Calendar tempCal = toCalendar(stamp, timeZone, locale);
        tempCal.add(adjType, adjQuantity);
        return new Timestamp(tempCal.getTimeInMillis());
    }

    public static Timestamp adjustTimestamp(Timestamp stamp, Integer adjType, Integer adjQuantity) {
        Calendar tempCal = toCalendar(stamp);
        tempCal.add(adjType, adjQuantity);
        return new Timestamp(tempCal.getTimeInMillis());
    }

    public static Timestamp getDayStart(Timestamp stamp, TimeZone timeZone, Locale locale) {
        return getDayStart(stamp, 0, timeZone, locale);
    }

    public static Timestamp getDayStart(Timestamp stamp, int daysLater, TimeZone timeZone, Locale locale) {
        Calendar tempCal = toCalendar(stamp, timeZone, locale);
        tempCal.set(tempCal.get(Calendar.YEAR), tempCal.get(Calendar.MONTH), tempCal.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        tempCal.add(Calendar.DAY_OF_MONTH, daysLater);
        Timestamp retStamp = new Timestamp(tempCal.getTimeInMillis());
        retStamp.setNanos(0);
        return retStamp;
    }

    public static Timestamp getDayEnd(Timestamp stamp, TimeZone timeZone, Locale locale) {
        return getDayEnd(stamp, 0L, timeZone, locale);
    }

    public static Timestamp getDayEnd(Timestamp stamp, Long daysLater, TimeZone timeZone, Locale locale) {
        Calendar tempCal = toCalendar(stamp, timeZone, locale);
        tempCal.set(tempCal.get(Calendar.YEAR), tempCal.get(Calendar.MONTH), tempCal.get(Calendar.DAY_OF_MONTH), 23, 59, 59);
        tempCal.add(Calendar.DAY_OF_MONTH, daysLater.intValue());
        Timestamp retStamp = new Timestamp(tempCal.getTimeInMillis());
        retStamp.setNanos(0);
        return retStamp;
    }

    public static Timestamp getWeekStart(Timestamp stamp, TimeZone timeZone, Locale locale) {
        return getWeekStart(stamp, 0, 0, timeZone, locale);
    }

    public static Timestamp getWeekStart(Timestamp stamp, int daysLater, TimeZone timeZone, Locale locale) {
        return getWeekStart(stamp, daysLater, 0, timeZone, locale);
    }

    public static Timestamp getWeekStart(Timestamp stamp, int daysLater, int weeksLater, TimeZone timeZone, Locale locale) {
        Calendar tempCal = toCalendar(stamp, timeZone, locale);
        tempCal.set(tempCal.get(Calendar.YEAR), tempCal.get(Calendar.MONTH), tempCal.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        tempCal.add(Calendar.DAY_OF_MONTH, daysLater);
        tempCal.set(Calendar.DAY_OF_WEEK, tempCal.getFirstDayOfWeek());
        tempCal.add(Calendar.WEEK_OF_MONTH, weeksLater);
        Timestamp retStamp = new Timestamp(tempCal.getTimeInMillis());
        retStamp.setNanos(0);
        return retStamp;
    }

    public static Timestamp getWeekEnd(Timestamp stamp, TimeZone timeZone, Locale locale) {
        Timestamp weekStart = getWeekStart(stamp, timeZone, locale);
        Calendar tempCal = toCalendar(weekStart, timeZone, locale);
        tempCal.add(Calendar.DAY_OF_MONTH, 6);
        return getDayEnd(new Timestamp(tempCal.getTimeInMillis()), timeZone, locale);
    }

    public static Timestamp getMonthStart(Timestamp stamp, TimeZone timeZone, Locale locale) {
        return getMonthStart(stamp, 0, 0, timeZone, locale);
    }

    public static Timestamp getMonthStart(Timestamp stamp, int daysLater, TimeZone timeZone, Locale locale) {
        return getMonthStart(stamp, daysLater, 0, timeZone, locale);
    }

    public static Timestamp getMonthStart(Timestamp stamp, int daysLater, int monthsLater, TimeZone timeZone, Locale locale) {
        Calendar tempCal = toCalendar(stamp, timeZone, locale);
        tempCal.set(tempCal.get(Calendar.YEAR), tempCal.get(Calendar.MONTH), 1, 0, 0, 0);
        tempCal.add(Calendar.MONTH, monthsLater);
        tempCal.add(Calendar.DAY_OF_MONTH, daysLater);
        Timestamp retStamp = new Timestamp(tempCal.getTimeInMillis());
        retStamp.setNanos(0);
        return retStamp;
    }

    public static Timestamp getMonthEnd(Timestamp stamp, TimeZone timeZone, Locale locale) {
        Calendar tempCal = toCalendar(stamp, timeZone, locale);
        tempCal.set(tempCal.get(Calendar.YEAR), tempCal.get(Calendar.MONTH), tempCal.getActualMaximum(Calendar.DAY_OF_MONTH), 0, 0, 0);
        return getDayEnd(new Timestamp(tempCal.getTimeInMillis()), timeZone, locale);
    }

    public static Timestamp getYearStart(Timestamp stamp, TimeZone timeZone, Locale locale) {
        return getYearStart(stamp, 0, 0, 0, timeZone, locale);
    }

    public static Timestamp getYearStart(Timestamp stamp, int daysLater, TimeZone timeZone, Locale locale) {
        return getYearStart(stamp, daysLater, 0, 0, timeZone, locale);
    }

    public static Timestamp getYearStart(Timestamp stamp, int daysLater, int yearsLater, TimeZone timeZone, Locale locale) {
        return getYearStart(stamp, daysLater, 0, yearsLater, timeZone, locale);
    }

    public static Timestamp getYearStart(Timestamp stamp, Number daysLater, Number monthsLater, Number yearsLater, TimeZone timeZone, Locale locale) {
        return getYearStart(stamp, (daysLater == null ? 0 : daysLater.intValue()),
                (monthsLater == null ? 0 : monthsLater.intValue()), (yearsLater == null ? 0 : yearsLater.intValue()), timeZone, locale);
    }

    public static Timestamp getYearStart(Timestamp stamp, int daysLater, int monthsLater, int yearsLater, TimeZone timeZone, Locale locale) {
        Calendar tempCal = toCalendar(stamp, timeZone, locale);
        tempCal.set(tempCal.get(Calendar.YEAR), Calendar.JANUARY, 1, 0, 0, 0);
        tempCal.add(Calendar.YEAR, yearsLater);
        tempCal.add(Calendar.MONTH, monthsLater);
        tempCal.add(Calendar.DAY_OF_MONTH, daysLater);
        Timestamp retStamp = new Timestamp(tempCal.getTimeInMillis());
        retStamp.setNanos(0);
        return retStamp;
    }

    public static Timestamp getYearEnd(Timestamp stamp, TimeZone timeZone, Locale locale) {
        Calendar tempCal = toCalendar(stamp, timeZone, locale);
        tempCal.set(tempCal.get(Calendar.YEAR), tempCal.getActualMaximum(Calendar.MONTH) + 1, 0, 0, 0, 0);
        return getMonthEnd(new Timestamp(tempCal.getTimeInMillis()), timeZone, locale);
    }

    public static int weekNumber(Timestamp stamp, TimeZone timeZone, Locale locale) {
        Calendar tempCal = toCalendar(stamp, timeZone, locale);
        return tempCal.get(Calendar.WEEK_OF_YEAR);
    }

    /**
     * 根据传递的语言环境对象，返回一周的字符串 例如 [星期日, 星期一, 星期二, 星期三, 星期四, 星期五, 星期六]
     */
    public static List<String> getDayNames(Locale locale) {
        Calendar tempCal = Calendar.getInstance(locale);
        tempCal.set(Calendar.DAY_OF_WEEK, tempCal.getFirstDayOfWeek());
        SimpleDateFormat dateFormat = new SimpleDateFormat("EEEE", locale);
        List<String> resultList = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            resultList.add(dateFormat.format(tempCal.getTime()));
            tempCal.roll(Calendar.DAY_OF_WEEK, 1);
        }
        return resultList;
    }

    /**
     * 返回月份名称字符串列表 - 适用于日历标题。
     *
     * @param locale
     * @return List of month name Strings
     */
    public static List<String> getMonthNames(Locale locale) {
        Calendar tempCal = Calendar.getInstance(locale);
        tempCal.set(Calendar.MONTH, Calendar.JANUARY);
        SimpleDateFormat dateFormat = new SimpleDateFormat("MMMM", locale);
        List<String> resultList = new ArrayList<>();
        for (int i = Calendar.JANUARY; i <= tempCal.getActualMaximum(Calendar.MONTH); i++) {
            resultList.add(dateFormat.format(tempCal.getTime()));
            tempCal.roll(Calendar.MONTH, 1);
        }
        return resultList;
    }

    public static DateFormat toDateTimeFormat(String dateTimeFormat, TimeZone tz, Locale locale) {
        DateFormat df;
        if (StringUtils.isBlank(dateTimeFormat)) {
            df = DateFormat.getDateTimeInstance(DateFormat.SHORT, DateFormat.MEDIUM, locale);
        } else {
            df = new SimpleDateFormat(dateTimeFormat, locale == null ? DEFAULT_LOCAL : locale);
        }
        df.setTimeZone(tz == null ?DEFAULT_ZONE:tz);
        return df;
    }

    /**
     * 字符串到时间戳转换。
     */
    public static Timestamp stringToTimeStamp(String dateTimeString, String dateTimeFormat, TimeZone tz, Locale locale) throws ParseException {
        DateFormat dateFormat = toDateTimeFormat(dateTimeFormat, tz, locale);
        Date parsedDate = dateFormat.parse(dateTimeString);
        return new Timestamp(parsedDate.getTime());
    }

    /**
     * 时间戳到字符串转换。
     */
    public static String timeStampToString(Timestamp stamp, String dateTimeFormat, TimeZone tz, Locale locale) {
        DateFormat dateFormat = toDateTimeFormat(dateTimeFormat, tz, locale);
        return dateFormat.format(stamp);
    }

    /**
     * 根据与 GMT 的小时偏移量返回 TimeZone 对象。
     * @see TimeZone
     */
    public static TimeZone toTimeZone(int gmtOffset) {
        if (gmtOffset > 12 || gmtOffset < -14) {
            throw new IllegalArgumentException("Invalid GMT offset");
        }
        String tzId = gmtOffset > 0 ? "GMT+" : "GMT";
        return TimeZone.getTimeZone(tzId + gmtOffset);
    }

    public static int getSecond(Timestamp stamp, TimeZone timeZone, Locale locale) {
        Calendar cal = toCalendar(stamp, timeZone, locale);
        return cal.get(Calendar.SECOND);
    }

    public static int getMinute(Timestamp stamp, TimeZone timeZone, Locale locale) {
        Calendar cal = toCalendar(stamp, timeZone, locale);
        return cal.get(Calendar.MINUTE);
    }

    public static int getHour(Timestamp stamp, TimeZone timeZone, Locale locale) {
        Calendar cal = toCalendar(stamp, timeZone, locale);
        return cal.get(Calendar.HOUR_OF_DAY);
    }

    public static int getDayOfWeek(Timestamp stamp, TimeZone timeZone, Locale locale) {
        Calendar cal = toCalendar(stamp, timeZone, locale);
        return cal.get(Calendar.DAY_OF_WEEK);
    }

    public static int getDayOfMonth(Timestamp stamp, TimeZone timeZone, Locale locale) {
        Calendar cal = toCalendar(stamp, timeZone, locale);
        return cal.get(Calendar.DAY_OF_MONTH);
    }

    public static int getDayOfYear(Timestamp stamp, TimeZone timeZone, Locale locale) {
        Calendar cal = toCalendar(stamp, timeZone, locale);
        return cal.get(Calendar.DAY_OF_YEAR);
    }

    public static int getWeekOfYear(Timestamp stamp, TimeZone timeZone, Locale locale) {
        Calendar cal = toCalendar(stamp, timeZone, locale);
        return cal.get(Calendar.WEEK_OF_YEAR)-1;
    }

    public static int getMonth(Timestamp stamp, TimeZone timeZone, Locale locale) {
        Calendar cal = toCalendar(stamp, timeZone, locale);
        return cal.get(Calendar.MONTH);
    }

    public static int getYear(Timestamp stamp, TimeZone timeZone, Locale locale) {
        Calendar cal = toCalendar(stamp, timeZone, locale);
        return cal.get(Calendar.YEAR);
    }

    public static String getDateFormat() {
        return DATE_FORMAT;
    }

    public static String getDateTimeFormat() {
        return DATE_TIME_FORMAT;
    }

    public static String getTimeFormat() {
        return TIME_FORMAT;
    }

    /**
     * @Description: 今天是周几
     * @param
     * @return int
     */
    public static int getCurrentWeekNum() {
        return dayNumber(nowTimestamp())-1;
    }

    public static int getCurrentMonth() {
        return getMonth(nowTimestamp(), DEFAULT_ZONE, DEFAULT_LOCAL);
    }

    /**
     * @Description: 上周结束日期
     * @param format    要求返回的时间格式
     * @return java.lang.String
     */
    public static String getBeforeWeekEndDate(String format) {
        Calendar calendar = Calendar.getInstance();
        int currentWeek = calendar.get(Calendar.DAY_OF_WEEK )-1;
        Date beforeWeekDate = new Date(new Date().getTime()- (long) currentWeek *24*60*60*1000);
        SimpleDateFormat simpleDateFormat= new SimpleDateFormat(format);
        return simpleDateFormat.format(beforeWeekDate);
    }

    /**
     * @Description: 上周开始日期
     * @param format
     * @return java.lang.String
     */
    public static String getBeforeWeekStartDate(String format) {
        Calendar calendar = Calendar.getInstance();
        int currentWeek = calendar.get(Calendar.DAY_OF_WEEK )-1;
        Date beforeWeekDate = new Date(new Date().getTime()- (long) (currentWeek + 6) *24*60*60*1000);
        SimpleDateFormat simpleDateFormat= new SimpleDateFormat(format);
        return simpleDateFormat.format(beforeWeekDate);
    }

    /**
     * @Description: 获取当前日期向前一天
     */
    public static String getBeforeDate(String format) {
        Date beforeDate = new Date(new Date().getTime()-24*60*60*1000);
        SimpleDateFormat simpleDateFormat= new SimpleDateFormat(format);
        return simpleDateFormat.format(beforeDate);
    }

    /**
     * @Description: 根据指定格式，获取今天多少天之前的日期字符串
     * @param format
     * @param beforeDay
     * @return java.lang.String
     */
    public static String getBeforeDate(String format,int beforeDay) {
        Date beforeDate = new Date(new Date().getTime()- (long) beforeDay *24*60*60*1000);
        SimpleDateFormat simpleDateFormat= new SimpleDateFormat(format);
        return simpleDateFormat.format(beforeDate);
    }

    /**
     * @Description:  根据指定格式，获取当前日期之后的指定天数的日期字符串
     * @param format
     * @param afterDay
     * @return java.lang.String
     */
    public static String getAfterDate(String format,int afterDay) {
        Date beforeDate = new Date(new Date().getTime()+ (long) afterDay *24*60*60*1000);
        SimpleDateFormat simpleDateFormat= new SimpleDateFormat(format);
        return simpleDateFormat.format(beforeDate);
    }


    /**
     * @Description: 根据指定格式，从指定日期开始获取之后 afterDay 天的日期字符串
     * @param dateFrom
     * @param format
     * @param afterDay
     * @return java.lang.String
     */
    public static String getAfterDate(Date dateFrom, String format,int afterDay) {
        Date beforeDate = new Date(dateFrom.getTime()+ (long) afterDay *24*60*60*1000);
        SimpleDateFormat simpleDateFormat= new SimpleDateFormat(format);
        return simpleDateFormat.format(beforeDate);
    }

    /**
     * @Description: 根据指定格式，从指定日期开始获取之后 afterDay 天的日期字符串
     * @param dateFrom
     * @param format
     * @param afterDay
     * @return java.lang.String
     */
    public static String getAfterDate(String dateFrom, String format,int afterDay) throws ParseException {
        Date beforeDate = new Date(datetimeTextToDate(dateFrom,format).getTime()+ (long) afterDay *24*60*60*1000);
        SimpleDateFormat simpleDateFormat= new SimpleDateFormat(format);
        return simpleDateFormat.format(beforeDate);
    }

    /**
     * @Description: 根据指定格式，返回开始日期和结束日期相差的天数
     * @param dateFrom  开始日期（必须符合日期格式）
     * @param format    日期格式
     * @param afterDay  结束日期（必须符合日期格式）
     * @return long
     */
    public static int getSubDate(String dateFrom, String format,String afterDay) throws ParseException {
        return (int) ((datetimeTextToDate(afterDay,format).getTime() - datetimeTextToDate(dateFrom,format).getTime())/(24*60*60*1000));
    }

    /**
     * @Description: 根据传递的格式将字符串转换为日期
     * @param datetime
     * @param dateformat
     * @return java.util.Date
     */
    public static Date datetimeTextToDate(String datetime, String dateformat) throws ParseException {
        return new SimpleDateFormat(dateformat).parse(datetime);
    }

    /**
     * @Description: 字符串转换为日期，格式为 yyyy-MM-dd HH:mm:ss
     * @param datetime
     * @return java.util.Date
     */
    public static Date datetimeTextToDate(String datetime) throws ParseException {
        LocalDateTime localDateTime = LocalDateTime.parse(datetime, dateTimeFormatter);
        return Date.from(localDateTime.atZone(DEFAULT_ZONE.toZoneId()).toInstant());
    }

    public static Date convertUTCToCSTDate(String utcDate) {
        LocalDateTime localDateTime;
        try{
            localDateTime = LocalDateTime.parse(utcDate, utcDateFormatter);
        } catch (Exception e){
            localDateTime = ZonedDateTime.parse(utcDate).toLocalDateTime();
        }
        return Date.from(localDateTime.toInstant(ZoneOffset.UTC));
    }

    /**
     * 转换日期对象到 UTC 格式的字符串
     * @param date
     * @return
     * @throws ParseException
     */
    public static String convertDateToUTC(Date date) throws ParseException {
        return date.toInstant().atZone(UTC_ZONE.toZoneId()).toLocalDateTime().format(utcDateFormatter);
    }

    /**
     * @Description: 将 UTC 日期转换为 CST 日期
     * @param utcDate
     * @return java.lang.String
     */
    public static String convertUTCToCSTDateString(String utcDate) throws ParseException {
        return datetimeToString(convertUTCToCSTDate(utcDate));
    }

    public static void main(String[] args) throws ParseException {
        System.out.println("当前日期向前一天（CST）：" + UtilDateTime.getBeforeDate(UtilDateTime.DATE_MASK_FULL));
        System.out.println("当前日期向前三天（CST）：" + UtilDateTime.getBeforeDate(UtilDateTime.DATE_MASK_FULL, 3));
        System.out.println(" 五天后：" + UtilDateTime.getAfterDate(new Date(), UtilDateTime.DATE_MASK_FULL, 5));

        System.out.println(" 2020-08-12 与 2020-08-18 相差：" + UtilDateTime.getSubDate("2020-08-12", "yyyy-MM-dd", "2020-08-18")+" 天");
        System.out.println("2020-08-12 与 2020-08-18 之间的日期列表是：" + UtilDateTime.getIntervalDateString("yyyy-MM-dd","2020-08-12", "2020-08-18"));

        System.out.println(" 今天是：" + UtilDateTime.getDayNames(Locale.CHINA).get(UtilDateTime.getCurrentWeekNum()));
        System.out.println(" 本周是今年的第：" + UtilDateTime.getWeekOfYear(nowTimestamp(),DEFAULT_ZONE,DEFAULT_LOCAL)+" 周");
        System.out.println(" 现在是 " + UtilDateTime.getMonthNames(Locale.CHINA).get(UtilDateTime.getCurrentMonth()));
        System.out.println(" 五天前：" + UtilDateTime.getBeforeDate("yyyy-MM-dd", 5));
        System.out.println(" 上周结束日期：" + UtilDateTime.getBeforeWeekEndDate("yyyy-MM-dd"));
        System.out.println(" 上周起始日期：" + UtilDateTime.getBeforeWeekStartDate("yyyy-MM-dd"));
        System.out.println("转换 UTC 到 CST：" + UtilDateTime.convertUTCToCSTDateString("2019-05-20T19:00:00.000Z"));
        System.out.println("当前日期格式化：" + UtilDateTime.datetimeToString(new Date()));
        System.out.println("当前日期转换为 UTC 日期：" + UtilDateTime.convertDateToUTC(new Date()));
        System.out.println("周列表：" + UtilDateTime.getDayNames(DEFAULT_LOCAL));
        System.out.println("默认时区是：" + TimeZone.getDefault());
        System.out.println("字符串转换日期：" + UtilDateTime.datetimeTextToDate("2020-08-12 12:00:00"));

    }
}
