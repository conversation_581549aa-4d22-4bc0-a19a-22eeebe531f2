package com.dcap.utils;

import com.fasterxml.jackson.dataformat.yaml.util.StringQuotingChecker;

public class NonQuotingChecker extends StringQuotingChecker implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private final static NonQuotingChecker INSTANCE = new NonQuotingChecker();

    public NonQuotingChecker() { }

    public static NonQuotingChecker instance() { return INSTANCE; }

    /**
     * 重写以减少对字段名的引号添加
     * 只有在严格必要时才添加引号
     */
    @Override
    public boolean needToQuoteName(String name)
    {
        // 只有在空字符串或纯空格时才添加引号
        if (name == null || name.isEmpty()) {
            return true;
        }

        // 检查是否为保留关键字
        if (isReservedKeyword(name)) {
            return true;
        }

        // 检查首字符是否合法（不以特殊字符开头）
        char firstChar = name.charAt(0);
        if (firstChar == '!' || firstChar == '*' || firstChar == '&' ||
                firstChar == '{' || firstChar == '}' || firstChar == '[' ||
                firstChar == ']' || firstChar == ',' || firstChar == '#' ||
                firstChar == '?' || firstChar == '-') {
            return true;
        }

        // 检查是否包含冒号、空格或换行符，这些在YAML中需要引号
        return name.contains(":") || name.contains(" ") ||
                name.contains("\n") || name.contains("\r");
    }

    /**
     * 重写以减少对字符串值的引号添加
     * 对于正则表达式尤其有效
     */
    @Override
    public boolean needToQuoteValue(String value)
    {
        // 空值需要引号
        if (value == null || value.isEmpty()) {
            return true;
        }

        // 检查是否为保留关键字（true, false, null等）
        if (isReservedKeyword(value)) {
            return true;
        }

        // 检查字符串首尾字符，某些字符开头或结尾需要引号
        char firstChar = value.charAt(0);
        char lastChar = value.charAt(value.length() - 1);

        if (firstChar == ' ' || lastChar == ' ' ||  // 首尾空格
                firstChar == '#' ||                     // 注释符号
                firstChar == '&' || firstChar == '*' || // 引用符号
                firstChar == '!' || lastChar == ':' ||  // 标签和键值分隔符
                firstChar == '|' || firstChar == '>' || // 块标记
                firstChar == '{' || firstChar == '[' || // 集合开始
                (firstChar == '-' && value.length() > 1 && Character.isWhitespace(value.charAt(1)))) { // 序列项
            return true;
        }

        // 检查是否包含必须引用的特殊字符
        return value.contains("\n") || value.contains("\r") || // 换行符
                value.contains(": ") || value.contains(" #") || // 冒号后空格或注释
                value.equals("-") || value.equals("---") || value.equals("..."); // 文档分隔符
    }

    @Override
    protected boolean valueHasQuotableChar(String value) {
        // 只检查最基本的需要引号的情况
        int len = value.length();

        // 检查是否全是空格或首尾空格
        boolean allSpace = true;
        for (int i = 0; i < len; ++i) {
            if (value.charAt(i) != ' ') {
                allSpace = false;
                break;
            }
        }
        if (allSpace) {
            return true;
        }

        // 检查常见的需要引号的字符
        for (int i = 0; i < len; ++i) {
            char c = value.charAt(i);
            if (c == ':' && (i + 1 < len && value.charAt(i + 1) == ' ')) {
                return true; // 键值对分隔符
            }
            if (c == '#' && (i > 0 && value.charAt(i - 1) == ' ')) {
                return true; // 注释
            }
        }

        // 检查流式集合中可能导致歧义的字符
        return false;
    }

    @Override
    protected boolean nameHasQuotableChar(String name) {
        // 与valueHasQuotableChar类似，但对字段名更严格
        return name.contains(":") || name.contains(" ") ||
                name.contains("\n") || name.contains("\r");
    }
}
