package com.dcap.utils;

import org.apache.commons.lang3.tuple.Pair;
import org.apache.tika.Tika;
import org.apache.tika.exception.EncryptedDocumentException;
import org.apache.tika.exception.TikaException;
import org.apache.tika.io.TikaInputStream;
import org.apache.tika.langdetect.optimaize.OptimaizeLangDetector;
import org.apache.tika.language.detect.LanguageDetector;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.mime.MediaType;
import org.apache.tika.parser.AutoDetectParser;
import org.apache.tika.parser.Parser;
import org.apache.tika.parser.ParserDecorator;
import org.apache.tika.parser.csv.TextAndCSVParser;
import org.apache.tika.parser.epub.EpubParser;
import org.apache.tika.parser.html.HtmlParser;
import org.apache.tika.parser.iwork.IWorkPackageParser;
import org.apache.tika.parser.microsoft.OfficeParser;
import org.apache.tika.parser.microsoft.OldExcelParser;
import org.apache.tika.parser.microsoft.ooxml.OOXMLParser;
import org.apache.tika.parser.microsoft.rtf.RTFParser;
import org.apache.tika.parser.odf.OpenDocumentParser;
import org.apache.tika.parser.pdf.PDFParser;
import org.apache.tika.parser.txt.TXTParser;
import org.apache.tika.parser.xml.DcXMLParser;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public final class UtilTika {

    static final LanguageDetector detector = new OptimaizeLangDetector().loadModels();

    private static final Set<MediaType> EXCLUDES = new HashSet<>(
            Arrays.asList(
                    MediaType.application("vnd.ms-visio.drawing"),
                    MediaType.application("vnd.ms-visio.drawing.macroenabled.12"),
                    MediaType.application("vnd.ms-visio.stencil"),
                    MediaType.application("vnd.ms-visio.stencil.macroenabled.12"),
                    MediaType.application("vnd.ms-visio.template"),
                    MediaType.application("vnd.ms-visio.template.macroenabled.12"),
                    MediaType.application("vnd.ms-visio.drawing")
            )
    );

    /** 支持的 tika 解析器 */
    private static final Parser[] PARSERS = new Parser[] {
            new HtmlParser(),
            new RTFParser(),
            new PDFParser(),
            new TXTParser(),
            new OfficeParser(),
            new OldExcelParser(),
            new TextAndCSVParser(),
            ParserDecorator.withoutTypes(new OOXMLParser(), EXCLUDES),
            new OpenDocumentParser(),
            new IWorkPackageParser(),
            new DcXMLParser(),
            new EpubParser(),
    };

    /** 使用指定子集自动解析 */
    private static final AutoDetectParser PARSER_INSTANCE = new AutoDetectParser(PARSERS);

    /** 单例 tika 实例 */
    private static final Tika TIKA_INSTANCE = new Tika(PARSER_INSTANCE.getDetector(), PARSER_INSTANCE);

    /**
     * 用 tika 解析，在解析文档时抛出任何异常命中
     */
    public static Pair<Metadata, String> parse(final byte[] content, final Integer limit) throws TikaException, IOException {
        Metadata metadata = new Metadata();
        if(content == null){
            return Pair.of(metadata, "");
        }
        String contentText = TIKA_INSTANCE.parseToString(new ByteArrayInputStream(content), metadata, limit);
        return Pair.of(metadata,contentText);
    }

    public static Pair<Metadata, String> parse(final InputStream content, final Integer limit) {
        Metadata metadata = new Metadata();
        return parse(content, metadata, limit);
    }

    public static Pair<Metadata, String> parse(final InputStream content, final Metadata metadata, final Integer limit) {
        try {
            String contentText = TIKA_INSTANCE.parseToString(content, metadata, limit);
            return Pair.of(metadata,contentText);
        } catch(EncryptedDocumentException e){
            return Pair.of(metadata, "文档是加密的，无法解密。");
        } catch (Exception e){
            e.printStackTrace();
            return Pair.of(metadata, "");
        }
    }

    public static Pair<Metadata, String> parse(final File file, final Integer limit) throws IOException {
        Metadata metadata = new Metadata();
        if(file == null || !file.exists() || !(file.length() > 0)){
            return Pair.of(metadata, "");
        }
        InputStream stream = TikaInputStream.get(file, metadata);
        return  parse(stream, metadata, limit);
    }

    public static Pair<Metadata, String> parse(final File file, Metadata metadata, final Integer limit) throws IOException {
        if (metadata == null){
            metadata = new Metadata();
        }
        if(file == null || !file.exists() || !(file.length() > 0)){
            return Pair.of(metadata, "");
        }
        InputStream stream = TikaInputStream.get(file, metadata);
        return  parse(stream, metadata, limit);
    }

    public static Pair<Metadata, String> getMimeType(File file) throws IOException {
        Metadata metadata = new Metadata();
        String mimeType = TIKA_INSTANCE.detect(TikaInputStream.get(file, metadata),metadata);
        return Pair.of(metadata, mimeType);
    }

    public static Pair<Metadata, String> getMimeType(InputStream stream) throws IOException {
        return getMimeType(stream, new Metadata());
    }

    public static Pair<Metadata, String> getMimeType(InputStream stream, Metadata metadata) throws IOException {
        try(InputStream mimeTypeStream = stream){
            String mimeType = TIKA_INSTANCE.detect(mimeTypeStream, metadata);
            return Pair.of(metadata, mimeType);
        }
    }

    public static MediaType getMediaType(InputStream stream, Metadata metadata) throws IOException {
        try(InputStream mimeTypeStream = stream){
            MediaType detect = TIKA_INSTANCE.getDetector().detect(mimeTypeStream, metadata);
            return detect;
        }
    }

    public static String detectLanguage(String content) throws IOException {
        return detector.detect(content).getLanguage();
    }





}
