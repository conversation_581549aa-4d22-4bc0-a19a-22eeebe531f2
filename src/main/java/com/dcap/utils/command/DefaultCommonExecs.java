package com.dcap.utils.command;

import com.google.common.collect.Lists;
import org.apache.commons.exec.ExecuteWatchdog;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.List;


public class DefaultCommonExecs extends AbstractCommonExecs {
    /**
     * @param bin
     * @param arguments
     */
    public DefaultCommonExecs(Charset charset, String bin, List<String> arguments) {
        super(bin, arguments);
        if (charset != null) {
            this.setEncoding(charset.name());
        }
    }

    public DefaultCommonExecs(String bin, List<String> arguments) {
        super(bin, arguments);
    }

    public DefaultCommonExecs(String bin, String... arguments) {
        super(bin, Arrays.asList(arguments));
    }

    /* (non-Javadoc)
     * @see com.bingosoft.proxy.helper.AbstractCommonExecs#supportWatchdog()
     */
    @Override
    public boolean supportWatchdog() {
        // TODO implement AbstractCommonExecs.supportWatchdog
        return false;
    }

    /* (non-Javadoc)
     * @see com.bingosoft.proxy.helper.AbstractCommonExecs#getWatchdog()
     */
    @Override
    public ExecuteWatchdog getWatchdog() {
        // TODO implement AbstractCommonExecs.getWatchdog
        return null;
    }

    //提供这个编码即可
//    public String getEncoding() {
//        return "GBK";
//    }

    public static void main(String[] args) throws IOException {
        String bin = "ping";
        String arg1 = "127.0.0.1";
        AbstractCommonExecs executable = new TimeoutCommonExecs(bin, Lists.newArrayList(arg1));
        ExecResult execResult = executable.exec();
        System.out.println(execResult.getExitCode());
        System.out.println(execResult.getStdout());
        System.out.println(execResult.getStderr());
    }
}
