package com.dcap.utils.command;

import org.apache.commons.exec.ExecuteWatchdog;

import java.util.List;

public class TimeoutCommonExecs extends AbstractCommonExecs {

    private long timeout = 10 * 1000; // 10 seconds

    public TimeoutCommonExecs(String bin, List<String> arguments) {
        super(bin, arguments);
    }

    public TimeoutCommonExecs(String bin, List<String> arguments, long timeout) {
        super(bin, arguments);
        this.timeout = timeout;
    }

    public boolean supportWatchdog() {
        return true; // 使用监视狗 监视脚本执行超时的情况
    }

    public ExecuteWatchdog getWatchdog() {
        return new ExecuteWatchdog(this.timeout);
    }

    /**
     * @return the timeout
     */
    public long getTimeout() {
        return timeout;
    }

    /**
     * @param timeout the timeout to set
     */
    public void setTimeout(long timeout) {
        this.timeout = timeout;
    }

}
