package com.dcap.utils.command;

import org.apache.commons.lang3.StringUtils;

public class ExecResult {

    private int exitCode = -1;
    private String stdout;
    private String stderr;
    private String codeInfo;

    public int getExitCode() {
        return exitCode;
    }

    public void setExitCode(int exitCode) {
        this.exitCode = exitCode;
    }

    public String getStdout() {
        return stdout;
    }

    public void setStdout(String stdout) {
        this.stdout = stdout;
    }

    public String getStderr() {
        return stderr;
    }

    public void setStderr(String stderr) {
        this.stderr = stderr;
    }

    public String getCodeInfo() {
        return codeInfo;
    }

    public void setCodeInfo(String codeInfo) {
        this.codeInfo = codeInfo;
    }

    public boolean execSuccess(){
        String stdErr = getStderr();
        return exitCode == 0
                &&
                (StringUtils.isBlank(stdErr) ||
                        StringUtils.isBlank(
                                stdErr.replaceAll("\r\n","")
                                .replaceAll("\r","")
                                .replaceAll("\n","")
                        ));
    }
}
