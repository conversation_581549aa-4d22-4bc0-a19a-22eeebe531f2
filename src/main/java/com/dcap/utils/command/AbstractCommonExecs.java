package com.dcap.utils.command;

import org.apache.commons.exec.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.*;
import java.nio.charset.Charset;
import java.util.List;

abstract class AbstractCommonExecs {

    private static final Logger LOG = LoggerFactory.getLogger(AbstractCommonExecs.class);
    private final String module = this.getClass().getName();
    private static final String DEFAULT_ENCODING = guessConsoleEncoding();
    private String encoding = DEFAULT_ENCODING;

    private String bin;

    private List<String> arguments;

    public AbstractCommonExecs(String bin, List<String> arguments) {
        this.bin = bin;
        this.arguments = arguments;
    }

    public ExecResult exec() throws IOException {
        ExecResult execResult = new ExecResult();
        PipedOutputStream outputStream = new PipedOutputStream();
        PipedInputStream pis = new PipedInputStream(outputStream);
        ByteArrayOutputStream errorStream = new ByteArrayOutputStream();
        CodeInfoCallback codeInfoCb = new CodeInfoCallback();
        StdOutputCallback stdoutCb = new StdOutputCallback();
        ErrorOutputCallback stderrCb = new ErrorOutputCallback();
        String stdout;
        String stderr;
        try {
            Executor executor = getExecutor();
            CommandLine cmdLine = getCommandLine();
            LOG.info("Executing script " + cmdLine.toString());
            if (supportWatchdog()) {
                executor.setWatchdog(getWatchdog());
            }
            PumpStreamHandler streamHandler = new PumpStreamHandler(outputStream, errorStream);
            executor.setStreamHandler(streamHandler);
            int ret = executor.execute(cmdLine);
            readStdout(pis, stdoutCb, codeInfoCb);
            readErrorStream(errorStream, stderrCb);
            stdout = stdoutCb.getLines();
            stderr = stderrCb.getErrors();
            execResult.setStdout(stdout);
            execResult.setStderr(stderr);
            execResult.setCodeInfo(codeInfoCb.getCodeInfo());
            execResult.setExitCode(ret);
            return execResult;
        } catch (ExecuteException e) {
            LOG.error(e.getMessage());
            readStdout(pis, stdoutCb, codeInfoCb);
            pis.close();
            readErrorStream(errorStream, stderrCb);
            stdout = stdoutCb.getLines();
            stderr = stderrCb.getErrors();
            int ret = e.getExitValue();

            execResult.setStdout(stdout);
            execResult.setStderr(stderr);
            execResult.setCodeInfo(codeInfoCb.getCodeInfo());
            execResult.setExitCode(ret);
            return execResult;
        } finally {
            pis.close();
        }

    }

    /**
     * 接口回调的方式解析脚本的错误输出
     *
     * @param baos
     * @param cbs
     * @throws IOException
     */
    private void readErrorStream(ByteArrayOutputStream baos, OutputCallback... cbs) throws IOException {
        String err = baos.toString(getEncoding());
        for (OutputCallback cb : cbs) {
            cb.parse(err);
        }
    }

    /**
     * 接口回调的方式解析脚本的标准输出
     *
     * @param pis
     * @param cbs
     * @throws IOException
     */
    private void readStdout(PipedInputStream pis, OutputCallback... cbs) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(pis, getEncoding()));
        String line;
        while ((line = br.readLine()) != null) {
            for (OutputCallback cb : cbs) {
                cb.parse(line);
            }
        }
    }

    public Executor getExecutor() {
        Executor executor = new DefaultExecutor();
        executor.setWorkingDirectory(new File(this.bin).getParentFile());
        return executor;
    }

    public CommandLine getCommandLine() {
        CommandLine commandLine = new CommandLine(bin);
        for (String arg : arguments) {
            commandLine.addArgument(arg, false);
        }
        return commandLine;
    }

    protected String join(List<String> arguments) {
        if (arguments == null || arguments.isEmpty()) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (String arg : arguments) {
            sb.append(" ").append(arg);
        }
        return sb.toString();
    }

    /**
     * @return the encoding
     */
    protected String getEncoding() {
        return encoding;
    }

    /**
     * @param encoding the encoding to set
     */
    public void setEncoding(String encoding) {
        this.encoding = encoding;
    }

    /**
     * @return the bin
     */
    protected String getBin() {
        return bin;
    }

    /**
     * @param bin the bin to set
     */
    public void setBin(String bin) {
        this.bin = bin;
    }

    /**
     * @return the arguments
     */
    protected List<String> getArguments() {
        return arguments;
    }

    /**
     * @param arguments the arguments to set
     */
    public void setArguments(List<String> arguments) {
        this.arguments = arguments;
    }

    public abstract boolean supportWatchdog();

    public abstract ExecuteWatchdog getWatchdog();

    public static String guessConsoleEncoding() {
        String charsetName = null;
        try {
            charsetName = System.getProperty("sun.stdout.encoding");
            if (StringUtils.isNotBlank(charsetName)) {
                return charsetName;
            }
            charsetName = System.getProperty("sun.std.encoding");
            if (StringUtils.isNotBlank(charsetName)) {
                return charsetName;
            }
            charsetName = System.getProperty("sun.jnu.encoding");
            if (StringUtils.isNotBlank(charsetName)) {
                return charsetName;
            }
            charsetName = System.getProperty("sun.io.unicode.encoding");
            if (StringUtils.isNotBlank(charsetName)) {
                return charsetName;
            }
            charsetName = "UTF-8";
            if (StringUtils.isNotBlank(charsetName)) {
                return charsetName;
            }
            charsetName = Charset.defaultCharset().name();
        } catch (Exception ignored) {
        }
        return charsetName;
    }
}
