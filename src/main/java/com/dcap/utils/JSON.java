package com.dcap.utils;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.fasterxml.jackson.dataformat.yaml.YAMLGenerator;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.yd.dcap.common.utils.sm4.SM4Utils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.yaml.snakeyaml.DumperOptions;
import org.yaml.snakeyaml.LoaderOptions;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.Reader;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.Map;

public class JSON {

    private static final ObjectMapper mapper = new ObjectMapper();

    private static final ObjectMapper xmlMapper = new XmlMapper();

    private static final ObjectMapper mapperForYaml;

    private static final ObjectMapper singleValueAsArrayObjectMapper = singleValueAsArrayObjectMapper();

    static {
        LoaderOptions yamlLoad = new LoaderOptions();
        // 创建 DumperOptions 并配置
        DumperOptions options = new DumperOptions();
        options.setDefaultFlowStyle(DumperOptions.FlowStyle.BLOCK);
        options.setDefaultScalarStyle(DumperOptions.ScalarStyle.SINGLE_QUOTED);
        options.setWidth(Integer.MAX_VALUE); // 设置最大宽度，防止自动换行
        options.setSplitLines(false); // 禁用自动换行

        yamlLoad.setCodePointLimit(50 * 1024 * 1024);// 50MB
        YAMLFactory yamlFactory = YAMLFactory.builder()
                .dumperOptions(options)
                .disable(YAMLGenerator.Feature.SPLIT_LINES)
//                .stringQuotingChecker(new NonQuotingChecker())
//                .enable(YAMLGenerator.Feature.MINIMIZE_QUOTES) // 启用尽量不加引号
                .loaderOptions(yamlLoad)
                .build();

        mapperForYaml = new ObjectMapper(yamlFactory);

        SimpleDateFormat myDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        mapper.setDateFormat(myDateFormat);
        mapperForYaml.setDateFormat(myDateFormat);
        xmlMapper.setDateFormat(myDateFormat);


        mapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        mapperForYaml.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        xmlMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);


        // 允许单引号
        mapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        mapperForYaml.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        xmlMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);

        // 解析为对象时，忽略不知道的字段
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapperForYaml.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        xmlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // 漂亮打印
        mapper.enable(SerializationFeature.INDENT_OUTPUT);
        mapperForYaml.enable(SerializationFeature.INDENT_OUTPUT);
        xmlMapper.enable(SerializationFeature.INDENT_OUTPUT);

        // 允许反斜杠转义任何字符
        mapper.configure(JsonParser.Feature.ALLOW_BACKSLASH_ESCAPING_ANY_CHARACTER, true);
        mapperForYaml.configure(JsonParser.Feature.ALLOW_BACKSLASH_ESCAPING_ANY_CHARACTER, true);
        xmlMapper.configure(JsonParser.Feature.ALLOW_BACKSLASH_ESCAPING_ANY_CHARACTER, true);

        // 输入时将单个值作为数组
        xmlMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
    }

    private static ObjectMapper singleValueAsArrayObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        objectMapper.registerModule(new JavaTimeModule());

        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.PUBLIC_ONLY);

        // 允许属性名称没有引号
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);

        // 允许单引号
        objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);

        // null 字段将不会被序列化
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);

        // 漂亮打印
        objectMapper.enable(SerializationFeature.INDENT_OUTPUT);

        // 设置输入时忽略 JSON 字符串中存在而 Java 对象实际没有的属性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // 输入时将单个值作为数组
        objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);

        // 接收空字符串为 null
        objectMapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);

        SimpleDateFormat myDateFormat = new SimpleDateFormat(UtilDateTime.DATE_MASK_FULL);
        myDateFormat.setTimeZone(UtilDateTime.DEFAULT_ZONE);
        objectMapper.setTimeZone(UtilDateTime.DEFAULT_ZONE);
        objectMapper.setDateFormat(myDateFormat);
        return objectMapper;
    }

    /**
     * Creates a <code>JSON</code> instance from an <code>InputStream</code>.
     * The method assumes the character set is UTF-8.
     *
     * @param inStream
     * @return a <code>JSON</code> instance
     * @throws IOException
     */
    public static JSON from(InputStream inStream) throws IOException {
        String jsonString = IOUtils.toString(inStream, "UTF-8");
        return from(jsonString);
    }

    public static JSON from(Object object) {
        try {
            return from(mapper.writeValueAsString(object));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static JSON from(Reader reader) throws IOException {
        String jsonString = IOUtils.toString(reader);
        return from(jsonString);
    }

    public static JSON fromXml(String xmlText) {
        // TODO: Validate String
        return new JSON(xmlText, ContentType.XML);
    }

    public static JSON from(String jsonString) {
        // TODO: Validate String
        return new JSON(jsonString, ContentType.JSON);
    }

    public enum ContentType {
        JSON,XML,YAML
    }
    private final String content;
    private final ContentType contentType;

    private JSON(String content, ContentType contentType) {
        this.content = content;
        this.contentType = contentType;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof JSON) {
            return content.equals(((JSON)obj).content);
        }
        return false;
    }

    @Override
    public int hashCode() {
        return this.content!=null?this.content.hashCode():super.hashCode();
    }

    public <T> T toObject(Class<T> targetClass) {
        try {
            if (contentType == ContentType.XML){
                JsonNode jsonNode = xmlMapper.readTree(content.getBytes());
                return singleValueAsArrayObjectMapper.readValue(jsonNode.toString(), targetClass);
            } else {
                return mapperForYaml.readValue(content, targetClass);
            }
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }


    @Override
    public String toString() {
        return content;
    }

    public static void main(String[] args) throws IOException {
        System.out.println(SM4Utils.decCheckSm4ForEcb("ENC[c69a78bd1489a8854766a76f908c6e5c]"));
//        String s = convertToYaml(new File("C:\\Users\\<USER>\\Desktop\\model\\结构化\\职业.model"));
//        System.out.println(s);
//        Collection<File> files = FileUtils.listFiles(new File("C:\\Users\\<USER>\\Desktop\\model\\非结构化\\新建文件夹"), new String[]{"model"}, false);
//        for (File file : files) {
//            String content = FileUtils.readFileToString(file, Charset.defaultCharset());
//            String encFileName = file.getName().substring(0,file.getName().lastIndexOf("."))+"（非结构化）.model";
//            String encText = SM4Utils.encCheckSm4ForEcb(content);
//            FileUtils.write(new File(file.getParent()+"\\"+encFileName),encText, Charset.defaultCharset());
//        }
    }

    public static String convertToYaml(File jsonFile) throws IOException {
        String s = FileUtils.readFileToString(jsonFile, Charset.defaultCharset());
        return JSON.from(s).convertToYaml();
    }

    public String convertToYaml() {
        try {
            return mapperForYaml.writeValueAsString(toObject(Map.class));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static String xmlConvertToJson(String xml) {
        try {
            JsonNode jsonNode = xmlMapper.readTree(xml.getBytes());
            return singleValueAsArrayObjectMapper.writeValueAsString(jsonNode);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
