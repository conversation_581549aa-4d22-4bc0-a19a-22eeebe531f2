package com.dcap.utils;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.instrumentation.NoopInstrumentation;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import com.dcap.datalayer.ConnectionException;
import com.google.common.collect.Sets;
import com.mongodb.MongoClient;
import com.mongodb.MongoClientURI;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpHost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.message.BasicHeader;
import org.elasticsearch.client.RestClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;

@Slf4j
public class UtilDB {
    private static final Logger LOG = LoggerFactory.getLogger(UtilDB.class);
    // mysql,mariadb,mssql,pgsql,gplum,oracle,mongo,hive,dameng
    private static final Set<String> jdbcSet = Sets.newHashSet("oracle", "mssql", "mysql", "starrocks", "pgsql",
            "hologres", "oushudb",
            "vastbase", "tidb", "doris", "opengauss", "gaussdb", "polardb_mysql", "polardb_pg", "analyticdb_mysql",
            "analyticdb_pg", "tdsql_mysql", "tdsql_pg", "mariadb", "gplum", "transwarp_inceptor","transwarp_argodb", "hive", "sparksql",
            "dameng", "hana", "db2", "maxcompute", "impala", "ob", "oceanbase_oracle", "kingbase", "dremio", "sinodb", "clickhouse", "gbase8s",
            "gbase8a", "gbase8c", "highgo", "trino", "prestodb", "prestosql", "cache", "sybase", "redshift");

    public static boolean useJDBC(String dbType) {
        return jdbcSet.contains(dbType);
    }

    public static boolean useMongo(String dbType) {
        return "mongo".equals(dbType);
    }

    public static boolean useRedis(String dbType) {
        return "redis".equals(dbType);
    }

    public static boolean useElasticsearch(String dbType) {
        return "elasticsearch".equals(dbType);
    }


    public static boolean useDynamodb(String dbType) {
        return "dynamodb".equals(dbType);
    }

    public static void main(String[] args) throws ConnectionException, SQLException, InterruptedException {
        Connection connection = UtilDB.buildDatabaseConnection("redshift",
                "yuandian-workgroup.528010440907.cn-north-1.redshift-serverless.amazonaws.com.cn", 5439,
                "admin", "HMXNKrrex012!-", "dev");
        System.out.println(connection.getMetaData().getCatalogs());
        System.out.println(connection.getMetaData().getDatabaseProductName());
    }

    public static Connection buildDatabaseConnection(String databaseType, String databaseHost, int databasePort,
                                                     String username, String password, String databaseName) throws ConnectionException {
        if (Objects.equals("$YD_EMPTY$", password)) {
            password = "";
        }

        String url = buildUrl(databaseType, databaseHost, databasePort, databaseName);
        Connection connection;
        try {
            connection = buildConnection(databaseType, url, username, password);
            log.info("success connect to database[{}]", url);
        } catch (SQLException e) {
            throw new ConnectionException(e.getMessage()+"; Can not connect to database: " + url, e);
        }
        return connection;
    }
    private static Connection buildConnection(String databaseType, String url, String username, String password) throws SQLException {
        switch (databaseType) {
            case "transwarp_inceptor":
                return HiveJDBCUtil.getTranswarpInceptorConnection(url, username, password);
            case "transwarp_argodb":
                return HiveJDBCUtil.getTranswarpInceptorArgoConnection(url, username, password,null,null,null);
            case "hive":
            case "sparksql":
                return HiveJDBCUtil.getHiveConnection(url, username, password, null, null, null);
            default:
                Properties props = new Properties();
                props.setProperty("user", username);
                props.setProperty("password", password);
                return DriverManager.getConnection(url, props);
        }
    }
    private static String buildUrl(String databaseType, String databaseHost, int databasePort, String databaseName) throws ConnectionException {
        StringBuilder url = new StringBuilder();
        switch (databaseType) {
            case "doris":
                if (Objects.equals("internal", databaseName)){
                    databaseName = null;
                }
            case "mysql":
            case "tdsql_mysql":
            case "starrocks":
            case "mariadb":
            case "tidb":
            case "ob":
            case "analyticdb_mysql":
            case "polardb_mysql":
                String extCfg = "?autoReconnect=true&allowPublicKeyRetrieval=true&useSSL=false&zeroDateTimeBehavior=convertToNull";
                url.append("jdbc:mysql://").append(databaseHost).append(":").append(databasePort);
                if (StringUtils.isNotBlank(databaseName) && !"null".equalsIgnoreCase(databaseName)) {
                    if (databaseName.startsWith("?") || databaseName.startsWith("/")) {
                        url.append(databaseName);
                    } else {
                        url.append("/").append(databaseName);
                        if (!databaseName.contains("?")) {
                            url.append(extCfg);
                        }
                    }
                } else {
                    url.append(extCfg);
                }
                break;
            case "oceanbase_oracle":
                url.append("jdbc:oceanbase://").append(databaseHost).append(":").append(databasePort);
                if (StringUtils.isNotBlank(databaseName) && !"null".equalsIgnoreCase(databaseName)) {
                    url.append("/").append(databaseName);
                }
                break;
            case "oracle":
                url.append("jdbc:oracle:thin:@//").append(databaseHost).append(":").append(databasePort);
                if (StringUtils.isNotBlank(databaseName) && !"null".equalsIgnoreCase(databaseName)) {
                    url.append("/").append(databaseName);
                }
                break;
            case "mssql":
                // URL	****************************=127.0.0.1;port=12433;databaseName=master
                url.append("****************************=").append(databaseHost).append(";port=").append(databasePort);
                if (StringUtils.isNotBlank(databaseName) && !"null".equalsIgnoreCase(databaseName)) {
                    url.append(";databaseName=").append(databaseName);
                }
                break;
            case "dameng":
                String dmCfg = "?loginEncrypt=false";
                url.append("jdbc:dm://").append(databaseHost).append(":").append(databasePort);
                if (StringUtils.isNotBlank(databaseName) && !"null".equalsIgnoreCase(databaseName)) {
                    if (databaseName.startsWith("?")) {
                        url.append(databaseName);
                    } else {
                        if (databaseName.startsWith("/")) {
                            url.append(databaseName);
                        } else {
                            url.append("/").append(databaseName);
                        }
                        if (!databaseName.contains("?")) {
                            url.append(dmCfg);
                        }
                    }
                } else {
                    url.append(dmCfg);
                }
                if (!url.toString().contains("loginEncrypt")) {
                    if (url.toString().contains("?")) {
                        if (url.toString().endsWith("&")) {
                            url.append("loginEncrypt=false");
                        } else {
                            url.append("&loginEncrypt=false");
                        }
                    } else {
                        url.append(dmCfg);
                    }
                }
                break;
            case "kingbase":
                url.append("jdbc:kingbase8://").append(databaseHost).append(":").append(databasePort);
                if (StringUtils.isNotBlank(databaseName) && !"null".equalsIgnoreCase(databaseName)) {
                    url.append("/").append(databaseName);
                } else if (StringUtils.isBlank(databaseName)) {
                    url.append("/").append("postgres");
                }
                break;
            case "pgsql":
            case "vastbase":
            case "gplum":
            case "hologres":
            case "oushudb":
            case "tdsql_pg":
            case "analyticdb_pg":
            case "polardb_pg":
                url.append("jdbc:postgresql://").append(databaseHost).append(":").append(databasePort);
                if (StringUtils.isNotBlank(databaseName) && !"null".equalsIgnoreCase(databaseName)) {
                    url.append("/").append(databaseName);
                } else if (StringUtils.isBlank(databaseName)) {
                    url.append("/").append("postgres");
                }
                break;
            case "redshift":
                url.append("jdbc:redshift://").append(databaseHost).append(":").append(databasePort);
                if (StringUtils.isNotBlank(databaseName) && !"null".equalsIgnoreCase(databaseName)) {
                    url.append("/").append(databaseName);
                }
                break;
            case "highgo":
                url.append("jdbc:highgo://").append(databaseHost).append(":").append(databasePort);
                if (StringUtils.isNotBlank(databaseName) && !"null".equalsIgnoreCase(databaseName)) {
                    url.append("/").append(databaseName);
                } else if (StringUtils.isBlank(databaseName)) {
                    url.append("/").append("highgo");
                }
                break;
            case "gbase8c":
            case "opengauss":
            case "gaussdb":
                url.append("jdbc:opengauss://").append(databaseHost).append(":").append(databasePort);
                if (StringUtils.isNotBlank(databaseName) && !"null".equalsIgnoreCase(databaseName)) {
                    url.append("/").append(databaseName);
                } else if (StringUtils.isBlank(databaseName)) {
                    url.append("/").append("postgres");
                }
                break;
            case "hana":
                url.append("jdbc:sap://").append(databaseHost).append(":").append(databasePort);
                if (StringUtils.isNotBlank(databaseName) && !"null".equalsIgnoreCase(databaseName)) {
                    url.append("/").append(databaseName);
                }
                break;
            case "db2":
                if (StringUtils.isBlank(databaseName) || "null".equalsIgnoreCase(databaseName)) {
                    throw new ConnectionException("创建 DB2 连接必须提供 databaseName");
                }
                url.append("jdbc:db2://").append(databaseHost).append(":").append(databasePort).append("/").append(databaseName);
                break;
            case "transwarp_inceptor":
                url.append("jdbc:hive2://").append(databaseHost).append(":").append(databasePort);
                String extC = "?transaction.type=inceptor";
                if (StringUtils.isNotBlank(databaseName) && !"null".equalsIgnoreCase(databaseName)) {
                    if (databaseName.startsWith("?")) {
                        url.append(databaseName);
                    } else {
                        if (databaseName.startsWith("/")) {
                            url.append(databaseName);
                        } else {
                            url.append("/").append(databaseName);
                        }
                        if (!databaseName.contains("?")) {
                            url.append(extC);
                        }
                    }
                } else {
                    url.append(extC);
                }
                break;
            case "transwarp_argodb":
                url.append("jdbc:transwarp2://").append(databaseHost).append(":").append(databasePort);
                if (StringUtils.isNotBlank(databaseName) && !"null".equalsIgnoreCase(databaseName)) {
                    if (databaseName.startsWith("?")) {
                        url.append(databaseName);
                    } else {
                        if (databaseName.startsWith("/")) {
                            url.append(databaseName);
                        } else {
                            url.append("/").append(databaseName);
                        }
                    }
                }
                break;
            case "hive":
            case "sparksql":
                url.append("jdbc:hive2://").append(databaseHost).append(":").append(databasePort);
                if (StringUtils.isNotBlank(databaseName) && !"null".equalsIgnoreCase(databaseName)) {
                    url.append("/").append(databaseName);
                }
                break;
            case "maxcompute":
                url.append("jdbc:odps:http://").append(databaseHost).append(":").append(databasePort).append("/api?project=").append(databaseName);
                break;
            case "impala":
                url.append("jdbc:impala://").append(databaseHost).append(":").append(databasePort);
                if (StringUtils.isNotBlank(databaseName)) {
                    databaseName = databaseName.replaceAll("\\?", ";");
                    if (databaseName.startsWith(";")) {
                        url.append(databaseName);
                    } else {
                        if (databaseName.startsWith("/")) {
                            url.append(databaseName);
                        } else {
                            url.append("/").append(databaseName);
                        }
                    }
                }
                break;
            case "dremio":
                String exCfg = ";disableCertificateVerification=true;useEncryption=false";
                url.append("jdbc:dremio:direct=").append(databaseHost).append(":").append(databasePort).append(exCfg);
                break;
            case "sinodb":
                // jdbc:informix-sqli://*************:14701/testdb:INFORMIXSERVER=ol_sinodb1210
                url.append("jdbc:informix-sqli://").append(databaseHost).append(":").append(databasePort);
                if (StringUtils.isNotBlank(databaseName) && !"null".equalsIgnoreCase(databaseName)) {
                    if (databaseName.startsWith("/")) {
                        url.append(databaseName);
                    } else {
                        url.append("/").append(databaseName);
                    }
                }
                break;
            case "gbase8s":
                // jdbc:gbasedbt-sqli://************:19088/testdb:GBASEDBTSERVER=gbase01;DB_LOCALE=zh_CN.utf8;CLIENT_LOCALE=zh_CN.utf8;IFX_LOCK_MODE_WAIT=30;
                url.append("jdbc:gbasedbt-sqli://").append(databaseHost).append(":").append(databasePort);
                if (StringUtils.isNotBlank(databaseName) && !"null".equalsIgnoreCase(databaseName)) {
                    if (databaseName.startsWith("/")) {
                        url.append(databaseName);
                    } else {
                        url.append("/").append(databaseName);
                    }
                }
                try {
                    Class.forName("com.gbasedbt.jdbc.Driver");
                } catch (ClassNotFoundException e) {
                    throw new RuntimeException(e);
                }
                break;
            case "clickhouse":
                url.append("jdbc:clickhouse://").append(databaseHost).append(":").append(databasePort);
                if (StringUtils.isNotBlank(databaseName) && !"null".equalsIgnoreCase(databaseName)) {
                    if (databaseName.startsWith("/")) {
                        url.append(databaseName);
                    } else {
                        url.append("/").append(databaseName);
                    }
                }
                break;
            case "gbase8a":
                url.append("jdbc:gbase://").append(databaseHost).append(":").append(databasePort);
                if (StringUtils.isNotBlank(databaseName) && !"null".equalsIgnoreCase(databaseName)) {
                    if (databaseName.startsWith("?")) {
                        url.append(databaseName);
                    } else {
                        if (databaseName.startsWith("/")) {
                            url.append(databaseName);
                        } else {
                            url.append("/").append(databaseName);
                        }
                    }
                }
                break;
            case "trino":
                url.append("jdbc:trino://").append(databaseHost).append(":").append(databasePort);
                if (StringUtils.isNotBlank(databaseName) && !"null".equalsIgnoreCase(databaseName)) {
                    if (databaseName.startsWith("?")) {
                        url.append(databaseName);
                    } else {
                        if (databaseName.startsWith("/")) {
                            url.append(databaseName);
                        } else {
                            url.append("/").append(databaseName);
                        }
                    }
                }
                break;
            case "prestodb":
            case "prestosql":
                url.append("jdbc:presto://").append(databaseHost).append(":").append(databasePort);
                if (StringUtils.isNotBlank(databaseName) && !"null".equalsIgnoreCase(databaseName)) {
                    if (databaseName.startsWith("?")) {
                        url.append(databaseName);
                    } else {
                        if (databaseName.startsWith("/")) {
                            url.append(databaseName);
                        } else {
                            url.append("/").append(databaseName);
                        }
                    }
                }
                break;
            case "cache":
                url.append("jdbc:Cache://").append(databaseHost).append(":").append(databasePort);
                if (StringUtils.isNotBlank(databaseName) && !"null".equalsIgnoreCase(databaseName)) {
                    if (databaseName.startsWith("?")) {
                        url.append(databaseName);
                    } else {
                        if (databaseName.startsWith("/")) {
                            url.append(databaseName);
                        } else {
                            url.append("/").append(databaseName);
                        }
                    }
                }
                break;
            case "sybase":
                url.append("jdbc:sybase:Tds:").append(databaseHost).append(":").append(databasePort);
                if (StringUtils.isNotBlank(databaseName) && !"null".equalsIgnoreCase(databaseName)) {
                    if (databaseName.startsWith("?")) {
                        url.append(databaseName);
                    } else {
                        url.append("?ServiceName=").append(databaseName);
                    }
                }
                break;
        }
        return url.toString();
    }

    public static MongoClient buildMongoClient(String host, int port, String username, String password, String extraCfg) {
        if (StringUtils.isBlank(extraCfg)) {
            extraCfg = "?maxPoolSize=1";
        }
        String encodePassword = URLEncoder.encode(password, Charset.defaultCharset());
        MongoClientURI uri = new MongoClientURI("mongodb://" + username + ":" + encodePassword + "@" + host + ":" + port + "/" + extraCfg);
        return new MongoClient(uri);
    }

    public static ElasticsearchClient buildElasticsearchClient(String host, int port, String username, String password) {
        ElasticsearchClient esClient;
        try {
            String serverUrl = "https://" + host + ":" + port;
            String apiKey = Base64Util.Encrypt(username + ":" + password);
            esClient = esClient(serverUrl, apiKey);
        } catch (Exception e) {
            LOG.error("getElasticsearchClient error", e);
            // 再试试 http
            String serverUrl = "http://" + host + ":" + port;
            String apiKey = Base64Util.Encrypt(username + ":" + password);
            try {
                esClient = esClient(serverUrl, apiKey);
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            }
        }
        return esClient;
    }


    private static ElasticsearchClient esClient(String serverUrl, String apiKey) throws IOException {
        // URL and API key
//        String serverUrl = "https://app-alpha.yuandiansec.net:30200";
//        String apiKey = "ZWxhc3RpYzp5dWFuZGlhbiMx";

        // Create the low-level client
        RestClient restClient = RestClient.builder(HttpHost.create(serverUrl)).setDefaultHeaders(new Header[]{new BasicHeader("Authorization", "Basic " + apiKey)}).setHttpClientConfigCallback(httpClientBuilder -> {
            SSLContext sslcontext;
            try {
                sslcontext = createIgnoreVerifySSL();
            } catch (NoSuchAlgorithmException | KeyManagementException e) {
                throw new RuntimeException(e);
            }
            HostnameVerifier defaultVerifier = new NoopHostnameVerifier();
            return httpClientBuilder.setSSLContext(sslcontext).setSSLHostnameVerifier(defaultVerifier);
        }).build();

        // Create the transport with a Jackson mapper
        ElasticsearchTransport transport = new RestClientTransport(restClient, new JacksonJsonpMapper(), null, NoopInstrumentation.INSTANCE);

        // And create the API client
        ElasticsearchClient elasticsearchClient = new ElasticsearchClient(transport);
        LOG.info("url {} connect success {}", serverUrl, elasticsearchClient.info().name());
        return elasticsearchClient;
    }

    private static SSLContext createIgnoreVerifySSL() throws NoSuchAlgorithmException, KeyManagementException {
        SSLContext sc = SSLContext.getInstance("TLSv1.2");

        // 实现一个X509TrustManager接口，用于绕过验证，不用修改里面的方法
        X509TrustManager trustManager = new X509TrustManager() {
            @Override
            public void checkClientTrusted(java.security.cert.X509Certificate[] paramArrayOfX509Certificate, String paramString) throws CertificateException {
            }

            @Override
            public void checkServerTrusted(java.security.cert.X509Certificate[] paramArrayOfX509Certificate, String paramString) throws CertificateException {
            }

            @Override
            public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                return null;
            }
        };

        sc.init(null, new TrustManager[]{trustManager}, null);
        return sc;
    }

    public static DynamoDbClient buildDynamoDbClient(String username, String password, String region) {
        if (StringUtils.isBlank(username)) {
            throw new IllegalArgumentException("缺少 access key");
        }
        if (StringUtils.isBlank(password)) {
            throw new IllegalArgumentException("缺少 access secret");
        }
        if (StringUtils.isBlank(region)) {
            throw new IllegalArgumentException("缺少 region");
        }
        // 创建凭证提供者
        AwsCredentialsProvider credentialsProvider = StaticCredentialsProvider.create(AwsBasicCredentials.create(username, password));
        return DynamoDbClient.builder().region(Region.of(region.toLowerCase())).credentialsProvider(credentialsProvider).build();
    }

}
