/*
 * @Descripttion: YD Security 
 * @version: 1.0
 * @Author: frank
 * @Date: 2021-07-31 03:03:07
 * @LastEditors: frank
 * @LastEditTime: 2021-08-02 18:25:02
 */
package com.dcap.utils;

import java.util.Base64;

public class Base64Util {
    public static String Encrypt(String content) {
        return Base64.getEncoder().encodeToString(content.getBytes());
    }
    public static String Decrypt(String content) {
        byte[] base64decodedBytes = Base64.getDecoder().decode(content);
        return new String(base64decodedBytes);
    }
    public static byte[] Encrypt_Bytes(byte[] bytes) {
        return Base64.getEncoder().encode(bytes);
    }
    public static byte[] Decrypt_Bytes(byte[] bytes) {
        return  Base64.getDecoder().decode(bytes);
    }
}