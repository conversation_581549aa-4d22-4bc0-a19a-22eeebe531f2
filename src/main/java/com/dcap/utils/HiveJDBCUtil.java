package com.dcap.utils;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.security.UserGroupInformation;
import org.apache.kerby.kerberos.kerb.client.KrbConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import sun.security.krb5.Config;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLClassLoader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.sql.Connection;
import java.sql.Driver;
import java.util.HashSet;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;

public final class HiveJDBCUtil {

    private static final Logger LOG = LoggerFactory.getLogger(HiveJDBCUtil.class);

    private static final String [] HIVE_2_LIBS= new String[]{
            "commons-codec.jar","commons-collections.jar","commons-lang.jar","commons-lang3.jar","commons-logging.jar","commons-math.jar","curator-client.jar","curator-framework.jar","curator-recipes.jar","disruptor.jar","dropwizard-metrics-hadoop-metrics2-reporter.jar","fastutil.jar","findbugs-annotations.jar","guava.jar","hbase-annotations.jar","hbase-client.jar","hbase-common-tests.jar","hbase-common.jar","hbase-hadoop-compat.jar","hbase-hadoop2-compat.jar","hbase-prefix-tree.jar","hbase-procedure.jar","hbase-protocol.jar","hbase-server.jar","hive-common.jar","hive-jdbc.jar","hive-llap-client.jar","hive-llap-common-tests.jar","hive-llap-common.jar","hive-llap-server.jar","hive-llap-tez.jar","hive-metastore.jar","hive-serde.jar","hive-service-rpc.jar","hive-service.jar","hive-shims-**********.jar","hive-shims-common.jar","hive-shims-scheduler.jar","hive-shims.jar","hive-storage-api.jar","httpclient.jar","httpcore.jar","jackson-annotations.jar","jackson-core.jar","jackson-databind.jar","jamon-runtime.jar","javax.servlet.jar","javolution.jar","jcodings.jar","joni.jar","libfb303.jar","libthrift.jar","metrics-core.jar","parquet-hadoop-bundle.jar","slf4j-api.jar","tephra-api.jar","tephra-core.jar","tephra-hbase-compat-1.0.jar","twill-api-incubating.jar","twill-common-incubating.jar","twill-core-incubating.jar","twill-discovery-api-incubating.jar","twill-discovery-core-incubating.jar","twill-zookeeper-incubating.jar","zookeeper.jar"
    };

    private static final String [] HIVE_2_1_LIBS= new String[]{
            "ant-launcher.jar","ant.jar","antlr-runtime.jar","aopalliance-repackaged.jar","aopalliance.jar","apache-jsp.jar","apache-jstl.jar","asm-commons.jar","asm-tree.jar","asm.jar","audience-annotations.jar","avro.jar","bcpkix-jdk15on.jar","bcprov-jdk15on.jar","bonecp.jar","commons-beanutils.jar","commons-cli.jar","commons-codec.jar","commons-collections.jar","commons-compress.jar","commons-configuration2.jar","commons-crypto.jar","commons-daemon.jar","commons-dbcp.jar","commons-el.jar","commons-httpclient.jar","commons-io.jar","commons-lang.jar","commons-lang3.jar","commons-logging.jar","commons-math3.jar","commons-net.jar","commons-pool.jar","curator-client.jar","curator-framework.jar","curator-recipes.jar","datanucleus-api-jdo.jar","datanucleus-core.jar","datanucleus-rdbms.jar","derby.jar","disruptor.jar","dropwizard-metrics-hadoop-metrics2-reporter.jar","ecj.jar","ehcache.jar","error_prone_annotations.jar","fastutil.jar","findbugs-annotations.jar","fst.jar","geronimo-jcache_1.0_spec.jar","gson.jar","guava.jar","guice-assistedinject.jar","guice-servlet.jar","guice.jar","hadoop-annotations.jar","hadoop-auth.jar","hadoop-client.jar","hadoop-common.jar","hadoop-distcp.jar","hadoop-hdfs-client.jar","hadoop-hdfs.jar","hadoop-mapreduce-client-app.jar","hadoop-mapreduce-client-common.jar","hadoop-mapreduce-client-core.jar","hadoop-mapreduce-client-jobclient.jar","hadoop-mapreduce-client-shuffle.jar","hadoop-yarn-api.jar","hadoop-yarn-client.jar","hadoop-yarn-common.jar","hadoop-yarn-registry.jar","hadoop-yarn-server-applicationhistoryservice.jar","hadoop-yarn-server-common.jar","hadoop-yarn-server-resourcemanager.jar","hadoop-yarn-server-web-proxy.jar","hamcrest-core.jar","hbase-client.jar","hbase-common.jar","hbase-hadoop-compat.jar","hbase-hadoop2-compat.jar","hbase-http.jar","hbase-mapreduce.jar","hbase-metrics-api.jar","hbase-metrics.jar","hbase-procedure.jar","hbase-protocol-shaded.jar","hbase-protocol.jar","hbase-replication.jar","hbase-server.jar","hbase-shaded-miscellaneous.jar","hbase-shaded-netty.jar","hbase-shaded-protobuf.jar","hbase-zookeeper.jar","HikariCP-java7.jar","HikariCP.jar","hive-classification.jar","hive-common.jar","hive-jdbc.jar","hive-llap-client.jar","hive-llap-common.jar","hive-llap-server.jar","hive-llap-tez.jar","hive-metastore.jar","hive-orc.jar","hive-serde.jar","hive-service-rpc.jar","hive-service.jar","hive-shims-0.23.jar","hive-shims-common.jar","hive-shims-scheduler.jar","hive-shims.jar","hive-storage-api.jar","hk2-api.jar","hk2-locator.jar","hk2-utils.jar","htrace-core4.jar","httpclient.jar","httpcore.jar","jackson-annotations.jar","jackson-core-asl.jar","jackson-core.jar","jackson-databind.jar","jackson-jaxrs-base.jar","jackson-jaxrs-json-provider.jar","jackson-jaxrs.jar","jackson-mapper-asl.jar","jackson-module-jaxb-annotations.jar","jackson-xc.jar","jamon-runtime.jar","jasper-compiler.jar","jasper-runtime.jar","java-util.jar","javassist.jar","javax.activation-api.jar","javax.annotation-api.jar","javax.el.jar","javax.inject.jar","javax.jdo.jar","javax.servlet-api.jar","javax.servlet.jsp-api.jar","javax.servlet.jsp.jar","javax.ws.rs-api.jar","javolution.jar","jaxb-api.jar","jaxb-impl.jar","jcip-annotations.jar","jcodings.jar","jcommander.jar","jdk.tools.jar","jdo-api.jar","jersey-client.jar","jersey-common.jar","jersey-container-servlet-core.jar","jersey-core.jar","jersey-guava.jar","jersey-guice.jar","jersey-json.jar","jersey-media-jaxb.jar","jersey-server.jar","jersey-servlet.jar","jettison.jar","jetty-annotations.jar","jetty-client.jar","jetty-http.jar","jetty-io.jar","jetty-jaas.jar","jetty-jndi.jar","jetty-plus.jar","jetty-rewrite.jar","jetty-runner.jar","jetty-schemas.jar","jetty-security.jar","jetty-server.jar","jetty-servlet.jar","jetty-util-ajax.jar","jetty-util.jar","jetty-webapp.jar","jetty-xml.jar","jetty.jar","jline.jar","joda-time.jar","joni.jar","jpam.jar","jsch.jar","json-io.jar","json.jar","jsp-api.jar","jsr305.jar","jsr311-api.jar","jta.jar","junit.jar","kerb-admin.jar","kerb-client.jar","kerb-common.jar","kerb-core.jar","kerb-crypto.jar","kerb-identity.jar","kerb-server.jar","kerb-simplekdc.jar","kerb-util.jar","kerby-asn1.jar","kerby-config.jar","kerby-pkix.jar","kerby-util.jar","kerby-xdr.jar","leveldbjni-all.jar","libfb303.jar","libthrift.jar","log4j-1.2-api.jar","log4j-api.jar","log4j-core.jar","log4j-slf4j-impl.jar","log4j-web.jar","log4j.jar","metrics-core.jar","metrics-json.jar","metrics-jvm.jar","mssql-jdbc.jar","netty-all.jar","netty.jar","nimbus-jose-jwt.jar","objenesis.jar","okhttp.jar","okio.jar","opencsv.jar","osgi-resource-locator.jar","paranamer.jar","parquet-hadoop-bundle.jar","protobuf-java.jar","re2j.jar","slf4j-api.jar","slf4j-log4j12.jar","slider-core.jar","snappy-java.jar","snappy.jar","stax2-api.jar","taglibs-standard-impl.jar","taglibs-standard-spec.jar","tephra-api.jar","tephra-core.jar","tephra-hbase-compat-1.0.jar","transaction-api.jar","twill-api.jar","twill-common.jar","twill-core.jar","twill-discovery-api.jar","twill-discovery-core.jar","twill-zookeeper.jar","validation-api.jar","websocket-api.jar","websocket-client.jar","websocket-common.jar","websocket-server.jar","websocket-servlet.jar","woodstox-core.jar","xz.jar","zookeeper.jar"
    };

    private static final String [] HIVE_1_1_LIBS = new String[]{
            "hive-jdbc-uber-2.5.0.0-1245.jar"
    };

    private static final String [] HIVE_1_1_0_LIBS = new String[]{
            "hive-jdbc-uber-1.0.jar"
    };

    private static final String [] HIVE_1_LIBS = new String[]{
            "apache-log4j-extras.jar","apacheds-i18n.jar","apacheds-kerberos-codec.jar","api-asn1-api.jar","api-util.jar","asm.jar","avro.jar","commons-beanutils-core.jar","commons-beanutils.jar","commons-cli.jar","commons-codec.jar","commons-collections.jar","commons-compress.jar","commons-configuration.jar","commons-digester.jar","commons-el.jar","commons-httpclient.jar","commons-io.jar","commons-lang.jar","commons-logging.jar","commons-math3.jar","commons-net.jar","curator-client.jar","curator-framework.jar","curator-recipes.jar","gson.jar","guava.jar","hadoop-annotations.jar","hadoop-auth.jar","hadoop-common.jar","hadoop-yarn-api.jar","hadoop-yarn-common.jar","hadoop-yarn-server-applicationhistoryservice.jar","hadoop-yarn-server-common.jar","hadoop-yarn-server-resourcemanager.jar","hadoop-yarn-server-web-proxy.jar","hive-common.jar","hive-jdbc.jar","hive-metastore.jar","hive-serde.jar","hive-service.jar","hive-shims-9.99.jar","hive-shims-9.99S.jar","hive-shims-common.jar","hive-shims-scheduler.jar","hive-shims.jar","htrace-core.jar","httpclient.jar","httpcore.jar","jackson-core-asl.jar","jackson-jaxrs.jar","jackson-mapper-asl.jar","jackson-xc.jar","jasper-compiler.jar","jasper-runtime.jar","java-xmlbuilder.jar","javax.activation-api.jar","jaxb-api.jar","jaxb-impl.jar","jersey-core.jar","jersey-json.jar","jersey-server.jar","jets3t.jar","jettison.jar","jsch.jar","json.jar","jsp-api.jar","libfb303.jar","libthrift.jar","log4j.jar","netty.jar","paranamer.jar","servlet-api.jar","slf4j-api.jar","slf4j-log4j12.jar","snappy-java.jar","xmlenc.jar","xz.jar","zookeeper.jar"
    };

    private static final String [] TRANSWARP_INCEPTOR_LIBS = new String[]{
            "inceptor-driver.jar"
    };

    private static final String [] TRANSWARP_INCEPTOR_ARGO_LIBS = new String[]{
            "inceptor-driver-8.31.2.jar"
    };

    private final URLClassLoader hive1ClassLoader;

    private final URLClassLoader hive11ClassLoader;

    private final URLClassLoader hive2ClassLoader;

    private final URLClassLoader hive21ClassLoader;

    private final URLClassLoader transwarpInceptorClassLoader;

    private final URLClassLoader transwarpInceptorArgoClassLoader;

    private final URLClassLoader hive110ClassLoader;

    private HiveJDBCUtil(URLClassLoader hive1ClassLoader, URLClassLoader hive11ClassLoader, URLClassLoader hive2ClassLoader,
                         URLClassLoader hive21ClassLoader, URLClassLoader transwarpInceptorClassLoader,
                         URLClassLoader transwarpInceptorArgoClassLoader,
                         URLClassLoader hive110ClassLoader) {
        this.hive1ClassLoader = hive1ClassLoader;
        this.hive11ClassLoader = hive11ClassLoader;
        this.hive2ClassLoader = hive2ClassLoader;
        this.hive21ClassLoader = hive21ClassLoader;
        this.transwarpInceptorClassLoader = transwarpInceptorClassLoader;
        this.transwarpInceptorArgoClassLoader = transwarpInceptorArgoClassLoader;
        this.hive110ClassLoader = hive110ClassLoader;
    }

    private static class HiveJDBCHolder {
        private static final HiveJDBCUtil HIVE_JDBC_UTIL_INSTANCE = createJdbcUtilInstance();
    }

    private static HiveJDBCUtil createJdbcUtilInstance()  {
        try {
            URLClassLoader hive1ClassLoader = createClassLoader("hive1");
            URLClassLoader hive11ClassLoader = createClassLoader("hive11");
            URLClassLoader hive2ClassLoader = createClassLoader("hive2");
            URLClassLoader hive21ClassLoader = createClassLoader("hive21");
            URLClassLoader transwarpInceptor = createClassLoader("transwarp_inceptor");
            URLClassLoader transwarpArgoInceptor = createClassLoader("transwarp_inceptor_argo");
            URLClassLoader hive110ClassLoader = createClassLoader("hive110");
            return new HiveJDBCUtil(hive1ClassLoader, hive11ClassLoader, hive2ClassLoader, hive21ClassLoader, transwarpInceptor,
                    transwarpArgoInceptor,
                    hive110ClassLoader);
        } catch (Exception e){
            LOG.error(e.getMessage(),e);
        }
        return null;
    }

    public static HiveJDBCUtil getInstance(){
        return HiveJDBCHolder.HIVE_JDBC_UTIL_INSTANCE;
    }

    public static Connection getHiveConnection(String url, String userName, String password, String authMethod, String krb5conf,
                                               String servicePrincipal){
        HiveJDBCUtil instance = HiveJDBCUtil.getInstance();
        if (instance == null){
            throw new RuntimeException("加载 hive jdbc util 失败");
        }
        Connection hiveConnection;

        try {
            hiveConnection = instance.getHive21Connection(url, userName, password,authMethod,krb5conf,servicePrincipal);
            if (hiveConnection != null){
                LOG.info("创建连接 hive 2.1.1 成功。");
                return hiveConnection;
            }
            LOG.error("创建连接 hive 2.1.1 失败。");
        } catch (Exception e){
            LOG.error("创建连接 hive 2.1.1 失败：", e);
        }

        try {
            hiveConnection = instance.getHive2Connection(url, userName, password,authMethod,krb5conf,servicePrincipal);
            if (hiveConnection != null){
                LOG.info("创建连接 hive 2 成功。");
                return hiveConnection;
            }
            LOG.error("创建连接 hive 2 失败。");
        } catch (Exception e){
            LOG.error("创建连接 hive 2 失败：", e);
        }

        try {
            LOG.error("尝试创建 hive 1.1.0 连接。");
            hiveConnection = instance.getHive110Connection(url, userName, password,authMethod,krb5conf,servicePrincipal);
            if (hiveConnection != null){
                LOG.info("创建连接 hive 1.1.0 成功。");
                return hiveConnection;
            }
            LOG.error("创建连接 hive 1.1.0 失败。");
        } catch (Exception e){
            LOG.error("创建连接 hive 1.1.0 失败：", e);
        }

        try{
            LOG.error("尝试创建 hive1 连接。");
            hiveConnection = instance.getHive1Connection(url, userName, password,authMethod,krb5conf,servicePrincipal);
            if (hiveConnection != null){
                LOG.info("创建连接 hive 1 成功。");
                return hiveConnection;
            }
            LOG.error("创建连接 hive 1 失败。");
        } catch (Exception e){
            LOG.error("创建连接 hive 1 失败：", e);
        }

        try {
            LOG.error("尝试创建 hive1.1 连接。");
            hiveConnection = instance.getHive11Connection(url, userName, password,authMethod,krb5conf,servicePrincipal);
            if (hiveConnection != null){
                LOG.info("创建连接 hive 1.1 成功。");
                return hiveConnection;
            }
            LOG.error("创建连接 hive 1.1 失败。");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        throw new RuntimeException("创建 hive 连接失败。");
    }

    private Connection getHive1Connection(String url, String userName, String password, String authMethod, String krb5conf,
                                          String servicePrincipal) throws Exception{
        return buildHiveConnection(hive1ClassLoader, url, userName, password, authMethod, krb5conf, servicePrincipal);
    }
    private Connection getHive11Connection(String url, String userName, String password, String authMethod, String krb5conf,
                                           String servicePrincipal) throws Exception{
        return buildHiveConnection(hive11ClassLoader, url, userName, password, authMethod, krb5conf, servicePrincipal);
    }

    private Connection getHive2Connection(String url, String userName, String password, String authMethod, String krb5conf,
                                          String servicePrincipal) throws Exception{
        return buildHiveConnection(hive2ClassLoader, url, userName, password, authMethod, krb5conf, servicePrincipal);
    }

    private Connection getHive21Connection(String url, String userName, String password, String authMethod, String krb5conf,
                                          String servicePrincipal) throws Exception{
        return buildHiveConnection(hive21ClassLoader, url, userName, password, authMethod, krb5conf, servicePrincipal);
    }

    private Connection getHive110Connection(String url, String userName, String password, String authMethod, String krb5conf,
                                           String servicePrincipal) throws Exception{
        return buildHiveConnection(hive110ClassLoader, url, userName, password, authMethod, krb5conf, servicePrincipal);
    }

    private Connection buildArgoConnection(ClassLoader classLoader, String url,
                                           String userName, String password,
                                           String authMethod, String krb5conf,
                                           String servicePrincipal) throws Exception{
        Class<?> cls = classLoader.loadClass("org.apache.hive.jdbc.HiveDriver");
        // 实例化 driver对象
        Driver driver = (Driver) cls.newInstance();
        Properties info = new Properties();
        if (Objects.equals(authMethod,"KERBEROS")){
            String keyTabPath = KerberosUtil.getKeyTabPath(userName, password);
            String krb5ConfPath = KerberosUtil.getKrb5ConfPath(krb5conf);
            krb5ConfPath = krb5ConfPath.replaceAll("\\\\","/");
            KrbConfig krbConfig = KerberosUtil.loadKrb5Conf(krb5ConfPath);
            if (!servicePrincipal.contains("@")){
                String kdcRealm = krbConfig.getKdcRealm();
                servicePrincipal += "@" + kdcRealm;
            }

            if (url.endsWith(";")){
                url += "principal=" + servicePrincipal+";"+"kuser="+userName+";authentication=kerberos;"+"keytab="+keyTabPath+";krb5conf="+krb5ConfPath+";";
            } else if (url.matches(".*\\d$")){
                url += "/;principal=" + servicePrincipal+";"+"kuser="+userName+";authentication=kerberos;"+"keytab="+keyTabPath+";krb5conf="+krb5ConfPath+";";
            } else {
                url += ";principal=" + servicePrincipal+";"+"kuser="+userName+";authentication=kerberos;"+"keytab="+keyTabPath+";krb5conf="+krb5ConfPath+";";
            }
        } else {
            info.setProperty("user", userName);
            info.setProperty("password",password);
        }

        try {
            // 获取连接对象
            return driver.connect(url, info);
        } catch (Exception e) {
            e.printStackTrace();
            LOG.error("连接 hive 失败：{}", e.getMessage());
            return null;
        }
    }

    private Connection buildHiveConnection(ClassLoader classLoader, String url,
                                           String userName, String password,
                                           String authMethod, String krb5conf,
                                           String servicePrincipal) throws Exception{
        Class<?> cls = classLoader.loadClass("org.apache.hive.jdbc.HiveDriver");
        // 实例化 driver对象
        Driver driver = (Driver) cls.newInstance();
        Properties info = new Properties();
        if (Objects.equals(authMethod,"KERBEROS")){
            String keyTabPath = KerberosUtil.getKeyTabPath(userName, password);
            String krb5ConfPath = KerberosUtil.getKrb5ConfPath(krb5conf);
            KrbConfig krbConfig = KerberosUtil.loadKrb5Conf(krb5ConfPath);
            if (!servicePrincipal.contains("@")){
                String kdcRealm = krbConfig.getKdcRealm();
                servicePrincipal += "@" + kdcRealm;
            }

            Config.refresh();
            Configuration conf = new Configuration();
            conf.set("hadoop.security.authentication", "Kerberos");
            conf.set("hive.server2.authentication", "Kerberos");
            UserGroupInformation.setConfiguration(conf);
            UserGroupInformation.loginUserFromKeytab(userName, keyTabPath);

            if (url.endsWith(";")){
                url += "principal=" + servicePrincipal+";"+"user.principal="+userName+";"+"user.keytab="+keyTabPath+";";
            } else if (url.matches(".*\\d$")){
                url += "/;principal=" + servicePrincipal+";"+"user.principal="+userName+";"+"user.keytab="+keyTabPath+";";
            } else {
                url += ";principal=" + servicePrincipal+";"+"user.principal="+userName+";"+"user.keytab="+keyTabPath+";";
            }
        } else {
            info.setProperty("user", userName);
            info.setProperty("password",password);
        }

        try {
            // 获取连接对象
            return driver.connect(url, info);
        } catch (Exception e) {
            LOG.error("连接 hive 失败：{}", e.getMessage());
            return null;
        }
    }



    private static URLClassLoader createClassLoader(String hiveVersion) throws IOException {
        String [] libs = null;
        if (Objects.equals(hiveVersion,"hive1")){
            libs = HIVE_1_LIBS;
        } else if (Objects.equals(hiveVersion,"hive11")){
            libs = HIVE_1_1_LIBS;
        } else if (Objects.equals(hiveVersion,"hive2")){
            libs = HIVE_2_LIBS;
        } else if (Objects.equals(hiveVersion,"hive21")){
            libs = HIVE_2_1_LIBS;
        } else if(Objects.equals(hiveVersion,"transwarp_inceptor")){
            libs = TRANSWARP_INCEPTOR_LIBS;
        } else if(Objects.equals(hiveVersion,"transwarp_inceptor_argo")){
            libs = TRANSWARP_INCEPTOR_ARGO_LIBS;
        } else if(Objects.equals(hiveVersion,"hive110")){
            libs = HIVE_1_1_0_LIBS;
        } else {
            throw new IllegalArgumentException(hiveVersion);
        }

        String prefix = null;
        switch (hiveVersion) {
            case "hive1":
                prefix = "hivelibs/hive1/";
                break;
            case "hive11":
                prefix = "hivelibs/hive11/";
                break;
            case "hive2":
                prefix = "hivelibs/hive2/";
                break;
            case "hive21":
                prefix = "hivelibs/hive21/";
                break;
            case "transwarp_inceptor":
                prefix = "hivelibs/transwarp_inceptor/";
                break;
            case "transwarp_inceptor_argo":
                prefix = "hivelibs/transwarp_inceptor_argo/";
                break;
            case "hive110":
                prefix = "hivelibs/hive110/";
                break;
        }

        ClassLoader contextClassLoader = Thread.currentThread().getContextClassLoader();
        // 创建一个 set 集合用于存储驱动文件的 url，使得 url 不能重复
        Set<URL> set = new HashSet<>();
        Path tempDir = Files.createTempDirectory(hiveVersion);
        for (String hiveLib : libs) {
            URL resource = contextClassLoader.getResource(prefix + hiveLib);
            // 将 Jar 文件解压到系统临时目录中
            File nestedJarFile = new File(tempDir.toFile(), hiveLib);
            try (InputStream inputStream = resource.openStream(); FileOutputStream outputStream = new FileOutputStream(nestedJarFile)) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }
            set.add(nestedJarFile.toURI().toURL());
        }

        URL[] urls = set.toArray(new URL[0]);
        //服务器上 获取当前线程 调用 上下文类加载器 亲测 可行
        URLClassLoader loader = new URLClassLoader(urls, contextClassLoader);
        return loader;
    }


    public static Connection getTranswarpInceptorArgoConnection(String url, String userName, String password, String authMethod, String krb5conf,
                                                            String servicePrincipal) {
        HiveJDBCUtil instance = HiveJDBCUtil.getInstance();
        if (instance == null){
            throw new RuntimeException("加载 hive jdbc util 失败");
        }
        try {
            return instance.buildArgoConnection(instance.transwarpInceptorArgoClassLoader, url, userName, password,
                    authMethod,krb5conf,servicePrincipal);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    public static Connection getTranswarpInceptorConnection(String url, String userName, String password, String authMethod, String krb5conf,
                                                            String servicePrincipal) {
        HiveJDBCUtil instance = HiveJDBCUtil.getInstance();
        if (instance == null){
            throw new RuntimeException("加载 hive jdbc util 失败");
        }
        try {
            return instance.buildHiveConnection(instance.transwarpInceptorClassLoader, url, userName, password,
                    authMethod,krb5conf,servicePrincipal);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static Connection getTranswarpInceptorConnection(String url, String userName, String password) {
        HiveJDBCUtil instance = HiveJDBCUtil.getInstance();
        if (instance == null){
            throw new RuntimeException("加载 hive jdbc util 失败");
        }
        try {
            return instance.buildHiveConnection(instance.transwarpInceptorClassLoader, url, userName, password,null,null,null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


}
