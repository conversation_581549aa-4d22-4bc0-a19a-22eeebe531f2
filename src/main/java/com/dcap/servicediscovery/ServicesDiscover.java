package com.dcap.servicediscovery;

import com.dcap.classifier.nmap.NMapScan;
import com.dcap.classifier.nmap.data.Address;
import com.dcap.classifier.nmap.data.Host;
import com.dcap.classifier.nmap.data.Port;
import com.dcap.classifier.nmap.data.Service;
import com.dcap.servicediscovery.entity.DiscoveredServiceRecord;
import com.yd.dcap.platform.model.ProbeClientTask;
import com.yd.dcap.probe.client.AbstractProbeClientProcessor;
import com.yd.dcap.probe.client.TaskType;
import com.yd.dcap.probe.entities.ProbeClientJobResult;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import com.yd.rules.utils.UtilMisc;
import org.zeroturnaround.process.ProcessUtil;
import org.zeroturnaround.process.Processes;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@TaskType(ProbeClientTask.TASK_TYPE_SERVICE_DISCOVERY)
public class ServicesDiscover extends AbstractProbeClientProcessor {

    private static final Map<Long, Long> processMap = new ConcurrentHashMap<>();

    public ServicesDiscover(ProbeClientTaskContext probeClientTaskContext) {
        super(probeClientTaskContext);
    }

    @Override
    protected Map<String, Object> doWork(long execId, long taskId, String taskType, long tenantId,
                                         Map<String, Object> taskParam, Consumer<ProbeClientJobResult> callback) throws Exception {
        String hostsText = Objects.toString(taskParam.get("hostsText"),null);
        String portsText = Objects.toString(taskParam.get("portsText"), null);
        // 1 发现或者的 ip
        List<String> upHosts = NMapScan.getUpHost(hostsText == null?null:hostsText.split(","), (pid)->{
            processMap.put(execId, pid);
        });
        processMap.remove(execId);
        if (upHosts == null) {
            logger.error("[services discovery] no ip address found from hosts: {}", hostsText);
            return UtilMisc.toMap("errorMessage","未找到任何可访问的 IP 地址。");
        }

        List<Host> hosts = NMapScan.dbScan(upHosts, portsText == null? null:portsText.split(","), (pid)->{
            processMap.put(execId, pid);
        });// new String[]{"31001-31200"}
        processMap.remove(execId);
        if (hosts.isEmpty()){
            logger.error("[services discovery] No accessible services found from \nports: {} \nhosts: {}", portsText, upHosts);
        } else {
            logger.info("[services discovery] [{}] services found from \nports: {} \nhosts: {}", hosts.size(), portsText, upHosts);
        }
        List<DiscoveredServiceRecord> serviceList = new ArrayList<>();
        for (Host host : hosts) {
            List<Address> ipv4List = host.getAddress()
                    .stream()
                    .filter(address -> Objects.equals(address.getAddrtype(), "ipv4"))
                    .collect(Collectors.toList());
            if (ipv4List.isEmpty()){
                logger.warn("[services discovery] No ipv4 address found from result: {}", host);
                continue;
            }

            final String addr = ipv4List.get(0).getAddr();
            for (Port port : host.getPorts().getPort()) {
                Service service = port.getService();
                if (service == null){
                    logger.warn("[services discovery] It was discovered that the port {} on the host {} is active " +
                            "but not recognized as any service", port.getPortid(), addr);
                    continue;
                }
                DiscoveredServiceRecord discoveredService = new DiscoveredServiceRecord();
                discoveredService.setIpv4Address(addr);
                discoveredService.setServicePort(Integer.parseInt(port.getPortid()));
                discoveredService.setServiceName(service.getName());
                discoveredService.setServiceVersion(service.getVersion());
                discoveredService.setServiceProtocol(port.getProtocol());
                discoveredService.setServiceMethod(service.getMethod());
                discoveredService.setState(port.getState().getState());
                discoveredService.setServiceProduct(service.getProduct());
                discoveredService.setTenantId(tenantId);
                serviceList.add(discoveredService);
            }
        }
        return UtilMisc.toMap("serviceList", serviceList);
    }

    @Override
    protected void onInterrupt(long jobId, long taskId, String taskType, long tenantId, Map<String, Object> taskParam) throws Exception {
        Long pid = processMap.get(jobId);
        if (pid != null){
            ProcessUtil.destroyForcefullyAndWait(Processes.newPidProcess(pid.intValue()));
        }
    }
}
