package com.dcap.classifier.rules;

import com.dcap.classifier.context.AbstractContext;
import com.dcap.classifier.context.AbstractInnerType;
import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.custom.Evaluation;
import com.dcap.classifier.custom.EvaluationException;

import java.util.*;
import java.util.regex.Pattern;

public class RuleHelper {
    public RuleHelper() {
    }

    public static boolean matchTableType(ContextTable table, List<ContextTable.TableType> tableTypes) {
        return matchType(table, tableTypes);
    }

    public static boolean matchColumnType(ContextColumn column, List<ContextColumn.ColumnType> columnDataTypes) {
        return matchType(column, columnDataTypes);
    }

    public static int matchColumnType(List<ContextColumn> columns, List<ContextColumn.ColumnType> columnDataTypes) {
        List<ContextColumn> typeUnmatchedColumns = new ArrayList<>();
        for (ContextColumn column : columns) {
            if (!matchColumnType(column, columnDataTypes)) {
                typeUnmatchedColumns.add(column);
            }
        }
        return filterList(columns, typeUnmatchedColumns);
    }

    public static int filterList(List<?> list, List<?> elementsToRemove) {
        if (!elementsToRemove.isEmpty()) {
            Set<?> removalSet = new HashSet<>(elementsToRemove);
            list.removeIf(removalSet::contains);
        }
        return list.size();
    }

    private static boolean matchType(AbstractContext tableOrColumn, List<? extends AbstractInnerType> innerTypes) {
        for (AbstractInnerType innerType : innerTypes) {
            if (tableOrColumn.isOfType(innerType)) {
                return true;
            }
        }
        return false;
    }

    public static int countMatchEvaluation(List<String> columnValues, Evaluation evaluation, List<String> reportValues) throws EvaluationException {
        int count = 0;
        if (columnValues == null || columnValues.isEmpty()) {
            return count;
        }
        boolean var4 = reportValues != null;
        if (var4) {
            reportValues.clear();
        }

        for (String columnValue : columnValues) {
            boolean matched = evaluation.evaluate(columnValue);
            if (matched) {
                ++count;
                if (var4) {
                    reportValues.add(columnValue);
                }
            }
        }
        return count;
    }

    public static boolean matchPattern(String var0, Pattern var1) {
        return var1.matcher(var0).find();
    }

    public static int filterColumnsByMinimumLength(List<ContextColumn> var0, int var1) {
        List<ContextColumn> var2 = new ArrayList<>();
        if (var1 <= 0) {
            return filterList(var0, var2);
        }
        for (ContextColumn var4 : var0) {
            if (var4.getSize() < var1) {
                var2.add(var4);
            }
        }
        return filterList(var0, var2);
    }

    public static int filterColumnsByMaximumLength(List<ContextColumn> columns, int limit) {
        List<ContextColumn> var2 = new ArrayList<>();
        if (limit > 0) {
            for (ContextColumn column : columns) {
                if (column.getSize() > limit && !"STRING".equals(column.getTypename())) {
                    var2.add(column);
                }
            }
        }

        return filterList(columns, var2);
    }

    public static int filterColumnsByName(List<ContextColumn> columns, Pattern var1) {
        ArrayList var2 = new ArrayList();

        for (ContextColumn column : columns) {
            String columnName = column.getColumnName();
            if (!matchPattern(columnName, var1)) {
                var2.add(column);
            }
        }

        filterList(columns, var2);
        return columns.size();
    }

    public static int filterOnColumnMatch(List<ContextColumn> var0, String var1) {
        Iterator var2 = var0.iterator();

        while (var2.hasNext()) {
            ContextColumn var3 = (ContextColumn) var2.next();
            String var4 = var3.getColumnName();
            if (var4.equals(var1)) {
                var2.remove();
                break;
            }
        }

        return var0.size();
    }

    public static int countMatchWildcards(List<String> columnValues, Pattern pattern, List<String> uniqueValues) {
        int count = 0;
        if (columnValues.isEmpty()) {
            return count;
        }
        if (uniqueValues != null) {
            uniqueValues.clear();
        }

        for (String columnValue : columnValues) {
            boolean matched = matchPattern(columnValue, pattern);
            if (matched) {
                ++count;
                if (uniqueValues != null) {
                    uniqueValues.add(columnValue);
                }
            }
        }
        return count;
    }

    public static int countMatchRegex(List<String> columnValues, Pattern searchValuePattern, List<String> var2) {
        int var3 = 0;
        if (columnValues == null || columnValues.isEmpty()) {
            return var3;
        }
        boolean var4 = var2 != null;
        if (var4) {
            var2.clear();
        }

        for (String columnValue : columnValues) {
            if (matchRegex(columnValue, searchValuePattern)) {
                ++var3;
                if (var4) {
                    var2.add(columnValue);
                }
            }
        }

        return var3;
    }

    public static boolean matchRegex(String var0, Pattern var1) {
        return var1.matcher(var0).matches();
    }
}
