package com.dcap.classifier.rules;

import com.dcap.classifier.ClassifierException;

public class RuleEvaluationException extends ClassifierException {
   public RuleEvaluationException(String var1) {
      super(var1);
   }

   public RuleEvaluationException(String var1, Throwable var2) {
      super(var1, var2);
   }

   public RuleEvaluationException(String var1, String var2, Throwable var3) {
      super(var1, var2, var3);
   }

   public RuleEvaluationException(String var1, String var2) {
      super(var1, var2);
   }
}
