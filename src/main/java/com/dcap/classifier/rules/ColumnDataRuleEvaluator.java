package com.dcap.classifier.rules;

import com.dcap.classifier.*;
import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.RuleContext;
import com.dcap.classifier.custom.Evaluation;
import com.dcap.datalayer.*;
import com.dcap.utils.Messages;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.rules.validate.Validator;
import com.yd.rules.validate.ValidatorCollections;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;
import java.util.stream.Collectors;

public class ColumnDataRuleEvaluator extends RuleEvaluator {

    private static final Logger LOG = LoggerFactory.getLogger(ColumnDataRuleEvaluator.class);

    private static final int MAX_UNIQUE_VALUES_STRING_SIZE = 524288;

    public static final List<ContextColumn.ColumnType> DEFAULT_COLUMN_TYPES;
    private List<ContextTable.TableType> tableTypes;
    private List<ContextColumn.ColumnType> dataTypes;
    private Pattern searchValueLike;
    private Pattern searchValueExpression;

    private Evaluation evaluation;
    private Integer hitPercentage;

    private String[] searchInSQL;
    private String[] searchValues;

    private boolean isSearchValuesWithin;

    private Pattern uniqueValueMask;
    private boolean uniqueValueMaskLoaded;
    private Pattern[] excludedSchemas;
    private Pattern[] excludedTables;
    private TableColumn[] excludedTableColumns;
    private boolean loadedTableNameLike;
    private boolean loadedColumnNameLike;
    private Pattern tableNameLike;
    private Pattern columnNameLike;

    private String marker;

    private boolean loadedSQL;

    private boolean loadedEvaluation;
    private boolean loadedSearchValuePattern;
    private boolean loadedSearchValueLike;
    private boolean loadedSearchValues;
    private boolean loadedHasMatchData;
    private boolean tableTypesLoaded;
    private boolean hasMatchData;

    private boolean loadedIsSearchWithin;



    public ColumnDataRuleEvaluator(AbstractClassifierColumnDataRule<? extends ClassifierRuleRecord> classifierRule) {
        super(classifierRule);
    }

    public AbstractClassifierColumnDataRule<? extends ClassifierRuleRecord> getRuleDefinition() {
        return (AbstractClassifierColumnDataRule<? extends ClassifierRuleRecord>) super.getRuleDefinition();
    }

//    protected boolean isRuleSupported(RuleContext ruleContext) {
//        // todo: 删除 isCsv
////        return !ruleContext.getDatasource().getType().isCsv();
//        return true;
//    }

    public Pattern getTableNameLike() {
        if (this.loadedTableNameLike) {
            return this.tableNameLike;
        }
        this.tableNameLike = ColumnNameRuleEvaluator.convertToPattern(this.getRuleDefinition().getTableNameLike());
        this.loadedTableNameLike = true;
        return this.tableNameLike;
    }

    public Pattern getColumnNameLike() {
        if (this.loadedColumnNameLike) {
            return columnNameLike;
        }
        this.columnNameLike = ColumnNameRuleEvaluator.convertToPattern(this.getRuleDefinition().getColumnNameLike());
        this.loadedColumnNameLike = true;
        return this.columnNameLike;
    }

    private List<ContextTable.TableType> getTableTypes() {
        if (this.tableTypesLoaded) {
            return tableTypes;
        }
        ContextTable.TableType[] tableTypes = this.getRuleDefinition().getTableTypes();
        if (tableTypes != null) {
            this.tableTypes = Arrays.asList(tableTypes);
        }

        this.tableTypesLoaded = true;
        return this.tableTypes;
    }

    private Integer getMinimumLength() {
        return this.getRuleDefinition().getMinimumLength();
    }

    private Integer getMaximumLength() {
        return this.getRuleDefinition().getMaximumLength();
    }

    public Pattern[] getExcludeSchema() {
        if (this.excludedSchemas != null) {
            return this.excludedSchemas;
        }
        Pattern[] excludedSchemas = ColumnNameRuleEvaluator.convertToPatterns(this.getRuleDefinition().getExcludedSchemas());
        if (excludedSchemas.length > 0) {
            ExcludePatternComparator excludePatternComparator = new ExcludePatternComparator();
            Arrays.sort(excludedSchemas, excludePatternComparator);
        }
        this.excludedSchemas = excludedSchemas;
        return this.excludedSchemas;
    }

    public Pattern[] getExcludeTable() {
        if (this.excludedTables != null) {
            return this.excludedTables;
        }
        Pattern[] excludedTables = ColumnNameRuleEvaluator.convertToPatterns(this.getRuleDefinition().getExcludedTables());
        if (excludedTables.length > 0) {
            ExcludePatternComparator var2 = new ExcludePatternComparator();
            Arrays.sort(excludedTables, var2);
        }
        this.excludedTables = excludedTables;
        return this.excludedTables;
    }

    public TableColumn[] getExcludeTableColumn() {
        if (this.excludedTableColumns != null) {
            return this.excludedTableColumns;
        }
        TableColumn[] excludedTableColumns = this.getRuleDefinition().getExcludedTableColumns();
        if (excludedTableColumns == null) {
            excludedTableColumns = ColumnNameRuleEvaluator.NO_COLUMNS;
        } else if (excludedTableColumns.length > 0) {
            ExcludeTableColumnComparator excludeTableColumnComparator = new ExcludeTableColumnComparator();
            Arrays.sort(excludedTableColumns, excludeTableColumnComparator);
        }
        this.excludedTableColumns = excludedTableColumns;
        return this.excludedTableColumns;
    }

    private List<ContextColumn.ColumnType> getDataTypes() {
        if (this.dataTypes != null) {
            return this.dataTypes;
        }
        List<ContextColumn.ColumnType> dataTypes;
        ContextColumn.ColumnType[] columnTypes = this.getRuleDefinition().getColumnTypes();
        if (columnTypes != null && columnTypes.length != 0) {
            dataTypes = Arrays.asList(columnTypes);
        } else {
            dataTypes = DEFAULT_COLUMN_TYPES;
        }
        this.dataTypes = dataTypes;
        return this.dataTypes;
    }

    private boolean showUniqueValues() {
        return this.getRuleDefinition().showUniqueValues();
    }

    private Pattern getUniqueValueMask() {
        if (this.uniqueValueMaskLoaded) {
            return this.uniqueValueMask;
        }
        this.uniqueValueMaskLoaded = true;

        String uniqueValueMask = this.getRuleDefinition().getUniqueValueMask();
        if (StringUtils.isNotBlank(uniqueValueMask)) {
            this.uniqueValueMask = Pattern.compile(uniqueValueMask);
        }
        return this.uniqueValueMask;
    }

    Evaluation getEvaluation(TaskGlobalDataHolder taskGlobalDataHolder) throws InitializationException {
        if (!this.loadedEvaluation) {
            String evaluationName = this.getRuleDefinition().getEvaluation();
            if (evaluationName != null && (evaluationName = evaluationName.trim()).length() > 0) {
                try {
                    this.evaluation = taskGlobalDataHolder.loadEvaluationClass(evaluationName);
                } catch (ReflectiveOperationException | SecurityException exception) {
                    String errorMessage = Messages.getString("rule.evaluation.create.failure", "expression", evaluationName);
                    throw new InitializationException(errorMessage, exception);
                }
            }
            this.loadedEvaluation = true;
        }
        return this.evaluation;
    }

    Pattern getSearchValuePattern() throws RuleEvaluationException {
        if (this.loadedSearchValuePattern) {
            return this.searchValueExpression;
        }
        String searchValuePattern = this.getRuleDefinition().getSearchValuePattern();
        if (StringUtils.isBlank(searchValuePattern)) {
            this.loadedSearchValuePattern = true;
            return this.searchValueExpression;
        }
        try {
            this.searchValueExpression = Pattern.compile(searchValuePattern);
        } catch (PatternSyntaxException var4) {
            String var3 = Messages.getString("Could not compile Regular Expression: ${expression}.", "expression", searchValuePattern);
            throw new RuleEvaluationException(var3, var4.getLocalizedMessage(), var4);
        }
        this.loadedSearchValuePattern = true;
        return this.searchValueExpression;
    }

    Pattern getSearchValueLike() {
        if (this.loadedSearchValueLike) {
            return this.searchValueLike;
        }
        String searchLikePattern = this.getRuleDefinition().getSearchValueLike();
        if (StringUtils.isBlank(searchLikePattern)) {
            this.loadedSearchValueLike = true;
            return null;
        }
        try {
            this.searchValueLike = Pattern.compile(searchLikePattern);
        } catch (Exception e){
            LOG.error("search value like text compile fail："+searchLikePattern+", error message："+e.getMessage());
        }
        this.loadedSearchValueLike = true;
        return this.searchValueLike;
    }

    public boolean reportHits() {
        try {
            return this.getRequiredHitPercentage() >= 0;
        } catch (RuleEvaluationException exception) {
            return false;
        }
    }

    private String[] getSearchInSQL(RuleContext ruleContext) {
        if (this.loadedSQL) {
            return this.searchInSQL;
        }
        String sql = this.getRuleDefinition().getSQL();
        if (StringUtils.isBlank(sql)) {
            this.loadedSQL = true;
            return this.searchInSQL;
        }
        sql = sql.trim();
        if (!sql.toLowerCase().startsWith("select ") || !(sql.indexOf(59) < 0)) {
            this.searchInSQL = ColumnNameRuleEvaluator.NO_STRINGS;
            this.loadedSQL = true;
            return this.searchInSQL;
        }

        try (Statement statement = ruleContext.getStatement(); ResultSet resultSet = statement.executeQuery(sql)) {
            if (resultSet == null) {
                this.searchInSQL = ColumnNameRuleEvaluator.NO_STRINGS;
            } else {
                Set<String> values = new HashSet<>();
                // the maximum result set row count
                int maxRow = 100000;

                for (int i = 0; i < maxRow && resultSet.next(); ++i) {
                    String columnValue = resultSet.getString(1);
                    if (StringUtils.isNotBlank(columnValue)) {
                        values.add(columnValue.trim().toUpperCase());
                    }
                }

                String[] searchValues = values.toArray(new String[0]);
                Arrays.sort(searchValues);
                this.searchInSQL = searchValues;
            }
        } catch (SQLException sqlException) {
            LOG.error(sqlException.toString());
            this.searchInSQL = ColumnNameRuleEvaluator.NO_STRINGS;
        }

        this.loadedSQL = true;
        return this.searchInSQL;
    }

    private String[] getSearchValues() {
        if (this.loadedSearchValues) {
            return this.searchValues;
        }

        this.searchValues = this.getRuleDefinition().getSearchValues();
        if (this.searchValues == null) {
            this.loadedSearchValues = true;
            return null;
        }

        for (int i = 0; i < this.searchValues.length; i++) {
            this.searchValues[i] = this.searchValues[i].toUpperCase();
        }
        Arrays.sort(this.searchValues);

        this.loadedSearchValues = true;
        return this.searchValues;
    }

    private boolean isValuesSearchWithin() {
        if (this.loadedIsSearchWithin) {
            return this.isSearchValuesWithin;
        }
        this.isSearchValuesWithin = this.getRuleDefinition().isValuesSearchWithin();
        this.loadedIsSearchWithin = true;
        return this.isSearchValuesWithin;
    }

    private int getRequiredHitPercentage() throws RuleEvaluationException {
        if (this.hitPercentage != null) {
            return this.hitPercentage;
        }
        Integer requiredHitPercentage = this.getRuleDefinition().getRequiredHitPercentage();
        if (requiredHitPercentage != null && requiredHitPercentage > 0) {
            this.hitPercentage = requiredHitPercentage;
        } else if (this.showUniqueValues()) {
            this.hitPercentage = 0;
        } else {
            this.hitPercentage = -1;
        }
        return this.hitPercentage;
    }

    public RuleResults runRule(RuleContext ruleContext, SearchParameters searchParameters) {
        try{
            //        这里排除的逻辑 是匹配时的排除，即使不匹配，也会收集到 inventory
            //        是否匹配支持的 tableTypes
            List<ContextTable.TableType> tableTypes = this.getTableTypes();
            // todo: 如果配置不采样视图，那么我们就也跳过视图的同义词采样。其实就获取引用的 table，再 match 一次。
            if (tableTypes != null
                    && !RuleHelper.matchTableType(ruleContext.getCurrentTable(), tableTypes)) {
                return new RuleResults(searchParameters, false);
            }

            // 排除 schema，排除指定 table，过滤匹配 tableNameLike
            RuleResults ruleResults = ColumnNameRuleEvaluator.evaluateInitial(ruleContext, searchParameters,
                    this.getExcludeSchema(), this.getExcludeTable(), this.getTableNameLike());
            if (ruleResults != null) {
                return ruleResults;
            }

            List<ContextColumn> searchableColumns = searchParameters.getSearchableColumns();
            if (searchableColumns.size() == 0) {
                return new RuleResults(searchParameters, false);
            }

            List<ContextColumn> columns = new LinkedList<>(searchableColumns);

            // 将查找到的 table column 与规则定义的 列的类型匹配，如果规则不要求匹配这种类型的 column，在这一步该 column 会被过滤掉。
            boolean unmatch = RuleHelper.matchColumnType(columns, this.getDataTypes()) == 0;
            TableColumn[] excludeTableColumns;
            if (!unmatch && (excludeTableColumns = this.getExcludeTableColumn()) != null && excludeTableColumns.length != 0) {
                String tableName = ruleContext.getCurrentTable().getTableName();
                for (TableColumn excludeTableColumn : excludeTableColumns) {
                    if (tableName.equals(excludeTableColumn.tableName) && RuleHelper.filterOnColumnMatch(columns, excludeTableColumn.columnName) == 0) {
                        return new RuleResults(searchParameters, false);
                    }
                }
            }

            boolean existDataType = this.getRuleDefinition().getColumnTypes() != null;
            Pattern columnNameLike = this.getColumnNameLike();
            if (!unmatch && columnNameLike != null) {
                existDataType = true;
                unmatch = RuleHelper.filterColumnsByName(columns, columnNameLike) == 0;
            }

            // 因为 max compute 目前读取到所有的数据类型长度，发现都是 0，所以这里针对 max compute 不再检查最小长度。
            Integer minLen;
            if (!unmatch && (minLen = this.getMinimumLength()) != null
                    && !ruleContext.getDatasource().getType().equals(DataSourceType.MAXCOMPUTE)
                    && !ruleContext.getDatasource().getType().equals(DataSourceType.HIVE)
                    && !ruleContext.getDatasource().getType().equals(DataSourceType.TRANSWARP_INCEPTOR)
            ) {
                existDataType = true;
                unmatch = RuleHelper.filterColumnsByMinimumLength(columns, minLen) == 0;
            }

            Integer maxLen;
            if (!unmatch && (maxLen = this.getMaximumLength()) != null
                    && !ruleContext.getDatasource().getType().equals(DataSourceType.MAXCOMPUTE)
                    && !ruleContext.getDatasource().getType().equals(DataSourceType.HIVE)
                    && !ruleContext.getDatasource().getType().equals(DataSourceType.TRANSWARP_INCEPTOR)
            ) {
                existDataType = true;
                unmatch = RuleHelper.filterColumnsByMaximumLength(columns, maxLen) == 0;
            }

            if (unmatch) {
                return new RuleResults(searchParameters, false, existDataType, this.getContinueOption().useUnmatchedColumns());
            }

            if (this.hasMatchData()) {
                try{
                    return this.matchData(ruleContext, searchParameters, columns);
                } catch (Exception e){
                    ruleContext.getTaskGlobalDataHolder().getProbeClientTaskContext()
                                    .reportErrorOccurredExecuting(StatusRecord.Position.MatchData, null, null, e);
                    return new RuleResults(searchParameters, false);
                }
            }
            return new RuleResults(searchParameters, true, true, columns, this.getContinueOption().useUnmatchedColumns());
        } catch (Exception e){
            ruleContext.getTaskGlobalDataHolder().getProbeClientTaskContext()
                    .reportErrorOccurredExecuting(StatusRecord.Position.RunRule, null, "ColumnDataRuleEvaluator.runRule", e);
            return new RuleResults(searchParameters, false);
        }
    }

    @Override
    public String getExprType() {
        return "data";
    }

    boolean hasMatchData() {
        if (this.loadedHasMatchData) {
            return this.hasMatchData;
        }
        this.hasMatchData =
                this.getSearchValueLike() != null
                // 相当于关键字匹配
                || this.getSearchValues() != null
                || this.getValidator(this.getTranslatedName()) != null
                || StringUtils.isNotBlank(this.getRuleDefinition().getSearchValuePattern());
        this.loadedHasMatchData = true;
        return this.hasMatchData;
    }

    RuleResults matchData(RuleContext ruleContext, SearchParameters searchParameters, List<ContextColumn> columns) throws ClassifierException {
        Pattern searchValueLike = this.getSearchValueLike();
        Pattern searchValuePattern = this.getSearchValuePattern();
//        Evaluation evaluation = this.getEvaluation(ruleContext.getTaskGlobalDataHolder());

        boolean isSearchValueLike = searchValueLike != null;
        boolean isSearchValuePattern = searchValuePattern != null;
//        boolean isEvaluation = evaluation != null;
        Validator validator = this.getValidator(this.getTranslatedName());

        List<ContextColumn> matchedColumns = new ArrayList<>(columns.size());

        boolean showUniqueValues = this.showUniqueValues();
        Pattern uniqueValueMaskPattern = showUniqueValues ? this.getUniqueValueMask() : null;
        RuleResults.Hits hits = new RuleResults.Hits();

        LOG.debug("Checking for: " + (isSearchValueLike ? "'" + searchValueLike.pattern() + "'" : "") + (isSearchValueLike && isSearchValuePattern ? ", " : "") + (isSearchValuePattern ? "'" + searchValuePattern.pattern() + "'" : "") + " on: " + ruleContext.getCurrentTableName());

        String tableName = ruleContext.getCurrentTable().getTableName();
        // 获取采样数据
        Map<String, List<String>> sampleData = null;
        try{
            sampleData = ruleContext.getSampleFor(tableName);
        } catch (Throwable e){
            LOG.error(e.getMessage());
        }
        if(sampleData == null || sampleData.isEmpty()){
            return new RuleResults(searchParameters, matchedColumns, hits, this.getContinueOption().useUnmatchedColumns());
        }

        label352:
        for (ContextColumn contextColumn : columns) {
            String columnName = contextColumn.getColumnName();
            List<String> columnValues = sampleData.get(columnName);
            if (columnValues == null || columnValues.isEmpty()) {
                continue;
            }
            columnValues = columnValues.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());

            int matchedCount;
            List<String> reportValues = showUniqueValues ? new ArrayList<>() : null;
            int columnValuesLength = columnValues.size();

            // todo: searchValueLike 使用的是 find 查找方式。 searchValuePattern 使用的是完全匹配方式
            if (isSearchValueLike && validator != null) {
                matchedCount = validator.countMatchWildcardsAndValidate(columnValues, searchValueLike, reportValues);
                if (!this.shouldFire(hits, columnValues, columnName, matchedCount, columnValuesLength, reportValues, uniqueValueMaskPattern)) {
                    continue;
                }
            } else {
                if (isSearchValueLike) {
                    matchedCount = RuleHelper.countMatchWildcards(columnValues, searchValueLike, reportValues);
                    if (!this.shouldFire(hits, columnValues, columnName, matchedCount, columnValuesLength, reportValues, uniqueValueMaskPattern)) {
                        continue;
                    }
                }
            }


            // todo: 使用 TranslatedName 获取的 validator，对应的是 data_tag
            if (isSearchValuePattern && validator != null) {
                matchedCount = validator.countMatchWildcardsAndValidate(columnValues, searchValuePattern, reportValues);
                if (!this.shouldFire(hits, columnValues, columnName, matchedCount, columnValuesLength, reportValues, uniqueValueMaskPattern)) {
                    continue;
                }
            } else {
                if (isSearchValuePattern) {
                    matchedCount = RuleHelper.countMatchRegex(columnValues, searchValuePattern, reportValues);
                    if (!this.shouldFire(hits, columnValues, columnName, matchedCount, columnValuesLength, reportValues, uniqueValueMaskPattern)) {
                        continue;
                    }
                }
            }

//            if (isEvaluation){
//                matchedCount = RuleHelper.countMatchEvaluation(columnValues, evaluation, reportValues);
//                if (!this.shouldFire(hits, columnValues, columnName, matchedCount, columnValuesLength, reportValues, uniqueValueMaskPattern)) {
//                    continue;
//                }
//            }

            // todo: searValues 提供的能力相当于精确数据匹配。
            String[][] searchValues = new String[][]{
                    //this.getSearchInSQL(ruleContext),
                    this.getSearchValues()};
            for (String[] values : searchValues) {
                if (values == null || values.length == 0) {
                    continue;
                }

                int matchedNum = 0;
                if (showUniqueValues) {
                    reportValues.clear();
                }

                List<String> modifiedSample = ruleContext.getModifiedSampleFor(columnName);
                if (modifiedSample == null) {
                    // 如果为空就创建更新后的采样数据
                    modifiedSample = ruleContext.createModifiedSampleFor(columnName, this.isValuesSearchWithin(), true, columnValues);
                }

                for (String sampleValue : modifiedSample) {
                    boolean sampleInValues = Arrays.binarySearch(values, sampleValue) >= 0;
                    if (!sampleInValues) {
                        continue;
                    }
                    ++matchedNum;
                    if (!this.reportHits()) {
                        break;
                    }

                    if (showUniqueValues) {
                        reportValues.add(sampleValue);
                    }
                }

                if (!this.shouldFire(hits, null, columnName, matchedNum, columnValuesLength, reportValues, uniqueValueMaskPattern)) {
                    continue label352;
                }
            }
            matchedColumns.add(contextColumn);
        }

//        List<String> accumulatedErrors = ruleContext.getSamplerFor(ruleContext.getCurrentTable().getTableName()).getAccumulatedErrors();
//        if (accumulatedErrors != null && accumulatedErrors.size() > 0) {
//            String message = Messages.getString("rule.data.access.errors", "tablename", ruleContext.getCurrentTableName());
//            String var43 = Utils.collectionToString(accumulatedErrors, ", \n");
//            ruleContext.getProcessLogger().logWarning(message, var43, this.getClass(), ruleContext);
//        }

        return new RuleResults(searchParameters, matchedColumns, hits, this.getContinueOption().useUnmatchedColumns());
    }

    private Validator getValidator(String ruleName) {
        Validator validator = null;
        for (ValidatorCollections collections : ValidatorCollections.values()) {
            if (ruleName.contains(collections.getRuleName())) {
                validator = collections.getValidator();
            }
            if (validator != null) {
                break;
            }
        }

        return validator;
    }

    private boolean shouldFire(RuleResults.Hits hits, List<String> columnValues, String columnName, int matchedCount, int columnValuesLength,
                               List<String> reportValues, Pattern uniqueValueMaskPattern) throws RuleEvaluationException {
        int hitsCount = matchedCount > 0 && this.reportHits() ? matchedCount : (matchedCount <= 0 ? 0 : 1);
        boolean isHit;
        if (columnValuesLength > 0 && hitsCount > 0 && this.reportHits()) {
            // 是否匹配命中率
            isHit = hitsCount * 100 / columnValuesLength >= this.getRequiredHitPercentage();
        } else {
            isHit = hitsCount > 0;
        }

        if (!isHit) {
            String dataTypeName = this.getName();
            String dataTag = this.getTranslatedName();
            if (columnValues != null) {
                LOG.debug("dataTypeName ["+dataTypeName+"], dataTag ["+dataTag+"], NO HIT for column: " + columnName + System.lineSeparator() + " values: " + columnValues);
            } else {
                LOG.debug("dataTypeName ["+dataTypeName+"], dataTag ["+dataTag+"], NO HIT for column: " + columnName);
            }
            return false;
        }

        if (reportValues == null || uniqueValueMaskPattern == null) {
            hits.update(columnValuesLength, hitsCount, columnName, reportValues);
            String dataTypeName = this.getName();
            String dataTag = this.getTranslatedName();
            if (columnValues != null) {
                LOG.debug("dataTypeName ["+dataTypeName+"], dataTag ["+dataTag+"], HIT for column: " + columnName + System.lineSeparator() + " values: " + columnValues);
            } else {
                LOG.debug("dataTypeName ["+dataTypeName+"], dataTag ["+dataTag+"], HIT for column: " + columnName);
            }
            return true;
        }

        for (int i = 0; i < reportValues.size(); i++) {
            String value = reportValues.get(i);
            if (StringUtils.isBlank(value)) {
                continue;
            }
            Matcher matcher = uniqueValueMaskPattern.matcher(value);
            if (!matcher.find() || !(matcher.groupCount() > 0)) {
                continue;
            }

            StringBuilder var10 = new StringBuilder();

            for (int n = 1; n <= matcher.groupCount(); ++n) {
                var10.append(matcher.group(n));
            }

            reportValues.set(i, var10.toString());
        }

        // 当 values 长度总和超过了 524288 时，只显示前 n 个匹配项目；
        List<String> newValues = new ArrayList<>();
        int lenSum = 0;
        for (int n = -1, len = reportValues.size(); ++n < len; ) {
            String value = reportValues.get(n);
            if (lenSum < MAX_UNIQUE_VALUES_STRING_SIZE) {
                newValues.add(value);
                lenSum += value.length();
            } else {
                break;
            }
        }
        if (newValues.size() < reportValues.size()) {
            reportValues.clear();
            reportValues.add(Messages.getString("Showing first ${0} matches", "0", "" + newValues.size()));
            reportValues.addAll(newValues);
        }

        hits.update(columnValuesLength, hitsCount, columnName, reportValues);

        if (columnValues != null) {
            LOG.debug("HIT for column: " + columnName + System.lineSeparator() + " values: " + columnValues);
        } else {
            LOG.debug("HIT for column: " + columnName);
        }

        return true;
    }

    static {
        DEFAULT_COLUMN_TYPES = Arrays.asList(ContextColumn.TEXT, ContextColumn.NUMBER, ContextColumn.DATE);
    }


}
