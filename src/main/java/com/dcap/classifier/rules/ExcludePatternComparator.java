package com.dcap.classifier.rules;

import java.util.Comparator;
import java.util.regex.Pattern;

public class ExcludePatternComparator implements Comparator<Pattern> {
    public ExcludePatternComparator() {
    }

    public int compare(Pattern var1, Pattern var2) {
        if (var1 != null && var1.pattern() != null) {
            return var2 != null && var2.pattern() != null ? var1.pattern().compareTo(var2.pattern()) : -1;
        } else {
            return 1;
        }
    }
}
