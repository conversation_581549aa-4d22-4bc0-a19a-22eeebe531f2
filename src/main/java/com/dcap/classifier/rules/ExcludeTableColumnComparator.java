package com.dcap.classifier.rules;

import com.dcap.datalayer.TableColumn;

import java.util.Comparator;

public class ExcludeTableColumnComparator implements Comparator<TableColumn> {
    public ExcludeTableColumnComparator() {
    }

    public int compare(TableColumn column1, TableColumn column2) {
        if (column1 != null && column1.tableName != null) {
            if (column2 != null && column2.tableName != null) {
                String tableAndColumn1 = column1.tableName + column1.columnName;
                String tableAndColumn2 = column2.tableName + column2.columnName;
                return tableAndColumn1.compareTo(tableAndColumn2);
            } else {
                return -1;
            }
        } else {
            return 1;
        }
    }
}
