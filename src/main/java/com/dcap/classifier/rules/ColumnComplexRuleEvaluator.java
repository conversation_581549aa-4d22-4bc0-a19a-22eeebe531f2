package com.dcap.classifier.rules;

import com.dcap.classifier.ClassifierException;
import com.dcap.classifier.RuleEvaluator;
import com.dcap.classifier.RuleResults;
import com.dcap.classifier.SearchParameters;
import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.RuleContext;
import com.dcap.datalayer.AbstractClassifierComplexRule;
import com.dcap.datalayer.ClassifierRuleRecord;
import com.dcap.datalayer.DataSourceType;
import com.dcap.datalayer.TableColumn;
import com.dcap.utils.Messages;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.rules.engine.ScoringModel;
import com.yd.rules.engine.models.ScoringModelInput;
import com.yd.rules.engine.models.ScoringModelOutput;
import com.yd.rules.engine.result.MatchedResult;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;
import java.util.stream.Collectors;

public class ColumnComplexRuleEvaluator extends RuleEvaluator implements Comparable<ColumnComplexRuleEvaluator> {

    public static final List<ContextColumn.ColumnType> DEFAULT_COLUMN_TYPES;
    private static final Logger LOG = LoggerFactory.getLogger(ColumnComplexRuleEvaluator.class);
    private static final int MAX_UNIQUE_VALUES_STRING_SIZE = 524288;

    static {
        DEFAULT_COLUMN_TYPES = Arrays.asList(ContextColumn.TEXT, ContextColumn.NUMBER);
    }

    private Set<String> tableTypes;
    private List<ContextColumn.ColumnType> dataTypes;
    private Pattern searchValueLike;
    private Pattern searchValueExpression;
    private Integer hitPercentage;
    private String[] searchValues;
    private boolean isSearchValuesWithin;
    private Pattern[] excludedSchemas;
    private Pattern[] excludedTables;
    private TableColumn[] excludedTableColumns;
    private boolean loadedTableNameLike;
    private boolean loadedColumnNameLike;
    private Pattern tableNameLike;
    private Pattern columnNameLike;

    private boolean loadedSearchValuePattern;
    private boolean loadedSearchValueLike;
    private boolean loadedSearchValues;
    private boolean loadedHasMatchData;
    private boolean tableTypesLoaded;
    private boolean hasMatchData;
    private boolean loadedIsSearchWithin;
    private final int orderBy;

    public ColumnComplexRuleEvaluator(AbstractClassifierComplexRule<? extends ClassifierRuleRecord> classifierRule, int orderBy) {
        super(classifierRule);
        this.orderBy = orderBy;
    }

    public AbstractClassifierComplexRule<? extends ClassifierRuleRecord> getRuleDefinition() {
        return (AbstractClassifierComplexRule<? extends ClassifierRuleRecord>) super.getRuleDefinition();
    }

    public Pattern getTableNameLike() {
        if (this.loadedTableNameLike) {
            return this.tableNameLike;
        }
        this.tableNameLike = ColumnNameRuleEvaluator.convertToPattern(this.getRuleDefinition().getTableNameLike());
        this.loadedTableNameLike = true;
        return this.tableNameLike;
    }

    public Pattern getColumnNameLike() {
        if (this.loadedColumnNameLike) {
            return columnNameLike;
        }
        this.columnNameLike = ColumnNameRuleEvaluator.convertToPattern(this.getRuleDefinition().getColumnNameLike());
        if (Objects.equals(columnNameLike.pattern(), ".*")){
            this.columnNameLike = null;
        }
        this.loadedColumnNameLike = true;
        return this.columnNameLike;
    }

    private Set<String> getTableTypes() {
        if (this.tableTypesLoaded) {
            return tableTypes;
        }
        ContextTable.TableType[] tableTypes = this.getRuleDefinition().getTableTypes();
        if (tableTypes != null) {
            this.tableTypes = Arrays.stream(tableTypes).map(ContextTable.TableType::getTypeName).collect(Collectors.toSet());
        }

        this.tableTypesLoaded = true;
        return this.tableTypes;
    }

    private Integer getMinimumLength() {
        return this.getRuleDefinition().getMinimumLength();
    }

    private Integer getMaximumLength() {
        return this.getRuleDefinition().getMaximumLength();
    }

    private Integer getMarkThresholds(){
        return this.getRuleDefinition().getMarkThresholds();
    }

    public Pattern[] getExcludeSchema() {
        if (this.excludedSchemas != null) {
            return this.excludedSchemas;
        }
        Pattern[] excludedSchemas = ColumnNameRuleEvaluator.convertToPatterns(this.getRuleDefinition().getExcludedSchemas());
        if (excludedSchemas.length > 0) {
            ExcludePatternComparator excludePatternComparator = new ExcludePatternComparator();
            Arrays.sort(excludedSchemas, excludePatternComparator);
        }
        this.excludedSchemas = excludedSchemas;
        return this.excludedSchemas;
    }

    public Pattern[] getExcludeTable() {
        if (this.excludedTables != null) {
            return this.excludedTables;
        }
        Pattern[] excludedTables = ColumnNameRuleEvaluator.convertToPatterns(this.getRuleDefinition().getExcludedTables());
        if (excludedTables.length > 0) {
            ExcludePatternComparator var2 = new ExcludePatternComparator();
            Arrays.sort(excludedTables, var2);
        }
        this.excludedTables = excludedTables;
        return this.excludedTables;
    }

    public TableColumn[] getExcludeTableColumn() {
        if (this.excludedTableColumns != null) {
            return this.excludedTableColumns;
        }
        TableColumn[] excludedTableColumns = this.getRuleDefinition().getExcludedTableColumns();
        if (excludedTableColumns == null) {
            excludedTableColumns = ColumnNameRuleEvaluator.NO_COLUMNS;
        } else if (excludedTableColumns.length > 0) {
            ExcludeTableColumnComparator excludeTableColumnComparator = new ExcludeTableColumnComparator();
            Arrays.sort(excludedTableColumns, excludeTableColumnComparator);
        }
        this.excludedTableColumns = excludedTableColumns;
        return this.excludedTableColumns;
    }

    private List<ContextColumn.ColumnType> getDataTypes() {
        if (this.dataTypes != null) {
            return this.dataTypes;
        }
        List<ContextColumn.ColumnType> dataTypes;
        ContextColumn.ColumnType[] columnTypes = this.getRuleDefinition().getColumnTypes();
        if (columnTypes != null && columnTypes.length != 0) {
            dataTypes = Arrays.asList(columnTypes);
        } else {
            dataTypes = DEFAULT_COLUMN_TYPES;
        }
        this.dataTypes = dataTypes;
        return this.dataTypes;
    }

    Pattern getSearchValuePattern() throws RuleEvaluationException {
        if (this.loadedSearchValuePattern) {
            return this.searchValueExpression;
        }
        String searchValuePattern = this.getRuleDefinition().getSearchValuePattern();
        if (StringUtils.isBlank(searchValuePattern)) {
            this.loadedSearchValuePattern = true;
            return this.searchValueExpression;
        }
        try {
            this.searchValueExpression = Pattern.compile(searchValuePattern);
        } catch (PatternSyntaxException var4) {
            String var3 = Messages.getString("Could not compile Regular Expression: ${expression}.", "expression", searchValuePattern);
            throw new RuleEvaluationException(var3, var4.getLocalizedMessage(), var4);
        }
        this.loadedSearchValuePattern = true;
        return this.searchValueExpression;
    }

    Pattern getSearchValueLike() {
        if (this.loadedSearchValueLike) {
            return this.searchValueLike;
        }
        this.searchValueLike = ColumnNameRuleEvaluator.convertToPattern(this.getRuleDefinition().getSearchValueLike());
        this.loadedSearchValueLike = true;
        return this.searchValueLike;
    }

    public boolean reportHits() {
        try {
            return this.getRequiredHitPercentage() >= 0;
        } catch (RuleEvaluationException exception) {
            return false;
        }
    }

    private int getRequiredHitPercentage() throws RuleEvaluationException {
        if (this.hitPercentage != null) {
            return this.hitPercentage;
        }
        Integer requiredHitPercentage = this.getRuleDefinition().getRequiredHitPercentage();
        if (requiredHitPercentage != null && requiredHitPercentage > 0) {
            this.hitPercentage = requiredHitPercentage;
        } else {
            this.hitPercentage = -1;
        }
        return this.hitPercentage;
    }

    public RuleResults runRule(RuleContext ruleContext, SearchParameters searchParameters) {
        try {
            // 这里排除的逻辑 是匹配时的排除，即使不匹配，也会收集到 inventory
            // 是否匹配支持的 tableTypes
            Set<String> tableTypes = this.getTableTypes();
            // todo: 如果配置不采样视图，那么我们就也跳过视图的同义词采样。其实就获取引用的 table，再 match 一次。
            String tableTypeName = ruleContext.getCurrentTable().getTableTypeName();
            String typename = ruleContext.getCurrentTable().getType().getTypeName();
            if (tableTypes != null && !tableTypes.contains(typename) && !tableTypes.contains(tableTypeName)) {
                LOG.debug("table type not matched, tableName: {}, tableType: {}{}, allowed tableTypes: {}",
                        ruleContext.getCurrentTable().getTableName(), typename, tableTypeName, tableTypes);
                return new RuleResults(searchParameters, false);
            }
            // 排除 schema，排除指定 table，过滤匹配 tableNameLike
            RuleResults ruleResults = ColumnNameRuleEvaluator.evaluateInitial(ruleContext, searchParameters,
                    this.getExcludeSchema(), this.getExcludeTable(), this.getTableNameLike());
            if (ruleResults != null) {
                if (LOG.isDebugEnabled()){
                    Pattern[] excludeSchema = this.getExcludeSchema();
                    String excludeSchemas = Arrays.stream(excludeSchema).map(Pattern::pattern).collect(Collectors.joining(","));
                    Pattern[] excludeTable = this.getExcludeTable();
                    String excludeTables = Arrays.stream(excludeTable).map(Pattern::pattern).collect(Collectors.joining(","));
                    LOG.debug("Filter check failed; tableIdentityName: {}, Exclude schema: {}, exclude specified table: {}, " +
                                    "filter matching tableNameLike: {}",
                            ruleContext.getCurrentTable().getIdentityName(),
                            excludeSchemas, excludeTables,this.getTableNameLike().pattern());
                }
                return ruleResults;
            }

            List<ContextColumn> searchableColumns = searchParameters.getSearchableColumns();
            if (searchableColumns.isEmpty()) {
                LOG.debug("searchableColumns check failed; tableIdentityName: {}",
                        ruleContext.getCurrentTable().getIdentityName());
                return new RuleResults(searchParameters, false);
            }

            List<ContextColumn> columns = new LinkedList<>(searchableColumns);
            int columnsSize = columns.size();
            // 将查找到的 table column 与扫描参数定义的 列的类型匹配，如果参数不要求匹配这种类型的 column，在这一步该 column 会被过滤掉。
            boolean unmatched = RuleHelper.matchColumnType(columns, this.getDataTypes()) == 0;//内部会同时删除类型不匹配的列
            LOG.debug("Columns type check; tableIdentityName: {}, Column type requirements: {}, columns size: {}, column type matched size: {}",
                    ruleContext.getCurrentTable().getIdentityName(), this.getDataTypes(), columnsSize, columns.size());
            TableColumn[] excludeTableColumns;
            if (!unmatched && (excludeTableColumns = this.getExcludeTableColumn()) != null && excludeTableColumns.length != 0) {
                String tableName = ruleContext.getCurrentTable().getTableName();
                for (TableColumn excludeTableColumn : excludeTableColumns) {
                    if (tableName.equals(excludeTableColumn.tableName) &&
                            RuleHelper.filterOnColumnMatch(columns, excludeTableColumn.columnName) == 0) {
                        return new RuleResults(searchParameters, false);
                    }
                }
            }

            boolean existDataType = this.getRuleDefinition().getColumnTypes() != null;
            Pattern columnNameLike = this.getColumnNameLike();
            if (!unmatched && columnNameLike != null) {
                existDataType = true;
                unmatched = RuleHelper.filterColumnsByName(columns, columnNameLike) == 0;
            }

            // 因为 max compute 目前读取到所有的数据类型长度，发现都是 0，所以这里针对 max compute 不再检查最小长度。
            Integer minLen;
            if (!unmatched && (minLen = this.getMinimumLength()) != null
                    && !ruleContext.getDatasource().getType().equals(DataSourceType.MAXCOMPUTE)
                    && !ruleContext.getDatasource().getType().equals(DataSourceType.HIVE)
                    && !ruleContext.getDatasource().getType().equals(DataSourceType.TRANSWARP_INCEPTOR)
            ) {
                existDataType = true;
                unmatched = RuleHelper.filterColumnsByMinimumLength(columns, minLen) == 0;
            }

            Integer maxLen;
            if (!unmatched && (maxLen = this.getMaximumLength()) != null
                    && !ruleContext.getDatasource().getType().equals(DataSourceType.MAXCOMPUTE)
                    && !ruleContext.getDatasource().getType().equals(DataSourceType.HIVE)
                    && !ruleContext.getDatasource().getType().equals(DataSourceType.TRANSWARP_INCEPTOR)
            ) {
                existDataType = true;
                unmatched = RuleHelper.filterColumnsByMaximumLength(columns, maxLen) == 0;
            }

            if (unmatched) {
                return new RuleResults(searchParameters, false, existDataType, this.getContinueOption().useUnmatchedColumns());
            }

            if (this.hasMatchData()) {
                try {
                    return this.matchData(ruleContext, searchParameters, columns);
                } catch (Exception e) {
                    LOG.error(e.getMessage(), e);
                    ruleContext.getTaskGlobalDataHolder().getProbeClientTaskContext()
                                    .reportErrorOccurredExecuting(StatusRecord.Position.MatchData, null, e.getMessage(), e);
                    return new RuleResults(searchParameters, false);
                }
            }

            return new RuleResults(searchParameters, true, true, columns,
                    this.getContinueOption().useUnmatchedColumns());
        } catch (Exception e) {
            ruleContext.getTaskGlobalDataHolder().getProbeClientTaskContext()
                    .reportErrorOccurredExecuting(StatusRecord.Position.RunRule, null,  "ColumnDataRuleEvaluator.runRule", e);
            return new RuleResults(searchParameters, false);
        }
    }

    @Override
    public String getExprType() {
        return "rule";
    }

    boolean hasMatchData() {
        if (this.loadedHasMatchData) {
            return this.hasMatchData;
        }
        this.hasMatchData = true;
        // 这里还要加上值的字典匹配
//                this.getSearchValueLike() != null
//                || this.getSearchValues() != null
//                // 校验
//                || StringUtils.isNotBlank(this.getRuleDefinition().getSearchValuePattern());
        this.loadedHasMatchData = true;
        return this.hasMatchData;
    }

    RuleResults matchData(RuleContext ruleContext, SearchParameters searchParameters, List<ContextColumn> columns) throws ClassifierException {
        Pattern searchValueLike = this.getSearchValueLike();
        Pattern searchValuePattern = this.getSearchValuePattern();

        boolean isSearchValueLike = searchValueLike != null;
        boolean isSearchValuePattern = searchValuePattern != null;

        List<Pair<ContextColumn, MatchedResult>> matchedColumnsResult = new ArrayList<>(columns.size());

        RuleResults.Hits hits = new RuleResults.Hits();

        LOG.debug("Checking for: {}{}{} on: {}", isSearchValueLike ? "'" + searchValueLike.pattern() + "'" : "", isSearchValueLike && isSearchValuePattern ? ", " : "", isSearchValuePattern ? "'" + searchValuePattern.pattern() + "'" : "", ruleContext.getCurrentTableName());

        ContextTable currentTable = ruleContext.getCurrentTable();
        String tableName = currentTable.getTableName();
        // 获取采样数据
        Map<String, List<String>> sampleData = null;
        try {
            sampleData = ruleContext.getSampleFor(tableName);
        } catch (Exception e){
            LOG.error(e.getMessage());
        }
        if (sampleData == null) {
            sampleData = new HashMap<>();
//            return new RuleResults(searchParameters, matchedColumnsResult, null, hits, this.getContinueOption().useUnmatchedColumns());
        }

        ScoringModel scoringModel = ruleContext.getTaskGlobalDataHolder().getEnhancedRuleModelMap().get(this.getTranslatedName());
        if (scoringModel == null) {
            LOG.error("dataTag[{}] scoring model is null", this.getTranslatedName());
            return new RuleResults(searchParameters, matchedColumnsResult, null, hits, this.getContinueOption().useUnmatchedColumns());
        }

        Map<String, Set<String>> dataMarkings = ruleContext.getTaskGlobalDataHolder().getDataMarkings();
        Long tenantId = ruleContext.getDatasource().getTenantId();
        String dataSourceId = ruleContext.getDatasource().getId();
        String inventoryDbName = ruleContext.getInventoryDbName();

        for (ContextColumn contextColumn : columns) {
            String qualName = inventoryDbName == null ? contextColumn.getIdentityName() : inventoryDbName + "." + contextColumn.getIdentityName();
            Set<String> columnManualTagsSet = new HashSet<>();
            Set<String> columnDeletedAutoTagsSet = new HashSet<>();
            if (dataMarkings != null) {
                String manualTagsKey = tenantId + "_" + dataSourceId + "_" + qualName.toLowerCase() + "_ManualTags";
                String deletedAutoTagsKey = tenantId + "_" + dataSourceId + "_" + qualName.toLowerCase() + "_DeletedAutoTags";
                Set<String> manualTagsSet = dataMarkings.get(manualTagsKey);
                if (manualTagsSet != null){
                    columnManualTagsSet.addAll(manualTagsSet);
                }
                Set<String> deletedAutoTagsSet = dataMarkings.get(deletedAutoTagsKey);
                if (deletedAutoTagsSet != null){
                    columnDeletedAutoTagsSet.addAll(deletedAutoTagsSet);
                }
            }
            String columnName = contextColumn.getColumnName();
            String comment = contextColumn.getRemarks();
            int size = contextColumn.getSize();
            String typename = contextColumn.getType().getTypeName();
            List<String> columnValues = sampleData.get(columnName);
            if (columnValues != null && !columnValues.isEmpty()) {
                columnValues = columnValues.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            }

            // 获取当前列的之前的打标结果。我们认为 基础类型和业务类型的 dataTag 应该是不一样的，
            // 所以这里可以统一将所有的打标结果放到一个列表中，不用考虑 dataTag 重复的问题。
            Map<String, List<MatchedResult>> matchedResultMap = searchParameters.getMatchedResultMap();
            List<MatchedResult> currentColumnTags = matchedResultMap.get(columnName);
            Map<String,Integer> currentColumnMarked = null;
            if (currentColumnTags != null && !currentColumnTags.isEmpty()) {
                currentColumnMarked = currentColumnTags.stream()
                        .filter(item -> Objects.equals(item.getEvaluation(), MatchedResult.CONFIRMED))
                        .map((matchedResult) -> {
                            return new AbstractMap.SimpleImmutableEntry<>(matchedResult.getDataTag(), matchedResult.getScore());
                        })
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,  (v1, v2)->{
                            // 如果有重复的，取分数高的
                            return v1>v2?v1:v2;
                        }));
            }

            Map<String, Object> modelInputData = UtilMisc.toMap(
                    "dataStore", UtilMisc.toMap(
                            "name", inventoryDbName
                    ),
                    "table", UtilMisc.toMap(
                            "name", tableName,
                            "type", currentTable.getTableTypeName(),
                            "comment", currentTable.getTableComment(),
                            "catalog", currentTable.getCatalog(),
                            "schema", currentTable.getSchema()
                    ),
                    "column", UtilMisc.toMap(
                            "name", columnName,
                            "type", typename,
                            "comment", comment,
                            "size", size,
                            "values", columnValues,
                            // 该列 当前已经自动标记的。这包含了分数
                            "currentMarked", currentColumnMarked,
                            // 该列 已经手动打标的
                            "manualTags", columnManualTagsSet,
                            // 该列 已经排除的标签
                            "deletedAutoTags", columnDeletedAutoTagsSet
                    ),
                    "dataTag", this.getTranslatedName()
            );

            ScoringModelOutput interpret = scoringModel.interpret(new ScoringModelInput(modelInputData));
            MatchedResult matchedResult = interpret.getMatchedResult(this.getMarkThresholds());
            if (matchedResult == null) {
                continue;
            }
            LOG.debug("输入数据: {}", modelInputData);
//          LOG.debug("模型数据: "+);
            LOG.debug("匹配结果: {}", matchedResult);
            if (matchedResult.notMatched()) {
                continue;
            }
            matchedColumnsResult.add(Pair.of(contextColumn, matchedResult));
        }
        List<ContextColumn> matchedColumns = matchedColumnsResult.stream().map(Pair::getKey).collect(Collectors.toList());
        return new RuleResults(searchParameters, matchedColumnsResult, matchedColumns, hits, this.getContinueOption().useUnmatchedColumns());
    }

    private boolean shouldFire(RuleResults.Hits hits, List<String> columnValues, String columnName, int matchedCount, int columnValuesLength) throws RuleEvaluationException {
        int hitsCount = matchedCount > 0 && this.reportHits() ? matchedCount : (matchedCount <= 0 ? 0 : 1);
        boolean isHit;
        if (columnValuesLength > 0 && hitsCount > 0 && this.reportHits()) {
            // 是否匹配命中率
            isHit = hitsCount * 100 / columnValuesLength >= this.getRequiredHitPercentage();
        } else {
            isHit = hitsCount > 0;
        }

        if (!isHit) {
            String dataTypeName = this.getName();
            String dataTag = this.getTranslatedName();
            if (columnValues != null) {
                LOG.debug("dataTypeName [" + dataTypeName + "], dataTag [" + dataTag + "], NO HIT for column: " + columnName + System.lineSeparator() + " values: " + columnValues);
            } else {
                LOG.debug("dataTypeName [" + dataTypeName + "], dataTag [" + dataTag + "], NO HIT for column: " + columnName);
            }
            return false;
        }

        hits.update(columnValuesLength, hitsCount, columnName, null);
        String dataTypeName = this.getName();
        String dataTag = this.getTranslatedName();
        if (columnValues != null) {
            LOG.debug("dataTypeName [" + dataTypeName + "], dataTag [" + dataTag + "], HIT for column: " + columnName + System.lineSeparator() + " values: " + columnValues);
        } else {
            LOG.debug("dataTypeName [" + dataTypeName + "], dataTag [" + dataTag + "], HIT for column: " + columnName);
        }
        return true;
    }

    @Override
    public int compareTo(ColumnComplexRuleEvaluator ruleEvaluator) {
        return Integer.compare(this.orderBy, ruleEvaluator.orderBy);
    }


}
