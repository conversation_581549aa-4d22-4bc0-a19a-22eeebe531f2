package com.dcap.classifier.rules;

import com.dcap.classifier.ClassifierException;
import com.dcap.classifier.RuleEvaluator;
import com.dcap.classifier.RuleResults;
import com.dcap.classifier.SearchParameters;
import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.RuleContext;
import com.dcap.datalayer.AbstractClassifierColumnNameRule;
import com.dcap.datalayer.ClassifierRuleRecord;
import com.dcap.datalayer.TableColumn;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.regex.Pattern;

public class ColumnNameRuleEvaluator extends RuleEvaluator {
    public static final List<ContextTable.TableType> DEFAULT_TABLE_TYPES;
    public static final Pattern SQL_CHAR_WILDCARD;
    public static final Pattern OS_CHAR_WILDCARD;
    public static final Pattern SQL_ESCAPE_BAR_WILDCARD;
    static final String[] NO_STRINGS = new String[0];
    static final Pattern[] NO_PATTERNS = new Pattern[0];
    static final TableColumn[] NO_COLUMNS = new TableColumn[0];
    private static final Pattern SQL_STRING_WILDCARD;
    private static final Pattern SQL_ESCAPE_PCT_WILDCARD;

    static {
        // todo: DEFAULT_TABLE_TYPES 删除 SYSTEM_TABLE
//        DEFAULT_TABLE_TYPES = Arrays.asList(ContextTable.TABLE, ContextTable.VIEW, ContextTable.SYNONYM, ContextTable.SYSTEM_TABLE);
        DEFAULT_TABLE_TYPES = Arrays.asList(ContextTable.TABLE, ContextTable.VIEW, ContextTable.SYNONYM);
        SQL_STRING_WILDCARD = Pattern.compile("(?<!\\\\)%|(?<!\\\\)\\*");
        SQL_ESCAPE_PCT_WILDCARD = Pattern.compile("\\\\%|\\\\\\\\%");
        SQL_CHAR_WILDCARD = Pattern.compile("(?<!\\\\)_");
        OS_CHAR_WILDCARD = Pattern.compile("(?<!\\\\)\\?");
        SQL_ESCAPE_BAR_WILDCARD = Pattern.compile("\\\\_|\\\\\\\\_");
    }

    private boolean loadedTableNameLike;
    private boolean loadedColumnNameLike;
    private Pattern tableNameLike;
    private Pattern columnNameLike;
    private Pattern[] excludedSchemas;
    private Pattern[] excludedTables;
    private TableColumn[] excludedTableColumns;
    private List<ContextTable.TableType> tableTypes;

    public ColumnNameRuleEvaluator(AbstractClassifierColumnNameRule<? extends ClassifierRuleRecord> var1) {
        super(var1);
    }

    static Pattern convertToPattern(String var0) {
        if (var0 == null) {
            return null;
        }
        var0 = var0.trim();
        if (var0.length() > 0) {
            return createWildcardPattern(var0);
        }
        return null;
    }

    private static Pattern createWildcardPattern(String var0) {
        var0 = SQL_ESCAPE_BAR_WILDCARD.matcher(OS_CHAR_WILDCARD.matcher(SQL_CHAR_WILDCARD.matcher(SQL_ESCAPE_PCT_WILDCARD.matcher(SQL_STRING_WILDCARD.matcher(var0).replaceAll(".*")).replaceAll("%")).replaceAll(".")).replaceAll(".")).replaceAll("_");
        return Pattern.compile(var0, Pattern.CASE_INSENSITIVE | Pattern.DOTALL | Pattern.UNICODE_CASE | Pattern.CANON_EQ);
    }

    static Pattern[] convertToPatterns(String[] var0) {
        if (var0 == null || var0.length == 0) {
            return NO_PATTERNS;
        }
        List<Pattern> patterns = new ArrayList<>();

        for (String var3 : var0) {
            Pattern var4 = convertToPattern(var3);
            if (var4 != null) {
                patterns.add(var4);
            }
        }
        if (patterns.size() > 0) {
            return patterns.toArray(new Pattern[0]);
        }
        return NO_PATTERNS;
    }

    static RuleResults evaluateInitial(RuleContext ruleContext, SearchParameters searchParameters, Pattern[] excludeSchemaPattern,
                                       Pattern[] excludeTablePattern, Pattern tableNameLikePattern) {
        if (excludeSchemaPattern != null && excludeSchemaPattern.length != 0) {
            String schema = ruleContext.getCurrentTable().getSchema();
            if (StringUtils.isNotBlank(schema)) {
                for (Pattern pattern : excludeSchemaPattern) {
                    if (RuleHelper.matchPattern(schema.toLowerCase(), pattern)) {
                        return new RuleResults(searchParameters, false);
                    }
                }
            }
        }

        if (excludeTablePattern.length != 0) {
            String tableName = ruleContext.getCurrentTable().getTableName();
            if (StringUtils.isNotBlank(tableName)) {
                for (Pattern pattern : excludeTablePattern) {
                    if (RuleHelper.matchPattern(tableName.toLowerCase(), pattern)) {
                        return new RuleResults(searchParameters, false);
                    }
                }
            }
        }

        if (tableNameLikePattern != null) {
            String tableName = ruleContext.getCurrentTable().getTableName();
            if (StringUtils.isNotBlank(tableName) && !RuleHelper.matchPattern(tableName.toLowerCase(), tableNameLikePattern)) {
                return new RuleResults(searchParameters, false);
            }
        }

        return null;
    }

    public AbstractClassifierColumnNameRule<? extends ClassifierRuleRecord> getRuleDefinition() {
        return (AbstractClassifierColumnNameRule<? extends ClassifierRuleRecord>) super.getRuleDefinition();
    }

    public Pattern[] getExcludeSchema() {
        if (this.excludedSchemas != null) {
            return this.excludedSchemas;
        }
        this.excludedSchemas = convertToPatterns(this.getRuleDefinition().getExcludedSchemas());
        if (this.excludedSchemas.length > 0) {
            Arrays.sort(this.excludedSchemas, new ExcludePatternComparator());
        }
        return this.excludedSchemas;
    }

    public Pattern[] getExcludeTable() {
        if (this.excludedTables != null) {
            return this.excludedTables;
        }
        this.excludedTables = convertToPatterns(this.getRuleDefinition().getExcludedTables());
        if (this.excludedTables.length > 0) {
            Arrays.sort(this.excludedTables, new ExcludePatternComparator());
        }
        return this.excludedTables;
    }

    public TableColumn[] getExcludeTableColumn() {
        if (this.excludedTableColumns != null) {
            return this.excludedTableColumns;
        }
        this.excludedTableColumns = this.getRuleDefinition().getExcludedTableColumns();
        if (this.excludedTableColumns == null) {
            this.excludedTableColumns = NO_COLUMNS;
        } else if (this.excludedTableColumns.length > 0) {
            Arrays.sort(this.excludedTableColumns, new ExcludeTableColumnComparator());
        }
        return this.excludedTableColumns;
    }

    List<ContextTable.TableType> getTableTypes() {
        if (this.tableTypes != null) {
            return this.tableTypes;
        }
        List<ContextTable.TableType> tableTypes;
        ContextTable.TableType[] tableTypeAry = this.getRuleDefinition().getTableTypes();
        if (tableTypeAry != null && tableTypeAry.length != 0) {
            tableTypes = Arrays.asList(tableTypeAry);
        } else {
            tableTypes = DEFAULT_TABLE_TYPES;
        }
        this.tableTypes = tableTypes;

        return this.tableTypes;
    }

    public Pattern getTableNameLike() {
        if (!this.loadedTableNameLike) {
            this.tableNameLike = convertToPattern(this.getRuleDefinition().getTableNameLike());
            this.loadedTableNameLike = true;
        }
        return this.tableNameLike;
    }

    public Pattern getColumnNameLike() {
        if (!this.loadedColumnNameLike) {
            String columnNameLikeText = this.getRuleDefinition().getColumnNameLike();
            try{
                // todo: 对于 ColumnNameRuleEvaluator 中，columnNameLike 参数是来自 data_type 表中的 expr 字段，这里直接 Pattern.compile
                this.columnNameLike = Pattern.compile(columnNameLikeText, Pattern.CASE_INSENSITIVE);
            } catch (Exception e){
                LOG.error("column name like text compile fail："+columnNameLikeText+", error message："+e.getMessage());
            }
//            this.columnNameLike = convertToPattern(this.getRuleDefinition().getColumnNameLike());
            this.loadedColumnNameLike = true;
        }
        return this.columnNameLike;
    }


    public RuleResults runRule(RuleContext ruleContext, SearchParameters searchParameters) throws ClassifierException {
        try {
            if (!RuleHelper.matchTableType(ruleContext.getCurrentTable(), this.getTableTypes())) {
                return new RuleResults(searchParameters, false);
            }
//            String tName = ruleContext.getCurrentTable().getTableName();
            RuleResults ruleResults = ColumnNameRuleEvaluator.evaluateInitial(ruleContext, searchParameters,
                    this.getExcludeSchema(), this.getExcludeTable(), this.getTableNameLike());
            if (ruleResults != null) {
                return ruleResults;
            }
            List<ContextColumn> columns = new LinkedList<>(searchParameters.getSearchableColumns());
            TableColumn[] excludeTableColumns = this.getExcludeTableColumn();
            if (excludeTableColumns != null && excludeTableColumns.length != 0) {
                String tableName = ruleContext.getCurrentTable().getTableName();
                for (TableColumn excludeTableColumn : excludeTableColumns) {
                    if (tableName.equals(excludeTableColumn.tableName) && RuleHelper.filterOnColumnMatch(columns, excludeTableColumn.columnName) == 0) {
                        return new RuleResults(searchParameters, false);
                    }
                }
            }

            Pattern columnNameLike = this.getColumnNameLike();
            if (columnNameLike != null) {
                boolean wasMatch = RuleHelper.filterColumnsByName(columns, columnNameLike) > 0;
                return new RuleResults(searchParameters, wasMatch, true, columns, this.getContinueOption().useUnmatchedColumns());
            }
            return new RuleResults(searchParameters, true);
        } catch (Exception e) {
            ruleContext.getTaskGlobalDataHolder().getProbeClientTaskContext()
                    .reportErrorOccurredExecuting(StatusRecord.Position.RunRule, null, "ColumnNameRuleEvaluator.runRule", e);
            return new RuleResults(searchParameters, false);
        }
    }

    @Override
    public String getExprType() {
        return "catalog";
    }

    private static class ExcludeTableColumnComparator implements Comparator<TableColumn> {
        private ExcludeTableColumnComparator() {
        }

        public int compare(TableColumn var1, TableColumn var2) {
            if (var1 != null && var1.tableName != null) {
                if (var2 != null && var2.tableName != null) {
                    String var3 = var1.tableName + var1.columnName;
                    String var4 = var2.tableName + var2.columnName;
                    return var3.compareTo(var4);
                } else {
                    return -1;
                }
            } else {
                return 1;
            }
        }
    }
}
