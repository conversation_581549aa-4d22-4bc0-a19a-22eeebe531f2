package com.dcap.classifier;


import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ClassifierException extends Exception {
   private String details = null;
   private StackTraceElement reporter = null;

   protected StackTraceElement getReporterElement() {
      if (this.reporter == null) {
         StackTraceElement[] var1 = this.getStackTrace();

         for (StackTraceElement stackTraceElement : var1) {
            Class var3 = null;

            try {
               var3 = Class.forName(stackTraceElement.getClassName());
            } catch (ClassNotFoundException var5) {
               log.warn("Could not find the class: " + stackTraceElement.getClassName(), var5);
               continue;
            }

            if (!Throwable.class.isAssignableFrom(var3)) {
               this.reporter = stackTraceElement;
               break;
            }
         }

         if (this.reporter == null) {
            this.reporter = var1[var1.length - 1];
         }
      }

      return this.reporter;
   }

   public String getReporterClassname() {
      return this.getReporterElement() != null ? this.getReporterElement().getClassName() : null;
   }

   public String getReporterMethod() {
      return this.getReporterElement() != null ? this.getReporterClassname() + '.' + this.getReporterElement().getMethodName() + '(' + this.getReporterElement().getLineNumber() + ')' : null;
   }

   public Class<?> getReporterClass() {
      Class var1 = null;

      try {
         var1 = Class.forName(this.getReporterClassname());
      } catch (ClassNotFoundException var3) {
         log.warn("Could not find the class: " + this.getReporterClassname(), var3);
      }

      return var1;
   }

   public String addDetails(String var1) {
      this.details = this.details + System.lineSeparator() + var1;
      return this.details;
   }

   public String getDetails() {
      return this.details;
   }

   public String getAll() {
      return this.getLocalizedMessage() + System.lineSeparator() + this.getDetails();
   }

   public ClassifierException(String var1) {
      super(var1);
   }

   public ClassifierException(String var1, String var2) {
      super(var1);
      this.details = var2;
   }

   public ClassifierException(String var1, Throwable var2) {
      super(var1, var2);
   }

   public ClassifierException(String var1, String var2, Throwable var3) {
      super(var1, var3);
      this.details = var2;
   }

   public ClassifierException(Throwable var1) {
      super(var1);
   }
}
