package com.dcap.classifier;


import lombok.extern.slf4j.Slf4j;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Properties;

@Slf4j
public class ClassifierProperties {
   private static final Properties PROPS = new Properties();
   private static final String FILENAME = "guard-classifier-application.properties";
   public static final String SAMPLE_SQL_EXEC_TIMEOUT = "sql.execution.timeout.seconds";
   public static final String COUNT_SQL_EXEC_TIMEOUT = "count.sql.execution.timeout.seconds";

   public ClassifierProperties() {
   }

   public static String getProperty(String var0, String var1) {
      return PROPS.getProperty(var0, var1);
   }

   public static String getProperty(String var0) {
      return PROPS.getProperty(var0);
   }

   public static void load(String var0) throws IOException {
      File var1 = new File(var0, "dcap-classifier-application.properties");
      if (!var1.canRead()) {
         String var14 = "Cannot read the properties file: " + var1;
         log.error(var14);
         throw new IOException(var14);
      } else {
         BufferedInputStream var2 = new BufferedInputStream(new FileInputStream(var1));
         Throwable var3 = null;

         try {
            PROPS.load(var2);
            if (log.isDebugEnabled()) {
               log.debug("Loaded properties from: " + var1);
            }
         } catch (Throwable var12) {
            var3 = var12;
            throw var12;
         } finally {
            if (var2 != null) {
               if (var3 != null) {
                  try {
                     var2.close();
                  } catch (Throwable var11) {
                     var3.addSuppressed(var11);
                  }
               } else {
                  var2.close();
               }
            }

         }

      }
   }
}
