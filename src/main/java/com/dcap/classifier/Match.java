package com.dcap.classifier;

import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.RuleContext;
import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.utils.Messages;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

public class Match implements ResultInfo {
    private static final ContextColumn[] NO_COLUMNS = new ContextColumn[0];
    private final ContextTable table;
    private final ClassifierDataSource datasource;
    private final RuleEvaluator rule;
//    private final RuleResults results;
    private final ContextColumn[] matchedColumns;
    private final RuleContext context;

    public Match(RuleEvaluator rule, RuleContext context) {
        this(context.getCurrentTable(), context.getDatasource(), rule, NO_COLUMNS, context);
    }

    public Match(RuleEvaluator rule, List<ContextColumn> matchedColumns, RuleContext context) {
        this(context.getCurrentTable(), context.getDatasource(), rule, matchedColumns.toArray(new ContextColumn[0]), context);
    }

    private Match(ContextTable table, ClassifierDataSource datasource, RuleEvaluator rule,
                  ContextColumn [] matchedColumns, RuleContext context) {
        this.table = table;
        this.datasource = datasource;
        this.rule = rule;
//        this.results = rule.getLastResults();
        this.matchedColumns = matchedColumns;
        this.context = context;
    }

    public ContextTable getTable() {
        return this.table;
    }

    public RuleEvaluator getRule() {
        return this.rule;
    }

//    public RuleResults getResults() {
//        return this.results;
//    }

    public String getPolicyCategory() {
        return this.rule.getCategory();
    }

    public String getPolicyClassification() {
        return this.rule.getClassification();
    }

    public String getRuleName() {
        return this.rule.getName();
    }

    public String getCatalog() {
        return this.table.getCatalog();
    }

    public String getSchema() {
        return this.table.getSchema();
    }

    public String getTableName() {
        return this.table.getIdentityName();
    }

    public String getTableNameNoSchema() {
        return this.table.getTableName();
    }

    public List<String> getMatchedColumnNames() {
        return matchedColumns == null? Collections.emptyList(): Arrays.stream(matchedColumns).map(ContextColumn::getColumnName).collect(Collectors.toList());
    }

    public RuleContext getContext() {
        return this.context;
    }

    public ClassifierDataSource getDatasource() {
        return this.datasource;
    }

    public String getDatasourceName() {
        return this.datasource.getAttributes();
    }

    public String getSummary() {
        return null;
//        if (!this.results.usedColumns()) {
//            this.describeResult(null, null);
//        }
//        String columnNames = null;
//        List<String> matchedColumnNames = this.results.getMatchedColumnNames();
//        if (matchedColumnNames != null && matchedColumnNames.size() > 0) {
//            String columnNamesText = matchedColumnNames.toString();
//            columnNames = columnNamesText.substring(1, columnNamesText.length() - 1);
//        }
//        return this.describeResult(columnNames, null);
    }

    public Iterator<ResultInfo.SpecificResultInfo> getColumnSpecificResults() {
        return new Iterator<ResultInfo.SpecificResultInfo>() {
            int resultIndex;

            {
                this.resultIndex = Match.this.matchedColumns.length == 0 ? -1 : 0;
            }

            public boolean hasNext() {
                return this.resultIndex < Match.this.matchedColumns.length;
            }

            public ResultInfo.SpecificResultInfo next() {
                if (!this.hasNext()) {
                    throw new NoSuchElementException();
                }
                ResultInfo.SpecificResultInfo specificResultInfo;
                if (this.resultIndex < 0) {
                    specificResultInfo = new ResultInfo.SpecificResultInfo() {
                        public String getColumnName() {
                            return null;
                        }

                        public String getSummary() {
                            return Match.this.getSummary();
                        }
                    };
                } else {
                    specificResultInfo = new ResultInfo.SpecificResultInfo() {
                        final int columnIndex;

                        {
                            this.columnIndex = resultIndex;
                        }

                        public String getColumnName() {
                            return Match.this.matchedColumns[this.columnIndex].getColumnName();
                        }

                        public String getSummary() {
                            return null;
//                            return this.describeColumnResult(this.getColumnName());
                        }

//                        private String describeColumnResult(String columnName) {
//
//                            if (!Match.this.rule.reportHits()) {
//                                return Match.this.describeResult(columnName, null);
//                            }
//
//
//                            String hits = Match.this.results.getHitsFor(columnName);
//                            if (hits == null) {
//                                return Match.this.describeResult(columnName, null);
//                            }
//                            String samples = Match.this.results.getSampleSizeFor(columnName);
//                            String hitsMsg = Messages.getString("${hits} hits of ${samples} distinct values in sample set", "hits", hits, "samples", samples);
//                            String hitsAndMatch = System.lineSeparator() + hitsMsg;
//                            String matchMsg = Match.this.results.getMatchesFor(columnName);
//                            if (matchMsg != null) {
//                                hitsAndMatch += System.lineSeparator() + matchMsg;
//                            }
//                            return Match.this.describeResult(columnName, hitsAndMatch);
//                        }
                    };
                }

                ++this.resultIndex;
                return specificResultInfo;
            }
        };
    }

    private String describeResult(String columnNames, String hitsAndMatchMessage) {
        StringBuilder describe = new StringBuilder();

        String dateMessage = Messages.getString("Date: ${date}", "date",
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(Calendar.getInstance().getTime()));
        describe.append(dateMessage);

        String datasourceMessage = Messages.getString("Datasource: ${datasource}", "datasource", this.datasource.getInstanceName());
        describe.append(System.lineSeparator()).append(datasourceMessage);

        String objectMessage = "";
        if (columnNames != null && columnNames.trim().length() > 0) {
            objectMessage = ' ' + columnNames;
        }
        objectMessage = Messages.getString("Object: ${object}", "object", this.getTableName() + objectMessage);
        describe.append(System.lineSeparator()).append(objectMessage);


        String categoryMessage = Messages.getString("Category: '${category}' Classification: '${classification}' Comprehensive: ${comprehensive}", "category", this.rule.getCategory(), "classification", this.rule.getClassification(), "comprehensive", "false");
        describe.append(System.lineSeparator()).append(categoryMessage);

        String ruleMessage = "Rule: ${rule}";
        String ruleVariable = "${rule}";
        int ruleVariableIndex = ruleMessage.indexOf(ruleVariable);
        String ruleText;
        if (ruleVariableIndex > -1) {
            ruleText = ruleMessage.substring(0, ruleVariableIndex) + this.rule.describe() + ruleMessage.substring(ruleVariableIndex + ruleVariable.length());
        } else {
            ruleText = Messages.getString("Rule: ${rule}", "rule", this.rule.describe());
        }
        describe.append(System.lineSeparator()).append(ruleText);

        if (hitsAndMatchMessage != null) {
            describe.append(hitsAndMatchMessage);
        }
        return describe.toString();
    }
}
