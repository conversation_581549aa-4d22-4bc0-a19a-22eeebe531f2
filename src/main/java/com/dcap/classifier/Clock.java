package com.dcap.classifier;

import com.dcap.utils.Messages;

public class Clock {
   private int counter;
   private long startTimeMilliseconds = System.currentTimeMillis();

   public Clock() {
   }

   public void incrementCount() {
      ++this.counter;
   }

   public void incrementCount(int var1) {
      this.counter += var1;
   }

   public void setCount(int var1) {
      this.counter = var1;
   }

   public int getCount() {
      return this.counter;
   }

   public void resetStartTime() {
      this.startTimeMilliseconds = System.currentTimeMillis();
   }

   public long getStartMillis() {
      return this.startTimeMilliseconds;
   }

   public boolean isElapsed(long var1) {
      return System.currentTimeMillis() - this.startTimeMilliseconds >= var1;
   }

   public long getElapsedTimeMillis() {
      return System.currentTimeMillis() - this.startTimeMilliseconds;
   }

   public String getElapsedMessageForCounter(String items) {
      return this.getElapsedMessage("Processed "+Long.toString(this.counter)+" "+items);
   }

   public String getElapsedMessage(String actionCompleted) {
      return Messages.getString("${actionCompleted} in ${elapsedTime}", "actionCompleted", actionCompleted, "elapsedTime", this.getElapsedTime());
   }

   public String getElapsedTime() {
      long var1 = System.currentTimeMillis() - this.startTimeMilliseconds;
      if (var1 <= 0L) {
         var1 = 1L;
      }

      long var3 = var1 / 1000L;
      long var5;
      long var7;
      String var9;
      String var10;
      if (var3 < 60L) {
         var5 = var1 - var3 * 1000L;
         var7 = (var5 + 5L) / 10L;
         if (var7 == 100L) {
            ++var3;
            var7 = 0L;
         }

         var9 = Long.toString(var7);
         var10 = "00".substring(var9.length()) + var9;
         String var11 = var3 > 1L ? "秒" : "秒";
         return Messages.getString("${seconds}.${secondsDecimal} ${secondsLabel}", "seconds", Long.toString(var3), "secondsDecimal", var10, "secondsLabel", var11);
      } else if (var3 < 3600L) {
         var5 = var3 / 60L;
         var7 = var3 - var5 * 60L;
         var9 = var5 > 1L ? "分钟" : "分钟";
         var10 = var7 > 1L ? "秒" : "秒";
         return Messages.getString("${minutes} ${minutesLabel} ${seconds} ${secondsLabel}", "seconds", Long.toString(var7), "minutes", Long.toString(var5), "secondsLabel", var10, "minutesLabel", var9);
      } else {
         var5 = var3 / 3600L;
         var3 -= var5 * 3600L;
         var7 = var3 / 60L;
         var9 = var5 > 1L ? "小时" : "小时";
         var10 = var7 > 1L ? "分钟" : "分钟";
         return Messages.getString("${hours} ${hoursLabel}, ${minutes} ${minutesLabel}", "hours", Long.toString(var5), "minutes", Long.toString(var7), "hoursLabel", var9, "minutesLabel", var10);
      }
   }
}
