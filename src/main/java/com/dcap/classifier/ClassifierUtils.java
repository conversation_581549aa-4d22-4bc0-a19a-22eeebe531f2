package com.dcap.classifier;


import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;

@Slf4j
public class ClassifierUtils {
//   public static final int queryTimeoutSeconds = findSampleSqlTimeoutSeconds();

   public ClassifierUtils() {
   }

//   public static int findSampleSqlTimeoutSeconds() {
//      String var0 = ClassifierProperties.getProperty("sql.execution.timeout.seconds", "180");
//      return Integer.parseInt(var0);
//   }

   public static int findCountSqlTimeoutSeconds() {
      String var0 = ClassifierProperties.getProperty("count.sql.execution.timeout.seconds", "180");
      return Integer.parseInt(var0);
   }

   public static Statement disposal(Statement var0) {
      try {
         if (var0 != null) {
            var0.close();
         }
      } catch (Throwable var2) {
         badClose(var0, "java.sql.Statement", var2);
      }

      return null;
   }

   private static void badClose(Object var0, String var1, Throwable var2) {
      if (log.isDebugEnabled()) {
         log.debug("Could not close: " + var1 + ": '" + var0 + "'" + System.getProperty("line.separator"));
      }

   }

   public static ResultSet disposal(ResultSet var0) {
      try {
         if (var0 != null) {
            var0.close();
         }
      } catch (Throwable var2) {
         badClose(var0, "java.sql.ResultSet", var2);
      }

      return null;
   }

   public static Connection disposal(Connection var0) {
      try {
         if (var0 != null) {
            var0.close();
         }
      } catch (Throwable var2) {
         badClose(var0, "java.sql.Connection", var2);
      }

      return null;
   }
}
