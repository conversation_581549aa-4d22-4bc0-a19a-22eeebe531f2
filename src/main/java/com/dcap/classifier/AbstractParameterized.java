package com.dcap.classifier;

public abstract class AbstractParameterized {
   public AbstractParameterized() {

   }

   public String describe() {
      String describeParams = this.describeParams();
      return describeParams.length() > 0 ? this.toString() + System.lineSeparator() + describeParams : this.toString();
   }

   public abstract String toString();

   public abstract String describeParams();

   public static String arrayToString(Object[] var0) {
      return arrayToString(var0, ",");
   }

   public static String arrayToString(Object[] var0, String var1) {
      if (var0.length == 0) {
         return "";
      } else {
         Object var2 = var0[0];
         if (var0.length == 1) {
            return String.valueOf(var2);
         } else {
            StringBuilder var3 = new StringBuilder();
            var3.append(var2);

            for(int var4 = 1; var4 < var0.length; ++var4) {
               var3.append(var1);
               Object var5 = var0[var4];
               if (var5 != null) {
                  var3.append(var5);
               }
            }
            return var3.toString();
         }
      }
   }


}
