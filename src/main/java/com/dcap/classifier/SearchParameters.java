package com.dcap.classifier;

import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.RuleContext;
import com.yd.rules.engine.result.MatchedResult;

import java.util.List;
import java.util.Map;

public class SearchParameters {
   private final List<ContextColumn> searchableColumns;
   private final RuleContext ruleContext;

   private Map<String, List<MatchedResult>> matchedResultMap;



   private SearchParameters(RuleContext ruleContext, List<ContextColumn> searchableColumns) {
      this.searchableColumns = searchableColumns;
      this.ruleContext = ruleContext;
   }

   SearchParameters(List<ContextColumn> searchableColumns) {
      this(null, searchableColumns);
   }

   public SearchParameters(RuleContext ruleContext) {
      this(ruleContext, null);
   }

   public Map<String, List<MatchedResult>> getMatchedResultMap() {
      return matchedResultMap;
   }

   public void setMatchedResultMap(Map<String, List<MatchedResult>> matchedResultMap) {
      this.matchedResultMap = matchedResultMap;
   }

   public List<ContextColumn> getSearchableColumns() throws InitializationException {
      return this.searchableColumns == null ? this.ruleContext.getTableColumns() : this.searchableColumns;
   }
}
