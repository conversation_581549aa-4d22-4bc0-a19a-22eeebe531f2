package com.dcap.classifier.access;

import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.RuleContext;
import org.apache.commons.lang3.StringUtils;

import java.sql.ResultSet;
import java.sql.Statement;
import java.util.List;

public class DorisDataPager extends SQLDataPager {

    private static final String SQL_PAGE_LIMIT = " limit ${SUB_START}, ${SUB_FETCH} ";

    protected DorisDataPager(RuleContext ruleContext) {
        super(ruleContext);
    }

    public String createSql(List<ContextColumn> columns, long start, long rowLimit, boolean useRandomSampling) {
        String qualifiedName = this.getContext().getQualifiedName(columns);
        StringBuilder sql = new StringBuilder(SQL_SELECT);
        sql.append(qualifiedName).append(this.getFrom());
        if (useRandomSampling) {
            sql.append(this.substituteRange(SQL_PAGE_LIMIT, Math.max(start - 1L, 0L), rowLimit));
        } else if (this.context.getTaskGlobalDataHolder().getTaskParam().getSamplingReverseOrder()){
            String order = getOrder();
            if (StringUtils.isNotBlank(order)) {
                sql.append(order).append(" desc");
            }
            sql.append(" LIMIT ").append(rowLimit);
        } else {
            sql.append(" LIMIT ").append(rowLimit);
        }
        return sql.toString();
    }

    @Override
    public String createSpecifyTableSql(String tableName, List<String> columns, long start, long rowLimit, boolean useRandomSampling) {
        String qualifiedName = String.join(",", columns);
        StringBuilder sql = new StringBuilder(SQL_SELECT);
        sql.append(qualifiedName).append(this.getFrom(tableName));
        if (useRandomSampling) {
            sql.append(this.substituteRange(SQL_PAGE_LIMIT, Math.max(start - 1L, 0L), rowLimit));
        } else {
            sql.append(" LIMIT ").append(rowLimit);
        }
        return sql.toString();
    }

    public String createSubsamplingSql(String tableName, String column, long start, long rowLimit, boolean useRandomSampling) {
        StringBuilder sql = new StringBuilder(SQL_SELECT);
        sql.append(column);
        String subQuery = sql.append(this.getFrom()).append(" LIMIT 10000").toString();
        String columnName = "_temp."+column;
        sql = new StringBuilder(SQL_SELECT)
                .append(columnName)
                .append( " from (").append(subQuery).append(") _temp")
                .append(" where ").append(this.appendExcludeEmptyCondition(columnName));
        if (useRandomSampling) {
            sql.append(this.substituteRange(SQL_PAGE_LIMIT, Math.max(start - 1L, 0L), rowLimit));
        } else {
            sql.append(" LIMIT ").append(rowLimit);
        }
        return sql.toString();
    }

    public String getViewDefSql(String owner, String viewName){
        // 使用 jdbc 获取视图定义的 sql
        String viewDefinitionSql = null;
        String query = "SELECT TABLE_NAME, VIEW_DEFINITION FROM information_schema.views " +
                "WHERE TABLE_NAME = '" + viewName + "'";
        try(Statement stmt = this.context.getConnection().createStatement();
            ResultSet resultSet = stmt.executeQuery(query)){
            while (resultSet.next()) {
                viewDefinitionSql = resultSet.getString("VIEW_DEFINITION");
            }
        } catch (Exception e){
            e.getMessage();
            return null;
        }
        return viewDefinitionSql;
    }
}
