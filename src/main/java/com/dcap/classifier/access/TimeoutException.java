package com.dcap.classifier.access;

import com.dcap.classifier.ClassifierException;

public class TimeoutException extends ClassifierException {
   public TimeoutException(String var1) {
      super(var1);
   }

   public TimeoutException(String var1, String var2) {
      super(var1, var2);
   }

   public TimeoutException(String var1, Throwable var2) {
      super(var1, var2);
   }

   public TimeoutException(String var1, String var2, Throwable var3) {
      super(var1, var2, var3);
   }
}
