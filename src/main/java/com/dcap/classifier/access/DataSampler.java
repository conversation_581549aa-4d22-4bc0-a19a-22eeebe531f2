package com.dcap.classifier.access;

import com.dcap.classifier.ClassifierException;
import com.dcap.classifier.Clock;
import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.RuleContext;
import com.dcap.datalayer.DataSourceType;
import com.dcap.sqllineage.LineageReport;
import com.dcap.sqllineage.SQLLineageRunner;
import com.yd.dcap.classifier.config.SpringContextUtil;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;

import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

import static com.dcap.classifier.context.ContextTable.SYNONYM;

public class DataSampler {
    private static final transient Logger LOG = LoggerFactory.getLogger(DataSampler.class);
    protected static final long MAX_ROWS = 2000L;
    protected static final int TURBO_THRESHOLD = 10000;
    protected static final transient int MAX_MAP_SIZE = 131072;
    protected static final transient int MAX_MAP_SIZE_FLAT = 65536;
    protected static final transient int FREE_MEM_MIN = 2097152;
    protected static final transient int QUERY_TIMEOUT_SECONDS = 135;
    protected static final transient List<ContextColumn.ColumnType> SUPPORTED_COLUMN_TYPES;
    private final List<String> accumulatedErrors = new ArrayList<>();
    private final RuleContext context;
    private long sampledRows;
    private DataPager pager;
    private Boolean flat = null;
    private long pageStart;
    private boolean turbo;
    private boolean noMoreRows;
    private boolean useUniqueValue = true;

    public boolean isUseUniqueValue() {
        return this.useUniqueValue;
    }

    protected void accumulateErrors(DataGrid dataGrid) {
        if (dataGrid == null) {
            return;
        }
        for (String error : dataGrid.getErrors()) {
            if (!this.accumulatedErrors.contains(error)) {
                this.accumulatedErrors.add(error);
            }
        }
    }

    protected boolean isTurbo() {
        return this.turbo;
    }

    protected void setTurbo(boolean turbo) {
        this.turbo = turbo;
    }

    protected void setTurbo(long turbo) {
        this.setTurbo(turbo > 10000L);
    }

    public DataSampler(RuleContext context) {
        this.context = context;
    }

    protected RuleContext getContext() {
        return this.context;
    }

    protected List<ContextColumn> filterSupportedColumns(List<ContextColumn> columns) {
        return columns.stream()
                .filter(column -> SUPPORTED_COLUMN_TYPES.contains(column.getColumnType()))
                .collect(Collectors.toList());
    }

    protected boolean isFlat() {
        if (this.flat == null) {
            // 不支持分页 或者 未开启随机采样 flat 就是 true
            this.flat = !DataPager.supportsPaging(this.getContext())
                    || !this.getContext().getTaskGlobalDataHolder().getTaskParam().isUseRandomSampling();
        }
        return this.flat;
    }

    public Map<String, List<String>> sample(List<ContextColumn> columns) throws ClassifierException, SQLException {
        if (this.exceedsReads()) {
            return null;
        }
        List<ContextColumn> supportedColumns = this.filterSupportedColumns(columns);
        if (supportedColumns == null || supportedColumns.isEmpty()) {
            return null;
        }
        try {
            ContextTable table = supportedColumns.get(0).getTable();
            boolean enabledViewDefinitionSampling  = this.context.getTaskGlobalDataHolder().getTaskParam().isViewDefinitionSampling();
            // 如果是 view 就对 view 关联的表进行采样
            if ("view".equalsIgnoreCase(table.getTableTypeName()) && enabledViewDefinitionSampling) {
                return this.mapViewResults(supportedColumns);
            } else {
                // 如果引用表也不为空，那么继续判断引用表是不是 view，如果是就对 view 关联的表进行采样
                ContextTable reference = table.getReference();
                if (reference != null && "view".equalsIgnoreCase(reference.getTableTypeName()) && enabledViewDefinitionSampling){
                    return this.mapViewResults(supportedColumns);
                }
            }
            return this.mapResults(supportedColumns);
        } catch (Exception e){
            LOG.error("Sample the original view directly. Because of error: {}", e.getMessage(), e);
            return this.mapResults(supportedColumns);
        }
    }

    long getSampledRows() {
        return this.sampledRows;
    }


    protected long bumpSampledRows(int var1) {
        this.sampledRows += var1;
        return this.sampledRows;
    }

    /**
     * 没有更多数据，或者已采样的记录已经超出了 rowLimit
     *
     * @return
     */
    protected boolean exceedsReads() {
        long rowLimit = this.getRowLimit();
        return this.noMoreRows || rowLimit > 0L && this.getSampledRows() >= rowLimit;
    }

    protected long findPageStart() {
        long pageStart = this.pageStart;
        if (!this.isFlat()) {
            if (pageStart == 0L) {
                this.pageStart = 1L;
            } else if (pageStart == 1L) {
                this.pageStart = 1000L;
            } else if (this.isTurbo()) {
                this.pageStart = pageStart * 10L;
            } else {
                this.pageStart = pageStart * 2L;
            }
        }

        if (this.pageStart <= 0L) {
            this.noMoreRows = true;
            this.pageStart = -1L;
            if (LOG.isDebugEnabled()) {
                LOG.debug("Page start overflow: {}. No more rows.", this.pageStart);
            }
        }
        return this.pageStart;
    }

    void logTimeout(TimeoutException timeoutException) {
        LOG.error(timeoutException.getMessage(), timeoutException.getDetails(), this.getClass(), this.getContext(), timeoutException);
    }

    protected long getRowLimit() {
        return this.context.getTaskGlobalDataHolder().getTaskParam().getSampleCount();
    }

    Map<String, List<String>> mapResults(List<ContextColumn> columns) throws ClassifierException, SQLException {
        Clock clock = new Clock();
        Clock var3 = new Clock();
        long rowLimit = this.getRowLimit();

        Map<String, List<String>> sampleResult = null;
        DataPager pager = this.getPager();
        boolean exceedsReads = this.exceedsReads();
        boolean isTooBig = false;

        long lastExecutionTime;
        long start = 0;
        DataGrid dataGrid = null;
        while (!exceedsReads && !isTooBig) {
            if(getContext().getTaskGlobalDataHolder().getProbeClientTaskContext().checkInterrupt()){
                LOG.info("Scan task is interrupted in sampling");
                context.getTaskGlobalDataHolder()
                        .getProbeClientTaskContext()
                        .reportInterrupted(StatusRecord.Position.Sampling, "Scan task is interrupted in sampling");
                break;
            }
            
            int rowCount = 0;
            lastExecutionTime = 0L;
            start = 0L;
            var3.resetStartTime();
            // isFlat 就不会分页 不支持分页 或者 未开启随机采样 flat 就是 true
            if (this.isFlat()) {
                dataGrid = pager.top(columns, rowLimit);
                rowCount = dataGrid.rowCount();
                if (dataGrid.tooBig()) {
                    // 1 兆字节等于 1024 * 1024 字节，即 1048576 字节
                    int megabyte = 1048576;
                    Runtime runtime = Runtime.getRuntime();
                    long realFreeMemory = runtime.maxMemory() - runtime.totalMemory() + runtime.freeMemory();
                    LOG.warn(
                            String.format("Short circuit: %-20s #rows: %7d, #cols: %3d, (MB)   max: %d, " +
                                    "total: %5d, free: %5d, real free: %5d, used: %5d",
                            this.getContext().getCurrentTable().getTableName() + ":", rowCount, dataGrid.columnCount(),
                            runtime.maxMemory() / (long) megabyte,
                            runtime.totalMemory() / (long) megabyte,
                            runtime.freeMemory() / (long) megabyte, realFreeMemory / (long) megabyte,
                            (runtime.maxMemory() - realFreeMemory) / (long) megabyte)
                    );
                }
                this.noMoreRows = true;
                lastExecutionTime = pager.getLastExecutionTime();
                this.accumulateErrors(dataGrid);
            } else {
                start = this.findPageStart();
                if (!this.noMoreRows) {
                    try {
                        dataGrid = pager.page(columns, start, rowLimit);
                        if (dataGrid != null) {
                            rowCount = dataGrid.rowCount();
                        }
                        if (rowCount <= 0) {
                            this.noMoreRows = true;
                        }
                    } catch (TimeoutException timeoutException) {
                        this.noMoreRows = true;
                        this.logTimeout(timeoutException);
                    } finally {
                        this.accumulateErrors(dataGrid);
                        lastExecutionTime = pager.getLastExecutionTime();
                    }
                    this.setTurbo(lastExecutionTime);
                }
            }

            this.bumpSampledRows(rowCount);
            clock.incrementCount(rowCount);

            LOG.debug(
                    "Sampling query ran in {} milliseconds - Turbo={}  Paged={}  Start={}  Rows={}  Total={}{}{}{}{}",
                    lastExecutionTime, this.turbo,
                    !this.isFlat(), start, rowCount,
                    clock.getCount(), System.lineSeparator(),
                    var3.getElapsedMessage("Overall fetch including SQL gen &  JDBC overhead"),
                    System.lineSeparator(), this.getContext()
            );

            if (rowCount > 0) {
                if (sampleResult == null) {
                    sampleResult = this.initMap(columns);
                }

                for (ContextColumn column : columns) {
                    String columnName = column.getColumnName();
                    List<String> columnValues = sampleResult.get(columnName);
                    dataGrid.listColumnStringValues(columnValues, columnName, this.isUseUniqueValue());
                }
            }

            if (dataGrid != null) {
                dataGrid.release();
            }

            exceedsReads = sampleResult == null || this.exceedsReads();
            if (!exceedsReads) {
                isTooBig = this.isTooBig(sampleResult, clock.getCount());
            }
        }

        int mapSize = sampleResult == null ? 0 : sampleResult.values().toString().length() * 2 / 1024;
        short maxSize = 128;
        LOG.debug("Map size={}KB  Max.Size={}KB  Tot.Rows={}  Max.Rows={}{}{}", mapSize, maxSize, this.getSampledRows(), this.getRowLimit(), System.lineSeparator(), clock.getElapsedMessageForCounter("Mapped Rows"));
        if (sampleResult == null
                || sampleResult.isEmpty()
                || this.getSampledRows() < rowLimit
                || !this.context.getTaskGlobalDataHolder().getTaskParam().getExcludeEmptyValues()
        ){
            return sampleResult;
        }

        Integer emptyPercentage = this.context.getTaskGlobalDataHolder().getTaskParam().getEmptyPercentage();

        // 这里的逻辑是如果采样结果就是 0 条记录，这里就不再进行二次采样。
        boolean totalItemsIgnoreZero = false;
        Environment bean = SpringContextUtil.getBean(Environment.class);
        if (bean != null){
            totalItemsIgnoreZero = bean.getProperty("totalItemsIgnoreZero", Boolean.class, false);
        }

        // 循环 sampleResult 查看哪一列的值有更多空值，超过阈值设置，就重新为这一列采样，会附加排除空值的条件。
        for (Map.Entry<String, List<String>> entry : sampleResult.entrySet()) {
            String columnName = entry.getKey();
            List<String> columnValues = entry.getValue();
            long totalItems = columnValues.size();
            long nullCount = columnValues.stream()
                    .filter(StringUtils::isBlank)
                    .count();
            // 如果开启了忽略 0 值，则不进行二次采样。这就是说明该列本身就是 0 条记录。
            if (totalItems == 0 && totalItemsIgnoreZero){
                continue;
            }
            // 列表中空值的百分比
            double percentage = (nullCount * 100.0) / totalItems;

            // 默认如果有 98% 都是空值，就重新单独为这一列采样。
            if (percentage >= emptyPercentage){
                columnValues.clear();
                List<ContextColumn> cols = columns.stream()
                        .filter(column -> Objects.equals(columnName, column.getColumnName()))
                        .collect(Collectors.toList());
                LOG.warn("column [{}] 触发二次采样,因为空值占比达到 {}%, 触发条件空值占比 {}%",cols.get(0).getIdentityName(), percentage, emptyPercentage);
                List<String> cls = cols.stream()
                        .map(ContextColumn::getColumnName)
                        .collect(Collectors.toList());
                dataGrid = pager.subsampling(this.context.getCurrentTableName(), cls.get(0), start, rowLimit);
                dataGrid.listColumnStringValues(columnValues, columnName, this.isUseUniqueValue());
                dataGrid.release();
            }
        }
        return sampleResult;
    }

    boolean isTooBig(Map<String, List<String>> sampleResult, int mapRows) {
        LOG.debug("Map={}", sampleResult);
        boolean isFlat = this.isFlat();
        int maxMap = isFlat ? MAX_MAP_SIZE_FLAT : MAX_MAP_SIZE;
        int mapSize = sampleResult.values().toString().length() * 2;
        int var6 = maxMap - mapSize;
        int avgRowSize = mapRows != 0 ? mapSize / mapRows : 0;
        long rowLimit = this.getRowLimit();
        long sampledRows = this.getSampledRows();
        boolean var12 = var6 < avgRowSize;
        LOG.debug("{}{}Paged={}  Map Full={}{}MapSize={}B/{}KB  MaxMap={}KB  Headroom={}KB{}MaxRows={}  SampledRows={}  MapRows={}  " +
                        "Avg.MapRowSize={}",
                this.getContext(), System.lineSeparator(), !isFlat, var12, System.lineSeparator(), mapSize,
                mapSize / 1024, maxMap / 1024, var6 / 1024, System.lineSeparator(), rowLimit, sampledRows, mapRows, avgRowSize);
        return var12;
    }

    Map<String, List<String>> initMap(List<ContextColumn> columns) {
        Map<String, List<String>> var2 = new HashMap<>(columns.size());

        for (ContextColumn column : columns) {
            var2.put(column.getColumnName(), new ArrayList<>());
        }

        if (LOG.isDebugEnabled()) {
            LOG.debug("Initialized Map: {}", var2);
        }

        return var2;
    }

    protected DataPager getPager() {
        if (this.pager == null) {
            this.pager = DataPager.createInstance(this.getContext());
        }
        return this.pager;
    }

    static {
        SUPPORTED_COLUMN_TYPES = Arrays.asList(ContextColumn.TEXT, ContextColumn.NUMBER, ContextColumn.DATE);
    }

    Map<String, List<String>> mapViewResults(List<ContextColumn> columns) throws ClassifierException, SQLException {
        DataPager pager = this.getPager();
        ContextTable table = columns.get(0).getTable();
        if (table.getTableType().equals(SYNONYM) && table.getReference() != null && "view".equalsIgnoreCase(table.getReference().getTableTypeName())) {
            LOG.info("[map-view-results] Synonym {}, use reference view: {}",table.getIdentityName(), table.getReference().getIdentityName());
            table = table.getReference();
        }
        String tableSchema = table.getSchema();
        String viewName = table.getTableName();
        // 获取视图的定义的 sql
        String viewDef = pager.getViewDefSql(tableSchema, viewName);
        if (viewDef == null){
            throw new IllegalArgumentException("Can't get view definition sql; view identity name: " + table.getIdentityName());
        }

        // 对 view 定义的 sql 进行血缘分析。
        LineageReport lineageReport = new SQLLineageRunner.Builder()
                .sqlType("view")
                .vendor(this.context.getDatasource().getType())
                .build()
                .run(viewDef);
        // 此处应该对扫描到的表进行采样。
        Map<String, List<String>> tableAndColumnsMap = lineageReport.getTableAndColumns();
        Map<String, List<String>> sampleResult = new HashMap<>();
        for (Map.Entry<String, List<String>> tableAndColumns : tableAndColumnsMap.entrySet()) {
            String tableName = tableAndColumns.getKey();
            // 是否添加 schema
            if (StringUtils.isNotBlank(tableSchema)
                    && !tableName.toLowerCase().startsWith(tableSchema.toLowerCase()+".")
                    && (this.context.getDatasource().getType().equals(DataSourceType.ORACLE) && !tableName.contains("."))
            ){
                tableName = tableSchema+"."+tableName;
            }
            List<String> columnsValue = tableAndColumns.getValue();
            Map<String, List<String>> tableSampleResult = sampleRealTableForView(tableName, columnsValue);
            if (tableSampleResult != null && !tableSampleResult.isEmpty()){
                tableSampleResult.forEach((k, v) -> 
                    sampleResult.merge(k, v, (v1, v2) -> {
                        List<String> merged = new ArrayList<>(v1);
                        merged.addAll(v2);
                        return merged;
                    })
                );
            }
            // 重置参数，因为在 sampleRealTableForView 内部会用于计算
            this.sampledRows = 0;
            this.noMoreRows = false;
        }
        Map<String, List<String>> finalSampleResult = new HashMap<>();
        for (ContextColumn column : columns) {
            for (LineageReport.LineageReportItem lineageItem : lineageReport.getLineageItems()) {
                if (!lineageItem.getTargetColumn().equals(column.getColumnName())) {
                    continue;
                }
                // 来源列就是表的列名，
                List<String> columnValues = sampleResult.get(lineageItem.getSourceColumn());
                if (columnValues == null){
                    columnValues = new ArrayList<>();
                }
                finalSampleResult.put(column.getColumnName(), columnValues);
                break;
            }
        }
        // 继续，要将列与视图的列对应起来
        return finalSampleResult;
    }

    private Map<String, List<String>> sampleRealTableForView(String tableName, List<String> columns) throws ClassifierException, SQLException {
        Clock clock = new Clock();
        Clock var3 = new Clock();
        long rowLimit = this.getRowLimit();
        Map<String, List<String>> sampleResult = null;
        DataPager pager = this.getPager();
        boolean exceedsReads = this.exceedsReads();
        boolean isTooBig = false;
        long lastExecutionTime;
        long start = 0;
        DataGrid dataGrid;
        while (!exceedsReads && !isTooBig) {
            if(getContext().getTaskGlobalDataHolder().getProbeClientTaskContext().checkInterrupt()){
                LOG.debug("Scan task is interrupted in sampling");
                context.getTaskGlobalDataHolder()
                        .getProbeClientTaskContext()
                        .reportInterrupted(StatusRecord.Position.Sampling, "Scan task is interrupted in sampling");
                break;
            }

            var3.resetStartTime();
            dataGrid = pager.pageRealTableForView(tableName, columns, 0, rowLimit);
            int rowCount = dataGrid.rowCount();

            if (dataGrid.tooBig()) {
                int var16 = 1048576;
                Runtime runtime = Runtime.getRuntime();
                long var18 = runtime.maxMemory() - runtime.totalMemory() + runtime.freeMemory();
                LOG.warn(String.format("Short circuit: %-20s #rows: %7d, #cols: %3d, (MB)   max: %d, " +
                                "total: %5d, free: %5d, real free: %5d, used: %5d",
                        this.getContext().getCurrentTable().getTableName() + ":",
                        rowCount,
                        dataGrid.columnCount(),
                        runtime.maxMemory() / (long) var16,
                        runtime.totalMemory() / (long) var16,
                        runtime.freeMemory() / (long) var16,
                        var18 / (long) var16,
                        (runtime.maxMemory() - var18) / (long) var16));
            }
            this.noMoreRows = true;
            lastExecutionTime = pager.getLastExecutionTime();
            this.accumulateErrors(dataGrid);

            this.bumpSampledRows(rowCount);
            clock.incrementCount(rowCount);

            LOG.debug("Sampling query ran in " + lastExecutionTime + " milliseconds - Turbo=" + this.turbo + "  Paged=" + !this.isFlat() +
                    "  Start=" + start + "  Rows=" + rowCount + "  Total=" + clock.getCount() +
                    System.lineSeparator() + var3.getElapsedMessage("Overall fetch including SQL gen &  JDBC overhead") +
                    System.lineSeparator() + this.getContext());

            if (rowCount > 0) {
                if (sampleResult == null) {
                    sampleResult = new HashMap<>(columns.size());
                    for (String columnName : columns) {
                        sampleResult.put(columnName, new ArrayList<>());
                    }
                }

                for (String columnName : columns) {
                    List<String> columnValues = sampleResult.get(columnName);
                    dataGrid.listColumnStringValues(columnValues, columnName, this.isUseUniqueValue());
                }
            }

            dataGrid.release();

            exceedsReads = sampleResult == null || this.exceedsReads();
            if (!exceedsReads) {
                isTooBig = this.isTooBig(sampleResult, clock.getCount());
            }
        }

        int mapSize = sampleResult == null ? 0 : sampleResult.values().toString().length() * 2 / 1024;
        short maxSize = 128;
        LOG.debug("Map size=" + mapSize + "KB  Max.Size=" + maxSize + "KB  Tot.Rows=" + this.getSampledRows() + "  Max.Rows=" + this.getRowLimit() +
                System.lineSeparator() + clock.getElapsedMessageForCounter("Mapped Rows"));
        if (sampleResult == null
                || sampleResult.isEmpty()
                || this.getSampledRows() < rowLimit
                || !this.context.getTaskGlobalDataHolder().getTaskParam().getExcludeEmptyValues()
        ){
            return sampleResult;
        }

        Integer emptyPercentage = this.context.getTaskGlobalDataHolder().getTaskParam().getEmptyPercentage();
        boolean totalItemsIgnoreZero = false;
        Environment bean = SpringContextUtil.getBean(Environment.class);
        if (bean != null){
            totalItemsIgnoreZero = bean.getProperty("totalItemsIgnoreZero", Boolean.class, false);
        }
        // 循环 sampleResult 查看哪一列的值有更多空值，超过阈值设置，就重新为这一列采样，会附加排除空值的条件。
        for (Map.Entry<String, List<String>> entry : sampleResult.entrySet()) {
            String columnName = entry.getKey();
            List<String> columnValues = entry.getValue();
            long totalItems = columnValues.size();
            long nullCount = columnValues.stream().filter(StringUtils::isBlank).count();
            // 如果开启了忽略 0 值，则不进行二次采样
            if (totalItems == 0 && totalItemsIgnoreZero){
                continue;
            }
            // 列表中空值的百分比
            double percentage = (nullCount * 100.0) / totalItems;

            // 默认如果有 98% 都是空值，就重新单独为这一列采样。
            if (percentage >= emptyPercentage){
                columnValues.clear();
                List<String> cols = columns.stream()
                        .filter(column -> Objects.equals(columnName, column))
                        .collect(Collectors.toList());
                LOG.warn("为视图扫描真实表；column [{}] 触发二次采样,因为空值占比达到 {}%, 触发条件空值占比 {}%",
                        cols.get(0), percentage, emptyPercentage);
                dataGrid = pager.subsampling(tableName, cols.get(0), start, rowLimit);
                dataGrid.listColumnStringValues(columnValues, columnName, this.isUseUniqueValue());
                dataGrid.release();
            }
        }
        return sampleResult;
    }
}
