package com.dcap.classifier.access;

import com.dcap.classifier.ClassifierException;
import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.RuleContext;
import com.yd.dcap.classifier.*;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.util.List;

public abstract class DataPager {

   protected static final transient  org.slf4j.Logger LOG = LoggerFactory.getLogger(DataPager.class);

   protected final RuleContext context;
   protected long lastExecutionTime;

   protected DataPager(RuleContext ruleContext) {
      this.context = ruleContext;
   }

   protected abstract DataGrid page(List<ContextColumn> columns, long start, long rowLimit) throws ClassifierException, SQLException;

   public abstract DataGrid pageRealTableForView(String tableName, List<String> columns, long start, long rowLimit) throws ClassifierException, SQLException;

   public abstract DataGrid subsampling(String tableName, String column, long start, long rowLimit) throws ClassifierException, SQLException;

   public DataGrid top(List<ContextColumn> columns, long rowLimit) throws ClassifierException, SQLException {
      return this.page(columns, 0L, rowLimit);
   }

   public static DataPager createInstance(RuleContext ruleContext) {
      switch(ruleContext.getDatasource().getType()) {
         case MSSQL:
            return new MsSqlServerDataPager(ruleContext);
         case DORIS:
            return new DorisDataPager(ruleContext);
         case MYSQL:
            return new MySqlDataPager(ruleContext);
         case DAMENG:
            return new DaMengDataPager(ruleContext);
         case HIVE:
         case SPARK_SQL:
            return new HiveDataPager(ruleContext);
         case TRANSWARP_INCEPTOR:
            return new InceptorDataPager(ruleContext);
         case OCEANBASE_ORACLE:
            return new OceanbaseOracleDataPager(ruleContext);
         case ORACLE:
            return new OracleDataPager(ruleContext);
         case POSTGRESQL:
         case REDSHIFT:
            return new PostgreSqlDataPager(ruleContext);
         case HANA:
            return new HanaDataPager(ruleContext);
         case DB2:
            return new Db2DataPager(ruleContext);
         case MAXCOMPUTE:
            return new MaxComputeDataPager(ruleContext);
         case IMPALA:
            return new ImpalaDataPager(ruleContext);
         case DREMIO:
            return new DremioDataPager(ruleContext);
         case SINODB:
            return new SinodbDataPager(ruleContext);
         case GBASE8S:
            return new Gbase8sDataPager(ruleContext);
         case GBASE8A:
            return new Gbase8aDataPager(ruleContext);
         case CLICKHOUSE:
            return new ClickhouseDataPager(ruleContext);
         case TRINO:
            return new TrinoDataPager(ruleContext);
         case PRESTO:
            return new PrestoDataPager(ruleContext);
         case CACHE:
            return new CacheDbDataPager(ruleContext);
         case SYBASE:
            return new SybaseDataPager(ruleContext);
         default:
            String message = "Datasource must create a pager for: " + ruleContext.getDatasource().getTypeName();
            LOG.error(message);
            throw new RuntimeException(message);
      }
   }

   protected RuleContext getContext() {
      return this.context;
   }

   public static boolean supportsPaging(RuleContext ruleContext) {
      switch(ruleContext.getDatasource().getType()) {
         case MSSQL:
         case HIVE:
         case TRANSWARP_INCEPTOR:
         case ORACLE:
         case MONGODB:
         case DYNAMODB:
         case DREMIO:
         case SINODB:
         case GBASE8S:
         case ELASTIC_SEARCH:
         case REDIS:
            return false;
         case DORIS:
         case MYSQL:
         case GBASE8A:
         case POSTGRESQL:
         case DAMENG:
         case DB2:
         default:
            return true;
      }
   }

   protected long getLastExecutionTime() {
      return this.lastExecutionTime;
   }

   public static int getDatabaseVersion(Connection var0) throws SQLException {
      DatabaseMetaData var1 = var0.getMetaData();
      return var1.getDatabaseMajorVersion();
   }

   public String getViewDefSql(String owner, String viewName){
      // 使用 jdbc 获取视图定义的 sql
      return null;
   }
}
