package com.dcap.classifier.access;

import com.dcap.classifier.Clock;
import lombok.extern.slf4j.Slf4j;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class DataGrid {

    private final ArrayList<Object[]> dataRows = new ArrayList<>();
    private final List<String> errors = new ArrayList<>();
    private final List<String> columnNames = new ArrayList<>();
    private final List<String> tableNames = new ArrayList<>();
    private boolean tooBig;
    private int columnCount = 0;
    private final int size = 0;

    public DataGrid(ResultSet resultSet) throws SQLException {
        this.populate(resultSet);
    }

    public DataGrid() {
    }

    public boolean tooBig() {
        return this.tooBig;
    }

    private void populate(ResultSet resultSet) throws SQLException {
        Clock clock = new Clock();
        this.errors.clear();
        this.initAttributes(resultSet);

        int columnIndex;
        try {
            while (resultSet.next()) {
                Object[] rowValue = new Object[this.columnCount];

                for (columnIndex = 0; columnIndex < this.columnCount; ++columnIndex) {
                    Object columnValue = null;
                    try {
                        columnValue = resultSet.getObject(columnIndex + 1);
                    } catch (Error ex) {
                        // todo: for exception: Value '0000-00-00' can not be represented as java.sql.Date
                        if (ex.getMessage().equalsIgnoreCase("Value '0000-00-00' can not be represented as java.sql.Date")) {
                            log.error(ex.getMessage());
                        } else {
                            ex.printStackTrace();
                        }
                    }
                    // 我们认为对超出 1024 长度的内容 识别是没有意义的。
                    if (columnValue instanceof String && ((String) columnValue).length() > 1024){
                        columnValue = ((String) columnValue).substring(0,1024);
                    }
                    this.setValue(rowValue, columnIndex, columnValue);
                }
                this.addRow(rowValue);
                clock.incrementCount();
            }
        } catch (OutOfMemoryError outOfMemoryError) {
            this.tooBig = true;
            columnIndex = this.dataRows.size() + 1 >>> 1;
            while (this.dataRows.size() > columnIndex) {
                this.dataRows.remove(this.dataRows.size() - 1);
            }
            System.gc();
            boolean var11 = false;
            try {
                var11 = true;
                this.dataRows.trimToSize();
                var11 = false;
            } finally {
                if (var11) {
                    String errorMessage = outOfMemoryError.getMessage();
                    if (this.addError(errorMessage)) {
                        log.warn(errorMessage, outOfMemoryError);
                    }
                }
            }
            String message = outOfMemoryError.getMessage();
            if (this.addError(message)) {
                log.warn(message, outOfMemoryError);
            }
        }

        if (log.isDebugEnabled()) {
            log.debug(clock.getElapsedMessageForCounter("Grid Rows"));
        }
    }

    protected boolean addError(String var1) {
        if (!this.errors.contains(var1)) {
            this.errors.add(var1);
            return true;
        } else {
            return false;
        }
    }

    public List<String> getErrors() {
        return this.errors;
    }

    protected void initAttributes(ResultSet resultSet) throws SQLException {
        ResultSetMetaData metaData = resultSet.getMetaData();
        this.columnCount = metaData.getColumnCount();
        this.columnNames.clear();
        this.tableNames.clear();

        for (int columnIndex = 0; columnIndex < this.columnCount; ++columnIndex) {
            int cIndex = columnIndex + 1;
            this.columnNames.add(metaData.getColumnName(cIndex));
            try {
                String tableName = metaData.getTableName(cIndex);
                this.tableNames.add(tableName);
            } catch (Throwable var5) {
                this.tableNames.add("");
            }
        }
        log.debug("Tables: " + this.tableNames + System.lineSeparator() + "Columns: " + this.columnNames);
    }

    public Object[] getRow(int rowIndex) throws IndexOutOfBoundsException {
        if (!this.isInBounds(rowIndex)) {
            String var2 = "Row: " + rowIndex + " exceeds current row count of: " + this.rowCount();
            log.warn(var2);
            throw new IndexOutOfBoundsException(var2);
        } else {
            return this.dataRows.get(rowIndex);
        }
    }

    protected void addRow(Object[] rowValue) {
        this.dataRows.add(rowValue);
    }


    public int rowCount() {
        return this.dataRows.size();
    }

    public int columnCount() {
        return this.columnCount;
    }


    boolean isInBounds(Object[] rowValue, int columnIndex) {
        return columnIndex >= 0 && columnIndex < rowValue.length;
    }

    boolean isInBounds(int rowIndex) {
        return rowIndex < this.rowCount();
    }

    public int size() {
        return this.size;
    }


    protected Object[] setValue(Object[] rowValue, int columnIndex, Object columnValue) {
        rowValue[columnIndex] = columnValue;
        return rowValue;
    }


    public String findTableColumnName(int columnIndex) {
        return this.findColumnTable(columnIndex) + "." + this.findColumnName(columnIndex);
    }

    public String listTableColumnNames() {
        StringBuilder columnNames = new StringBuilder();

        for (int columnIndex = 0; columnIndex < this.columnCount; ++columnIndex) {
            if (columnIndex != 0) {
                columnNames.append(", ");
            }
            columnNames.append(this.findTableColumnName(columnIndex));
        }

        return columnNames.toString();
    }

    public void release() {
        if (log.isDebugEnabled()) {
            log.debug("Releasing data for: " + this.listTableColumnNames());
        }

        this.dataRows.clear();
    }


    public int findColumnIndex(String columnName) {
        return this.columnNames.indexOf(columnName);
    }

    public String findColumnName(int index) {
        return this.columnNames.size() > index ? this.columnNames.get(index) : "";
    }

    public String findColumnTable(int columnIndex) {
        return this.tableNames.size() > columnIndex ? this.tableNames.get(columnIndex) : "";
    }


    public void listColumnStringValues(List<String> columnValues, String columnName, boolean isUseUniqueValue) {
        int columnIndex = this.findColumnIndex(columnName);
        this.listColumnStringValues(columnValues, columnIndex, isUseUniqueValue);
    }

    private void listColumnStringValues(List<String> columnValues, int columnIndex, boolean isUseUniqueValue) {
        int rowCount = this.rowCount();
        String[] values = new String[rowCount];

        for (int i = 0; i < rowCount; ++i) {
            values[i] = this.getStringValue(i, columnIndex);
        }

        if (!isUseUniqueValue) {
            columnValues.addAll(Arrays.asList(values));
            return;
        }
        if (rowCount <= 0) {
            return;
        }
        if (rowCount == 1) {
            columnValues.add(values[0]);
            return;
        }
        Arrays.sort(values);
        String columnValue = values[0];
        columnValues.add(columnValue);

        for (int i = 1; i < values.length; ++i) {
            String value = values[i];
            if (!value.equals(columnValue)) {
                columnValues.add(value);
                columnValue = value;
            }
        }
    }

    protected Object getValue(Object[] rowValue, int columnIndex) {
        if (this.isInBounds(rowValue, columnIndex)) {
            return rowValue[columnIndex];
        }
        return null;
    }

    protected String getStringValue(Object[] row, int columnIndex) {
        Object columnValue = this.getValue(row, columnIndex);
        return columnValue == null ? "" : String.valueOf(columnValue);
    }


    public String getStringValue(int rowIndex, int columnIndex) {
        Object[] row = this.getRow(rowIndex);
        return this.getStringValue(row, columnIndex);
    }

    public String toString() {
        return "Rows: " + this.rowCount() + " Columns: " + this.columnCount + " Size: " + this.size();
    }
}
