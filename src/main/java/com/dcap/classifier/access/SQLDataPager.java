package com.dcap.classifier.access;

import com.dcap.classifier.ClassifierException;
import com.dcap.classifier.Clock;
import com.dcap.classifier.InitializationException;
import com.dcap.classifier.RuleResults;
import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.RuleContext;
import com.dcap.datalayer.DatasourceDriver;
import com.dcap.utils.Messages;
import org.slf4j.LoggerFactory;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.SQLTimeoutException;
import java.sql.Statement;
import java.util.Iterator;
import java.util.List;

public abstract class SQLDataPager extends DataPager {

    protected static final transient  org.slf4j.Logger LOG = LoggerFactory.getLogger(SQLDataPager.class);
    protected static final transient String SQL_SELECT = "select ";
    protected static final transient String SQL_FROM = " from ";
    protected static final transient String SQL_ORDER = " order by ";

    protected static final String SUB_START = "${SUB_START}";
    protected static final String SUB_FETCH = "${SUB_FETCH}";
    protected static final String SUB_LEAD = "${SUB_";
    private long rowCount = 0L;
    private int readRows = 0;
    private long start = 0L;
    protected long lastExecutionTime = 0L;
    private String order = null;

    protected SQLDataPager(RuleContext ruleContext) {
        super(ruleContext);
    }

    public abstract String createSql(List<ContextColumn> columns, long start, long rowLimit, boolean useRandomSampling);

    public abstract String createSpecifyTableSql(String tableName, List<String> columns, long start, long rowLimit, boolean useRandomSampling);

    public abstract String createSubsamplingSql(String tableName, String column, long start, long rowLimit, boolean useRandomSampling);

    public DataGrid page(List<ContextColumn> columns, long start, long rowLimit) throws ClassifierException, SQLException {
        Clock clock = new Clock();
        int sampleSqlTimeoutSeconds = this.getContext().getTaskGlobalDataHolder().getTaskParam().getSampleSqlTimeout();
        boolean connectionFailure = false;
        boolean useRandomSampling = this.getContext().getTaskGlobalDataHolder().getTaskParam().isUseRandomSampling();
        String sql = this.createSql(columns, start, rowLimit, useRandomSampling);
        LOG.debug(clock.getElapsedMessage("Created SQL"));
        LOG.debug("Executing on: " + this.getContext() + System.lineSeparator() + sql);
        clock.resetStartTime();
        DataGrid dataGrid;
        try (Statement statement = this.getContext().getStatement();
             ResultSet resultSet = executeTimed(this.getContext().getDatasource().supportedQueryTimeout(), statement, sql, sampleSqlTimeoutSeconds)) {
            this.lastExecutionTime = clock.getElapsedTimeMillis();
            LOG.debug(clock.getElapsedMessage("Executed Query"));
            return new DataGrid(resultSet);
        } catch (SQLException sqlException) {
            if(sqlException.getMessage().contains("permission denied")){
                throw sqlException;
            }
            if (!(this instanceof MsSqlServerDataPager) || start == -9999L || !((MsSqlServerDataPager) this).isVer2005()) {
                connectionFailure = sqlException.getErrorCode() != -204
                        || !sqlException.getSQLState().equals("42704")
                        || !sqlException.getMessage().contains("*MEM");

                DatasourceDriver datasourceDriver = this.getContext().getDatasource().getDriverInterface();
                String exceptionDetails = datasourceDriver.getExceptionDetails(sql, sqlException);

                String message = Messages.getString("Unable to retrieve results for: '${tablename}' columns: '${columnList}'.", "tablename", this.getContext().getCurrentTableName(), "columnList", RuleResults.toColumnNamesString(columns));
                LOG.warn("{}{}{}", message, System.lineSeparator(), exceptionDetails, sqlException);
                // todo: return empty grid instead of throwing exception
                return new DataGrid();
                // throw new RuleEvaluationException(var28 + System.lineSeparator() + var12, var14, var28);
            }
            dataGrid = this.page(columns, -9999L, rowLimit);
        } catch (TimeoutException timeoutException) {
            connectionFailure = true;
            String message = Messages.getString("The query on: '${tablename}' exceeded the timeout period of: ${timeperiod} seconds.  Results will be based on: ${rowcount} rows.", "tablename", this.getContext().getCurrentTableName(),
                    "timeperiod", String.valueOf(sampleSqlTimeoutSeconds), "rowcount", String.valueOf(this.getReadRows()));
            LOG.warn("{}{}{}", message, System.lineSeparator(), timeoutException, timeoutException);
            throw new TimeoutException(message, timeoutException);
        } finally {
            if (connectionFailure) {
                LOG.warn("Recycling Connection... this context connection will be closed...");
                this.context.closeConnection();
            }
            LOG.debug("Released resources for: " + this.context);
        }
        return dataGrid;
    }

    public DataGrid pageRealTableForView(String tablename, List<String> columns, long start, long rowLimit) throws ClassifierException, SQLException {
        Clock clock = new Clock();
        int sampleSqlTimeoutSeconds = this.getContext().getTaskGlobalDataHolder().getTaskParam().getSampleSqlTimeout();
        boolean isTimeout = false;
        boolean useRandomSampling = this.getContext().getTaskGlobalDataHolder().getTaskParam().isUseRandomSampling();
        String sql = this.createSpecifyTableSql(tablename, columns, start, rowLimit, useRandomSampling);
        LOG.debug(clock.getElapsedMessage("Created SQL"));
        LOG.debug("Executing on: " + this.getContext() + System.lineSeparator() + sql);
        clock.resetStartTime();
        DataGrid dataGrid;
        try (Statement statement = this.getContext().getStatement();
             ResultSet resultSet = executeTimed(this.getContext().getDatasource().supportedQueryTimeout(),
                     statement, sql, sampleSqlTimeoutSeconds)) {
            this.lastExecutionTime = clock.getElapsedTimeMillis();
            LOG.debug(clock.getElapsedMessage("Executed Query"));
            return new DataGrid(resultSet);
        } catch (SQLException sqlException) {
            if(sqlException.getMessage().contains("permission denied")){
                throw sqlException;
            }
            if (!(this instanceof MsSqlServerDataPager) || start == -9999L || !((MsSqlServerDataPager) this).isVer2005()) {
                isTimeout = sqlException.getErrorCode() != -204
                        || !sqlException.getSQLState().equals("42704")
                        || !sqlException.getMessage().contains("*MEM");

                DatasourceDriver datasourceDriver = this.getContext().getDatasource().getDriverInterface();
                String exceptionDetails = datasourceDriver.getExceptionDetails(sql, sqlException);

                String message = Messages.getString("Unable to retrieve results for: '${tablename}' columns: '${columnList}'.",
                        "tablename", this.getContext().getCurrentTableName(), "columnList", String.join(",", columns));
                LOG.warn("{}{}{}", message, System.lineSeparator(), exceptionDetails, sqlException);
                // todo: return empty grid instead of throwing exception
                return new DataGrid();
            }
            dataGrid = this.pageRealTableForView(tablename, columns, -9999L, rowLimit);
        } catch (TimeoutException timeoutException) {
            isTimeout = true;
            String message = Messages.getString("The query on: '${tablename}' exceeded the timeout period of: ${timeperiod} seconds.  Results will be based on: ${rowcount} rows.", "tablename", this.getContext().getCurrentTableName(),
                    "timeperiod", String.valueOf(sampleSqlTimeoutSeconds), "rowcount", String.valueOf(this.getReadRows()));
            LOG.warn("{}{}{}", message, System.lineSeparator(), timeoutException, timeoutException);
            throw new TimeoutException(message, timeoutException);
        } finally {
            if (isTimeout) {
                LOG.warn("Recycling Connection... this context connection will be closed...");
                this.context.closeConnection();
            }
            LOG.debug("Released resources for: " + this.context);
        }
        return dataGrid;
    }

    public DataGrid subsampling(String tablename, String column, long start, long rowLimit) throws ClassifierException, SQLException {
        Clock clock = new Clock();
        int sampleSqlTimeoutSeconds = this.getContext().getTaskGlobalDataHolder().getTaskParam().getSampleSqlTimeout();
        boolean isTimeout = false;
        boolean useRandomSampling = this.getContext().getTaskGlobalDataHolder().getTaskParam().isUseRandomSampling();
        String sql = this.createSubsamplingSql(tablename, column, start, rowLimit, useRandomSampling);
        LOG.info(clock.getElapsedMessage("Created SQL"));
        LOG.info("Executing on: {}{}{}", this.getContext(), System.lineSeparator(), sql);
        clock.resetStartTime();
        DataGrid dataGrid;
        try (Statement statement = this.getContext().getStatement();
             ResultSet resultSet = executeTimed(this.getContext().getDatasource().supportedQueryTimeout(), statement, sql, sampleSqlTimeoutSeconds)) {
            this.lastExecutionTime = clock.getElapsedTimeMillis();
            LOG.info(clock.getElapsedMessage("Executed Query"));
            return new DataGrid(resultSet);
        } catch (SQLException sqlException) {
            if(sqlException.getMessage().contains("permission denied")){
                throw sqlException;
            }
            if (!(this instanceof MsSqlServerDataPager) || start == -9999L || !((MsSqlServerDataPager) this).isVer2005()) {
                isTimeout = sqlException.getErrorCode() != -204
                        || !sqlException.getSQLState().equals("42704")
                        || !sqlException.getMessage().contains("*MEM");
                DatasourceDriver datasourceDriver = this.getContext().getDatasource().getDriverInterface();
                String exceptionDetails = datasourceDriver.getExceptionDetails(sql, sqlException);
                String message = Messages.getString("Unable to retrieve results for: '${tablename}' column: '${columnList}'.",
                        "tablename", this.getContext().getCurrentTableName(), "columnList", column);
                LOG.warn("{}{}{}", message, System.lineSeparator(), exceptionDetails, sqlException);
                // todo: return empty grid instead of throwing exception
                return new DataGrid();
            }
            dataGrid = this.subsampling(tablename, column, -9999L, rowLimit);
        } catch (TimeoutException timeoutException) {
            isTimeout = true;
            String message = Messages.getString("The query on: '${tablename}' exceeded the timeout period of: ${timeperiod} seconds.  Results will be based on: ${rowcount} rows.", "tablename", this.getContext().getCurrentTableName(),
                    "timeperiod", String.valueOf(sampleSqlTimeoutSeconds), "rowcount", String.valueOf(this.getReadRows()));
            LOG.warn("{}{}{}", message, System.lineSeparator(), timeoutException, timeoutException);
            throw new TimeoutException(message, timeoutException);
        } finally {
            if (isTimeout) {
                LOG.warn("Recycling Connection... this context connection will be closed...");
                this.context.closeConnection();
            }
            LOG.debug("Released resources for: " + this.context);
        }
        return dataGrid;
    }

    public int getReadRows() {
        return this.readRows;
    }

    public long getRowCount() {
        if (this.rowCount == 0L) {
            this.rowCount = this.getContext().getRowCount();
        }
        return this.rowCount;
    }

    protected String getFrom() {
        return this.getFrom(this.getContext().getQualifiedName());
    }

    protected String getFrom(String qualifiedName) {
        return SQL_FROM + qualifiedName;
    }

    protected String getOrder() {
        if (this.order == null) {
            try {
                List<ContextColumn> columns = this.getContext().getPrimaryKeyColumns();
                if (columns != null && !columns.isEmpty()) {
                    StringBuilder order = new StringBuilder(SQL_ORDER);
                    Iterator<ContextColumn> columnIterator = columns.iterator();
                    while (columnIterator.hasNext()) {
                        ContextColumn column = columnIterator.next();
                        order.append(this.getContext().getQualifiedName(column));
                        if (columnIterator.hasNext()) {
                            order.append(",");
                        }
                    }
                    this.order = order.toString();
                }
            } catch (InitializationException var7) {
                if (LOG.isDebugEnabled()) {
                    LOG.debug("Could not get primary key for: " + this.getContext().getCurrentTableName(), var7);
                }
            }
        }

        if (this.order == null) {
            try {
                for (ContextColumn column : this.getContext().getTableColumns()) {
                    if (column.isOfType(ContextColumn.DATE)) {
                        this.order = " order by " + this.getContext().getQualifiedName(column);
                        break;
                    }
                }
            } catch (InitializationException var6) {
                if (LOG.isDebugEnabled()) {
                    LOG.debug("Could not get date column for: " + this.getContext().getCurrentTableName(), var6);
                }
            }
        }

        if (this.order == null) {
            try {
                ContextColumn column = this.getContext().getTableColumns().get(0);
                this.order = " order by " + this.getContext().getQualifiedName(column);
            } catch (InitializationException initializationException) {
                LOG.debug("Could not find an order for: {}", this.getContext().getCurrentTableName(), initializationException);
            }
        }

        if (this.order == null) {
            this.order = "";
        }

        return this.order;
    }

    protected long getStart() {
        return this.start;
    }

    protected void setStart(long var1) {
        this.start = var1;
    }

    protected String substituteRange(String sql, long start, long rowLimit) {
        int subStartLen = SUB_START.length();
        StringBuilder var7 = new StringBuilder();
        int fromIndex = 0;

        int var8;
        while ((var8 = sql.indexOf(SUB_LEAD, fromIndex)) >= 0 && sql.length() >= var8 + subStartLen) {
            var7.append(sql, fromIndex, var8);
            String var10 = sql.substring(var8, var8 + subStartLen);
            byte var13 = -1;
            switch (var10) {
                case SUB_FETCH:
                    var13 = 1;
                    break;
                case SUB_START:
                    var13 = 0;
                    break;
            }

            Object var11;
            switch (var13) {
                case 0:
                    var11 = start;
                    break;
                case 1:
                    var11 = rowLimit;
                    break;
                default:
                    var10 = SUB_LEAD;
                    var11 = SUB_LEAD;
            }

            fromIndex = var8 + var10.length();
            var7.append(var11);
        }
        var7.append(sql.substring(fromIndex));
        return var7.toString();
    }

    public static ResultSet executeTimed(boolean supportedQueryTimeout, Statement statement, String sql, int sqlTimeoutSeconds) throws SQLException, TimeoutException {
        try {
            if (supportedQueryTimeout){
                statement.setQueryTimeout(sqlTimeoutSeconds);
            }
            return statement.executeQuery(sql);
        } catch (SQLTimeoutException sqlTimeoutException) {
            String message = Messages.getString("DB operation timed out after ${timeout} seconds", "timeout", String.valueOf(sqlTimeoutSeconds));
            LOG.warn("{} running query is:{}", message, sql);
            throw new TimeoutException(message, sqlTimeoutException);
        }
    }
    public String appendExcludeEmptyCondition(String qualifiedName) {
        return " " + qualifiedName + " is not null and " + qualifiedName + " != '' ";
    }
}
