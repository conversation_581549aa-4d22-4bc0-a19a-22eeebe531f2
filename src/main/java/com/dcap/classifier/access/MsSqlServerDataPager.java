package com.dcap.classifier.access;

import com.dcap.classifier.ClassifierUtils;
import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.RuleContext;
import com.dcap.datalayer.ClassifierDataSource;
import org.apache.commons.lang3.StringUtils;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.util.List;

public class MsSqlServerDataPager extends SQLDataPager {

   public static final transient int START_NO_TABLE_SAMPLE = -9999;
   private static final transient String SQL_TOP = "select top ${SUB_FETCH} ";
   private static final transient String SQL_SUB_SEL_PFX = "* from ( select ";
   private static final transient String SQL_OVER_PFX = ", row_number() over (";
   private static final transient String SQL_OVER_SFX = ") as DATA_PGR_ROW_NUM";
   private static final transient String SQL_SUB_SEL_SFX = ") as PGR where DATA_PGR_ROW_NUM >= ${SUB_START}";
   private Boolean ver2005 = null;

   protected MsSqlServerDataPager(RuleContext ruleContext) {
      super(ruleContext);
   }

   public boolean isVer2005() {
      if (this.ver2005 == null) {
         this.ver2005 = isVer2005(this.getContext().getDatasource());
      }

      return this.ver2005;
   }

   public static boolean isVer2005(ClassifierDataSource dataSource) {
      boolean isVer2005 = false;
      Connection connection = null;

      try {
         if (dataSource.getType().name().equals("MSSQL")) {
            connection = dataSource.connect();
            DatabaseMetaData metaData = connection.getMetaData();
            int majorVersion = metaData.getDatabaseMajorVersion();
            isVer2005 = majorVersion >= 9;
         }
      } catch (Throwable ignored) {
      } finally {
         ClassifierUtils.disposal(connection);
      }
      return isVer2005;
   }

   public String createSql(List<ContextColumn> columns, long start, long rowLimit, boolean useRandomSampling) {
      String qualifiedName = this.getContext().getQualifiedName(columns);
      StringBuilder sql = new StringBuilder();
      boolean isVer2005 = this.isVer2005();
      if (!isVer2005 && useRandomSampling) {
         sql.append(SQL_TOP);
         if (start != 0L) {
            sql.append(SQL_SUB_SEL_PFX);
         }
         sql.append(qualifiedName);
         if (start != 0L) {
            sql.append(SQL_OVER_PFX);
            sql.append(this.getOrder());
            sql.append(SQL_OVER_SFX);
         }
         sql.append(this.getFrom());
         if (start != 0L) {
            sql.append(SQL_SUB_SEL_SFX);
         }
         return this.substituteRange(sql.toString(), start, rowLimit);
      } else {
         sql.append("SELECT TOP ").append(rowLimit).append(" ");
         sql.append(this.getContext().getQualifiedName(columns));
         sql.append(this.getFrom());
         if (useRandomSampling) {
            long rowCount = this.getRowCount();
            double var11;
            if (start != START_NO_TABLE_SAMPLE && rowCount > rowLimit) {
               var11 = Math.max(1.0E-4D, 100.0D * (double)rowLimit / (double)rowCount);
               sql.append(" TABLESAMPLE(").append(var11).append(" PERCENT)");
            } else if (start != START_NO_TABLE_SAMPLE && rowCount == -1L) {
               var11 = 1.0D;
               sql.append(" TABLESAMPLE(").append(var11).append(" PERCENT)");
            }
         } else if (context.getTaskGlobalDataHolder().getTaskParam().getSamplingReverseOrder()){
            String order = getOrder();
            if (StringUtils.isNotBlank(order)) {
               sql.append(order).append(" desc");
            }
         }
         return sql.toString();
      }
   }

   @Override
   public String createSpecifyTableSql(String tableName, List<String> columns, long start, long rowLimit, boolean useRandomSampling) {
      String qualifiedName = String.join(",", columns);
      StringBuilder sql = new StringBuilder();
      boolean isVer2005 = this.isVer2005();
      if (!isVer2005 && useRandomSampling) {
         sql.append(SQL_TOP);
         if (start != 0L) {
            sql.append(SQL_SUB_SEL_PFX);
         }

         sql.append(qualifiedName);
         if (start != 0L) {
            sql.append(SQL_OVER_PFX);
            sql.append(this.getOrder());
            sql.append(SQL_OVER_SFX);
         }

         sql.append(this.getFrom(tableName));

         if (start != 0L) {
            sql.append(SQL_SUB_SEL_SFX);
         }

         return this.substituteRange(sql.toString(), start, rowLimit);
      } else {
         sql.append("SELECT TOP ").append(rowLimit).append(" ");
         sql.append(qualifiedName);
         sql.append(this.getFrom(tableName));
         if (useRandomSampling) {
            long rowCount = this.getRowCount();
            double var11;
            if (start != START_NO_TABLE_SAMPLE && rowCount > rowLimit) {
               var11 = Math.max(1.0E-4D, 100.0D * (double)rowLimit / (double)rowCount);
               sql.append(" TABLESAMPLE(").append(var11).append(" PERCENT)");
            } else if (start != START_NO_TABLE_SAMPLE && rowCount == -1L) {
               var11 = 1.0D;
               sql.append(" TABLESAMPLE(").append(var11).append(" PERCENT)");
            }
         }
         return sql.toString();
      }
   }

   @Override
   public String createSubsamplingSql(String tableName, String column, long start, long rowLimit, boolean useRandomSampling) {
      String subQuery =  "select top 10000 " + column + this.getFrom(tableName) + " ";
      String columnName = "temp."+column;
      StringBuilder sql = new StringBuilder();
      boolean isVer2005 = this.isVer2005();
      if (!isVer2005 && useRandomSampling) {
         sql.append(SQL_TOP);
         if (start != 0L) {
            sql.append(SQL_SUB_SEL_PFX);
         }
         sql.append(column);
         if (start != 0L) {
            sql.append(SQL_OVER_PFX);
            sql.append(this.getOrder());
            sql.append(SQL_OVER_SFX);
         }

         sql.append(this.getFrom(tableName));
         sql.append(" where ").append(this.appendExcludeEmptyCondition(column));

         if (start != 0L) {
            sql.append(SQL_SUB_SEL_SFX);
         }

         return this.substituteRange(sql.toString(), start, rowLimit);
      } else {
         if (useRandomSampling) {
            long rowCount = this.getRowCount();
            double var11;
            if (start != START_NO_TABLE_SAMPLE && rowCount > rowLimit) {
               var11 = Math.max(1.0E-4D, 100.0D * (double)rowLimit / (double)rowCount);
               subQuery+=" TABLESAMPLE("+var11+" PERCENT)";
            } else if (start != START_NO_TABLE_SAMPLE && rowCount == -1L) {
               var11 = 1.0D;
               subQuery+=" TABLESAMPLE("+var11+" PERCENT)";
            }
         }

         sql.append("SELECT TOP ").append(rowLimit).append(" ");
         sql.append(columnName).append(" from (").append(subQuery).append(") temp");
         sql.append(" where ").append(this.appendExcludeEmptyCondition(columnName));
         return sql.toString();
      }
   }
}
