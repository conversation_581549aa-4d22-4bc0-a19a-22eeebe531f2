package com.dcap.classifier.access;

import com.dcap.classifier.ClassifierException;
import com.dcap.classifier.Clock;
import com.dcap.classifier.RuleResults;
import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.RuleContext;
import com.dcap.classifier.rules.RuleEvaluationException;
import com.dcap.datalayer.DatasourceDriver;
import com.dcap.utils.Messages;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.SQLRecoverableException;
import java.sql.Statement;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class OracleDataPager extends SQLDataPager {

    protected OracleDataPager(RuleContext ruleContext) {
        super(ruleContext);
    }

    @Override
    public String createSql(List<ContextColumn> columns, long start, long rowLimit, boolean useRandomSampling) {
        String qualifiedName = this.getContext().getQualifiedName(columns);
        StringBuilder sql = new StringBuilder(SQL_SELECT);
        sql.append(qualifiedName).append(this.getFrom());
        if (useRandomSampling) {
            long rowCount = this.getRowCount();
            int startNum = RandomUtils.nextInt(0, (int) rowCount);
            if (rowCount <= rowLimit) {
                return sql.toString();
            }
            sql = new StringBuilder();
            sql.append("select ");
            sql.append(this.getContext().getColumnNameOnly(columns));
            sql.append(" FROM (select ");
            sql.append(qualifiedName);
            sql.append(",rownum rowno ");
            sql.append(this.getFrom());
            sql.append(" where rownum <= ").append(startNum+rowLimit).append(") a WHERE a.rowno >= ").append(startNum);
            return sql.toString();
        }
        sql.append(" WHERE ROWNUM <= ").append(rowLimit);
        if (context.getTaskGlobalDataHolder().getTaskParam().getSamplingReverseOrder()){
            String order = getOrder();
            if (StringUtils.isNotBlank(order)) {
                sql.append(order).append(" desc");
            }
        }
        return sql.toString();
    }

    @Override
    public String createSpecifyTableSql(String tableName, List<String> columns, long start, long rowLimit, boolean useRandomSampling) {
        String qualifiedName = String.join(",", columns);
        StringBuilder sql = new StringBuilder(SQL_SELECT);
        sql.append(qualifiedName).append(this.getFrom());

        if (useRandomSampling) {
            // 开启随机采样，就会去获取 rowCount，会扫描全表。
            long rowCount = this.getRowCount();
            int startNum = RandomUtils.nextInt(0, (int) rowCount);
            if (rowCount <= rowLimit) {
                return sql.toString();
            }
            String columnNameOnly = columns.stream().map(column -> column.substring(column.lastIndexOf(".")+1))
                    .collect(Collectors.joining(","));
            sql = new StringBuilder();
            sql.append("select ");
            sql.append(columnNameOnly);
            sql.append(" FROM (select ");
            sql.append(qualifiedName);
            sql.append(",rownum rowno ");
            sql.append(this.getFrom(tableName));
            sql.append(" where rownum <= ").append(startNum+rowLimit).append(") a WHERE a.rowno >= ").append(startNum);
            return sql.toString();
        }
        sql.append(" WHERE ROWNUM <= ").append(rowLimit);
        return sql.toString();
    }

    @Override
    public String createSubsamplingSql(String tableName, String column, long start, long rowLimit,
                            boolean useRandomSampling) {
        String qualifiedName = column;
        String columnNameOnly = column.substring(column.lastIndexOf(".")+1);
        StringBuilder sql = new StringBuilder(SQL_SELECT);
        sql.append(qualifiedName);
        String subQuery = sql.append(this.getFrom()).append(" where ROWNUM <= 10000").toString();
        String columnName = "temp."+qualifiedName;
        sql = new StringBuilder(SQL_SELECT)
                .append(columnName)
                .append(" from (").append(subQuery).append(") temp")
                .append(" where ").append(columnName).append(" is not null ");

        if (useRandomSampling) {
            // 开启随机采样，就会去获取 rowCount，会扫描全表。
            long rowCount = this.getRowCount();
            int startNum = RandomUtils.nextInt(0, (int) rowCount);
            if (rowCount <= rowLimit) {
                return sql.toString();
            }
            sql = new StringBuilder();
            sql.append("select ");
            sql.append(columnNameOnly);
            sql.append(" FROM (select ");
            sql.append(qualifiedName);
            sql.append(",rownum rowno ");
            sql.append(this.getFrom(tableName));
            sql.append(" where rownum <= ").append(startNum+rowLimit).append(") a");
            // 开启排除空值采样的时候，一定是只有一列
            sql.append(" where ").append(qualifiedName).append(" is not null and a.rowno >= ").append(startNum);
            return sql.toString();
        }
        sql.append(" and ROWNUM <= ").append(rowLimit);
        return sql.toString();
    }

    /**
     * 采样失败时的后备逻辑，所以不存在随机了，只是简单使用 rownum
     * @param columns
     * @param rowLimit
     * @return
     */
    private String createSqlRNum(List<ContextColumn> columns, long rowLimit) {
        String qualifiedName = this.getContext().getQualifiedName(columns);
        StringBuilder sql = new StringBuilder(SQL_SELECT);
        sql.append(qualifiedName).append(this.getFrom());
        sql.append(" WHERE ROWNUM <= ").append(rowLimit);
         return sql.toString();
    }

    @Override
    public DataGrid page(List<ContextColumn> columns, long start, long rowLimit) throws ClassifierException {
        boolean useRandomSampling = this.getContext().getTaskGlobalDataHolder().getTaskParam().isUseRandomSampling();
        return this.doPage(columns, start, rowLimit, !useRandomSampling);
    }

    public DataGrid doPage(List<ContextColumn> columns, long start, long rowLimit, boolean useRowNum) throws ClassifierException {
        boolean isTimeout = false;
        Clock clock = new Clock();
        String sql = this.createSql(columns, start, rowLimit, !useRowNum);

        LOG.debug(clock.getElapsedMessage("Created SQL"));
        LOG.debug("Executing on: " + this.getContext() + System.lineSeparator() + sql);

        DataGrid dataGrid;
        int queryTimeoutSeconds = this.getContext().getTaskGlobalDataHolder().getTaskParam().getSampleSqlTimeout();
        try (Statement statement = this.getContext().getStatement();
             ResultSet resultSet = executeTimed(this.context.getDatasource().supportedQueryTimeout(), statement, sql, queryTimeoutSeconds)) {
            clock.resetStartTime();
            this.lastExecutionTime = clock.getElapsedTimeMillis();
            LOG.debug(clock.getElapsedMessage("Executed Query"));
            return new DataGrid(resultSet);
        }  catch (TimeoutException | SQLRecoverableException exception) {
            isTimeout = true;
            String message = Messages.getString("The query on: '${tablename}' exceeded the timeout period of: ${timeperiod} seconds.  Results will be based on: ${rowcount} rows.", "tablename", this.getContext().getCurrentTableName(), "timeperiod", String.valueOf(this.context.getTaskGlobalDataHolder().getTaskParam().getSampleSqlTimeout()), "rowcount", String.valueOf(this.getReadRows()));
            LOG.warn("{}{}{}", message, System.lineSeparator(), exception, exception);
            throw new TimeoutException(message, exception.toString());
        } catch (SQLException sqlException) {
            DatasourceDriver datasourceDriver = this.getContext().getDatasource().getDriverInterface();
            String exceptionDetails = datasourceDriver.getExceptionDetails(sql, sqlException);
            String message;
            if (!this.getContext().getCurrentTable().getTableTypeName().equalsIgnoreCase("view") || sqlException.getErrorCode() != 1445 && sqlException.getErrorCode() != 1446) {
                message = Messages.getString("Unable to retrieve results for: '${tablename}' columns: '${columnList}'.", "tablename", this.getContext().getCurrentTableName(), "columnList", RuleResults.toColumnNamesString(columns));
                LOG.warn("{}{}{}", message, System.lineSeparator(), exceptionDetails, sqlException);
                throw new RuleEvaluationException(sqlException + System.lineSeparator() + message, exceptionDetails, sqlException);
            }

            message = Messages.getString("Unable to get sample for: '${tablename}'. " +
                    "Classification process will use rownum and attempt to retrieve sample data.", "tablename", this.getContext().getCurrentTableName());
            LOG.warn("{}{}{}", message, System.lineSeparator(), exceptionDetails, sqlException);
            dataGrid = this.doPage(columns, start, rowLimit, true);
        } finally {
            if (isTimeout) {
                LOG.debug("Recycling Connection...");
                this.context.closeConnection();
            }
            LOG.debug("Released resources for: " + this.context);
        }
        return dataGrid;
    }


    @Override
    public DataGrid pageRealTableForView(String tablename, List<String> columns, long start, long rowLimit) throws ClassifierException {
        return this.pageRealTableForView(tablename, columns, start, rowLimit, false);
    }

    public DataGrid pageRealTableForView(String tableName, List<String> columns, long start, long rowLimit, boolean useRownum) throws ClassifierException {
        boolean isTimeout = false;
        Clock clock = new Clock();
        String sql = this.createSpecifyTableSql(tableName, columns, start, rowLimit, !useRownum);

        LOG.debug(clock.getElapsedMessage("Created SQL"));
        LOG.debug("Executing on: " + this.getContext() + System.lineSeparator() + sql);

        DataGrid dataGrid;
        int queryTimeoutSeconds = this.getContext().getTaskGlobalDataHolder().getTaskParam().getSampleSqlTimeout();
        try (Statement statement = this.getContext().getStatement();
             ResultSet resultSet = executeTimed(this.context.getDatasource().supportedQueryTimeout(), statement, sql, queryTimeoutSeconds)) {
            clock.resetStartTime();
            this.lastExecutionTime = clock.getElapsedTimeMillis();
            LOG.debug(clock.getElapsedMessage("Executed Query"));
            return new DataGrid(resultSet);
        }  catch (TimeoutException | SQLRecoverableException exception) {
            isTimeout = true;
            String message = Messages.getString("The query on: '${tablename}' exceeded the timeout period of: ${timeperiod} seconds.  Results will be based on: ${rowcount} rows.", "tablename", this.getContext().getCurrentTableName(), "timeperiod", String.valueOf(this.context.getTaskGlobalDataHolder().getTaskParam().getSampleSqlTimeout()), "rowcount", String.valueOf(this.getReadRows()));
            LOG.warn("{}{}{}", message, System.lineSeparator(), exception, exception);
            throw new TimeoutException(message, exception.toString());
        } catch (SQLException sqlException) {
            DatasourceDriver datasourceDriver = this.getContext().getDatasource().getDriverInterface();
            String exceptionDetails = datasourceDriver.getExceptionDetails(sql, sqlException);
            String message;
            if (!this.getContext().getCurrentTable().getTableTypeName().equalsIgnoreCase("view") || sqlException.getErrorCode() != 1445 && sqlException.getErrorCode() != 1446) {
                message = Messages.getString("Unable to retrieve results for: '${tablename}' columns: '${columnList}'.", "tablename", this.getContext().getCurrentTableName(), "columnList", String.join(",", columns));
                LOG.warn("{}{}{}", message, System.lineSeparator(), exceptionDetails, sqlException);
                throw new RuleEvaluationException(sqlException + System.lineSeparator() + message, exceptionDetails, sqlException);
            }

            message = Messages.getString("Unable to get sample for: '${tablename}'. " +
                    "Classification process will use rownum and attempt to retrieve sample data.", "tablename", this.getContext().getCurrentTableName());
            LOG.warn("{}{}{}", message, System.lineSeparator(), exceptionDetails, sqlException);
            dataGrid = this.pageRealTableForView(tableName, columns, start, rowLimit, true);
        } finally {
            if (isTimeout) {
                LOG.debug("Recycling Connection...");
                this.context.closeConnection();
            }
            LOG.debug("Released resources for: " + this.context);
        }
        return dataGrid;
    }

    @Override
    public String getViewDefSql(String owner, String viewName) {
        // 使用 jdbc 获取视图定义的 sql
        String viewDefinitionSql = null;
        String query = "SELECT VIEW_NAME, TEXT FROM ALL_VIEWS " +
                "WHERE VIEW_NAME = '" + viewName.toUpperCase() + "' AND OWNER = '"+owner+"'";
        try(Statement stmt = this.context.getConnection().createStatement();
            ResultSet resultSet = stmt.executeQuery(query)){
            while (resultSet.next()) {
                viewDefinitionSql = resultSet.getString("TEXT");
            }
        } catch (Exception e){
            e.getMessage();
            return null;
        }
        return viewDefinitionSql;
    }
}
