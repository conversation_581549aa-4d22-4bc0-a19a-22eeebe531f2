package com.dcap.classifier.context;

import java.sql.SQLException;

public interface ColumnProvider {
   String getColumnName() throws SQLException;

   int getColumnDataType() throws SQLException;

   String getColumnTypeName() throws SQLException;

   int getColumnSize() throws SQLException;

   int getColumnScale() throws SQLException;

   String getColumnRemarks() throws SQLException;

   int getOrdinalPosition() throws SQLException;

}
