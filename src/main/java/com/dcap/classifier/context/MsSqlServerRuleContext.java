package com.dcap.classifier.context;

import com.dcap.classifier.ClassifierException;
import com.dcap.classifier.Clock;
import com.dcap.classifier.InitializationException;
import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.TaskGlobalDataHolder;
import com.dcap.utils.Messages;
import com.dcap.utils.UtilDB;
import com.yd.dcap.classifier.SqlServerDatasource;
import com.yd.dcap.classifier.taskreport.ScanDbReport;
import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.*;

@Slf4j
public class MsSqlServerRuleContext extends RuleContext {

    private static final transient String[] TBL_AND_VIEW = new String[]{"TABLE", "VIEW"};
    private static final transient String SYN_OWNER = "SN_OWN";
    private static final transient String SYN_NAME = "SN_NM";
    private static final transient String REF_OWNER = "RF_OWN";
    private static final transient String REF_NAME = "RF_NM";
    private static final transient String REF_TYPE = "RF_TYP";

    private static final String tableAttributesSql = "SELECT "
            + "DB_NAME() AS TABLE_CATALOG, S.NAME AS TABLE_SCHEMA, O.NAME AS TABLE_NAME, EP.VALUE AS REMARKS "
            + "FROM SYS.EXTENDED_PROPERTIES EP "
            + "INNER JOIN SYS.ALL_OBJECTS O ON EP.MAJOR_ID = O.OBJECT_ID "
            + "INNER JOIN SYS.SCHEMAS S ON O.SCHEMA_ID = S.SCHEMA_ID "
            + "WHERE EP.NAME = 'MS_Description' "
            + "ORDER BY "
            + "TABLE_SCHEMA, TABLE_NAME ";

    private static final String columnAttributesSql = "SELECT " +
            "DB_NAME() AS TABLE_CATALOG, S.name AS TABLE_SCHEMA, O.name AS TABLE_NAME, C.name AS COLUMN_NAME, EP.value AS REMARKS " +
            "FROM sys.extended_properties EP " +
            "LEFT JOIN sys.all_objects O ON EP.major_id = O.object_id " +
            "LEFT JOIN sys.schemas S ON O.schema_id = S.schema_id " +
            "LEFT JOIN sys.columns AS C ON EP.major_id = C.object_id AND EP.minor_id = C.column_id " +
            "WHERE EP.name = 'MS_Description' AND EP.minor_id > 0 AND O.name = ? " +
            "ORDER BY TABLE_SCHEMA, TABLE_NAME ";

    private static final transient String SQL_TBL_SYNONYM = "SELECT syn.name As SN_NM,SCHEMA_NAME(syn.SCHEMA_ID) As SN_OWN,COALESCE(PARSENAME(base_object_name,4),@@servername) AS serverName,COALESCE(PARSENAME(base_object_name,3),DB_NAME(DB_ID())) AS dbName,COALESCE(PARSENAME(base_object_name,2),SCHEMA_NAME(SCHEMA_ID())) AS RF_OWN,PARSENAME(base_object_name,1) AS RF_NM,obj.type_desc As RF_TYP FROM ?.sys.synonyms syn, ?.sys.all_objects obj where COALESCE(PARSENAME(base_object_name,2),SCHEMA_NAME(SCHEMA_ID())) = schema_name(obj.schema_id) and PARSENAME(base_object_name,1) = obj.name";
    private static final String BRACKET_LEFT = "[";
    private static final String BRACKET_RIGHT = "]";
    private static final String SELECT_COUNT = "SELECT COUNT_BIG(*) FROM ";

    protected MsSqlServerRuleContext(ClassifierDataSource dataSource, TaskGlobalDataHolder globalDataHolder) throws ConnectionException {
        super(dataSource, globalDataHolder);
    }

    protected Map<String, String> getTableComments(String catalog) {
        Map<String, String> tableComments = new HashMap<>();
        String tableAttributesSql = "SELECT "
                + "'" + catalog + "' AS TABLE_CATALOG, S.NAME AS TABLE_SCHEMA, O.NAME AS TABLE_NAME, EP.VALUE AS REMARKS "
                + "FROM [" + catalog + "].SYS.EXTENDED_PROPERTIES EP "
                + "INNER JOIN [" + catalog + "].SYS.ALL_OBJECTS O ON EP.MAJOR_ID = O.OBJECT_ID "
                + "INNER JOIN [" + catalog + "].SYS.SCHEMAS S ON O.SCHEMA_ID = S.SCHEMA_ID "
                + "WHERE EP.NAME = 'MS_Description' AND EP.MINOR_ID = 0 "  // MINOR_ID = 0 表示表级别的属性
                + "ORDER BY S.NAME, O.NAME ";

        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = this.getConnection().createStatement();
            resultSet = statement.executeQuery(tableAttributesSql);
            while (resultSet.next()) {
                String tableCatalog = resultSet.getString("TABLE_CATALOG");
                String tableSchema = resultSet.getString("TABLE_SCHEMA");
                String tableName = resultSet.getString("TABLE_NAME");
                String remarks = resultSet.getString("REMARKS");

                String fullTableName = tableCatalog+"."+tableSchema + "." + tableName;
                // 或者根据需要使用 tableSchema + "." + tableName
                tableComments.put(fullTableName, remarks);
            }
        } catch (SQLException e) {
            LOG.error("Could not execute SQL Server query table comment", e);
        } finally {
            try {
                if (resultSet != null && !resultSet.isClosed()) {
                    resultSet.close();
                }
                if (statement != null && !statement.isClosed()) {
                    statement.close();
                }
            } catch (SQLException e) {
                LOG.error("关闭资源时发生错误", e);
            }
        }
        return tableComments;
    }

    protected List<ContextTable> findCatalogTables(String catalog) {
        LOG.info("获取所有的表："  + catalog );
        Clock clock = new Clock();
        this.globalDataHolder.getProbeClientTaskContext().recordField(ScanDbReport.FIELD_DATASOURCE_COUNT, 1);
        this.findCatalogTables(catalog, TBL_AND_VIEW);
        List<ContextTable> tables = this.findTableSynonyms(catalog);
        this.addTables(tables);

        clock.incrementCount(this.listCatalogTables().size());
        this.globalDataHolder.getProbeClientTaskContext().recordField(ScanDbReport.FIELD_TABLE_COUNT, clock.getCount()).sendToServer();
        LOG.debug(this.toString() + ' ' + clock.getElapsedMessageForCounter("Total Tables for: '" + catalog + "'"));
        return this.listCatalogTables();
    }

    protected List<ContextTable> findTableSynonyms(String catalog) {
        List<ContextTable> synonyms = new ArrayList<>();
        String sql = SQL_TBL_SYNONYM.replace("?", catalog);
        SqlServerDatasource datasource = (SqlServerDatasource) this.getDatasource();
        try (Connection mssqlConnection = UtilDB.buildDatabaseConnection("mssql",
                datasource.getHost(), Integer.parseInt(datasource.getPort()), datasource.getUsername(), datasource.getPassword(), catalog);
             PreparedStatement preparedStatement = mssqlConnection.prepareStatement(sql);
             ResultSet resultSet = preparedStatement.executeQuery()) {
            Set<String> selectedDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedDatabase();
            Set<String> excludedDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedDatabase();
            Set<String> selectedSchema = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedSchema();
            Set<String> excludedSchema = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedSchema();
            Set<String> selectedSynonym = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedSynonym();
            Set<String> excludedSynonym = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedSynonym();


            while (resultSet.next()) {
                String synonymOwner = resultSet.getString(SYN_OWNER);
                String synonymName = resultSet.getString(SYN_NAME);
                String referencesName = resultSet.getString(REF_NAME);
                String referenceType = resultSet.getString(REF_TYPE);
                String referencesOwner = resultSet.getString(REF_OWNER);

                // 当前表是属于内部 schema 或 内部 catalog 就跳过
                if (isInternalSchema(this.getDatasource(), synonymOwner) || isInternalCatalog(this.getDatasource(), catalog)) {
                    LOG.debug("Catalog: " + catalog + ", Schema: " + synonymOwner + " has been skipped ");
                    continue;
                }
                ContextTable table = this.createContextTable(catalog, synonymOwner, synonymName, "SYNONYM", null);
                if (table == null) {
                    continue;
                }
                String tableIdentityName = table.getIdentityName().toLowerCase();
                String databaseIdentityName = tableIdentityName.substring(0, tableIdentityName.indexOf("."));
                String schemaIdentityName = tableIdentityName.substring(0, tableIdentityName.lastIndexOf("."));
                if (excludedDatabase.contains(databaseIdentityName)){
                    continue;
                }
                if (excludedSchema.contains(schemaIdentityName)){
                    continue;
                }
                if (excludedSynonym.contains(tableIdentityName)){
                    continue;
                }

                if ((selectedDatabase.isEmpty() || selectedDatabase.contains(databaseIdentityName))
                        && (selectedSchema.isEmpty() || selectedSchema.contains(schemaIdentityName))
                        && (selectedSynonym.isEmpty() || selectedSynonym.contains(tableIdentityName))) {
                    synonyms.add(table);
                }
            }
        } catch (Exception sqlException) {
            String message = Messages.getString("Could not access table(s) for: '${datasourceName}' on: '${datasourceUrl}'", "datasourceName", String.valueOf(this.getDatasource()), "datasourceUrl", this.getConnectionDescriptor());
            LOG.warn(message + System.lineSeparator() + sqlException);
        }
        return synonyms;
    }



    /**
     * 根据传入的 column 返回： `tableName.columnName`
     *
     * @param column
     * @return
     */
    public String getQualifiedName(ContextColumn column) {
        StringBuilder qualifiedName = new StringBuilder();
        ContextTable table = column.getTable();
        if (table != null) {
            qualifiedName.append(this.getQualifiedName(table)).append('.');
        }
        return qualifiedName.append(this.bracketIdentifiers(column.getColumnName())).toString();
    }

    public String bracketIdentifiers(String columnName) {
        if (columnName != null && columnName.length() > 0) {
            return !columnName.startsWith("[") ? "[" + columnName + "]" : this.quoteIdentifiers(columnName);
        } else {
            return columnName;
        }
    }

    @Override
    protected boolean isInternalSchema(ClassifierDataSource dataSource, String tableSchema) {
        return "sys".equalsIgnoreCase(tableSchema) || "INFORMATION_SCHEMA".equalsIgnoreCase(tableSchema);
    }

    @Override
    protected List<ContextColumn> findTableColumns() throws InitializationException {
        Clock clock = new Clock();

        ContextTable currentTable = this.getCurrentTable();
        if (currentTable.getReference() != null) {
            currentTable = currentTable.getReference();
        }

        List<ContextColumn> contextColumns = new ArrayList<>();
        try{
            Connection connection = this.getConnection();
            if (connection == null) {
                return contextColumns;
            }
            String catalog = currentTable.getCatalog();
            String schemaPattern = currentTable.getSchema();
            String tableNamePattern = currentTable.getTableName();

            connection.setCatalog(catalog);
            connection.setSchema(schemaPattern);
            final Map<String,String> columnRemarksMap = new HashMap<>();
            try(PreparedStatement preparedStatement = connection.prepareStatement(columnAttributesSql)){
                preparedStatement.setString(1,currentTable.getTableName());
                try(ResultSet resultSet = preparedStatement.executeQuery()){
                    while (resultSet.next()) {
                        String tableCatalog = resultSet.getString("TABLE_CATALOG");
                        String tableSchema = resultSet.getString("TABLE_SCHEMA");
                        String tableName = resultSet.getString("TABLE_NAME");
                        String columnName = resultSet.getString("COLUMN_NAME");
                        String remarks = null;
                        try{
                            remarks = resultSet.getString("REMARKS");
                        } catch (Exception ignored){}
                        columnRemarksMap.put(tableCatalog + "." + tableSchema + "." + tableName + "." + columnName, remarks);
                    }
                }
            }

            DatabaseMetaData metaData = connection.getMetaData();
            try (ColumnIterator columnIterator = this.getColumnProvider(catalog, schemaPattern, tableNamePattern, metaData)) {
                Map<String, Boolean> primaryColumns = getPrimaryColumns(metaData, catalog, schemaPattern, currentTable.getTableName());
                while (columnIterator.hasNext()) {
                    ColumnProvider columnProvider = columnIterator.next();
                    String columnName = columnProvider.getColumnName();
                    String fullColumnName = catalog+"."+schemaPattern+"."+tableNamePattern+"."+columnProvider.getColumnName();
                    ContextColumn contextColumn = this.createContextColumn(currentTable, columnName,
                            columnProvider.getColumnDataType(), columnProvider.getColumnTypeName(),
                            columnProvider.getColumnSize(), columnProvider.getColumnScale(),
                            columnRemarksMap.get(fullColumnName), columnProvider.getOrdinalPosition(),
                            primaryColumns.get(columnName) == Boolean.TRUE);
                    if (contextColumn == null) {
                        continue;
                    }
                    contextColumns.add(contextColumn);
                    clock.incrementCount();
                }
            }
            return contextColumns;
        } catch (SQLException | NullPointerException | ClassifierException exception) {
            String message = Messages.getString("Could not access column(s) for table: '${tablename}' on '${datasourceUrl}'", "datasourceUrl", this.getConnectionDescriptor(), "tablename", String.valueOf(currentTable));
            LOG.error(message, exception);
            throw new InitializationException(clock.getElapsedMessage(message), exception.toString(), exception);
        }
    }

    protected String getCountSQLQuery(String var1) {
        // 解析表名格式，提取数据库名
        String[] parts = parseTableName(var1);
        String database = parts[0];
        String schema = parts[1];
        String table = parts[2];

        return String.format(
                "SELECT p.rows FROM [%s].sys.partitions p " +
                        "INNER JOIN [%s].sys.objects o ON p.object_id = o.object_id " +
                        "WHERE o.name = '%s' AND SCHEMA_NAME(o.schema_id) = '%s' " +
                        "AND p.index_id IN (0,1)",
                database, database, table, schema
        );
    }

    private String[] parseTableName(String fullTableName) {
        // 处理你的 "big"."dbo"."big.dbo.big_table1" 格式
        // 需要根据实际格式调整解析逻辑
        String cleaned = fullTableName.replaceAll("\"", "");
        String[] parts = cleaned.split("\\.");

        if (parts.length >= 3) {
            String database = parts[0];
            String schema = parts[1];
            // 从索引2开始到最后，组合成完整表名
            StringBuilder tableBuilder = new StringBuilder();
            for (int i = 2; i < parts.length; i++) {
                if (i > 2) {
                    tableBuilder.append(".");
                }
                tableBuilder.append(parts[i]);
            }
            String table = tableBuilder.toString(); // "big.dbo.big_table1"

            return new String[]{database, schema, table};
        }

        throw new IllegalArgumentException("Invalid table name format: " + fullTableName);
    }
}
