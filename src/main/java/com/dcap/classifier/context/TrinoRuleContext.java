package com.dcap.classifier.context;


import com.dcap.classifier.ClassifierException;
import com.dcap.classifier.Clock;
import com.dcap.classifier.InitializationException;
import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.TaskGlobalDataHolder;
import com.dcap.utils.Messages;
import com.yd.dcap.classifier.taskreport.StatusRecord;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.util.*;

public class TrinoRuleContext extends RuleContext {

    private final Set<String> internalSchema = new HashSet<String>(Arrays.asList(
            "information_schema"
    ));
    public TrinoRuleContext(ClassifierDataSource dataSource, TaskGlobalDataHolder globalDataHolder) throws ConnectionException {
        super(dataSource, globalDataHolder);
    }

    @Override
    protected boolean isInternalSchema(ClassifierDataSource dataSource, String tableSchema) {
        return internalSchema.contains(tableSchema);
    }

    protected List<ContextColumn> findTableColumns() throws InitializationException {
        Clock clock = new Clock();
        ContextTable currentTable = this.getCurrentTable();
        if (currentTable.getReference() != null) {
            currentTable = currentTable.getReference();
        }

        List<ContextColumn> contextColumns = new ArrayList<>();
        try {
            String catalog = currentTable.getCatalog();
            String schemaPattern = currentTable.getSchema();
            String tableNamePattern = currentTable.getTableName();
            Connection connection = this.getConnection();
            if (connection == null) {
                LOG.error("query columns info failed, because get connection is null");
                return contextColumns;
            }
            tableNamePattern = tableNamePattern.replaceAll("_","\\\\_");
            DatabaseMetaData metaData = connection.getMetaData();
            try (ColumnIterator columnIterator = this.getColumnProvider(catalog, schemaPattern, tableNamePattern, "%", metaData)) {
                while (columnIterator.hasNext()) {
                    ColumnProvider columnProvider = columnIterator.next();
                    String columnName = columnProvider.getColumnName();
                    ContextColumn contextColumn = this.createContextColumn(currentTable, columnName,
                            columnProvider.getColumnDataType(), columnProvider.getColumnTypeName(),
                            columnProvider.getColumnSize(), columnProvider.getColumnScale(), columnProvider.getColumnRemarks(), columnProvider.getOrdinalPosition(),
                            false);
                    if (contextColumn == null) {
                        continue;
                    }
                    contextColumns.add(contextColumn);
                    clock.incrementCount();
                }
            }
            return contextColumns;
        } catch (SQLException | NullPointerException | ClassifierException exception) {
            String message = Messages.getString("Could not access column(s) for table: '${tablename}' on '${datasourceUrl}'", "datasourceUrl", this.getConnectionDescriptor(), "tablename", String.valueOf(currentTable));
            LOG.error(message, exception);
            this.globalDataHolder.getProbeClientTaskContext()
                    .reportErrorOccurredExecuting(StatusRecord.Position.FindTableColumns,
                            clock.getElapsedMessage(message), null, exception);
            return contextColumns;
        } catch (Exception exception) {
            String message = Messages.getString("Could not access column(s) for table: '${tablename}' on '${datasourceUrl}'", "datasourceUrl", this.getConnectionDescriptor(), "tablename", String.valueOf(currentTable));
            LOG.error(message, exception);
            this.globalDataHolder.getProbeClientTaskContext()
                    .reportErrorOccurredExecuting(
                            StatusRecord.Position.FindTableColumns, clock.getElapsedMessage(message), null, exception
                    );
            return contextColumns;
        }
    }
}
