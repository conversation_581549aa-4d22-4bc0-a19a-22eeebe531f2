package com.dcap.classifier.context;

import com.dcap.classifier.Clock;
import com.dcap.classifier.RuleEvaluator;
import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.TaskGlobalDataHolder;
import com.dcap.utils.Messages;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class SparkSqlRuleContext extends RuleContext {

    public SparkSqlRuleContext(ClassifierDataSource dataSource, TaskGlobalDataHolder globalDataHolder) throws ConnectionException {
        super(dataSource, globalDataHolder);
    }

    protected List<String> findCatalogs() {
        LOG.info("find Catalogs");
        this.resetBrowse();
        String dbName = this.getDatasource().getDbName();
        if (dbName != null && !dbName.isEmpty()) {
            this.catalogs.add(dbName);
            return this.catalogs;
        }

        Set<String> emptyCatalogs = this.getEmptyCatalogs().stream().map(String::toUpperCase).collect(Collectors.toCollection(HashSet::new));
        try {
            Connection connection = this.getConnection();
            Set<String> selectedSchemaOrDatabase;
            Set<String> excludedSchemaOrDatabase;
            String inventoryDbName = this.instanceName;
            if (StringUtils.isBlank(inventoryDbName)) {
                inventoryDbName = this.getDatasource().getInventoryDbName();
            }

            String columnLabel = "catalog";
            if (this.getDatasource().isCatalog()) {
//                resultSet = metaData.getCatalogs();
//                columnLabel = Constants.DBMD_TABLE_CAT;
                // database
                selectedSchemaOrDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedDatabase();
                excludedSchemaOrDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedDatabase();
            } else {
//                resultSet = metaData.getSchemas();
//                columnLabel = Constants.DBMD_TABLE_SCHEM;
                // schema
                selectedSchemaOrDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedSchema();
                excludedSchemaOrDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedSchema();
            }
            try(PreparedStatement preparedStatement = connection.prepareStatement("show catalogs;");
                ResultSet resultSet = preparedStatement.executeQuery()) {
                while (resultSet.next()) {
                    String catalogName = resultSet.getString(columnLabel);
                    if (!emptyCatalogs.isEmpty() && emptyCatalogs.contains(catalogName.toUpperCase())) {
                        continue;
                    }
                    String catalogIdentityName = inventoryDbName != null ?
                            (inventoryDbName.toLowerCase() + "." + catalogName.toLowerCase()) : catalogName.toLowerCase();
                    // 如果是排除，就跳过。必须包含
                    if (excludedSchemaOrDatabase.contains(catalogIdentityName)){
                        continue;
                    }
                    if (selectedSchemaOrDatabase.isEmpty() || selectedSchemaOrDatabase.contains(catalogIdentityName)) {
                        this.catalogs.add(catalogName);
                    }
                }
                return this.catalogs;
            }
        } catch (SQLException sqlException) {
            sqlException.printStackTrace();
            LOG.warn("[sparkSql] Could not get catalogs for '{}'{}{}", this, System.lineSeparator(), sqlException);
            this.globalDataHolder.getProbeClientTaskContext()
                    .reportErrorOccurredExecuting(StatusRecord.Position.FindCatalogs,null,null,sqlException);
        }
        return this.catalogs;
    }

    protected List<ContextTable> findCatalogTables(String catalog, String[] tableTypes) {
        Clock clock = new Clock();
        String catalogName = null;
        String schemaName = null;
        if (this.getDatasource().isCatalog()) {
            catalogName = catalog;
        } else {
            schemaName = catalog;
        }

        if (this.tables != null) {
            this.tables.clear();
        } else {
            this.tables = new ArrayList<>();
        }

        Connection connection = this.getConnection();

        this.currentTable = null;
        ResultSet resultSet = null;
        try(Statement statement = connection.createStatement();) {
            statement.execute("use "+catalogName);
            // todo: 这里是调整一下传入的 tableTypes。
            tableTypes = this.getDatasource().getDriverInterface().adjustTableTypes(tableTypes, true, this.getConnection());
            DatabaseMetaData metaData = connection.getMetaData();
            // todo: this maybe very slow for wrong input
            LOG.debug("Get tables with catalog = {} and schema = {}", catalogName, schemaName);
            resultSet = metaData.getTables(catalogName, schemaName, null, tableTypes);
            LOG.debug("Get tables - end");
            Set<String> selectedDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedDatabase();
            Set<String> excludedDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedDatabase();
            Set<String> selectedSchema = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedSchema();
            Set<String> excludedSchema = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedSchema();
            Set<String> selectedTable = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedTable();
            Set<String> excludedTable = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedTable();
            Set<String> selectedView = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedView();
            Set<String> excludedView = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedView();
            String inventoryDbName = this.instanceName;
            if (StringUtils.isBlank(inventoryDbName)) {
                inventoryDbName = this.getDatasource().getInventoryDbName();
            }
            while (resultSet.next()) {
                String tableType = resultSet.getString("TABLE_TYPE");
                String tableName = resultSet.getString("TABLE_NAME");
                String tableCatalog = resultSet.getString("TABLE_CAT");
                String tableSchema = resultSet.getString(Constants.DBMD_TABLE_SCHEM);
                String tableComment = resultSet.getString("REMARKS");

                if (StringUtils.isBlank(tableCatalog) && StringUtils.isNotBlank(catalogName)) {
                    tableCatalog = catalogName;
                }

                // 当前表是属于内部 schema 或 内部 catalog 就跳过
                if (isInternalSchema(this.getDatasource(), tableSchema)
                        || isInternalCatalog(this.getDatasource(), tableCatalog)) {
                    LOG.debug("Catalog: {}, Schema: {} has been skipped ", catalog, tableSchema);
                    continue;
                }
                ContextTable table = this.createContextTable(tableCatalog, tableSchema, tableName, tableType, tableComment);
                if (table == null) {
                    continue;
                }
                String tableIdentityName = table.getIdentityName().toLowerCase();
                String databaseIdentityName = tableIdentityName.substring(0, tableIdentityName.indexOf(".")).toLowerCase();
                String schemaIdentityName = tableIdentityName.substring(0, tableIdentityName.lastIndexOf(".")).toLowerCase();
                tableIdentityName = inventoryDbName == null ? tableIdentityName : (inventoryDbName + "." + tableIdentityName).toLowerCase();
                databaseIdentityName = inventoryDbName == null ? databaseIdentityName : (inventoryDbName + "." + databaseIdentityName).toLowerCase();
                schemaIdentityName = inventoryDbName == null ? schemaIdentityName : (inventoryDbName + "." + schemaIdentityName).toLowerCase();
                String tableTypeName = table.getType().getTypeName();
                if (excludedDatabase.contains(databaseIdentityName)){
                    continue;
                }
                if (StringUtils.isNotBlank(tableSchema) && excludedSchema.contains(schemaIdentityName)){
                    continue;
                }
                if ("TABLE".equals(tableTypeName) && excludedTable.contains(tableIdentityName)){
                    continue;
                }
                if ("VIEW".equals(tableTypeName) && excludedView.contains(tableIdentityName)){
                    continue;
                }
                if ((selectedDatabase.isEmpty() || selectedDatabase.contains(databaseIdentityName))
                        && (selectedSchema.isEmpty() || (StringUtils.isNotBlank(tableSchema) && selectedSchema.contains(schemaIdentityName)))
                        && (selectedTable.isEmpty() || selectedTable.contains(tableIdentityName))
                        && "TABLE".equals(tableTypeName)) {
                    this.addTable(table);
                    clock.incrementCount();
                } else if ((selectedDatabase.isEmpty() || selectedDatabase.contains(databaseIdentityName))
                        && (selectedSchema.isEmpty() || (StringUtils.isNotBlank(tableSchema) && selectedSchema.contains(schemaIdentityName)))
                        && (selectedView.isEmpty() || selectedView.contains(tableIdentityName))
                        && "VIEW".equals(tableTypeName)) {
                    this.addTable(table);
                    clock.incrementCount();
                }
            }
            if (LOG.isInfoEnabled()) {
                String tableType = tableTypes == null ? "Catalog Tables" : RuleEvaluator.arrayToString(tableTypes);
                LOG.debug("{}: {}", catalog, clock.getElapsedMessageForCounter(tableType));
            }
        } catch (SQLException sqlException) {
            String message = Messages.getString("Could not access table(s) for: '${datasourceName}' on: '${datasourceUrl}'", "datasourceName", String.valueOf(this.getDatasource().getDescriptor()), "datasourceUrl", this.getConnectionDescriptor());
            LOG.error(message, sqlException);
            this.globalDataHolder.getProbeClientTaskContext()
                    .reportErrorOccurredExecuting(StatusRecord.Position.FindCatalogTables,
                            "catalog [" + catalog + "]",null,sqlException);
        } catch (NullPointerException nullPointerException) {
            StringBuilder logMsg = new StringBuilder();
            String message = Messages
                    .getString("Could not access table(s) for: '${datasourceName}' on: '${datasourceUrl}'",
                            "datasourceName", String.valueOf(this.getDatasource().getDescriptor()),
                            "datasourceUrl", this.getConnectionDescriptor());
            logMsg.append(message);
            throw new DBDriverException(clock.getElapsedMessage(logMsg.toString()), nullPointerException);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (Exception ignored) {
                }
            }
        }
        return this.listCatalogTables();
    }

}
