package com.dcap.classifier.context;

import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.TaskGlobalDataHolder;


public class DremioRuleContext extends RuleContext {

    public DremioRuleContext(ClassifierDataSource dataSource, TaskGlobalDataHolder globalDataHolder) throws ConnectionException {
        super(dataSource, globalDataHolder);
    }

    @Override
    public boolean isCatalogUsed() {
        return false;
    }


}
