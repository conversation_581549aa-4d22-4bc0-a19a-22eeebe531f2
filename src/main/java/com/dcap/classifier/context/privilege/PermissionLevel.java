package com.dcap.classifier.context.privilege;

import com.yd.dcap.common.utils.UtilMisc;

import java.util.Map;

public enum PermissionLevel {
    UNKNOWN(0),
    READ(1),
    WRITE(2),
    FULL(3);

    private final int level;

    PermissionLevel(int level) {
        this.level = level;
    }

    private static final Map<String,PermissionLevel> levelMap = UtilMisc.toMap(
            "SELECT", PermissionLevel.READ,
            "REFERENCES", PermissionLevel.READ,
            "INSERT", PermissionLevel.WRITE,
            "UPDATE", PermissionLevel.WRITE,
            "DELETE", PermissionLevel.WRITE,
            "TRUNCATE", PermissionLevel.WRITE,
            "TRIGGER", PermissionLevel.WRITE,
            "SUPER_USER", PermissionLevel.FULL
    );
    public static PermissionLevel map(String privilege){
        return levelMap.get(privilege);
    }

    public int getLevel() {
        return level;
    }

    public static PermissionLevel valueOf(int level){
        if (level == 0){
            return UNKNOWN;
        } else if (level == 1){
            return READ;
        } else if (level == 2){
            return WRITE;
        } else if (level == 3){
            return FULL;
        }
        return UNKNOWN;
    }
}
