package com.dcap.classifier.context;

import com.dcap.classifier.ClassifierException;
import com.dcap.classifier.Clock;
import com.dcap.classifier.RuleEvaluator;
import com.dcap.classifier.context.privilege.DatabaseUser;
import com.dcap.classifier.context.privilege.DbPrivilege;
import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.TaskGlobalDataHolder;
import com.dcap.utils.Messages;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import org.apache.commons.lang3.StringUtils;

import java.sql.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class MySqlRuleContext extends RuleContext {

    protected List<DatabaseUser> databaseUsers;

    protected List<DbPrivilege> dbPrivileges;

    private static final String QUERY_USER_SQL = "SELECT USER AS USERNAME, HOST, PLUGIN, PASSWORD_LAST_CHANGED, " +
            "PASSWORD_EXPIRED,ACCOUNT_LOCKED " +
            " FROM mysql.user ORDER BY USER ";

    private static final String QUERY_TABLE_PRIVILEGES_SQL="SELECT HOST, DB, USER USERNAME, TABLE_NAME, GRANTOR, TIMESTAMP, " +
            "TABLE_PRIV TABLE_PRIVILEGES, COLUMN_PRIV COLUMN_PRIVILEGES FROM mysql.tables_priv WHERE DB NOT IN ('mysql')";


    private static final String QUERY_COLUMN_PRIVILEGES_SQL="SELECT HOST, DB, USER USERNAME, TABLE_NAME, COLUMN_NAME, TIMESTAMP, " +
            "COLUMN_PRIV COLUMN_PRIVILEGES " +
            " FROM mysql.columns_priv";


    public MySqlRuleContext(ClassifierDataSource dataSource, TaskGlobalDataHolder globalDataHolder) throws ConnectionException {
        super(dataSource, globalDataHolder);
    }


    protected List<ContextTable> findCatalogTables(String catalog, String[] tableTypes) {
        Clock clock = new Clock();
        String catalogName = null;
        String schemaName = null;
        if (this.getDatasource().isCatalog()) {
            catalogName = catalog;
        } else {
            schemaName = catalog;
        }

        if (this.tables != null) {
            this.tables.clear();
        } else {
            this.tables = new ArrayList<>();
        }

        Map<String, String> tableComments = new HashMap<>();
        Statement statement = null;
        ResultSet queryCommentResultSet = null;
        try {
            String sql = "SELECT concat(TABLE_SCHEMA,'.',TABLE_NAME)  FULL_TABLE_NAME, TABLE_COMMENT " +
                    "FROM INFORMATION_SCHEMA.TABLES where TABLE_SCHEMA = '"+catalog+"'";
            statement = this.getConnection().createStatement();
            queryCommentResultSet = statement.executeQuery(sql);
            while (queryCommentResultSet.next()) {
                String fullTableName = queryCommentResultSet.getString("FULL_TABLE_NAME");
                String tableComment = queryCommentResultSet.getString("TABLE_COMMENT");
                tableComments.put(fullTableName, tableComment);
            }
        } catch (SQLException e) {
            LOG.error("Could not execute SQL query table comment", e);
        } finally {
            try {
                if (statement != null && !statement.isClosed()){
                    statement.close();
                }
                if (queryCommentResultSet != null && !queryCommentResultSet.isClosed()){
                    queryCommentResultSet.close();
                }
            } catch (SQLException e) {
                LOG.error("关闭资源时发生错误", e);
            }
        }

        this.currentTable = null;
        ResultSet resultSet = null;
        try {
            // todo: 这里是调整一下传入的 tableTypes。
            tableTypes = this.getDatasource().getDriverInterface().adjustTableTypes(tableTypes, true, this.getConnection());
            DatabaseMetaData metaData = this.getConnection().getMetaData();
            // todo: this maybe very slow for wrong input
            LOG.debug("Get tables with catalog = {} and schema = {}", catalogName, schemaName);
            resultSet = metaData.getTables(catalogName, schemaName, null, tableTypes);
            LOG.debug("Get tables - end");
            Set<String> selectedDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedDatabase();
            Set<String> excludedDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedDatabase();
            Set<String> selectedSchema = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedSchema();
            Set<String> excludedSchema = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedSchema();
            Set<String> selectedTable = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedTable();
            Set<String> excludedTable = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedTable();
            Set<String> selectedView = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedView();
            Set<String> excludedView = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedView();
            String inventoryDbName = this.instanceName;
            if (StringUtils.isBlank(inventoryDbName)) {
                inventoryDbName = this.getDatasource().getInventoryDbName();
            }
            while (resultSet.next()) {
                try {
                    String tableType = resultSet.getString("TABLE_TYPE");
                    String tableName = resultSet.getString("TABLE_NAME");
                    String tableCatalog = resultSet.getString("TABLE_CAT");
                    String tableSchema = resultSet.getString(Constants.DBMD_TABLE_SCHEM);
                    String tableComment = resultSet.getString("REMARKS");
                    if (StringUtils.isBlank(tableComment)){
                        tableComment = tableComments.get(tableCatalog+"."+tableName);
                    }
                    if (StringUtils.isBlank(tableCatalog) && StringUtils.isNotBlank(catalogName)) {
                        tableCatalog = catalogName;
                    }

                    // 当前表是属于内部 schema 或 内部 catalog 就跳过
                    if (isInternalSchema(this.getDatasource(), tableSchema)
                            || isInternalCatalog(this.getDatasource(), tableCatalog)) {
                        LOG.debug("Catalog: {}, Schema: {} has been skipped ", catalog, tableSchema);
                        continue;
                    }
                    ContextTable table = this.createContextTable(tableCatalog, tableSchema, tableName, tableType, tableComment);
                    if (table == null) {
                        continue;
                    }
                    String tableIdentityName = table.getIdentityName().toLowerCase();
                    String databaseIdentityName = tableIdentityName.substring(0, tableIdentityName.indexOf(".")).toLowerCase();
                    String schemaIdentityName = tableIdentityName.substring(0, tableIdentityName.lastIndexOf(".")).toLowerCase();
                    tableIdentityName = inventoryDbName == null ? tableIdentityName : (inventoryDbName + "." + tableIdentityName).toLowerCase();
                    databaseIdentityName = inventoryDbName == null ? databaseIdentityName : (inventoryDbName + "." + databaseIdentityName).toLowerCase();
                    schemaIdentityName = inventoryDbName == null ? schemaIdentityName : (inventoryDbName + "." + schemaIdentityName).toLowerCase();
                    String tableTypeName = table.getType().getTypeName();
                    if (excludedDatabase.contains(databaseIdentityName)){
                        continue;
                    }
                    if (org.apache.commons.lang.StringUtils.isNotBlank(tableSchema) && excludedSchema.contains(schemaIdentityName)){
                        continue;
                    }
                    if (("TABLE".equals(tableTypeName) || "SYSTEM TABLE".equals(tableTypeName)) && excludedTable.contains(tableIdentityName)){
                        continue;
                    }
                    if ("VIEW".equals(tableTypeName) && excludedView.contains(tableIdentityName)){
                        continue;
                    }

                    if ((selectedDatabase.isEmpty() || selectedDatabase.contains(databaseIdentityName))
                            && (selectedSchema.isEmpty() || (org.apache.commons.lang.StringUtils.isNotBlank(tableSchema) && selectedSchema.contains(schemaIdentityName)))
                            && (selectedTable.isEmpty() || selectedTable.contains(tableIdentityName))
                            && ("TABLE".equals(tableTypeName) || "SYSTEM TABLE".equals(tableTypeName))) {
                        this.addTable(table);
                        clock.incrementCount();
                    } else if ((selectedDatabase.isEmpty() || selectedDatabase.contains(databaseIdentityName))
                            && (selectedSchema.isEmpty() || (org.apache.commons.lang.StringUtils.isNotBlank(tableSchema) && selectedSchema.contains(schemaIdentityName)))
                            && (selectedView.isEmpty() || selectedView.contains(tableIdentityName))
                            && "VIEW".equals(tableTypeName)) {
                        this.addTable(table);
                        clock.incrementCount();
                    }
                } catch (Exception e){
                    String message = Messages.getString("Could not access table for: '${datasourceName}' on: '${datasourceUrl}'",
                            "datasourceName", String.valueOf(this.getDatasource().getDescriptor()),
                            "datasourceUrl", this.getConnectionDescriptor());
                    LOG.error(message, e);
                }
            }

            if (LOG.isInfoEnabled()) {
                String tableType = tableTypes == null ? "Catalog Tables" : RuleEvaluator.arrayToString(tableTypes);
                LOG.debug(catalog + ": " + clock.getElapsedMessageForCounter(tableType));
            }
        } catch (SQLException sqlException) {
            String message = Messages.getString("Could not access table(s) for: '${datasourceName}' on: '${datasourceUrl}'", "datasourceName", String.valueOf(this.getDatasource().getDescriptor()), "datasourceUrl", this.getConnectionDescriptor());
            LOG.error(message, sqlException);
            this.globalDataHolder.getProbeClientTaskContext()
                    .reportErrorOccurredExecuting(StatusRecord.Position.FindCatalogTables,
                            "catalog [" + catalog + "]",null,sqlException);
        } catch (NullPointerException nullPointerException) {
            StringBuilder logMsg = new StringBuilder();
            String message = Messages
                    .getString("Could not access table(s) for: '${datasourceName}' on: '${datasourceUrl}'",
                            "datasourceName", String.valueOf(this.getDatasource().getDescriptor()),
                            "datasourceUrl", this.getConnectionDescriptor());
            logMsg.append(message);
            throw new DBDriverException(clock.getElapsedMessage(logMsg.toString()), nullPointerException);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (Exception ignored) {
                }
            }
        }
        // 根据表类型进行排序
        this.sortTables();
        return this.listCatalogTables();
    }


    public List<DatabaseUser> findDatabaseUsers (){
        if (this.databaseUsers != null) {
            return this.databaseUsers;
        }

        this.databaseUsers = new ArrayList<>();
        Connection connection = this.getConnection();
        try(Statement statement = connection.createStatement()) {
            ResultSet resultSet = statement.executeQuery(QUERY_USER_SQL);
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            while (resultSet.next()){
                DatabaseUser databaseUser = new DatabaseUser();
                databaseUser.setAttributes(new HashMap<>());
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnLabel(i);
                    if (Objects.equals(columnName, "USERNAME")){
                        String username = resultSet.getString(columnName);
                        databaseUser.setUsername(username);
                    } else {
                        databaseUser.getAttributes().put(columnName, resultSet.getObject(columnName));
                    }
                }
                this.databaseUsers.add(databaseUser);
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return this.databaseUsers;
    }


//    public static void main(String[] args) throws ConnectionException {
////        Connection connection = UtilDB.buildDatabaseConnection("mysql",
////                "app-alpha.yuandiansec.net", 30113,
////                "root", "p0o9i8u7y6", null);
//        Connection connection = UtilDB.buildDatabaseConnection("mysql",
//                "app-alpha.yuandiansec.net", 30113,
//                "root", "p0o9i8u7y6", null);
//        List<DbPrivilege> dbPrivileges1 = retrieveTablePrivilegesFromMetadata();
//    }
    public List<DbPrivilege> retrievePrivileges(){
        if (this.dbPrivileges != null) {
            return this.dbPrivileges;
        }
        this.dbPrivileges = new ArrayList<>();
        try (final Connection connection = this.getConnection();
             ResultSet columnPrivilegesResultSet = connection.createStatement().executeQuery(QUERY_COLUMN_PRIVILEGES_SQL);
             ResultSet tablePrivilegesResultSet = connection.createStatement().executeQuery(QUERY_TABLE_PRIVILEGES_SQL);
        ) {
            Map<String,List<String>> columnPrivilegesMap = new HashMap<>();
            while (columnPrivilegesResultSet.next()){
                final String host = columnPrivilegesResultSet.getString("HOST");
                final String catalogName = columnPrivilegesResultSet.getString("DB");
                final String grantee = columnPrivilegesResultSet.getString("USERNAME");
                final String tableName = columnPrivilegesResultSet.getString("TABLE_NAME");
                final String columnPrivileges = columnPrivilegesResultSet.getString("COLUMN_PRIVILEGES");
                final String columnName = columnPrivilegesResultSet.getString("COLUMN_NAME");
                String[] privileges = columnPrivileges.split(",");
                for (String privilege : privileges) {
                    String fetchColumnsKey = StringUtils.joinWith(".", host, catalogName, grantee, tableName, privilege);
                    columnPrivilegesMap.putIfAbsent(fetchColumnsKey, new ArrayList<>());
                    columnPrivilegesMap.get(fetchColumnsKey).add(columnName);
                }
            }
            while (tablePrivilegesResultSet.next()){
                final String host = tablePrivilegesResultSet.getString("HOST");
                final String catalogName = tablePrivilegesResultSet.getString("DB");
                final String tableName = tablePrivilegesResultSet.getString("TABLE_NAME");
                final String tablePrivileges = tablePrivilegesResultSet.getString("TABLE_PRIVILEGES");
                final String columnPrivileges = tablePrivilegesResultSet.getString("COLUMN_PRIVILEGES");
                final String grantor = tablePrivilegesResultSet.getString("GRANTOR");
                final String grantee = tablePrivilegesResultSet.getString("USERNAME");
                final String isGrantable = tablePrivileges.contains("Grant")?"Y":"N";
                final String fullTableName = StringUtils.joinWith(".", catalogName, tableName);
                if (StringUtils.isNotBlank(tablePrivileges)){
                    String [] privileges = tablePrivileges.split(",");
                    for (String s : privileges) {
                        String privilege = s.toUpperCase().replaceAll(" ", "_");
                        DbPrivilege dbPrivilege = new DbPrivilege();
                        dbPrivilege.setGrantor(grantor);
                        dbPrivilege.setGrantee(grantee);
                        dbPrivilege.setTableCatalog(catalogName);
                        dbPrivilege.setDatabaseObjectType("TABLE");
                        dbPrivilege.setDatabaseObjectName(tableName);
                        dbPrivilege.setDatabaseObjectFullName(fullTableName);
                        dbPrivilege.setIsGrantable(isGrantable);
                        dbPrivilege.setPrivilegeName(privilege);
                        dbPrivileges.add(dbPrivilege);
                    }
                }

                if (StringUtils.isBlank(columnPrivileges)){
                    continue;
                }
                String[] privileges = columnPrivileges.split(",");
                for (String privilege : privileges) {
                    String fetchColumnsKey = StringUtils.joinWith(".", host, catalogName, grantee, tableName, privilege);
                    List<String> columnNames = columnPrivilegesMap.get(fetchColumnsKey);
                    if (columnNames == null || columnNames.isEmpty()){
                        // 理论上不可能是 null
                        break;
                    }
                    for (String columnName : columnNames) {
                        final String fullColumnName = StringUtils.joinWith(".", fullTableName, columnName);
                        DbPrivilege dbPrivilege = new DbPrivilege();
                        dbPrivilege.setGrantor(grantor);
                        dbPrivilege.setGrantee(grantee);
                        dbPrivilege.setTableCatalog(catalogName);
                        dbPrivilege.setDatabaseObjectType("COLUMN");
                        dbPrivilege.setDatabaseObjectName(columnName);
                        dbPrivilege.setDatabaseObjectFullName(fullColumnName);
                        dbPrivilege.setIsGrantable(isGrantable);
                        dbPrivilege.setPrivilegeName(privilege.toUpperCase().replaceAll(" ","_"));
                        dbPrivileges.add(dbPrivilege);
                    }
                }
            }
        } catch (final Exception e) {
            LOG.error("Could not retrieve table privileges", e);
        }
        return this.dbPrivileges;
    }

    @Override
    protected List<ContextColumn> findTableColumns() {
        Clock clock = new Clock();

        ContextTable currentTable = this.getCurrentTable();
        if (currentTable.getReference() != null) {
            currentTable = currentTable.getReference();
        }

        List<ContextColumn> contextColumns = new ArrayList<>();
        try {
            String catalog = currentTable.getCatalog();
            String schemaPattern = currentTable.getSchema();
            String tableNamePattern = currentTable.getTableName();
            Connection connection = this.getConnection();
            if (connection == null) {
                return contextColumns;
            }
            DatabaseMetaData metaData = connection.getMetaData();
            tableNamePattern = tableNamePattern.replaceAll("_","\\\\_");
            try (ColumnIterator columnIterator = this.getColumnProvider(catalog, schemaPattern, tableNamePattern, metaData)) {
                Map<String, Boolean> primaryColumns = getPrimaryColumns(metaData, catalog, schemaPattern, currentTable.getTableName());
                while (columnIterator.hasNext()) {
                    ColumnProvider columnProvider = columnIterator.next();
                    String columnName = columnProvider.getColumnName();
                    ContextColumn contextColumn = this.createContextColumn(currentTable, columnName,
                            columnProvider.getColumnDataType(), columnProvider.getColumnTypeName(),
                            columnProvider.getColumnSize(), columnProvider.getColumnScale(),
                            columnProvider.getColumnRemarks(), columnProvider.getOrdinalPosition(),
                            primaryColumns.get(columnName) == Boolean.TRUE);
                    if (contextColumn == null) {
                        continue;
                    }
                    contextColumns.add(contextColumn);
                    clock.incrementCount();
                }
            }
            LOG.info(clock.getElapsedMessage("get table "+currentTable+" columns"));
            return contextColumns;
        } catch (SQLException | NullPointerException | ClassifierException exception) {
            String message = Messages.getString("Could not access column(s) for table: '${tablename}' on '${datasourceUrl}'", "datasourceUrl", this.getConnectionDescriptor(), "tablename", String.valueOf(currentTable));
            LOG.error(message, exception);
            this.globalDataHolder.getProbeClientTaskContext()
                    .reportErrorOccurredExecuting(StatusRecord.Position.FindTableColumns,
                            clock.getElapsedMessage(message), null, exception);
            return contextColumns;
        } catch (Exception exception) {
            String message = Messages.getString("Could not access column(s) for table: '${tablename}' on '${datasourceUrl}'", "datasourceUrl", this.getConnectionDescriptor(), "tablename", String.valueOf(currentTable));
            LOG.error(message, exception);
            this.globalDataHolder.getProbeClientTaskContext()
                    .reportErrorOccurredExecuting(
                            StatusRecord.Position.FindTableColumns, clock.getElapsedMessage(message), null, exception
                    );
            return contextColumns;
        }
    }

    final Pattern pattern = Pattern.compile("`([^`]+)`\\.`([^`]+)`");
    @Override
    protected String getCountSQLQuery(String qualifiedTableName) {
        Matcher matcher = pattern.matcher(qualifiedTableName);
        String schema = null;
        String tableName = null;
        if (matcher.matches()) {
            schema = matcher.group(1);
            tableName = matcher.group(2);
        }
        StringBuilder sql = new StringBuilder("SELECT  table_rows FROM information_schema.tables " +
                "WHERE table_schema = '"+schema+"' AND table_name = '"+tableName+"';");
        return sql.toString();
    }
}
