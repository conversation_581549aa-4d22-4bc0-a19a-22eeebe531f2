package com.dcap.classifier.context;

import com.dcap.classifier.InitializationException;
import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.TaskGlobalDataHolder;
import com.dcap.utils.JSON;
import com.yd.dcap.classifier.taskreport.ScanDbReport;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.rules.engine.result.MatchedResult;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.*;


public class SinodbRuleContext extends RuleContext {

    public SinodbRuleContext(ClassifierDataSource dataSource, TaskGlobalDataHolder globalDataHolder) throws ConnectionException {
        super(dataSource, globalDataHolder);
    }

    @Override
    public boolean isCatalogUsed() {
        return true;
    }

    @Override
    public void recordInventory(final Long tableRowCount, Long tenantId, String dataSourceID,
                                Map<String, List<MatchedResult>> matchedResultMap) throws InitializationException {
        Long currentVersion = this.globalDataHolder.getCurrentVersion();
        String inventoryDbName = getInventoryDbName();
        String rowCount = tableRowCount == null ? "0" : String.valueOf(tableRowCount);
        String rowCountLimit = this.getTaskGlobalDataHolder().getTaskParam().isTableRowCountEnabled() ? this.getTaskGlobalDataHolder().getTaskParam().getTableRowCountLimit() + "" : "0";
        List<ContextColumn> tableColumns = this.getTableColumns();
        this.globalDataHolder.getProbeClientTaskContext().recordField(ScanDbReport.FIELD_COLUMN_COUNT, tableColumns.size());
        List<String> columns = new ArrayList<>();
        for (ContextColumn column : tableColumns) {
            String qualName = inventoryDbName == null ? column.getIdentityName() : inventoryDbName + "." + column.getIdentityName();
            int firstPointIndex = qualName.indexOf(".")+1;
            qualName = qualName.substring(0,firstPointIndex)+qualName.substring(qualName.indexOf(".",firstPointIndex)+1);
            String database = inventoryDbName == null? column.getTable().getCatalog(): inventoryDbName;
            String schema = null;
            String tableName = column.getTable().getTableName();

            String columnName = column.getColumnName();
            boolean isPrimaryKey = column.isPrimaryKey();
            String columnComment = column.getRemarks();
            int columnPosition = column.getPosition();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(columnComment)) {
                columnComment = columnComment.replaceAll(",", " ");
            }
            ContextColumn.ColumnType columnType = column.getColumnType();
            String actualColumnTypeName = column.getTypename();
            String columnTypeCategoryName = columnType.getTypeName();
            int columnTypeSize = column.getSize();
            int columnScale = column.getScale();
            List<MatchedResult> resultList = matchedResultMap.getOrDefault(columnName.toLowerCase(), Collections.emptyList());
            Set<String> dataTagsHashSet = new HashSet<>();
            Map<String,String> matchingResultMap = new HashMap<>();
            for (MatchedResult matchedResult : resultList) {
                String dataTag = matchedResult.getDataTag();
                if (dataTag == null){
                    continue;
                }
                dataTagsHashSet.add(dataTag);
                String score = String.valueOf(matchedResult.getScore());
                matchingResultMap.put(dataTag, dataTag.concat(MATCHING_RESULT_DELIMITER).concat(score)
                        .concat(MATCHING_RESULT_DELIMITER).concat(matchedResult.getEvaluation()));
            }


            Map<String, Set<String>> dataMarkings = this.getTaskGlobalDataHolder().getDataMarkings();
            if (dataMarkings != null) {
                String manualTagsKey = tenantId + "_" + dataSourceID + "_" + qualName.toLowerCase() + "_ManualTags";
                String deletedAutoTagsKey = tenantId + "_" + dataSourceID + "_" + qualName.toLowerCase() + "_DeletedAutoTags";
                Set<String> manualTagsSet = dataMarkings.get(manualTagsKey);
                Set<String> deletedAutoTagsSet = dataMarkings.get(deletedAutoTagsKey);
                if (deletedAutoTagsSet != null) {
                    dataTagsHashSet.removeAll(deletedAutoTagsSet);
                    for (String deletedAutoTag : deletedAutoTagsSet) {
                        matchingResultMap.remove(deletedAutoTag);
                    }
                }
                // 手动打标要添加
                if (manualTagsSet != null) {
                    dataTagsHashSet.addAll(manualTagsSet);
                    for (String manualTag : manualTagsSet) {
                        matchingResultMap.put(manualTag, manualTag.concat(MATCHING_RESULT_DELIMITER)
                                .concat("100").concat(MATCHING_RESULT_DELIMITER).concat("CONFIRMED"));
                    }
                }
            }

            String dataTags = StringUtils.join(dataTagsHashSet, ";");
            String dataTagAndScoreResult = StringUtils.join(matchingResultMap.values(), ";");
            try {
                String tableTypeName = null;
                String tableComment = null;
                ContextTable table = column.getTable();
                long tableCreatedTimestamp = table.getTableCreatedTimestamp();

                tableTypeName = table.getTableTypeName();
                tableComment = table.getTableComment();
                if (tableComment != null){
                    tableComment = tableComment.replaceAll("\r\n", " ");
                    tableComment = tableComment.replaceAll("\r", " ");
                    tableComment = tableComment.replaceAll("\n", " ");
                }
                if (columnComment != null){
                    columnComment = columnComment.replaceAll("\r\n", " ");
                    columnComment = columnComment.replaceAll("\n", " ");
                    columnComment = columnComment.replaceAll("\r", " ");
                }

                if (!scanResultRecordAsTable){
                    String[] entries = {
                            String.valueOf(dataSourceID), qualName, dataTags,
                            String.valueOf(System.currentTimeMillis()),
                            String.valueOf(tenantId), "Scan",
                            tableTypeName, columnComment,
                            columnTypeCategoryName, String.valueOf(columnTypeSize),
                            String.valueOf(columnPosition), rowCount, rowCountLimit,
                            dataTagAndScoreResult, tableComment,
                            String.valueOf(this.getTaskGlobalDataHolder().getScanJobHistoryId()),
                            String.valueOf(currentVersion),
                            String.valueOf(tableCreatedTimestamp),
                            database, schema, tableName, columnName
                    };
                    this.globalDataHolder.getInventoryWriter().writeEntries(entries);
                } else {
                    columns.add(JSON.from(UtilMisc.toMap(
                            "dataSourceId", dataSourceID,
                            "databaseInstanceName", this.getDatasource().getName(),
                            "qualName", qualName,
                            "dataTags", dataTags,
                            "updateTime", System.currentTimeMillis(),
                            "tenantId", tenantId,
                            "tableTypeName", tableTypeName,
                            "columnComment", columnComment,
//                            "dataTagType", columnTypeName,
                            "columnTypeCategoryName", columnTypeCategoryName,
                            "actualColumnTypeName", actualColumnTypeName,
                            "columnTypeSize", columnTypeSize,
                            "columnScale", columnScale,
                            "columnPosition", columnPosition,
                            "rowCount", rowCount,
                            "rowCountLimit", rowCountLimit,
                            "dataTagAndScoreResult", dataTagAndScoreResult,
                            "tableComment", tableComment,
                            "scanJobHistoryId", this.getTaskGlobalDataHolder().getScanJobHistoryId(),
                            "currentVersion", currentVersion,
                            "tableCreatedTimestamp", tableCreatedTimestamp,
                            "database", database,
                            "schema", schema,
                            "tableName", tableName,
                            "columnName", columnName,
                            "primaryKey", isPrimaryKey
                    )).toString());
                }
            } catch (Exception e) {
                LOG.error("Failed to write the inventory csv file", e);
                e.printStackTrace();
            }
        }
        try {
            if (scanResultRecordAsTable){
                this.globalDataHolder.getInventoryWriter().writeEntries(columns.toArray(new String[0]));
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
