package com.dcap.classifier.context;

import java.util.Objects;

public class ContextTable extends AbstractContext {
   public static final ContextTable.TableType TABLE = new ContextTable.TableType("TABLE", new String[]{"HIERARCHY TABLE", "TYPED TABLE", "GLOBAL TEMPORARY"});
   public static final ContextTable.TableType SYSTEM_TABLE = new ContextTable.TableType("SYSTEM TABLE", new String[]{"SYSTEM_TABLE", "SYSTEM VIEW"});
   public static final ContextTable.TableType VIEW = new ContextTable.TableType("VIEW", new String[]{
           "INOPERATIVE VIEW",
           "MATERIALIZED VIEW",
           "MATERIALIZED QUERY TABLE",
           "TYPED VIEW",
           "CALC VIEW",
           "JOIN VIEW"});
//   public static final ContextTable.TableType MATERIALIZED_VIEW = new ContextTable.TableType("MATERIALIZED VIEW",
//           new String[]{ "MATERIALIZED VIEW"}
//   );
   public static final ContextTable.TableType SYNONYM = new ContextTable.TableType("SYNONYM", new String[]{"ALIAS", "NICKNAME"});
   public static final ContextTable.TableType UNKNOWN = new ContextTable.TableType("UNKNOWN");
   public static final ContextTable.TableType[] TABLE_TYPES;
   private String catalog;
   private final String schema;
   private final String tableName;
   private final String tableTypeName;

   private final String tableComment;
   private ContextTable reference;

   private int rowCount;

   private long tableCreatedTimestamp;

   public ContextTable getReference() {
      return this.reference;
   }

   public void setReference(ContextTable contextTable) {
      this.reference = contextTable;
   }

   public ContextTable(String catalog, String schema, String tableName, String tableTypeName, String tableComment) throws TypeNotSupportedException {
      super(TABLE_TYPES, tableTypeName);
      this.reference = null;
      this.catalog = catalog != null ? catalog.trim() : null;
      this.schema = schema != null ? schema.trim() : null;
      this.tableName = tableName.trim();
      this.tableTypeName = tableTypeName.trim().toUpperCase();
      this.tableComment = tableComment;
   }

   public ContextTable(String catalog, String schema, String tableName, ContextTable.TableType tableType, String tableComment) throws TypeNotSupportedException {
      this(catalog, schema, tableName, tableType.getTypeName(), tableComment);
   }

   public String getIdentityName() {
      StringBuilder identityName = new StringBuilder();
      if (this.catalog != null && this.catalog.length() > 0) {
         identityName.append(this.catalog);
      }

      if (this.schema != null && this.schema.length() > 0) {
         if (identityName.length() > 0) {
            identityName.append(".");
         }

         identityName.append(this.schema);
      }

      if (identityName.length() > 0) {
         identityName.append(".");
      }

      identityName.append(this.tableName);
      return identityName.toString();
   }

   public String getIdentityPrefix() {
      StringBuilder identityName = new StringBuilder();
      if (this.catalog != null && this.catalog.length() > 0) {
         identityName.append(this.catalog);
      }

      if (this.schema != null && this.schema.length() > 0) {
         if (identityName.length() > 0) {
            identityName.append(".");
         }

         identityName.append(this.schema);
      }

      if (identityName.length() > 0) {
         identityName.append(".");
      }
      return identityName.toString();
   }

   public String getTableName() {
      return this.tableName;
   }

   public boolean isOfType(ContextTable.TableType tableType) {
      return super.isOfType(tableType);
   }

   public String getTableTypeName() {
      return this.tableTypeName;
   }

   public ContextTable.TableType getTableType() {
      return (ContextTable.TableType)this.getType();
   }

   public String getCatalog() {
      return this.catalog;
   }

   public void setCatalog(String catalog) {
      this.catalog = catalog;
   }


   public String getTableComment() {
      return tableComment;
   }

   public String getSchema() {
      return this.schema;
   }

   public int getRowCount() {
      return rowCount;
   }

   public void setRowCount(int rowCount) {
      this.rowCount = rowCount;
   }

   public long getTableCreatedTimestamp() {
      return tableCreatedTimestamp;
   }

   public void setTableCreatedTimestamp(long tableCreatedTimestamp) {
      this.tableCreatedTimestamp = tableCreatedTimestamp;
   }

   public boolean equals(ContextTable table) {
      return table != null && Objects.equals(this.tableName, table.tableName) && Objects.equals(this.tableTypeName, table.tableTypeName) && Objects.equals(this.schema, table.schema) && Objects.equals(this.catalog, table.catalog);
   }

   static {
      TABLE_TYPES = new ContextTable.TableType[]{
              TABLE,
              SYSTEM_TABLE,
              VIEW,
//              MATERIALIZED_VIEW,
              SYNONYM,
              UNKNOWN
      };
   }

   public static class TableType extends AbstractInnerType {
      TableType(String typeName) {
         super(typeName);
      }

      public TableType(String typename, String[] equivalents) {
         super(typename, equivalents);
      }
   }

}
