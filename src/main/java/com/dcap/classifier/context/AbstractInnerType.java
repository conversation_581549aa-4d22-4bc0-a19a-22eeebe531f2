package com.dcap.classifier.context;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

public abstract class AbstractInnerType implements Comparable<AbstractInnerType> {
   /**
    * 类型的名称
    */
   private final transient String typeName;

   /**
    * 可能为 null
    * 当实现为 ColumnType 这里保存的是 typeName 对应的数字
    * 当实现为 PrivilegeType 这里保存的是 null/ typeName
    */
   private final transient List<String> equivalents;

   protected AbstractInnerType(String typeName, String[] equivalents) {
      this.equivalents = new ArrayList<>();
      this.typeName = typeName.toUpperCase();
      if (equivalents == null) {
         return;
      }
      for (String equivalent : equivalents) {
         this.equivalents.add(equivalent.trim().toUpperCase());
      }
   }

   protected AbstractInnerType(String typeName) {
      this(typeName, null);
   }

   public String toString() {
      return this.typeName;
   }

   /**
    *
    * @param dataType
    * @return
    */
   public boolean isEquivalent(String dataType) {
      if (dataType == null) {
         return false;
      } else {
         String type = dataType.trim().toUpperCase();
         return this.typeName.equals(type) || this.equivalents.contains(type);
      }
   }

   public String getTypeName() {
      return this.typeName;
   }
   public String getEquivalents() {
      return StringUtils.join(this.equivalents,",");
   }

//   public static AbstractInnerType findByName(AbstractInnerType[] innerTypes, String typeName) throws TypeNotSupportedException {
//      for (AbstractInnerType abstractInnerType : innerTypes) {
//         if (abstractInnerType.isEquivalent(typeName)) {
//            return abstractInnerType;
//         }
//      }
//      throw new TypeNotSupportedException("'" + typeName + "' is not a member of: " + Arrays.toString(innerTypes));
//   }
//
//   public List<String> getEquivalents() {
//      return this.equivalents;
//   }

   public int compareTo(AbstractInnerType innerType) {
      return innerType.getTypeName().compareTo(this.getTypeName());
   }
}
