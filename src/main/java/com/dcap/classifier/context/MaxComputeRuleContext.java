package com.dcap.classifier.context;

import com.dcap.classifier.InitializationException;
import com.dcap.classifier.access.SQLDataPager;
import com.dcap.classifier.access.TimeoutException;
import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.TaskGlobalDataHolder;
import com.dcap.utils.Messages;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Iterator;
import java.util.List;


public class MaxComputeRuleContext extends RuleContext {

    public MaxComputeRuleContext(ClassifierDataSource dataSource, TaskGlobalDataHolder globalDataHolder) throws ConnectionException {
        super(dataSource, globalDataHolder);
    }

    public String getCurrentTableName() {
        return this.getCurrentTable().getIdentityName();
    }

    public String getColumnNameOnly(List<ContextColumn> var1) {
        StringBuilder var2 = new StringBuilder();
        String var3 = ", ";

        ContextColumn var5;
        for (Iterator<ContextColumn> var4 = var1.iterator(); var4.hasNext(); var2.append(var5.getColumnName())) {
            var5 = var4.next();
            if (var2.length() != 0) {
                var2.append(var3);
            }
        }

        return var2.toString();
    }

    public String getQualifiedName() {
        return this.getQualifiedName(this.getCurrentTable());
    }

    public String getQualifiedName(ContextTable contextTable) {
        String quoteIdentifiers = this.quoteIdentifiers(contextTable.getTableName());
        String schema = contextTable.getSchema();
        if (schema == null) {
            schema = "";
        }

        String catalog = contextTable.getCatalog();
        if (catalog == null) {
            catalog = "";
        }

        StringBuilder qualifiedName = new StringBuilder(quoteIdentifiers.length() + schema.length() + catalog.length() + 6);

        qualifiedName.append(this.quoteIdentifiers(catalog)).append(this.getCatalogSeparator());


        qualifiedName.append(this.quoteIdentifiers(schema)).append('.');

        qualifiedName.append(quoteIdentifiers);

        return qualifiedName.toString();
    }

    public String getQualifiedName(ContextColumn contextColumn) {
        StringBuilder qualifiedName = new StringBuilder();
        ContextTable contextTable = contextColumn.getTable();
        if (contextTable != null) {
            String tableName = contextTable.getTableName();
            if (tableName != null && tableName.length() > 0) {
                qualifiedName.append(this.getQualifiedName(contextTable)).append('.');
            }
        }

        qualifiedName.append(this.quoteIdentifiers(contextColumn.getColumnName()));
        return qualifiedName.toString();
    }

    public String getQualifiedName(List<ContextColumn> contextColumnList) {
        StringBuilder qualifiedName = new StringBuilder();
        String var3 = ", ";
        for (ContextColumn column : contextColumnList) {
            if (qualifiedName.length() > 0) {
                qualifiedName.append(var3);
            }
            qualifiedName.append(this.quoteIdentifiers(column.getColumnName()));
        }
        return qualifiedName.toString();
    }

    protected String getCountSQLQuery(String qualifiedTableName) {
//        select count(1) from (select 1 from my_table limit 51) a
        if (this.getTaskGlobalDataHolder().getTaskParam().isTableRowCountEnabled()) {
            StringBuilder sql = new StringBuilder("select count(1) from (select 1 from ");
            sql.append(qualifiedTableName).append(" limit ");
            long rowCountValue = this.getTaskGlobalDataHolder().getTaskParam().getTableRowCountLimit();
            sql.append(rowCountValue).append(") a");
            return sql.toString();
        } else {
            StringBuilder sql = new StringBuilder("select count(1) from (select 1 from ");
            sql.append(qualifiedTableName).append(" limit ");
            int rowCountValue = this.getTaskGlobalDataHolder().getTaskParam().getSampleCount() + 1;
            sql.append(rowCountValue).append(") a");
            return sql.toString();
//            return SELECT_COUNT + qualifiedTableName;
        }
    }


    protected long countRows(String qualifiedTableName) throws InitializationException {
        long rowCount = 0L;
        String countSql = getCountSQLQuery(qualifiedTableName);
        int sqlTimeoutSeconds = this.globalDataHolder.getTaskParam().getSampleSqlTimeout();
        LOG.debug("Executing sql - " + countSql);
        try (Statement statement = getStatement();
             ResultSet resultSet = SQLDataPager.executeTimed(true, statement, countSql, sqlTimeoutSeconds)) {
            LOG.debug("Done Executing sql - " + countSql);
            if (resultSet.next()) {
                rowCount = resultSet.getLong(1);
            }
            return rowCount;
        } catch (SQLException exception) {
            String message = Messages.getString("Could not get a row count for: '${tablename}' on '${datasourceUrl}'", "tablename", String.valueOf(qualifiedTableName), "datasourceUrl", getDatasource().getInstanceName());
            LOG.warn(message + " running query is:" + countSql);
            throw new InitializationException(message, countSql, exception);
        } catch (TimeoutException e) {
            e.printStackTrace();
        }
        return rowCount;
    }

    public long getRowCount() {
        if (this.getCurrentTable().getRowCount() == 0L) {
            return this.countRows();
        }
        return this.getCurrentTable().getRowCount();
    }

    /**
     * 将传入的 name 使用引用字符包起来，例如 `tableName`
     *
     * @param name 可能的值 catalog/schema/tableName/columnName
     * @return
     */
    public String quoteIdentifiers(String name) {
        if (name != null && name.length() > 0) {
            String quote = this.getQuote();
            if (quote != null && quote.length() > 0) {
                return quote + name + quote;
            }
        }
        return name;
    }
}
