package com.dcap.classifier.context;

import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.TaskGlobalDataHolder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GenericRuleContext extends RuleContext {

    public GenericRuleContext(ClassifierDataSource dataSource, TaskGlobalDataHolder globalDataHolder) throws ConnectionException {
        super(dataSource, globalDataHolder);
    }
}
