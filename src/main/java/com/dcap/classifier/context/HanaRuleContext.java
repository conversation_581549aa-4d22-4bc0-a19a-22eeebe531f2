package com.dcap.classifier.context;

import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.TaskGlobalDataHolder;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import org.apache.commons.lang3.StringUtils;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


public class HanaRuleContext extends RuleContext {

    public HanaRuleContext(ClassifierDataSource dataSource, TaskGlobalDataHolder globalDataHolder) throws ConnectionException {
        super(dataSource, globalDataHolder);
    }

    protected List<String> findCatalogs() {
        LOG.debug("find Catalogs");
        this.resetBrowse();
        String dbName = this.getDatasource().getDbName();
        if (dbName != null && dbName.length() > 0) {
            this.catalogs.add(dbName);
            return this.catalogs;
        }

        Set<String> emptyCatalogs = this.getEmptyCatalogs().stream().map(String::toUpperCase).collect(Collectors.toCollection(HashSet::new));
        try {
            Connection connection = this.getConnection();
            if (StringUtils.isBlank(this.instanceName)){
                this.instanceName = this.getDatasource().getInstanceName();
            }
            String inventoryDbName = this.instanceName;
            DatabaseMetaData metaData = connection.getMetaData();

            ResultSet resultSet;
            String columnLabel;
            resultSet = metaData.getSchemas();
            columnLabel = Constants.DBMD_TABLE_SCHEM;
            // database
            Set<String> selectedSchemaOrDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedDatabase();
            Set<String> excludedSchemaOrDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedDatabase();
            try {
                while (resultSet.next()) {
                    String catalogName = resultSet.getString(columnLabel);
                    if (!emptyCatalogs.isEmpty() && emptyCatalogs.contains(catalogName.toUpperCase())) {
                        continue;
                    }
                    String catalogIdentityName = inventoryDbName != null ? (inventoryDbName + "." + catalogName).toLowerCase() : catalogName.toLowerCase();
                    // 如果是排除，就跳过。必须包含
                    if (excludedSchemaOrDatabase.contains(catalogIdentityName)){
                        continue;
                    }
                    if (selectedSchemaOrDatabase.isEmpty() || selectedSchemaOrDatabase.contains(catalogIdentityName)) {
                        this.catalogs.add(catalogName);
                    }
                }
                return this.catalogs;
            } finally {
                if (resultSet != null) {
                    resultSet.close();
                }
            }
        } catch (SQLException sqlException) {
            sqlException.printStackTrace();
            LOG.warn("[hana] Could not get catalogs for '" + this + "'" + System.lineSeparator() + sqlException);
            this.globalDataHolder.getProbeClientTaskContext()
                            .reportErrorOccurredExecuting(StatusRecord.Position.FindCatalogs,null,null,sqlException);
        }
        return this.catalogs;
    }

    protected String getCountSQLQuery(String qualifiedTableName) {
//        select count(1) from (select 1 from my_table limit 51) a
        if (this.getTaskGlobalDataHolder().getTaskParam().isTableRowCountEnabled()){
            StringBuilder sql = new StringBuilder("select count(1) from (select 1 from ");
            sql.append(qualifiedTableName).append(" OFFSET 0 ROWS FETCH FIRST ");
            long rowCountValue = this.getTaskGlobalDataHolder().getTaskParam().getTableRowCountLimit();
            sql.append(rowCountValue).append(" ROWS ONLY ) a");
            return sql.toString();
        } else{
            StringBuilder sql = new StringBuilder("select count(1) from (select 1 from ");
            sql.append(qualifiedTableName).append(" OFFSET 0 ROWS FETCH FIRST ");
            int rowCountValue = this.getTaskGlobalDataHolder().getTaskParam().getSampleCount() + 1;
            sql.append(rowCountValue).append(" ROWS ONLY ) a");
            return sql.toString();
//            return SELECT_COUNT + qualifiedTableName;
        }
    }
}
