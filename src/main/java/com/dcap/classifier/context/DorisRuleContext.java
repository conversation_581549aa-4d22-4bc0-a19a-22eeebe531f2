package com.dcap.classifier.context;

import com.dcap.classifier.Clock;
import com.dcap.classifier.RuleEvaluator;
import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.TaskGlobalDataHolder;
import com.dcap.utils.Messages;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import org.apache.commons.lang.StringUtils;

import java.sql.*;
import java.util.*;


public class DorisRuleContext extends RuleContext {

    private static final String QUERY_USER_SQL = "SELECT USER AS USERNAME, HOST, PLUGIN, PASSWORD_LAST_CHANGED, " +
            "PASSWORD_EXPIRED,ACCOUNT_LOCKED " +
            " FROM mysql.user ORDER BY USER ";

    private static final String QUERY_TABLE_PRIVILEGES_SQL="SELECT HOST, DB, USER USERNAME, TABLE_NAME, GRANTOR, TIM<PERSON>TA<PERSON>, " +
            "TABLE_PRIV TABLE_PRIVILEGES, COLUMN_PRIV COLUMN_PRIVILEGES FROM mysql.tables_priv WHERE DB NOT IN ('mysql')";


    private static final String QUERY_COLUMN_PRIVILEGES_SQL="SELECT HOST, DB, USER USERNAME, TABLE_NAME, COLUMN_NAME, TIMESTAMP, " +
            "COLUMN_PRIV COLUMN_PRIVILEGES " +
            " FROM mysql.columns_priv";

    private String currentSelectedCatalog;


    public DorisRuleContext(ClassifierDataSource dataSource, TaskGlobalDataHolder globalDataHolder) throws ConnectionException {
        super(dataSource, globalDataHolder);
    }

    @Override
    protected List<String> findCatalogs() {
        LOG.info("find doris catalogs");
        this.resetBrowse();
        Connection connection = this.getConnection();
        try (Statement statement = connection.createStatement();
             ResultSet resultSet = statement.executeQuery("select distinct CatalogName from catalogs()")) {
            Set<String> selectedDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedDatabase();
            Set<String> excludedDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedDatabase();
            String columnLabel = "CatalogName";
            try {
                while (resultSet.next()) {
                    String catalogName = resultSet.getString(columnLabel);
                    String catalogIdentityName = catalogName.toLowerCase();
                    // 如果是排除，就跳过。必须包含
                    if (excludedDatabase.contains(catalogIdentityName)){
                        continue;
                    }
                    if (selectedDatabase.isEmpty() || selectedDatabase.contains(catalogIdentityName)) {
                        this.catalogs.add(catalogName);
                    }
                }
                return this.catalogs;
            } finally {
                if (resultSet != null) {
                    resultSet.close();
                }
            }
        } catch (SQLException sqlException) {
            sqlException.printStackTrace();
            LOG.warn("[doris] Could not get catalogs for '{}'{}{}", this, System.lineSeparator(), sqlException);
            this.globalDataHolder.getProbeClientTaskContext()
                    .reportErrorOccurredExecuting(StatusRecord.Position.FindCatalogs,null,null,sqlException);
        }
        return this.catalogs;
    }

    protected boolean isInternalSchema(ClassifierDataSource dataSource, String tableSchema) {
        return dataSource.getEmptyCatalogs().contains(tableSchema);
    }

    private void matchCatalog(Connection connection, String catalog){
        if (Objects.equals(this.currentSelectedCatalog, catalog)){
            return;
        }
        try (Statement statement = connection.createStatement()){
            statement.setQueryTimeout(30);
            statement.executeUpdate("switch "+catalog);
            this.currentSelectedCatalog = catalog;
        } catch (SQLException e){
            LOG.error(e.getMessage(), e);
        }
    }
    protected List<ContextTable> findCatalogTables(String catalog, String[] tableTypes) {
        Clock clock = new Clock();
        if (this.tables != null) {
            this.tables.clear();
        } else {
            this.tables = new ArrayList<>();
        }
        this.currentSelectedCatalog = null;

        Set<String> selectedDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedDatabase();
        Set<String> excludedDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedDatabase();
        Set<String> selectedSchema = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedSchema();
        Set<String> excludedSchema = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedSchema();
        Set<String> selectedTable = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedTable();
        Set<String> excludedTable = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedTable();
        Set<String> selectedView = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedView();
        Set<String> excludedView = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedView();

        Connection connection = this.getConnection();
        List<String> schemaNames = new ArrayList<>();
        this.currentTable = null;
        ResultSet resultSet = null;
        this.matchCatalog(connection, catalog);

        try (Statement statement = connection.createStatement();){
            statement.setQueryTimeout(30);
            resultSet = statement.executeQuery("SHOW DATABASES FROM " + catalog);
            while (resultSet.next()) {
                String schemaName = resultSet.getString(1);
                if (StringUtils.isBlank(schemaName)) {
                    continue;
                }
                if (isInternalSchema(this.getDatasource(), schemaName)) {
                    LOG.debug("Schema: {} has been skipped ", schemaName);
                    continue;
                }
                if (excludedSchema.contains(schemaName.toLowerCase())){
                    continue;
                }
                if (selectedSchema.isEmpty() || selectedSchema.contains(schemaName.toLowerCase())) {
                    schemaNames.add(schemaName);
                }
            }
        } catch (Exception e){
            LOG.error(e.getMessage(), e);
            return this.tables;
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (Exception ignored) {
                }
            }
        }

        if (schemaNames.isEmpty()) {
            return this.tables;
        }
        // todo: 这里是调整一下传入的 tableTypes。
        tableTypes = this.getDatasource().getDriverInterface().adjustTableTypes(tableTypes, true, connection);
        for (String schemaName : schemaNames) {
            try {
                DatabaseMetaData metaData = connection.getMetaData();
                // todo: this maybe very slow for wrong input
                LOG.debug("Get tables with catalog = {} and schema = {}", catalog, schemaName);
                resultSet = metaData.getTables(schemaName, null, null, tableTypes);
                LOG.debug("Get tables - end");
                while (resultSet.next()) {
                    String tableType = resultSet.getString("TABLE_TYPE");
                    String tableName = resultSet.getString("TABLE_NAME");
                    String tableCatalog = catalog;// resultSet.getString("TABLE_CAT");
                    String tableSchema = schemaName; //resultSet.getString(Constants.DBMD_TABLE_SCHEM);
                    String tableComment = resultSet.getString("REMARKS");

                    if (StringUtils.isBlank(tableCatalog) && StringUtils.isNotBlank(catalog)) {
                        tableCatalog = catalog;
                    }

                    // 当前表是属于内部 schema 或 内部 catalog 就跳过
                    if (isInternalSchema(this.getDatasource(), tableSchema)
                            || isInternalCatalog(this.getDatasource(), tableCatalog)) {
                        LOG.debug("Catalog: {}, Schema: {} has been skipped ", catalog, tableSchema);
                        continue;
                    }
                    ContextTable table = this.createContextTable(tableCatalog, tableSchema, tableName, tableType, tableComment);
                    if (table == null) {
                        continue;
                    }
                    String tableIdentityName = table.getIdentityName().toLowerCase();
                    String databaseIdentityName = tableIdentityName.substring(0, tableIdentityName.indexOf(".")).toLowerCase();
                    String schemaIdentityName = tableIdentityName.substring(0, tableIdentityName.lastIndexOf(".")).toLowerCase();
                    String tableTypeName = table.getType().getTypeName();
                    if (excludedDatabase.contains(databaseIdentityName)){
                        continue;
                    }
                    if (StringUtils.isNotBlank(tableSchema) && excludedSchema.contains(schemaIdentityName)){
                        continue;
                    }
                    if ("TABLE".equals(tableTypeName) && excludedTable.contains(tableIdentityName)){
                        continue;
                    }
                    if ("VIEW".equals(tableTypeName) && excludedView.contains(tableIdentityName)){
                        continue;
                    }
                    if ((selectedDatabase.isEmpty() || selectedDatabase.contains(databaseIdentityName))
                            && (selectedSchema.isEmpty() || (StringUtils.isNotBlank(tableSchema) && selectedSchema.contains(schemaIdentityName)))
                            && (selectedTable.isEmpty() || selectedTable.contains(tableIdentityName))
                            && "TABLE".equals(tableTypeName)) {
                        this.addTable(table);
                        clock.incrementCount();
                    } else if ((selectedDatabase.isEmpty() || selectedDatabase.contains(databaseIdentityName))
                            && (selectedSchema.isEmpty() || (StringUtils.isNotBlank(tableSchema) && selectedSchema.contains(schemaIdentityName)))
                            && (selectedView.isEmpty() || selectedView.contains(tableIdentityName))
                            && "VIEW".equals(tableTypeName)) {
                        this.addTable(table);
                        clock.incrementCount();
                    }
                }
                if (LOG.isInfoEnabled()) {
                    String tableType = tableTypes == null ? "Catalog Tables" : RuleEvaluator.arrayToString(tableTypes);
                    LOG.debug("{}: {}", catalog, clock.getElapsedMessageForCounter(tableType));
                }
            } catch (SQLException sqlException) {
                String message = Messages.getString("Could not access table(s) for: '${datasourceName}' on: '${datasourceUrl}'", "datasourceName", String.valueOf(this.getDatasource().getDescriptor()), "datasourceUrl", this.getConnectionDescriptor());
                LOG.error(message, sqlException);
                this.globalDataHolder.getProbeClientTaskContext()
                        .reportErrorOccurredExecuting(StatusRecord.Position.FindCatalogTables,
                                "catalog [" + catalog + "]",null,sqlException);
            } catch (NullPointerException nullPointerException) {
                StringBuilder logMsg = new StringBuilder();
                String message = Messages
                        .getString("Could not access table(s) for: '${datasourceName}' on: '${datasourceUrl}'",
                                "datasourceName", String.valueOf(this.getDatasource().getDescriptor()),
                                "datasourceUrl", this.getConnectionDescriptor());
                logMsg.append(message);
                throw new DBDriverException(clock.getElapsedMessage(logMsg.toString()), nullPointerException);
            } finally {
                if (resultSet != null) {
                    try {
                        resultSet.close();
                    } catch (Exception ignored) {
                    }
                }
            }
        }
        return this.listCatalogTables();
    }

    public boolean isSchemaUsed() {
        return true;
    }

    @Override
    protected List<ContextColumn> findTableColumns() {
        Clock clock = new Clock();

        ContextTable currentTable = this.getCurrentTable();
        if (currentTable.getReference() != null) {
            currentTable = currentTable.getReference();
        }

        List<ContextColumn> contextColumns = new ArrayList<>();
        try {
            String catalog = currentTable.getCatalog();
            String schemaPattern = currentTable.getSchema();
            String tableNamePattern = currentTable.getTableName();
            Connection connection = this.getConnection();
            if (connection == null) {
                return contextColumns;
            }
            this.matchCatalog(connection, catalog);

            DatabaseMetaData metaData = connection.getMetaData();
            tableNamePattern = tableNamePattern.replaceAll("_","\\\\_");
            try (ColumnIterator columnIterator = this.getColumnProvider(schemaPattern, null, tableNamePattern, metaData)) {
                Map<String, Boolean> primaryColumns = getPrimaryColumns(metaData, catalog, schemaPattern, currentTable.getTableName());
                while (columnIterator.hasNext()) {
                    ColumnProvider columnProvider = columnIterator.next();
                    String columnName = columnProvider.getColumnName();
                    ContextColumn contextColumn = this.createContextColumn(currentTable, columnName,
                            columnProvider.getColumnDataType(), columnProvider.getColumnTypeName(),
                            columnProvider.getColumnSize(), columnProvider.getColumnScale(), columnProvider.getColumnRemarks(), columnProvider.getOrdinalPosition(),
                            primaryColumns.get(columnName) == Boolean.TRUE);
                    if (contextColumn == null) {
                        continue;
                    }
                    contextColumns.add(contextColumn);
                    clock.incrementCount();
                }
            }
            return contextColumns;
        } catch (Exception exception) {
            String message = Messages.getString("Could not access column(s) for table: '${tablename}' on '${datasourceUrl}'", "datasourceUrl", this.getConnectionDescriptor(), "tablename", String.valueOf(currentTable));
            LOG.error(message, exception);
            this.globalDataHolder.getProbeClientTaskContext()
                    .reportErrorOccurredExecuting(
                            StatusRecord.Position.FindTableColumns, clock.getElapsedMessage(message), null, exception
                    );
            return contextColumns;
        }
    }
}
