package com.dcap.classifier.context;


import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class ContextColumn extends AbstractContext {

   protected static final Map<String, String> typeMap = new HashMap<>();
   public static final ContextColumn.ColumnType TEXT;
   public static final ContextColumn.ColumnType NUMBER;
   public static final ContextColumn.ColumnType DATE;
   public static final ContextColumn.ColumnType BINARY;
   public static final ContextColumn.ColumnType BIT;
   public static final ContextColumn.ColumnType STRUCTURE;
   public static final ContextColumn.ColumnType OTHER;
   public static final ContextColumn.ColumnType[] COLUMN_TYPES;
   private final ContextTable table;
   private final String columnName;
   private final int datatype;
   private final String typename;
   private final int size;
   private final int scale;
   private final boolean isPrimaryKey;
   private final String remarks;

   private int pkOrdinal;

   private final int position;

   private final String identityName;



   ContextColumn(ContextTable table, String columnName, int datatype, String typeName, int size, int scale,String remarks, int position, boolean isPrimaryKey) throws TypeNotSupportedException {
      this(table, columnName, datatype, typeName, size, scale, 0, remarks, position, isPrimaryKey);
   }

   ContextColumn(ContextTable table, String columnName, int datatype, String typeName, int size, int scale, int pkOrdinal, String remarks, int position, boolean isPrimaryKey) throws TypeNotSupportedException {
      super(COLUMN_TYPES, String.valueOf(datatype));
      this.table = table;
      this.columnName = columnName.trim();
      this.datatype = datatype;
      this.typename = typeName.trim();
      this.size = size;
      this.scale = scale;
      this.pkOrdinal = pkOrdinal;
      this.remarks = remarks;
      this.position = position;
      this.identityName = table.getIdentityName() + "." + columnName;
      this.isPrimaryKey = isPrimaryKey;
   }

   public static int findJdbcType(String typename) throws TypeNotSupportedException {
      String dataType = typeMap.get(typename.toUpperCase());
      try {
         return Integer.parseInt(dataType);
      } catch (NumberFormatException var5) {
         throw new TypeNotSupportedException("Could not match a type of: '" + typename + "'");
      }
   }

   public boolean isOfType(ContextColumn.ColumnType columnType) {
      return super.isOfType(columnType);
   }

   public static String findDatatypeByTypename(String typename) {
      return typeMap.get(typename.toUpperCase());
   }

   public ContextTable getTable() {
      return this.table;
   }

   public ContextColumn.ColumnType getColumnType() {
      return (ContextColumn.ColumnType)this.getType();
   }

   public String getColumnName() {
      return this.columnName;
   }

   public String getTypename() {
      return this.typename;
   }

   public String getRemarks() {
      return remarks;
   }

   public int getPosition() {
      return position;
   }

   public int getDatatype() {
      return this.datatype;
   }

   public int getSize() {
      return this.size;
   }

   public int getScale() {
      return scale;
   }

   public boolean isPk() {
      return this.pkOrdinal > 0;
   }

   public void setPkOrdinal(int pkOrdinal) {
      this.pkOrdinal = pkOrdinal;
   }

   public int getPkOrdinal() {
      return this.pkOrdinal;
   }

   public String getIdentityName() {
      return identityName;
   }

   public boolean isPrimaryKey() {
      return isPrimaryKey;
   }

   public String toString() {
      return this.getTypename() + "(" + this.getSize() + ") " + this.getColumnName();
   }

   static {
      typeMap.put("UROWID", String.valueOf(-8));
      typeMap.put("ROWID", String.valueOf(-8));
      typeMap.put("BIT", String.valueOf(-7));
      typeMap.put("TINYINT", String.valueOf(-6));
      typeMap.put("BIGINT", String.valueOf(-5));
      typeMap.put("LONGVARBINARY", String.valueOf(-4));
      typeMap.put("LONG RAW", String.valueOf(-4));
      typeMap.put("VARBINARY", String.valueOf(-3));
      typeMap.put("RAW", String.valueOf(-3));
      typeMap.put("BINARY", String.valueOf(-2));
      typeMap.put("LONGVARCHAR", String.valueOf(-1));
      typeMap.put("LONG", String.valueOf(-1));
      typeMap.put("NTEXT", String.valueOf(-16));
      typeMap.put("SQL_VARIANT", String.valueOf(-150));

      typeMap.put("CHAR", String.valueOf(1));

      typeMap.put("NUMERIC", String.valueOf(2));
      typeMap.put("NUMBER", String.valueOf(2));
      typeMap.put("DECIMAL", String.valueOf(3));
      typeMap.put("INTEGER", String.valueOf(4));
      typeMap.put("SMALLINT", String.valueOf(5));
      typeMap.put("FLOAT", String.valueOf(6));
      typeMap.put("REAL", String.valueOf(7));
      typeMap.put("DOUBLE", String.valueOf(8));


      typeMap.put("VARCHAR", String.valueOf(12));
      typeMap.put("VARCHAR2", String.valueOf(12));
      typeMap.put("NCHAR", String.valueOf(-15));
      typeMap.put("NVARCHAR2", String.valueOf(12));
      typeMap.put("NVARCHAR", String.valueOf(-9));

      typeMap.put("BOOLEAN", String.valueOf(16));


      typeMap.put("DATETIMEOFFSET", String.valueOf(-155));
      typeMap.put("DATE", String.valueOf(91));
      typeMap.put("TIME", String.valueOf(92));
      typeMap.put("TIMESTAMP", String.valueOf(93));

      typeMap.put("TIME_WITH_TIMEZONE", String.valueOf(2013));
      typeMap.put("TIMESTAMP WITH TIME ZONE", String.valueOf(2014));
      typeMap.put("TIMESTAMP_WITH_TIMEZONE", String.valueOf(2014));
      typeMap.put("TIMESTAMP WITH LOCAL TIME ZONE", String.valueOf(2015));
      typeMap.put("INTERVAL YEAR TO MONTH", String.valueOf(2016));
      typeMap.put("INTERVAL DAY TO SECOND", String.valueOf(2017));



      typeMap.put("TIME WITH TIME ZONE", String.valueOf(94));
      typeMap.put("DATETIME WITH TIME ZONE", String.valueOf(95));
      typeMap.put("INTERVAL YEAR", String.valueOf(101));
      typeMap.put("INTERVAL MONTH", String.valueOf(102));
      typeMap.put("INTERVAL DAY", String.valueOf(103));
      typeMap.put("INTERVAL HOUR", String.valueOf(104));
      typeMap.put("INTERVAL MINUTE", String.valueOf(105));
      typeMap.put("INTERVAL SECOND", String.valueOf(106));
      typeMap.put("INTERVAL YEAR TO MONTH", String.valueOf(107));
      typeMap.put("INTERVAL DAY TO HOUR", String.valueOf(108));
      typeMap.put("INTERVAL DAY TO MINUTE", String.valueOf(109));
      typeMap.put("INTERVAL DAY TO SECOND", String.valueOf(110));






      typeMap.put("INTERVAL HOUR TO SECOND", String.valueOf(112));
      typeMap.put("INTERVAL HOUR TO MINUTE", String.valueOf(111));
      typeMap.put("INTERVAL MINUTE TO SECOND", String.valueOf(113));


      typeMap.put("STRUCT", String.valueOf(2002));
      typeMap.put("ARRAY", String.valueOf(2003));
      typeMap.put("BLOB", String.valueOf(2004));
      typeMap.put("BFILE", String.valueOf(2004));
      typeMap.put("CLOB", String.valueOf(2005));
      typeMap.put("REF", String.valueOf(2006));
      typeMap.put("NCLOB", String.valueOf(2011));

      typeMap.put("SQLXML", String.valueOf(2009));
      typeMap.put("XMLTYPE", String.valueOf(2009));

      typeMap.put("ANYTYPE", String.valueOf(1111));
      typeMap.put("ANYDATA", String.valueOf(1111));
      typeMap.put("ANYDATASET", String.valueOf(1111));
      typeMap.put("GEOMETRY", String.valueOf(1111));
      typeMap.put("SDO_GEOMETRY", String.valueOf(1111));
      typeMap.put("SDO_TOPO_GEOMETRY", String.valueOf(1111));
      typeMap.put("SDO_GEORASTER", String.valueOf(1111));
      typeMap.put("BINARY_FLOAT", String.valueOf(1111));
      typeMap.put("BINARY_DOUBLE", String.valueOf(1111));

      TEXT = new ContextColumn.ColumnType("TEXT", new String[]{
              findDatatypeByTypename("CHAR"), findDatatypeByTypename("LONGVARCHAR"),
              findDatatypeByTypename("VARCHAR"), findDatatypeByTypename("VARCHAR2"),
              findDatatypeByTypename("NVARCHAR2"), findDatatypeByTypename("NCHAR"),
              findDatatypeByTypename("NVARCHAR"), findDatatypeByTypename("NTEXT"),
              findDatatypeByTypename("SQL_VARIANT")});
      NUMBER = new ContextColumn.ColumnType("NUMBER", new String[]{findDatatypeByTypename("BIGINT"), findDatatypeByTypename("DECIMAL"), findDatatypeByTypename("DOUBLE"), findDatatypeByTypename("FLOAT"), findDatatypeByTypename("INTEGER"), findDatatypeByTypename("NUMERIC"), findDatatypeByTypename("REAL"), findDatatypeByTypename("SMALLINT"), findDatatypeByTypename("TINYINT"),
              "UNSIGNED INT", "PLS_INTEGER", "BINARY_INTEGER"});

      DATE = new ContextColumn.ColumnType("DATE", new String[]{findDatatypeByTypename("DATE"), findDatatypeByTypename("TIME"), findDatatypeByTypename("TIMESTAMP"),
              findDatatypeByTypename("TIME_WITH_TIMEZONE"),findDatatypeByTypename("TIMESTAMP_WITH_TIMEZONE"),findDatatypeByTypename("TIMESTAMP WITH TIME ZONE"),
              findDatatypeByTypename("TIMESTAMP WITH LOCAL TIME ZONE"), findDatatypeByTypename("INTERVAL YEAR TO MONTH"), findDatatypeByTypename("INTERVAL DAY TO SECOND")
              , findDatatypeByTypename("INTERVAL MINUTE"), findDatatypeByTypename("INTERVAL MINUTE TO SECOND"), findDatatypeByTypename("INTERVAL SECOND")
              , findDatatypeByTypename("INTERVAL HOUR"), findDatatypeByTypename("INTERVAL HOUR TO MINUTE"), findDatatypeByTypename("INTERVAL DAY")
              , findDatatypeByTypename("INTERVAL DAY TO HOUR"), findDatatypeByTypename("INTERVAL DAY TO MINUTE"), findDatatypeByTypename("INTERVAL YEAR")
              , findDatatypeByTypename("INTERVAL MONTH"), findDatatypeByTypename("TIME WITH TIME ZONE"), findDatatypeByTypename("DATETIME WITH TIME ZONE")
              , findDatatypeByTypename("INTERVAL HOUR TO SECOND"), findDatatypeByTypename("DATETIMEOFFSET")
      });

      BINARY = new ContextColumn.ColumnType("BINARY", new String[]{findDatatypeByTypename("BINARY"), findDatatypeByTypename("BLOB"), findDatatypeByTypename("CLOB"), findDatatypeByTypename("LONGVARBINARY"), findDatatypeByTypename("VARBINARY"), findDatatypeByTypename("NCLOB"), findDatatypeByTypename("BFILE"),
              findDatatypeByTypename("LONG"), "RAW",findDatatypeByTypename("SQLXML"),  findDatatypeByTypename("XMLTYPE"),
              findDatatypeByTypename("ROWID"),findDatatypeByTypename("UROWID"),"TEXT"});
      BIT = new ContextColumn.ColumnType("BIT", new String[]{findDatatypeByTypename("BIT"), findDatatypeByTypename("BOOLEAN")});
      STRUCTURE = new ContextColumn.ColumnType("STRUCTURE", new String[]{findDatatypeByTypename("ARRAY"), findDatatypeByTypename("REF"), findDatatypeByTypename("STRUCT")});
      OTHER = new ContextColumn.ColumnType("OTHER", new String[]{findDatatypeByTypename("ANYTYPE"), findDatatypeByTypename("ANYDATA"),
              findDatatypeByTypename("ANYDATASET"),findDatatypeByTypename("GEOMETRY"),findDatatypeByTypename("SDO_GEOMETRY"),findDatatypeByTypename("SDO_TOPO_GEOMETRY"),
              findDatatypeByTypename("SDO_GEORASTER"),findDatatypeByTypename("BINARY_FLOAT"),findDatatypeByTypename("BINARY_DOUBLE")});
      COLUMN_TYPES = new ContextColumn.ColumnType[]{ TEXT, NUMBER, DATE, BIT, BINARY, STRUCTURE,OTHER };
   }

   public static class ColumnType extends AbstractInnerType {
      ColumnType(String typeName) {
         super(typeName);
      }

      ColumnType(String typeName, String[] equivalents) {
         super(typeName, equivalents);
      }
   }
}
