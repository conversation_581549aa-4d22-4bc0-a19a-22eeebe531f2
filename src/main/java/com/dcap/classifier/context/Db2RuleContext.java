package com.dcap.classifier.context;

import com.dcap.classifier.ClassifierException;
import com.dcap.classifier.Clock;
import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.TaskGlobalDataHolder;
import com.dcap.utils.Messages;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import org.apache.commons.lang3.StringUtils;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;


public class Db2RuleContext extends RuleContext {

    public Db2RuleContext(ClassifierDataSource dataSource, TaskGlobalDataHolder globalDataHolder) throws ConnectionException {
        super(dataSource, globalDataHolder);
    }

    protected List<String> findCatalogs() {
        LOG.debug("find Catalogs");
        this.resetBrowse();
        String dbName = this.getDatasource().getDbName();
        if (dbName != null && !dbName.isEmpty()) {
            this.catalogs.add(dbName);
            return this.catalogs;
        }

        Set<String> emptyCatalogs = this.getEmptyCatalogs().stream().map(String::toUpperCase).collect(Collectors.toCollection(HashSet::new));
        try {
            Connection connection = this.getConnection();
            if (StringUtils.isBlank(this.instanceName)){
                this.instanceName = this.getDatasource().getInstanceName();
            }
            String inventoryDbName = this.instanceName;
            DatabaseMetaData metaData = connection.getMetaData();

            ResultSet resultSet;
            String columnLabel;
            resultSet = metaData.getSchemas();
            columnLabel = Constants.DBMD_TABLE_SCHEM;
            // schema
            Set<String> selectedSchemaOrDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedSchema();
            Set<String> excludedSchemaOrDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedSchema();
            try {
                while (resultSet.next()) {
                    String catalogName = resultSet.getString(columnLabel);
                    if (!emptyCatalogs.isEmpty() && emptyCatalogs.contains(catalogName.toUpperCase())) {
                        continue;
                    }
                    String catalogIdentityName = inventoryDbName != null ? (inventoryDbName + "." + catalogName).toLowerCase() : catalogName.toLowerCase();
                    // 如果是排除，就跳过。必须包含
                    if (excludedSchemaOrDatabase.contains(catalogIdentityName)){
                        continue;
                    }
                    if (selectedSchemaOrDatabase.isEmpty() || selectedSchemaOrDatabase.contains(catalogIdentityName)) {
                        this.catalogs.add(catalogName);
                    }
                }
                return this.catalogs;
            } finally {
                if (resultSet != null) {
                    resultSet.close();
                }
            }
        } catch (SQLException sqlException) {
            sqlException.printStackTrace();
            LOG.warn("[db2] Could not get catalogs for '" + this + "'" + System.lineSeparator() + sqlException);
            this.globalDataHolder.getProbeClientTaskContext()
                    .reportErrorOccurredExecuting(StatusRecord.Position.FindCatalogs, null,null,sqlException);
        }
        return this.catalogs;
    }

    protected String getCountSQLQuery(String qualifiedTableName) {
        long rowCountValue = 0;
        if (this.getTaskGlobalDataHolder().getTaskParam().isTableRowCountEnabled()){
            rowCountValue = this.getTaskGlobalDataHolder().getTaskParam().getTableRowCountLimit();
        } else {
            rowCountValue = this.getTaskGlobalDataHolder().getTaskParam().getSampleCount() + 1;
        }
        return "SELECT COUNT(1) FROM (" + "SELECT 1 FROM ( " +
                "SELECT ROW_NUMBER() OVER (ORDER BY 1) AS RN " +
                "FROM " + qualifiedTableName +
                ") AS SubQuery " +
                "WHERE SubQuery.RN BETWEEN 1 AND " + rowCountValue +
                ") AS a";
    }

    @Override
    protected List<ContextColumn> findTableColumns() {
        Clock clock = new Clock();

        ContextTable currentTable = this.getCurrentTable();
        if (currentTable.getReference() != null) {
            currentTable = currentTable.getReference();
        }

        List<ContextColumn> contextColumns = new ArrayList<>();
        try {
            String catalog = currentTable.getCatalog();
            String schemaPattern = currentTable.getSchema();
            String tableNamePattern = currentTable.getTableName();
            Connection connection = this.getConnection();
            if (connection == null) {
                return contextColumns;
            }
            DatabaseMetaData metaData = connection.getMetaData();
            tableNamePattern = tableNamePattern.replaceAll("_","\\\\_");
            try (ColumnIterator columnIterator = this.getColumnProvider(catalog, schemaPattern, tableNamePattern, metaData)) {
                Map<String, Boolean> primaryColumns = getPrimaryColumns(metaData, catalog, schemaPattern, currentTable.getTableName());
                while (columnIterator.hasNext()) {
                    ColumnProvider columnProvider = columnIterator.next();
                    String columnName = columnProvider.getColumnName();
                    ContextColumn contextColumn = this.createContextColumn(currentTable, columnName,
                            columnProvider.getColumnDataType(), columnProvider.getColumnTypeName(),
                            columnProvider.getColumnSize(), columnProvider.getColumnScale(), columnProvider.getColumnRemarks(), columnProvider.getOrdinalPosition(),
                            primaryColumns.get(columnName) == Boolean.TRUE);
                    if (contextColumn == null) {
                        continue;
                    }
                    contextColumns.add(contextColumn);
                    clock.incrementCount();
                }
            }
            return contextColumns;
        } catch (SQLException | NullPointerException | ClassifierException exception) {
            String message = Messages.getString("Could not access column(s) for table: '${tablename}' on '${datasourceUrl}'", "datasourceUrl", this.getConnectionDescriptor(), "tablename", String.valueOf(currentTable));
            LOG.error(message, exception);
            this.globalDataHolder.getProbeClientTaskContext()
                    .reportErrorOccurredExecuting(StatusRecord.Position.FindTableColumns,
                            clock.getElapsedMessage(message), null, exception);
            return contextColumns;
        } catch (Exception exception) {
            String message = Messages.getString("Could not access column(s) for table: '${tablename}' on '${datasourceUrl}'", "datasourceUrl", this.getConnectionDescriptor(), "tablename", String.valueOf(currentTable));
            LOG.error(message, exception);
            this.globalDataHolder.getProbeClientTaskContext()
                    .reportErrorOccurredExecuting(
                            StatusRecord.Position.FindTableColumns, clock.getElapsedMessage(message), null, exception
                    );
            return contextColumns;
        }
    }
}
