package com.dcap.classifier.context;

import java.sql.ResultSet;
import java.sql.SQLException;

public class JDBCColumnProvider implements ColumnProvider {

   ResultSet rs;

   JDBCColumnProvider(ResultSet var1) {
      this.rs = var1;
   }

   public String getColumnName() throws SQLException {
      return this.rs.getString("COLUMN_NAME");
   }

   public int getColumnDataType() throws SQLException {
      return this.rs.getInt("DATA_TYPE");
   }

   public String getColumnRemarks() throws SQLException {
      return this.rs.getString("REMARKS");
   }

   public int getOrdinalPosition() throws SQLException {
      return this.rs.getInt("ORDINAL_POSITION");
   }



   public String getColumnTypeName() throws SQLException {
      return this.rs.getString("TYPE_NAME");
   }

   public int getColumnSize() throws SQLException {
      return this.rs.getInt("COLUMN_SIZE");
   }

   @Override
   public int getColumnScale() throws SQLException {
      return this.rs.getInt("DECIMAL_DIGITS");
   }
}
