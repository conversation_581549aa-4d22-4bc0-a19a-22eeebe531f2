package com.dcap.classifier.context;

import com.dcap.classifier.Clock;
import com.dcap.classifier.RuleEvaluator;
import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.TaskGlobalDataHolder;
import com.dcap.utils.Messages;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import org.apache.commons.lang.StringUtils;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


public class ClickhouseRuleContext extends RuleContext {

    public ClickhouseRuleContext(ClassifierDataSource dataSource, TaskGlobalDataHolder globalDataHolder) throws ConnectionException {
        super(dataSource, globalDataHolder);
    }

    protected List<String> findCatalogs() {
        LOG.info("find Catalogs");
        this.resetBrowse();
        String dbName = this.getDatasource().getDbName();
        if (dbName != null && !dbName.isEmpty()) {
            this.catalogs.add(dbName);
            return this.catalogs;
        }

        Set<String> emptyCatalogs = this.getEmptyCatalogs().stream().map(String::toUpperCase).collect(Collectors.toCollection(HashSet::new));
        try {
            Connection connection = this.getConnection();
            DatabaseMetaData metaData = connection.getMetaData();
            Set<String> selectedSchemaOrDatabase;
            Set<String> excludedSchemaOrDatabase;
            String inventoryDbName = this.instanceName;
            if (StringUtils.isBlank(inventoryDbName)) {
                inventoryDbName = this.getDatasource().getInventoryDbName();
            }
            ResultSet resultSet;
            String columnLabel;
            resultSet = metaData.getCatalogs();
            columnLabel = Constants.DBMD_TABLE_CAT;
            // database
            selectedSchemaOrDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedDatabase();
            excludedSchemaOrDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedDatabase();
            try {
                while (resultSet.next()) {
                    String catalogName = resultSet.getString(columnLabel);
                    if (!emptyCatalogs.isEmpty() && emptyCatalogs.contains(catalogName.toUpperCase())) {
                        continue;
                    }
                    String catalogIdentityName = inventoryDbName != null ?
                            (inventoryDbName + "." + catalogName.toLowerCase()) : catalogName.toLowerCase();
                    // 如果是排除，就跳过。必须包含
                    if (excludedSchemaOrDatabase.contains(catalogIdentityName)){
                        continue;
                    }
                    if (selectedSchemaOrDatabase.isEmpty() || selectedSchemaOrDatabase.contains(catalogIdentityName)) {
                        this.catalogs.add(catalogName);
                    }
                }
                return this.catalogs;
            } finally {
                if (resultSet != null) {
                    resultSet.close();
                }
            }
        } catch (SQLException sqlException) {
            sqlException.printStackTrace();
            LOG.warn("[clickhouse] Could not get catalogs for '" + this + "'" + System.lineSeparator() + sqlException);
            this.globalDataHolder.getProbeClientTaskContext()
                    .reportErrorOccurredExecuting(StatusRecord.Position.FindCatalogs,null,null,sqlException);
        }
        return this.catalogs;
    }

    protected List<ContextTable> findCatalogTables(String catalog, String[] tableTypes) {
        Clock clock = new Clock();
        String catalogName = null;
        String schemaName = null;
        if (this.getDatasource().isCatalog()) {
            catalogName = catalog;
        } else {
            schemaName = catalog;
        }

        if (this.tables != null) {
            this.tables.clear();
        } else {
            this.tables = new ArrayList<>();
        }

        this.currentTable = null;
        ResultSet resultSet = null;
        try(Statement statement = this.getConnection().createStatement()) {
            // todo: 这里是调整一下传入的 tableTypes。
            tableTypes = this.getDatasource().getDriverInterface().adjustTableTypes(tableTypes, true, this.getConnection());
            // todo: this maybe very slow for wrong input
            LOG.debug("Get tables with catalog = " + catalogName + " and schema = " + schemaName);
            resultSet = statement.executeQuery("select `database` TABLE_CAT, name TABLE_NAME, `engine` TABLE_TYPE, `comment` REMARKS  " +
                    "from system.tables where database = '"+catalogName+"'");
            LOG.debug("Get tables - end");
            Set<String> selectedDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedDatabase();
            Set<String> excludedDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedDatabase();
            Set<String> selectedTable = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedTable();
            Set<String> excludedTable = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedTable();
            Set<String> selectedView = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedView();
            Set<String> excludedView = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedView();
            String inventoryDbName = this.instanceName;
            if (StringUtils.isBlank(inventoryDbName)) {
                inventoryDbName = this.getDatasource().getInventoryDbName();
            }
            while (resultSet.next()) {
                String tableType = resultSet.getString("TABLE_TYPE");
                if ("view".equalsIgnoreCase(tableType)){
                    tableType = "VIEW";
                } else {
                    tableType = "TABLE";
                }
                String tableName = resultSet.getString("TABLE_NAME");
                String tableCatalog = resultSet.getString("TABLE_CAT");
                String tableComment = resultSet.getString("REMARKS");

                if (StringUtils.isBlank(tableCatalog) && StringUtils.isNotBlank(catalogName)) {
                    tableCatalog = catalogName;
                }

                // 当前表是属于内部 schema 或 内部 catalog 就跳过
                if (isInternalCatalog(this.getDatasource(), tableCatalog)) {
                    LOG.debug("Catalog: " + catalog + ",  has been skipped ");
                    continue;
                }
                ContextTable table = this.createContextTable(tableCatalog, null, tableName, tableType, tableComment);
                if (table == null) {
                    continue;
                }
                String tableIdentityName = table.getIdentityName().toLowerCase();
                String databaseIdentityName = tableIdentityName.substring(0, tableIdentityName.indexOf(".")).toLowerCase();

                tableIdentityName = inventoryDbName == null ? tableIdentityName : (inventoryDbName + "." + tableIdentityName).toLowerCase();
                databaseIdentityName = inventoryDbName == null ? databaseIdentityName : (inventoryDbName + "." + databaseIdentityName).toLowerCase();

                String tableTypeName = table.getType().getTypeName();
                if (excludedDatabase.contains(databaseIdentityName)){
                    continue;
                }

                if ("TABLE".equals(tableTypeName) && excludedTable.contains(tableIdentityName)){
                    continue;
                }
                if ("VIEW".equals(tableTypeName) && excludedView.contains(tableIdentityName)){
                    continue;
                }
                if ((selectedDatabase.isEmpty() || selectedDatabase.contains(databaseIdentityName))
                        && (selectedTable.isEmpty() || selectedTable.contains(tableIdentityName))
                        && "TABLE".equals(tableTypeName)) {
                    this.addTable(table);
                    clock.incrementCount();
                } else if ((selectedDatabase.isEmpty() || selectedDatabase.contains(databaseIdentityName))
                        && (selectedView.isEmpty() || selectedView.contains(tableIdentityName))
                        && "VIEW".equals(tableTypeName)) {
                    this.addTable(table);
                    clock.incrementCount();
                }
            }
            if (LOG.isInfoEnabled()) {
                String tableType = tableTypes == null ? "Catalog Tables" : RuleEvaluator.arrayToString(tableTypes);
                LOG.debug(catalog + ": " + clock.getElapsedMessageForCounter(tableType));
            }
        } catch (SQLException sqlException) {
            String message = Messages.getString("Could not access table(s) for: '${datasourceName}' on: '${datasourceUrl}'", "datasourceName", String.valueOf(this.getDatasource().getDescriptor()), "datasourceUrl", this.getConnectionDescriptor());
            LOG.error(message, sqlException);
            this.globalDataHolder.getProbeClientTaskContext()
                    .reportErrorOccurredExecuting(StatusRecord.Position.FindCatalogTables,
                            "catalog [" + catalog + "]",null,sqlException);
        } catch (NullPointerException nullPointerException) {
            StringBuilder logMsg = new StringBuilder();
            String message = Messages
                    .getString("Could not access table(s) for: '${datasourceName}' on: '${datasourceUrl}'",
                            "datasourceName", String.valueOf(this.getDatasource().getDescriptor()),
                            "datasourceUrl", this.getConnectionDescriptor());
            logMsg.append(message);
            throw new DBDriverException(clock.getElapsedMessage(logMsg.toString()), nullPointerException);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (Exception ignored) {
                }
            }
        }
        return this.listCatalogTables();
    }

}
