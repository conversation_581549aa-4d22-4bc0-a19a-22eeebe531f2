package com.dcap.classifier.context;

import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.util.NoSuchElementException;

public class JDBCColumnIterator extends ColumnIterator {
   JDBCColumnIterator(String catalog, String schemaPattern, String tableNamePattern, DatabaseMetaData metaData) throws SQLException {
      this.rs = metaData.getColumns(catalog, schemaPattern, tableNamePattern, null);
   }
   JDBCColumnIterator(String catalog, String schemaPattern, String tableNamePattern, String columnNamePattern, DatabaseMetaData metaData) throws SQLException {
      this.rs = metaData.getColumns(catalog, schemaPattern, tableNamePattern, columnNamePattern);
   }

   public ColumnProvider next() throws SQLException {
      if (this.hasNext()) {
         this.hasNext = null;
         return new JDBCColumnProvider(this.rs);
      } else {
         throw new NoSuchElementException();
      }
   }
}
