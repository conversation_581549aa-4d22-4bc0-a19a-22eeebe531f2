package com.dcap.classifier.context;

import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.TaskGlobalDataHolder;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import org.apache.commons.lang3.StringUtils;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


public class DaMengRuleContext extends RuleContext {

    public DaMengRuleContext(ClassifierDataSource dataSource, TaskGlobalDataHolder globalDataHolder) throws ConnectionException {
        super(dataSource, globalDataHolder);
    }

    protected List<String> findCatalogs() {
        LOG.debug("find Catalogs");
        this.resetBrowse();
        String dbName = this.getDatasource().getDbName();
        if (dbName != null && dbName.length() > 0) {
            this.catalogs.add(dbName);
            return this.catalogs;
        }

        Set<String> emptyCatalogs = this.getEmptyCatalogs().stream().map(String::toUpperCase).collect(Collectors.toCollection(HashSet::new));
        try {
            Connection connection = this.getConnection();
            if (StringUtils.isBlank(this.instanceName)){
                this.instanceName = connection.getClientInfo("dbname");
            }
            String inventoryDbName = this.instanceName;
            DatabaseMetaData metaData = connection.getMetaData();

            ResultSet resultSet;
            String columnLabel;
            resultSet = metaData.getSchemas();
            columnLabel = Constants.DBMD_TABLE_SCHEM;
            // schema
            Set<String> selectedSchemaOrDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedSchema();
            Set<String> excludedSchemaOrDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedSchema();
            try {
                while (resultSet.next()) {
                    String catalogName = resultSet.getString(columnLabel);
                    if (!emptyCatalogs.isEmpty() && emptyCatalogs.contains(catalogName.toUpperCase())) {
                        continue;
                    }
                    String catalogIdentityName = inventoryDbName != null ? (inventoryDbName + "." + catalogName).toLowerCase() : catalogName.toLowerCase();
                    // 如果是排除，就跳过。必须包含
                    if (excludedSchemaOrDatabase.contains(catalogIdentityName)){
                        continue;
                    }
                    if (selectedSchemaOrDatabase.isEmpty() || selectedSchemaOrDatabase.contains(catalogIdentityName)) {
                        this.catalogs.add(catalogName);
                    }
                }
                return this.catalogs;
            } finally {
                if (resultSet != null) {
                    resultSet.close();
                }
            }
        } catch (SQLException sqlException) {
            sqlException.printStackTrace();
            LOG.warn("[dameng] Could not get catalogs for '" + this + "'" + System.lineSeparator() + sqlException);
            this.globalDataHolder.getProbeClientTaskContext()
                            .reportErrorOccurredExecuting(StatusRecord.Position.FindCatalogs, null,null, sqlException);
        }
        return this.catalogs;
    }
}
