package com.dcap.classifier.context;

import com.dcap.classifier.context.privilege.DatabaseUser;
import com.dcap.classifier.context.privilege.DbPrivilege;
import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.TaskGlobalDataHolder;
import com.google.common.collect.ImmutableSet;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.*;

public class PostgreSqlRuleContext extends RuleContext {
    private static final String QUERY_USER_SQL = "SELECT r.rolname as username, r.rolsuper as superuser, r1.rolname as \"role\", " +
            "r.rolcanlogin as login FROM pg_catalog.pg_roles r FULL OUTER JOIN pg_catalog.pg_auth_members m ON (m.member = r.oid) FULL OUTER JOIN pg_roles r1 ON (m.roleid = r1.oid) " +
            "WHERE r.rolname IS NOT NULL AND r.rolname NOT LIKE 'pg_%' ";

    private static final String QUERY_TABLE_PRIVILEGES_SQL="select table_catalog, table_schema, table_name, " +
            "privilege_type, grantor, grantee, is_grantable " +
            "FROM information_schema.table_privileges " +
            "where table_schema not like 'pg_%' and grantee <> 'PUBLIC' and table_schema <> 'information_schema' ";


    private static final String QUERY_COLUMN_PRIVILEGES_SQL="select table_catalog, table_schema, table_name, column_name, " +
            "privilege_type, grantor, grantee, is_grantable from information_schema.column_privileges " +
            "where table_schema not like 'pg_%' and grantee <> 'PUBLIC' and table_schema <> 'information_schema' ";


    protected static final Logger LOG = LoggerFactory.getLogger(PostgreSqlRuleContext.class);

    protected List<DatabaseUser> databaseUsers;

    protected List<DbPrivilege> dbPrivileges;

    private final Set<String> internalSchema = ImmutableSet.of( "dbe_perf", "oracle", "information_schema",
            "INFORMATION_SCHEMA", "pg_catalog", "pg_namespace",
            "PG_CATALOG", "PG_NAMESPACE"
    );

    public PostgreSqlRuleContext(ClassifierDataSource dataSource, TaskGlobalDataHolder globalDataHolder) throws ConnectionException {
        super(dataSource, globalDataHolder);
    }

    protected boolean isInternalSchema(ClassifierDataSource dataSource, String tableSchema) {
        return internalSchema.contains(tableSchema);
    }

//    protected List<String> findCatalogs() {
//        this.resetBrowse();
//        String dbName = this.getDatasource().getDbName();
//        if (dbName == null) {
//            return this.catalogs;
//        }
//        if (!dbName.trim().intern().equals("postgres")) {
//            this.catalogs.add(dbName);
//            return this.catalogs;
//        }
//        Set<String> emptyCatalogs = this.getEmptyCatalogs()
//                .stream().map(String::toUpperCase)
//                .collect(Collectors.toCollection(HashSet::new));
//
//        try (PreparedStatement preparedStatement = this.getPreparedStatement("SELECT datname FROM pg_database");
//             ResultSet resultSet = preparedStatement.executeQuery()) {
//            while (resultSet.next()) {
//                String catalogName = resultSet.getString("datname");
//                if (!emptyCatalogs.isEmpty() && emptyCatalogs.contains(catalogName.toUpperCase())) {
//                    continue;
//                }
//                this.catalogs.add(catalogName);
//            }
//            return this.catalogs;
//        } catch (SQLException sqlException) {
//            LOG.warn("Could not get catalogs for '" + this + "'" + System.lineSeparator() + sqlException);
//        }
//        return this.catalogs;
//    }


    /**
     * 使用 jdbc 获取 postgresql 数据库中的用户数据，并且写入 DatabaseUser 对象。
     * @return
     */
    @Override
    public List<DatabaseUser> findDatabaseUsers() {
        if (this.databaseUsers != null) {
            return this.databaseUsers;
        }

        this.databaseUsers = new ArrayList<>();
        Connection connection = this.getConnection();
        try(Statement statement = connection.createStatement()) {
            ResultSet resultSet = statement.executeQuery(QUERY_USER_SQL);
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            while (resultSet.next()){
                DatabaseUser databaseUser = new DatabaseUser();
                databaseUser.setAttributes(new HashMap<>());
                String username = resultSet.getString("username");
                databaseUser.setUsername(username);
                String roleName = resultSet.getString("role");
                databaseUser.getAttributes().put("roleName", roleName);
                String rolCanLogin = resultSet.getString("login");
                databaseUser.getAttributes().put("rolCanLogin", rolCanLogin);
                String superUser = resultSet.getString("superuser");
                if (Objects.equals("t", superUser) || Objects.equals(roleName,"pg_rds_superuser")){
                    databaseUser.getAttributes().put("superUser", true);
                } else {
                    databaseUser.getAttributes().put("superUser", false);
                }
                this.databaseUsers.add(databaseUser);
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return this.databaseUsers;
    }

    @Override
    public List<DbPrivilege> retrievePrivileges() {
        if (this.dbPrivileges != null) {
            return this.dbPrivileges;
        }
        this.dbPrivileges = new ArrayList<>();
        try (final Connection connection = this.getConnection();
             ResultSet columnPrivilegesResultSet = connection.createStatement().executeQuery(QUERY_COLUMN_PRIVILEGES_SQL);
             ResultSet tablePrivilegesResultSet = connection.createStatement().executeQuery(QUERY_TABLE_PRIVILEGES_SQL);
        ) {
            while (tablePrivilegesResultSet.next()){
                final String catalogName = tablePrivilegesResultSet.getString("table_catalog");
                final String tableSchema = tablePrivilegesResultSet.getString("table_schema");
                final String tableName = tablePrivilegesResultSet.getString("table_name");
                final String tablePrivileges = tablePrivilegesResultSet.getString("privilege_type");
                final String grantor = tablePrivilegesResultSet.getString("grantor");
                final String grantee = tablePrivilegesResultSet.getString("grantee");
                final String isGrantable = Objects.equals("YES",tablePrivilegesResultSet.getString("is_grantable"))?"Y":"N";
                final String fullTableName = StringUtils.joinWith(".", catalogName, tableSchema, tableName);
                DbPrivilege dbPrivilege = new DbPrivilege();
                dbPrivilege.setGrantor(grantor);
                dbPrivilege.setGrantee(grantee);
                dbPrivilege.setTableCatalog(catalogName);
                dbPrivilege.setTableSchema(tableSchema);
                dbPrivilege.setDatabaseObjectType("TABLE");
                dbPrivilege.setDatabaseObjectName(tableName);
                dbPrivilege.setDatabaseObjectFullName(fullTableName);
                dbPrivilege.setIsGrantable(isGrantable);
                dbPrivilege.setPrivilegeName(tablePrivileges);
                dbPrivileges.add(dbPrivilege);
            }

            while (columnPrivilegesResultSet.next()){
                final String catalogName = columnPrivilegesResultSet.getString("table_catalog");
                final String tableSchema = columnPrivilegesResultSet.getString("table_schema");
                final String tableName = columnPrivilegesResultSet.getString("table_name");
                final String columnName = columnPrivilegesResultSet.getString("column_name");
                final String columnPrivilege = columnPrivilegesResultSet.getString("privilege_type");
                final String grantor = columnPrivilegesResultSet.getString("grantor");
                final String grantee = columnPrivilegesResultSet.getString("grantee");
                final String isGrantable = Objects.equals("YES", columnPrivilegesResultSet.getString("is_grantable"))?"Y":"N";
                final String fullTableName = StringUtils.joinWith(".", catalogName, tableSchema, tableName);
                final String fullColumnName = StringUtils.joinWith(".", fullTableName, columnName);

                DbPrivilege dbPrivilege = new DbPrivilege();
                dbPrivilege.setGrantor(grantor);
                dbPrivilege.setGrantee(grantee);
                dbPrivilege.setTableCatalog(catalogName);
                dbPrivilege.setTableSchema(tableSchema);
                dbPrivilege.setDatabaseObjectType("COLUMN");
                dbPrivilege.setDatabaseObjectName(columnName);
                dbPrivilege.setDatabaseObjectFullName(fullColumnName);
                dbPrivilege.setIsGrantable(isGrantable);
                dbPrivilege.setPrivilegeName(columnPrivilege);
                dbPrivileges.add(dbPrivilege);
            }
        } catch (final Exception e) {
            LOG.error("Could not retrieve table privileges", e);
        }
        return this.dbPrivileges;
    }
}
