package com.dcap.classifier.context;

public abstract class AbstractContext implements Comparable<AbstractContext> {

   private final AbstractInnerType type;

   private final String typename;

   protected AbstractContext(AbstractInnerType[] innerTypes, String typename) throws TypeNotSupportedException {
      this.typename = typename;
      this.type = findTypeByName(innerTypes, typename);
   }

   private static AbstractInnerType findTypeByName(AbstractInnerType[] innerTypes, String typename) throws TypeNotSupportedException {
      for (AbstractInnerType innerType : innerTypes) {
         if (innerType.isEquivalent(typename)) {
            return innerType;
         }
      }

      throw new TypeNotSupportedException("Could not match a type of: '" + typename + "' to any supported types or equivalents ");
   }

   public boolean isOfType(String typeName) {
      return this.getType().isEquivalent(typeName);
   }

   public boolean isOfType(AbstractInnerType innerType) {
      return this.isOfType(innerType.getTypeName());
   }

//   public boolean isOfType(AbstractContext context) {
//      return this.isOfType(context.getType());
//   }

   public AbstractInnerType getType() {
      return this.type;
   }

   public String getTypename() {
      return this.typename;
   }

   public abstract String getIdentityName();

   public String toString() {
      return this.getIdentityName();
   }

   public boolean equals(Object context) {
      return context instanceof AbstractContext && this.equals((AbstractContext) context);
   }

   public boolean equals(AbstractContext var1) {
      if (var1 == null) {
         return false;
      } else {
         return super.equals(var1) || this.type.equals(var1.getType()) && this.getIdentityName().equals(var1.getIdentityName());
      }
   }

   public int hashCode() {
      return (this.getTypename() + ":" + this.getIdentityName()).hashCode();
   }

   public int compareTo(AbstractContext var1) {
      return var1.getTypename().compareTo(this.getTypename());
   }
}
