package com.dcap.classifier.context;

import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.TaskGlobalDataHolder;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class SybaseRuleContext extends RuleContext {

    private final Set<String> internalSchema = new HashSet<String>(Arrays.asList(
            "information_schema"
    ));
    public SybaseRuleContext(ClassifierDataSource dataSource, TaskGlobalDataHolder globalDataHolder) throws ConnectionException {
        super(dataSource, globalDataHolder);
    }

    @Override
    protected boolean isInternalSchema(ClassifierDataSource dataSource, String tableSchema) {
        return internalSchema.contains(tableSchema);
    }

}
