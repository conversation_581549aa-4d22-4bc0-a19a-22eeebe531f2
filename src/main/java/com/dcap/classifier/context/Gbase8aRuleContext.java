package com.dcap.classifier.context;

import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.TaskGlobalDataHolder;


public class Gbase8aRuleContext extends RuleContext {
    public Gbase8aRuleContext(ClassifierDataSource dataSource, TaskGlobalDataHolder globalDataHolder) throws ConnectionException {
        super(dataSource, globalDataHolder);
    }
}
