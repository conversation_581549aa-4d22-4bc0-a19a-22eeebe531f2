package com.dcap.classifier.context;

import java.sql.ResultSet;
import java.sql.SQLException;

public abstract class ColumnIterator implements AutoCloseable {
   protected ResultSet rs;
   protected Boolean hasNext;

   public ColumnIterator() {
   }

   public boolean hasNext() throws SQLException {
      if (this.hasNext == null) {
         this.hasNext = this.rs != null && this.rs.next() ? Boolean.TRUE : Boolean.FALSE;
      }

      return this.hasNext;
   }

   public abstract ColumnProvider next() throws SQLException;

   public void close() throws SQLException {
      if (this.rs != null) {
         this.rs.close();
      }
   }
}
