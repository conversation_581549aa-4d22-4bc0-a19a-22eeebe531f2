package com.dcap.classifier.context;

public interface Constants {
   String DBMD_COLUMN_DATA_TYPE = "DATA_TYPE";
   String DBMD_COLUMN_NAME = "COLUMN_NAME";
   String DBMD_COLUMN_SIZE = "COLUMN_SIZE";
   String DBMD_COLUMN_TYPE_NAME = "TYPE_NAME";
   String DBMD_KEY_SEQ = "KEY_SEQ";
   String DBMD_PRIVILEGE = "PRIVILEGE";
   String DBMD_PRIVILEGE_GRANTEE = "GRANTEE";
   String DBMD_PRIVILEGE_GRANTOR = "GRANTOR";
   String DBMD_PRIVILEGE_IS_GRANTABLE = "IS_GRANTABLE";
   String DBMD_TABLE_CAT = "TABLE_CAT";
   String DBMD_TABLE_NAME = "TABLE_NAME";
   String DBMD_TABLE_SCHEM = "TABLE_SCHEM";
   String DBMD_TABLE_TYPE = "TABLE_TYPE";
   String PUBLIC = "PUBLIC";
   int SQL_ERROR_CODE_204 = -204;
   String SQL_STATE_42704 = "42704";
   int SQL_ERROR_CODE_551 = -551;
   String SQL_STATE_42501 = "42501";
}
