package com.dcap.classifier;

import com.dcap.datalayer.ClassifierDataSource;

import java.util.Iterator;
import java.util.List;

public interface ResultInfo {
   String getPolicyCategory();

   String getPolicyClassification();

   String getRuleName();

   String getCatalog();

   String getSchema();

   String getTableName();

   String getTableNameNoSchema();

   List<String> getMatchedColumnNames();

   default String getMatchColumnNameString() {
      List<String> var1 = this.getMatchedColumnNames();
      if (var1 != null) {
         String var2 = var1.toString();
         return var2.substring(1, var2.length() - 1);
      } else {
         return null;
      }
   }

   ClassifierDataSource getDatasource();

   String getDatasourceName();

   Iterator<ResultInfo.SpecificResultInfo> getColumnSpecificResults();

   String getSummary();

   public interface SpecificResultInfo {
      String getColumnName();

      String getSummary();
   }
}
