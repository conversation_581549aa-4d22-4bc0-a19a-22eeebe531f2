package com.dcap.classifier.writer;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;

import java.util.Arrays;


public enum RecordTypeEnum {

    PROBE_CLIENT_STATUS("Probe Client 任务状态"),
    DB_SCAN_RESULT("数据库结构扫描结果"),
    DB_SCAN_STATUS("数据库结构扫描状态"),
    DB_SCAN_END("数据库结构扫描结束"),
    SNAPSHOT_ASSETS_INFO("ECS快照资产扫描"),
    VFS_ASSETS_METADATA("VFS 文件扫描元数据"),
    VFS_METADATA_COMPLETED("VFS 元数据扫描完成"),
    VFS_ASSETS_DATA("VFS 文件识别的结果");

    @Getter
    private final String logName;
    @Getter
    private final String value;

    RecordTypeEnum(String logName) {
        this.logName = logName;
        this.value = this.name();
    }

    @JsonCreator
    public static RecordTypeEnum of(String logName) {
        return Arrays.stream(values()).filter((lt) -> lt.getLogName().equals(logName)).findAny().orElse(null);
    }
}
