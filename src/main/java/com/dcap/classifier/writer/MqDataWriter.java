package com.dcap.classifier.writer;

import com.dcap.utils.JSON;
import com.yd.dcap.classifier.config.KafkaConfig;
import com.yd.dcap.classifier.config.SpringContextUtil;
import com.yd.dcap.probe.mq.MqManager;

/**
 * mq格式结果输出工具
 */
public class MqDataWriter implements DataWriter {
    private final Long jobId;
    private final RecordTypeEnum recordType;

    public MqDataWriter(Long jobId, RecordTypeEnum recordType) {
        this.jobId = jobId;
        this.recordType = recordType;
    }

    @Override
    public void writeEntries(String[] entries) {
        MqManager mqManager = SpringContextUtil.getBean(MqManager.class);
        if (mqManager != null){
            String dataJson = JSON.from(entries).toString();
            mqManager.clientReportData(jobId, recordType, dataJson);
        }
    }

    @Override
    public void writeContent(String content) {
        MqManager mqManager = SpringContextUtil.getBean(MqManager.class);
        if (mqManager != null){
            mqManager.clientReportData(jobId, recordType, content);
        }
    }

    public void writeEndRecordToDataTopic(long status, String dataContent) {
        MqManager mqManager = SpringContextUtil.getBean(MqManager.class);
        if (mqManager != null){
            mqManager.sendMessageData(KafkaConfig.getKafkaConfig().DATA_TOPIC, status, jobId, RecordTypeEnum.DB_SCAN_END.name(), dataContent);
        }

    }
    @Override
    public void close() throws Exception {
    }
}
