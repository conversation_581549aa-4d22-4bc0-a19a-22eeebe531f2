package com.dcap.classifier.writer;

import com.opencsv.CSVWriter;
import org.eclipse.jgit.errors.NotSupportedException;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;

/**
 * csv格式结果输出工具
 */
public class CsvDataWriter implements DataWriter {
    private CSVWriter writer = null;

    public CsvDataWriter(FileOutputStream csvFOS) {
        writer = new CSVWriter(new OutputStreamWriter(csvFOS, StandardCharsets.UTF_8));
    }

    @Override
    public void writeEntries(String[] entries) throws IOException {
        writer.writeNext(entries, false);
        writer.flush();
    }

    @Override
    public void writeContent(String content) throws IOException {
        throw new NotSupportedException(" ");
    }

    @Override
    public void writeEndRecordToDataTopic(long status, String dataContent) throws Exception {

    }


    @Override
    public void close() throws Exception {
        if (writer != null) {
            writer.flush();
            writer.close();
        }
    }
}
