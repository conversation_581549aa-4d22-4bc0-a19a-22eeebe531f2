package com.dcap.classifier.writer;

import com.yd.dcap.classifier.config.SpringContextUtil;
import com.yd.dcap.probe.mq.MqManager;
import org.apache.commons.lang3.StringUtils;

import java.beans.Introspector;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;


/**
 * 数据输出者 工厂
 */
public class DataWriterFactory {

    /**
     * 创建扫描结果数据输出工具实现
     * 当前逻辑是：如果是 dspm 应用，则使用 mq，否则使用 csv
     *
     * @param jobId
     * @param recordType
     * @return
     */
    public static DataWriter createDataWriter(long jobId, RecordTypeEnum recordType) {
        String simpName = MqManager.class.getSimpleName();
        String beanName = Introspector.decapitalize(simpName);
        try {
            if (SpringContextUtil.containsBean(beanName)) {
                // 使用 mq
                return createMqDataWriter(jobId, recordType);
            } else {
                return createCsvDataWriter(jobId, recordType);
            }
        } catch (Exception e){
            return createCsvDataWriter(jobId, recordType);
        }
    }

    public static DataWriter createMqDataWriter(long jobId, RecordTypeEnum recordType) {
        return new MqDataWriter(jobId, recordType);
    }

    public static DataWriter createCsvDataWriter(long jobId, RecordTypeEnum recordType) {
        String fileDir = "/var/log/dcap/";
        String fileName = null;
        String csvName = recordType.name();
        if (StringUtils.isEmpty(csvName)) {
            throw new RuntimeException("数据类型不支持生成固定名称的csv." + recordType.name());
        }

        if (csvName.contains("/")) { //带二级目录的
            int idx = csvName.lastIndexOf("/");
            String dirPart = csvName.substring(0, idx);
            fileDir += dirPart;
        }

        fileName = jobId + "_" + recordType.name() + ".csv";
        //文件路径定死
        File f = new File(fileDir, fileName);
        f.getParentFile().mkdirs();

        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(f);
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }
        return new CsvDataWriter(fos);
    }
}
