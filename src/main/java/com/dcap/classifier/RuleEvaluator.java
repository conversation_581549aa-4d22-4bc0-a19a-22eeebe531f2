package com.dcap.classifier;

import com.dcap.classifier.context.RuleContext;
import com.dcap.datalayer.ClassifierRule;
import com.dcap.datalayer.ClassifierRuleRecord;
import com.dcap.datalayer.ContinueOption;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class RuleEvaluator extends AbstractParameterized {

    protected final Logger LOG = LoggerFactory.getLogger(getClass());

//    private RuleResults lastResults;

    private final ClassifierRule<? extends ClassifierRuleRecord> classifierRule;

    protected RuleEvaluator(ClassifierRule<? extends ClassifierRuleRecord> classifierRule) {
        this.classifierRule = classifierRule;
    }

    public ClassifierRule<? extends ClassifierRuleRecord> getRuleDefinition() {
        return this.classifierRule;
    }

    public String getName() {
        return this.classifierRule.getName();
    }

    public String getTranslatedName() {
        return this.classifierRule.getTranslatedName();
    }

    public String getDataTagTypeForDspm(){
        return this.classifierRule.getDataTagTypeForDspm();
    }

    public String getId() {
        return this.classifierRule.getId();
    }

    public String getTypeName() {
        return this.classifierRule.getRuleTypeName();
    }

    public ContinueOption getContinueOption() {
        return this.getRuleDefinition().getContinueOption();
    }

    public String describeParams() {
        return this.classifierRule.describeParams();
    }

    /**
     * 初始化数据源
     */
//   public void initOnNewDatasource() {
//      for (ActionTrigger actionTrigger : this.getActions()) {
//         actionTrigger.initOnNewDatasource();
//      }
//   }

//   public void completeOnNewDatasource(RuleContext ruleContext) {
//      for (ActionTrigger actionTrigger : this.getActions()) {
//         try {
//            actionTrigger.completeOnNewDatasource(ruleContext, this);
//         } catch (ClassifierException ignored) {
//         }
//      }
//   }

//   public boolean addAction(ActionTrigger actionTrigger) {
//      return this.actions.add(actionTrigger);
//   }
//
//   public int countActions() {
//      return this.actions.size();
//   }
//
//   public Iterator<ActionTrigger> iterateActions() {
//      return this.getActions().iterator();
//   }

//   public List<ActionTrigger> getActions() {
//      return this.actions;
//   }

//   public String describeActions() {
//      StringBuilder describe = new StringBuilder();
//      for (ActionTrigger actionTrigger: this.getActions()) {
//         describe.append('-').append(actionTrigger.describe())
//                 .append(' ').append(System.lineSeparator());
//      }
//      return describe.toString();
//   }
    public String toString() {
        return this.getTypeName() + ": " + this.getName();
    }

    public String getCategory() {
        //      if (categoryName == null || categoryName.length() == 0) {
//         categoryName = this.getPolicy().getCategoryName();
//      }
        return this.classifierRule.getCategoryName();
    }

    public String getClassification() {
//    if (classificationName == null || classificationName.length() == 0) {
//         classificationName = this.getPolicy().getClassificationName();
//    }
        return this.classifierRule.getClassificationName();
    }

    public boolean reportHits() {
        return false;
    }

//   protected abstract boolean isRuleSupported(RuleContext ruleContext);

//    void setLastResults(RuleResults ruleResults) {
//        this.lastResults = ruleResults;
//    }
//
//    public RuleResults getLastResults() {
//        return this.lastResults;
//    }

    public final RuleResults invokeRule(RuleContext ruleContext, SearchParameters searchParameters) throws ClassifierException {
//      if (!this.isRuleSupported(ruleContext)) {
//         throw new UnsupportedRuleException(this + " - " + ruleContext);
//      } else {
        RuleResults ruleResults = this.runRule(ruleContext, searchParameters);
//        this.setLastResults(ruleResults);
        return ruleResults;
//      }
    }

    public abstract RuleResults runRule(RuleContext ruleContext, SearchParameters searchParameters) throws ClassifierException;

    public abstract String getExprType();
}
