package com.dcap.classifier;

import com.dcap.classifier.context.ContextColumn;
import com.dcap.utils.Utils;
import com.yd.rules.engine.result.MatchedResult;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;
import java.util.stream.Collectors;

public class RuleResults {
   private final SearchParameters searchParameters;
   private final List<ContextColumn> matchedColumns;

   private final List<Pair<ContextColumn, MatchedResult>> matchedColumnsResult;
   private List<String> matchedColumnNames;
   private final boolean usedColumns;
   private final boolean wasMatch;
   private final RuleResults.Hits hits;
   private final boolean continueWithUnmatchedColumns;

   private RuleResults(SearchParameters searchParameters, boolean wasMatch, boolean usedColumns,
                       List<Pair<ContextColumn, MatchedResult>> matchedColumnsResult,
                       List<ContextColumn> matchedColumns,
                       RuleResults.Hits hits, boolean continueWithUnmatchedColumns) {
      this.searchParameters = searchParameters;
      this.wasMatch = wasMatch;
      this.usedColumns = usedColumns;
      if (matchedColumnsResult == null){
         this.matchedColumnsResult = Collections.emptyList();
      } else {
         this.matchedColumnsResult = matchedColumnsResult;
      }
      this.matchedColumns = matchedColumns == null ? this.matchedColumnsResult.stream().map(Pair::getKey).collect(Collectors.toList()) : matchedColumns;
      this.hits = hits;
      this.continueWithUnmatchedColumns = continueWithUnmatchedColumns;
   }

   public RuleResults(SearchParameters searchParameters, boolean wasMatch, boolean usedColumns, List<ContextColumn> matchedColumns, boolean continueWithUnmatchedColumns) {
      this(searchParameters, wasMatch, usedColumns, null, matchedColumns, null, continueWithUnmatchedColumns);
   }

   public RuleResults(SearchParameters searchParameters, List<Pair<ContextColumn, MatchedResult>> matchedColumnsResult,
                      List<ContextColumn> matchedColumns,
                      RuleResults.Hits hits, boolean continueWithUnmatchedColumns) {
      this(searchParameters, matchedColumns != null && matchedColumns.size() > 0, true, matchedColumnsResult, matchedColumns, hits, continueWithUnmatchedColumns);
   }

   public RuleResults(SearchParameters searchParameters, List<ContextColumn> matchedColumns, RuleResults.Hits hits, boolean continueWithUnmatchedColumns) {
      this(searchParameters, matchedColumns != null && matchedColumns.size() > 0, true, null, matchedColumns, hits, continueWithUnmatchedColumns);
   }

   public RuleResults(SearchParameters searchParameters, boolean wasMatch, boolean usedColumns, boolean continueWithUnmatchedColumns) {
      this(searchParameters, wasMatch, usedColumns, null, null, null, continueWithUnmatchedColumns);
   }

   public RuleResults(SearchParameters searchParameters, boolean wasMatch) {
      this(searchParameters, wasMatch, false, null, null, null, false);
   }

   private List<ContextColumn> getUnmatchedColumns() throws InitializationException {
      List<ContextColumn> unmatchedColumns = this.searchParameters.getSearchableColumns();
      if (this.matchedColumns.isEmpty()) {
         return unmatchedColumns;
      } else {
         List<ContextColumn> result = new LinkedList<>(unmatchedColumns);
         result.removeAll(this.matchedColumns);
         return result;
      }
   }

   public SearchParameters getNextSearchParameters() throws InitializationException {
      return this.continueWithUnmatchedColumns
              && this.wasMatch
              && this.usedColumns ? new SearchParameters(this.getUnmatchedColumns()) : this.searchParameters;
   }

   public List<ContextColumn> getMatchedColumns() {
      return this.matchedColumns;
   }

   public List<String> getMatchedColumnNames() {
      return this.matchedColumnNames != null ? this.matchedColumnNames : (this.matchedColumnNames = toColumnNames(this.matchedColumns));
   }

   public static List<String> toColumnNames(List<ContextColumn> columns) {
      return columns.stream().map(ContextColumn::getColumnName).collect(Collectors.toList());
   }

   public static String toColumnNamesString(List<ContextColumn> columns) {
      return Utils.collectionToString(toColumnNames(columns));
   }

   public boolean usedColumns() {
      return this.usedColumns;
   }

   public boolean wasMatch() {
      return this.wasMatch;
   }

   public String getSampleSizeFor(String columnName) {
      return this.hits == null ? null : this.hits.getSampleSizeFor(columnName);
   }

   public String getHitsFor(String columnName) {
      return this.hits == null ? null : this.hits.getHitsFor(columnName);
   }

   public String getMatchesFor(String columnName) {
      return this.hits == null ? null : this.hits.getMatchesFor(columnName);
   }

//   public List<String> getMatchesForList(String var1) {
//      return this.hits == null ? null : this.hits.getMatchesForList(var1);
//   }

   public String toString() {
      return this.matchedColumns != null && this.matchedColumns.size() != 0 ? " " + this.matchedColumns.size() + " columns" : "No match";
   }

   public List<Pair<ContextColumn, MatchedResult>> getMatchedColumnsResult() {
      return matchedColumnsResult;
   }

   public static class Hits {
      // key 是 column name
      private final Map<String, Integer> sampleSize = new HashMap<>(10);
      private final Map<String, Integer> hits = new HashMap<>(10);
      private final Map<String, List<String>> matches = new HashMap<>(10);

      public Hits() {}

      public void update(int columnValuesLength, int hitsCount, String columnName, List<String> matchedItems) {
         int sampleSize = 0;
         int hits = 0;
         if (this.sampleSize.containsKey(columnName)) {
            sampleSize = this.sampleSize.get(columnName);
            hits = this.hits.get(columnName);
         }

         if (columnValuesLength > sampleSize) {
            sampleSize = columnValuesLength;
         }

         if (hits == 0) {
            hits = hitsCount;
         } else {
            hits = Math.min(hits, hitsCount);
         }

         this.sampleSize.put(columnName, sampleSize);
         this.hits.put(columnName, hits);
         if (matchedItems != null) {
            this.matches.put(columnName, matchedItems);
         }
      }

      public String getSampleSizeFor(String columnName) {
         return this.sampleSize.containsKey(columnName) ? this.sampleSize.get(columnName).toString() : null;
      }

      public String getHitsFor(String columnName) {
         return this.hits.containsKey(columnName) ? this.hits.get(columnName).toString() : null;
      }

      public String getMatchesFor(String columnName) {
         return this.matches.containsKey(columnName) ? this.matches.get(columnName).toString() : null;
      }

//      public List<String> getMatchesForList(String columnName) {
//         return this.matches.getOrDefault(columnName, null);
//      }
   }
}
