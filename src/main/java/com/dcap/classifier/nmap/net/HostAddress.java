package com.dcap.classifier.nmap.net;

public class HostAddress {
    private String ipAddr;
    private String subnetMask;
    private String cidr;

    public HostAddress(String ipAddr, String subnetMask, String cidr) {
        this.ipAddr = ipAddr;
        this.subnetMask = subnetMask;
        this.cidr = cidr;
    }

    public String getIpAddr() {
        return ipAddr;
    }

    public void setIpAddr(String ipAddr) {
        this.ipAddr = ipAddr;
    }

    public String getSubnetMask() {
        return subnetMask;
    }

    public void setSubnetMask(String subnetMask) {
        this.subnetMask = subnetMask;
    }

    public String getCidr() {
        return cidr;
    }

    public void setCidr(String cidr) {
        this.cidr = cidr;
    }
}
