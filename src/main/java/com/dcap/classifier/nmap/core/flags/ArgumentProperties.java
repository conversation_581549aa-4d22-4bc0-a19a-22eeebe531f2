package com.dcap.classifier.nmap.core.flags;

import java.util.*;

public class ArgumentProperties {
   private Map<String,String> flagMap = new LinkedHashMap<>();
   private final Set<String> includeHosts = new HashSet<>();
   private final Set<String> excludeHosts = new HashSet<>();

   public void addFlag(Flag f) {
      this.addFlag(f.toString());
   }

   public void addFlag(Flag f, String value) {
      this.addFlag(f.toString(), value);
   }

   public void addFlag(String singleFlag) {
      this.addFlag(singleFlag, null);
   }

   public void addFlag(String flag, String arg) {
      if (this.flagMap.containsKey(flag) && this.flagMap.get(flag) != null) {
         String tempFlags = this.flagMap.get(flag) + "," + arg;
         this.flagMap.remove(flag);
         this.flagMap.put(flag, tempFlags);
      } else {
         this.flagMap.put(flag, arg);
      }

   }

   public void replaceFlag(Flag f, String value) {
      if (this.flagMap.get(f.toString()) != null) {
         this.removeFlag(f);
      }

      this.flagMap.put(f.toString(), value);
   }

   public void removeFlag(Flag f) {
      this.flagMap.remove(f.toString());
   }

   public Map<String,String> getFlagMap() {
      return this.flagMap;
   }

   public void setFlagMap(Map<String, String> newMap) {
      this.flagMap = newMap;
   }

   public String getFlags() {
      StringBuilder flags = new StringBuilder();
      Set<String> mapKeys = this.flagMap.keySet();

      for(Iterator<String> keysIt = mapKeys.iterator(); keysIt.hasNext(); flags.append(" ")) {
         String keyValue = keysIt.next();
         String argumentForKey = this.flagMap.get(keyValue);
         flags.append(keyValue);
         if (argumentForKey != null) {
            flags.append(" ");
            flags.append(argumentForKey);
         }
      }

      if (!this.excludeHosts.isEmpty()) {
         flags.append(Flag.EXCLUDE_HOSTS.toString());
         flags.append(" ");
         flags.append(this.convertSetToString(this.excludeHosts, ","));
      }

      if (!this.includeHosts.isEmpty()) {
         flags.append(" ");
         flags.append(this.convertSetToString(this.includeHosts, " "));
      }

      return flags.toString();
   }

   public void addIncludedHost(String host) {
      if (!this.includeHosts.contains(host)) {
         this.includeHosts.add(host);
      }
   }

   public void removeIncludedHost(String host) {
      if (this.includeHosts.contains(host)) {
         this.includeHosts.remove(host);
      }

   }

   public String getIncludedHostsAsString() {
      return this.convertSetToString(this.includeHosts, " ");
   }

   public Set<String> getIncludedHosts() {
      return this.includeHosts;
   }

   public void addExcludedHost(String host) {
      if (!this.excludeHosts.contains(host)) {
         this.excludeHosts.add(host);
      }

   }

   public void removeExcludedHost(String host) {
      if (this.excludeHosts.contains(host)) {
         this.excludeHosts.remove(host);
      }

   }

   public Set<String> getExcludeHost() {
      return this.excludeHosts;
   }

   public String getExcludedHostsAsString() {
      return this.convertSetToString(this.excludeHosts, ",");
   }

   public void clearFlags() {
      this.flagMap.clear();
   }

   private String convertSetToString(Set<String> hosts, String delimiter) {
      StringBuilder hostsBuffer = new StringBuilder();
      for (String host : hosts) {
         hostsBuffer.append(host);
         hostsBuffer.append(delimiter);
      }
      return hostsBuffer.toString();
   }
}
