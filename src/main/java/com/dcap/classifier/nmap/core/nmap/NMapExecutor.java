package com.dcap.classifier.nmap.core.nmap;


import com.dcap.classifier.nmap.core.flags.ArgumentProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Consumer;

public class NMapExecutor {
   private static final Logger log = LoggerFactory.getLogger(NMapExecutor.class);
   private final ArgumentProperties nmapArguments;
   private final NMapProperties nmapProperties;
   private final Consumer<Long> callback;

   public NMapExecutor(ArgumentProperties argProps, NMapProperties nmapProps, Consumer<Long> processCallback) throws NMapInitializationException {
      this.nmapArguments = argProps;
      this.nmapProperties = nmapProps;
      if (this.nmapArguments != null && this.nmapProperties != null) {
         if (nmapProps.getPath() == null || nmapProps.getPath() != null && nmapProps.getPath().length() <= 0) {
            throw new NMapInitializationException("the NMAP_HOME variable is not set or you did not set this path.");
         }
      } else {
         throw new NMapInitializationException("You cannot instantiate an NMapExecutor with nulls in either argument. Please refer to the documentation if you aren't sure how to proceed.");
      }
      this.callback = processCallback;
   }

   private String getOS() {
      return System.getProperty("os.name");
   }

   private StringBuilder getCommand() {
      StringBuilder fullCommand = new StringBuilder();
      fullCommand.append(this.nmapProperties.getFullyFormattedCommand());
      fullCommand.append(" ");
      fullCommand.append(this.nmapArguments.getFlags());
      return fullCommand;
   }

   public ExecutionResults execute() throws NMapExecutionException {
      StringBuilder command = this.getCommand();
      ExecutionResults results = new ExecutionResults();

      try {
         String commandText = command.toString();
         results.setExecutedCommand(commandText);
         Process process = Runtime.getRuntime().exec(commandText);
         if (callback != null){
            callback.accept(process.pid());
         }
         CompletableFuture errorPromise = this.convertStream(process.getErrorStream());
         CompletableFuture outputPromise = this.convertStream(process.getInputStream());
         results.setOutput((String)outputPromise.get());
         results.setErrors((String)errorPromise.get());
         int exitCode = process.waitFor();
         if (exitCode != 0) {
            log.error("nmap exit. exitCode={}", exitCode);
         }

         return results;
      } catch (ExecutionException | IOException var7) {
         throw new NMapExecutionException(var7.getMessage(), var7);
      } catch (InterruptedException var8) {
         Thread.currentThread().interrupt();
         throw new NMapExecutionException(var8.getMessage(), var8);
      }
   }

   private CompletableFuture convertStream(InputStream is) throws IOException {
      return CompletableFuture.supplyAsync(() -> {
         StringBuilder outputBuffer = new StringBuilder();
         BufferedReader streamReader = new BufferedReader(new InputStreamReader(is));

         String output;
         try {
            while((output = streamReader.readLine()) != null) {
               outputBuffer.append(output);
               outputBuffer.append("\n");
            }
         } catch (IOException var5) {
            var5.printStackTrace();
         }

         return outputBuffer.toString();
      });
   }

   public String toString() {
      return this.getCommand().toString();
   }
}
