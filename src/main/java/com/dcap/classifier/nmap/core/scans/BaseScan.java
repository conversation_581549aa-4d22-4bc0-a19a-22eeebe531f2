package com.dcap.classifier.nmap.core.scans;

import com.dcap.classifier.nmap.core.flags.ArgumentProperties;
import com.dcap.classifier.nmap.core.flags.Flag;
import com.dcap.classifier.nmap.core.nmap.*;
import com.google.common.base.Preconditions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.function.Consumer;

public class BaseScan implements Scanner {

   private final Logger logger = LoggerFactory.getLogger(this.getClass());

   protected ArgumentProperties argProps;
   protected NMapProperties nmapProps;
   private String executedCommand;

   public BaseScan() {
      this.nmapProps = new NMapProperties();
      this.argProps = new ArgumentProperties();
      this.argProps.addFlag(Flag.XML_OUTPUT, "-");
   }

   public BaseScan(String nmapPath) {
      this();
      this.nmapProps.setPath(nmapPath);
   }

   public void excludeHost(String host) {
      Preconditions.checkNotNull(host, "host cannot be null");
      this.argProps.addExcludedHost(host);
   }

   public void excludeHosts(String[] hosts) {
      if (hosts != null) {
          for (String h : hosts) {
              this.excludeHost(h);
          }
      }

   }

   public void includeHost(String host) {
      if (host != null && !host.isEmpty()) {
         this.argProps.addIncludedHost(host);
      }

   }

   public void includeHosts(String[] hosts) {
      if (hosts != null) {
          for (String h : hosts) {
              this.includeHost(h);
          }
      }

   }

   public void addPort(int port) {
      this.argProps.addFlag(Flag.PORT_SPEC, Integer.toString(port));
   }

   public void addPorts(String[] ports) {
      if (ports != null) {
         for (String p : ports) {
            this.argProps.addFlag(Flag.PORT_SPEC, p);
         }
      }
   }

   public void executeScanAsync(Consumer action) {
      Runnable task = new NmapAsyncTask(this.argProps, this.nmapProps, action);
      Thread aThread = new Thread(task);
      aThread.start();
   }

   public ExecutionResults executeScan(Consumer<Long> processCallback) throws NMapExecutionException, NMapInitializationException {
      NMapExecutor executor = new NMapExecutor(this.argProps, this.nmapProps, processCallback);
      this.executedCommand = executor.toString();
      logger.info("command: {}", executedCommand);
      return executor.execute();
   }

   public void removeExcludeHost(String host) {
      if (host != null && host.length() > 0) {
         this.argProps.removeExcludedHost(host);
      }

   }

   public void removeExcludeHosts(String[] hosts) {
      if (hosts != null && hosts.length > 0) {
         String[] var2 = hosts;
         int var3 = hosts.length;

         for(int var4 = 0; var4 < var3; ++var4) {
            String h = var2[var4];
            this.removeExcludeHost(h);
         }
      }

   }

   public void removeIncludeHost(String host) {
      if (host != null && host.length() > 0) {
         this.argProps.removeIncludedHost(host);
      }

   }

   public void removeIncludeHosts(String[] hosts) {
      if (hosts != null && hosts.length > 0) {
         String[] var2 = hosts;
         int var3 = hosts.length;

         for(int var4 = 0; var4 < var3; ++var4) {
            String h = var2[var4];
            this.removeIncludeHost(h);
         }
      }

   }

   public void setOutputType(Scanner.OutputType ot, String fName) {
      String fileName = fName;
      if (fName == null || fName.length() <= 0) {
         fileName = "-";
      }

      switch(ot) {
      case GREPPABLE:
         this.argProps.addFlag(Flag.GREPPABLE_OUTPUT, fileName);
         break;
      case SCRIPT_KIDDIE:
         this.argProps.addFlag(Flag.SCRIPT_KIDDIE_OUPUT, fileName);
         break;
      case XML:
         this.argProps.addFlag(Flag.XML_OUTPUT, fileName);
         break;
      case NORMAL:
         this.argProps.addFlag(Flag.NORMAL_OUTPUT, fileName);
         break;
      default:
         this.argProps.addFlag(Flag.XML_OUTPUT, fileName);
      }

   }

   public void validate() {
   }

   public void setTiming(Scanner.TimingFlag tf) {
      switch(tf) {
      case PARANOID:
         this.argProps.addFlag(Flag.PARANOID_TIMING);
         break;
      case SNEAKY:
         this.argProps.addFlag(Flag.SNEAKY_TIMING);
         break;
      case POLITE:
         this.argProps.addFlag(Flag.POLITE_TIMING);
         break;
      case NORMAL:
         this.argProps.addFlag(Flag.NORMAL_TIMING);
         break;
      case AGGRESSIVE:
         this.argProps.addFlag(Flag.AGGRESIVE_TIMING);
         break;
      case INSANE:
         this.argProps.addFlag(Flag.INSANE_TIMING);
      }

   }

   public void setNMapPath(String path) {
      this.nmapProps.setPath(path);
   }

   public void addFlag(Flag flag) {
      this.argProps.addFlag(flag);
   }

   public void removeFlag(Flag flag) {
      this.argProps.removeFlag(flag);
   }

   public ArgumentProperties getArgumentProperties() {
      return this.argProps;
   }

   public NMapProperties getNMapProperties() {
      return this.nmapProps;
   }

   public static class NmapAsyncTask implements Runnable {
      private ArgumentProperties nmapArguments;
      private NMapProperties nmapProperties;
      private Consumer action;

      public NmapAsyncTask(ArgumentProperties ap, NMapProperties nmp, Consumer action) {
         this.nmapArguments = ap;
         this.nmapProperties = nmp;
         this.action = action;
      }

      public void run() {
         try {
            NMapExecutor executor = new NMapExecutor(this.nmapArguments, this.nmapProperties, null);
            ExecutionResults results = executor.execute();
            this.action.accept(results);
         } catch (NMapInitializationException var3) {
            var3.printStackTrace();
         } catch (NMapExecutionException var4) {
            var4.printStackTrace();
         }

      }
   }
}
