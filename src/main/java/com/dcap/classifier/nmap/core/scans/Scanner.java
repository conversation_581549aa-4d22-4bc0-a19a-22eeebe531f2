package com.dcap.classifier.nmap.core.scans;

import com.dcap.classifier.nmap.core.flags.ArgumentProperties;
import com.dcap.classifier.nmap.core.flags.Flag;
import com.dcap.classifier.nmap.core.nmap.ExecutionResults;
import com.dcap.classifier.nmap.core.nmap.NMapExecutionException;
import com.dcap.classifier.nmap.core.nmap.NMapInitializationException;
import com.dcap.classifier.nmap.core.nmap.NMapProperties;

import java.util.function.Consumer;

public interface Scanner {

   void setTiming(TimingFlag var1);

   void addPort(int var1);

   void addPorts(String[] ports);

   void includeHost(String var1);

   void includeHosts(String[] var1);

   void excludeHost(String var1);

   void excludeHosts(String[] var1);

   void removeIncludeHost(String var1);

   void removeIncludeHosts(String[] var1);

   void removeExcludeHost(String var1);

   void removeExcludeHosts(String[] var1);

   void setOutputType(OutputType var1, String var2);

   ExecutionResults executeScan(Consumer<Long> processCallback) throws ParameterValidationFailureException, NMapExecutionException, NMapInitializationException;

   void executeScanAsync(Consumer var1) throws ParameterValidationFailureException, NMapExecutionException;

   void validate();

   void setNMapPath(String var1);

   void addFlag(Flag var1);

   void removeFlag(Flag var1);

   ArgumentProperties getArgumentProperties();

   NMapProperties getNMapProperties();

   public static enum OutputType {
      XML,
      JSON,
      GREPPABLE,
      SCRIPT_KIDDIE,
      NORMAL;
   }

   public static enum TimingFlag {
      PARANOID,
      SNEAKY,
      POLITE,
      NORMAL,
      AGGRESSIVE,
      INSANE;
   }
}
