package com.dcap.classifier.nmap.core.nmap;

public class ExecutionResults {
   private String errors;
   private String output;
   private String executedCommand;

   public ExecutionResults() {
   }

   public ExecutionResults(String err, String out) {
      this.errors = err;
      this.output = out;
   }

   public String getErrors() {
      return this.errors;
   }

   public void setErrors(String errors) {
      this.errors = errors;
   }

   public String getOutput() {
      return this.output;
   }

   public void setOutput(String output) {
      this.output = output;
   }

   public boolean hasErrors() {
      return this.errors != null && this.errors.length() > 0;
   }

   public String getExecutedCommand() {
      return this.executedCommand;
   }

   public void setExecutedCommand(String command) {
      this.executedCommand = command;
   }
}
