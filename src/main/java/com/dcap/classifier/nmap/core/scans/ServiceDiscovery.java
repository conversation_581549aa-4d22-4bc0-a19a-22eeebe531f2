package com.dcap.classifier.nmap.core.scans;

import com.dcap.classifier.nmap.core.flags.Flag;
import com.dcap.classifier.nmap.core.nmap.ExecutionResults;
import com.dcap.classifier.nmap.core.nmap.NMapExecutionException;
import com.dcap.classifier.nmap.core.nmap.NMapInitializationException;
import org.apache.commons.lang3.StringUtils;

import java.util.function.Consumer;

public class ServiceDiscovery extends BaseScan {
   public ServiceDiscovery(String[] includeHosts, String[] excludeHosts) {
      this.includeHosts(includeHosts);
      this.excludeHosts(excludeHosts);
      this.setTiming(Scanner.TimingFlag.NORMAL);
      this.argProps.addFlag(Flag.SERVICE_VERSION);
//      this.argProps.addFlag(Flag.OS_DETECTION);
//      this.argProps.addFlag(Flag.XML_OUTPUT, "-");
   }

   @Override
   public ExecutionResults executeScan(Consumer<Long> processCallback) throws NMapExecutionException, NMapInitializationException {
      addDefaultPorts();
      return super.executeScan(processCallback);
   }

   @Override
   public void executeScanAsync(Consumer action) {
      addDefaultPorts();
      super.executeScanAsync(action);
   }

   private void addDefaultPorts() {
      if (!this.argProps.getFlagMap().containsKey(Flag.PORT_SPEC.toString())){
         String ports = StringUtils.join(new String[]{
                 "7210", "3306", "1521-1830", "1433", "1434", "8529", "7000", "7001", "9042", "5984",
                 "9200", "9300", "27017", "27018", "27019", "28017",
                 "7473", "7474", "6379", "8087", "8098", "8080", "28015", "29015", "7574", "8983",
                 "3528", "3529", "4447", "8009", "8443", "9990", "9999","2379","2380","6443"
         }, ",");
         this.argProps.addFlag(Flag.PORT_SPEC, ports);
      }
   }
}
