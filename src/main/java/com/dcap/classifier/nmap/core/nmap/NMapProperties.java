package com.dcap.classifier.nmap.core.nmap;

import com.dcap.classifier.nmap.core.flags.Flag;

import java.io.File;

public class NMapProperties {
   private String pathToNMap;
   private final String BIN = "bin";
   private final String SHARE = "share";
   private final String COMMAND = "nmap";

   public NMapProperties() {
      String path = (String)System.getenv().get("NMAP_HOME");
      if (path != null && path.length() > 0) {
         this.pathToNMap = path;
      }

   }

   public NMapProperties(String path) {
      this.pathToNMap = path;
   }

   public String getPath() {
      return this.pathToNMap;
   }

   public void setPath(String pathToBinDir) {
      this.pathToNMap = pathToBinDir;
   }

   public String getBinDir() {
      return this.pathToNMap + File.separator + "bin";
   }

   public String getShareDir() {
      return this.pathToNMap + File.separator + "share" + File.separator + "nmap";
   }

   private String getOS() {
      return System.getProperty("os.name");
   }

   public String getFullyFormattedCommand() {
      StringBuilder command = new StringBuilder();
      if (this.getOS().toLowerCase().contains("windows")) {
         command.append(this.pathToNMap);
         command.append(File.separator);
         command.append("nmap");
         command.append(".exe");
      } else {
         command.append(this.getBinDir());
         command.append(File.separator);
         command.append("nmap");
         command.append(" ");
         command.append(Flag.DATADIR);
         command.append(" ");
         command.append(this.getShareDir());
      }

      return command.toString();
   }
}
