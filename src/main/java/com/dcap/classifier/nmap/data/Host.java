package com.dcap.classifier.nmap.data;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class Host {

   private List<Address> address;

   private Ports ports;

   private String starttime;

   private String endtime;

    public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Host)) {
         return false;
      } else {
         Host other = (Host)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            Object address = this.getAddress();
            Object other$address = other.getAddress();
            if (address == null) {
               if (other$address != null) {
                  return false;
               }
            } else if (!address.equals(other$address)) {
               return false;
            }

            Object this$ports = this.getPorts();
            Object other$ports = other.getPorts();
            if (this$ports == null) {
               if (other$ports != null) {
                  return false;
               }
            } else if (!this$ports.equals(other$ports)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Host;
   }

   public int hashCode() {
      int PRIME = 59;
      int result = 1;
      Object address = this.getAddress();
      result = result * PRIME + (address == null ? 43 : address.hashCode());
      Object ports = this.getPorts();
      result = result * PRIME + (ports == null ? 43 : ports.hashCode());
      return result;
   }

   public String toString() {
      return "Host(address=" + this.getAddress() + ", ports=" + this.getPorts() + ")";
   }
}
