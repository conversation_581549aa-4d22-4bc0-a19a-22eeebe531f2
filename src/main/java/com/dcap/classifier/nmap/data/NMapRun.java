package com.dcap.classifier.nmap.data;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class NMapRun {

   public static final String NMAP_RUN_TAG = "nmaprun";

   private List<Host> host;

    public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof NMapRun)) {
         return false;
      } else {
         NMapRun other = (NMapRun)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            Object this$host = this.getHost();
            Object other$host = other.getHost();
            if (this$host == null) {
               if (other$host != null) {
                  return false;
               }
            } else if (!this$host.equals(other$host)) {
               return false;
            }
            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof NMapRun;
   }

   public int hashCode() {
      int PRIME = 59;
      int result = 1;
      Object host = this.getHost();
      result = result * PRIME + (host == null ? 43 : host.hashCode());
      return result;
   }

   public String toString() {
      return "NMapRun(host=" + this.getHost() + ")";
   }
}
