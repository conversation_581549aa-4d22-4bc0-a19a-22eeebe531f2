package com.dcap.classifier.nmap.data;

import java.util.List;

public class Ports {

   private List<Port> port;

   public List<Port> getPort() {
      return this.port;
   }

   public void setPort(List<Port> port) {
      this.port = port;
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Ports)) {
         return false;
      } else {
         Ports other = (Ports)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            Object this$port = this.getPort();
            Object other$port = other.getPort();
            if (this$port == null) {
               if (other$port != null) {
                  return false;
               }
            } else if (!this$port.equals(other$port)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Ports;
   }

   public int hashCode() {
      int PRIME = 59;
      int result = 1;
      Object port = this.getPort();
      result = result * PRIME + (port == null ? 43 : port.hashCode());
      return result;
   }

   public String toString() {
      return "Ports(port=" + this.getPort() + ")";
   }
}
