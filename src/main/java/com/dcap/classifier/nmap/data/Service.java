package com.dcap.classifier.nmap.data;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class Service {
   private String method;
   private Integer conf;
   private String name;
   private String product;
   private String version;

    public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Service)) {
         return false;
      } else {
         Service other = (Service)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            label47: {
               Object this$method = this.getMethod();
               Object other$method = other.getMethod();
               if (this$method == null) {
                  if (other$method == null) {
                     break label47;
                  }
               } else if (this$method.equals(other$method)) {
                  break label47;
               }

               return false;
            }

            Object this$conf = this.getConf();
            Object other$conf = other.getConf();
            if (this$conf == null) {
               if (other$conf != null) {
                  return false;
               }
            } else if (!this$conf.equals(other$conf)) {
               return false;
            }

            Object this$name = this.getName();
            Object other$name = other.getName();
            if (this$name == null) {
               if (other$name != null) {
                  return false;
               }
            } else if (!this$name.equals(other$name)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Service;
   }

   public int hashCode() {
      final int PRIME = 31;
      int result = 1;
      Object method = this.getMethod();
      result = result * PRIME + (method == null ? 43 : method.hashCode());
      Object conf = this.getConf();
      result = result * PRIME + (conf == null ? 43 : conf.hashCode());
      Object name = this.getName();
      result = result * PRIME + (name == null ? 43 : name.hashCode());
      return result;
   }

   public String toString() {
      return "Service(method=" + this.getMethod() + ", conf=" + this.getConf() + ", name=" + this.getName() + ")";
   }
}
