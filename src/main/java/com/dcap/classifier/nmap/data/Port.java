package com.dcap.classifier.nmap.data;

public class Port {
   private String portid;
   private String protocol;
   private Service service;
   private State state;

   public String getPortid() {
      return this.portid;
   }

   public Service getService() {
      return this.service;
   }

   public State getState() {
      return this.state;
   }

   public void setPortid(String portid) {
      this.portid = portid;
   }

   public void setService(Service service) {
      this.service = service;
   }

   public void setState(State state) {
      this.state = state;
   }

   public String getProtocol() {
      return protocol;
   }

   public void setProtocol(String protocol) {
      this.protocol = protocol;
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Port)) {
         return false;
      } else {
         Port other = (Port)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            label59: {
               Object this$portid = this.getPortid();
               Object other$portid = other.getPortid();
               if (this$portid == null) {
                  if (other$portid == null) {
                     break label59;
                  }
               } else if (this$portid.equals(other$portid)) {
                  break label59;
               }

               return false;
            }

            Object this$portocol = this.getProtocol();
            Object other$portocol = other.getProtocol();
            if (this$portocol == null) {
               if (other$portocol != null) {
                  return false;
               }
            } else if (!this$portocol.equals(other$portocol)) {
               return false;
            }

            Object this$service = this.getService();
            Object other$service = other.getService();
            if (this$service == null) {
               if (other$service != null) {
                  return false;
               }
            } else if (!this$service.equals(other$service)) {
               return false;
            }

            Object this$state = this.getState();
            Object other$state = other.getState();
            if (this$state == null) {
               if (other$state != null) {
                  return false;
               }
            } else if (!this$state.equals(other$state)) {
               return false;
            }
            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Port;
   }

   public int hashCode() {
      int PRIME = 59;
      int result = 1;
      Object portid = this.getPortid();
      result = result * PRIME + (portid == null ? 43 : portid.hashCode());
      Object portocol = this.getProtocol();
      result = result * PRIME + (portocol == null ? 43 : portocol.hashCode());
      Object service = this.getService();
      result = result * PRIME + (service == null ? 43 : service.hashCode());
      Object state = this.getState();
      result = result * PRIME + (state == null ? 43 : state.hashCode());
      return result;
   }

   public String toString() {
      return "Port(portid=" + this.getPortid() + ", protocol=" + this.getProtocol() + ", service=" + this.getService() + ", state=" + this.getState() + ")";
   }
}
