package com.dcap.classifier.nmap.data;


public class State {
   private String reason;
   private String state;
   private String reasonTtl;

   public String getReason() {
      return this.reason;
   }

   public String getState() {
      return this.state;
   }

   public String getReasonTtl() {
      return this.reasonTtl;
   }

   public void setReason(String reason) {
      this.reason = reason;
   }

   public void setState(String state) {
      this.state = state;
   }

   public void setReasonTtl(String reasonTtl) {
      this.reasonTtl = reasonTtl;
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof State)) {
         return false;
      } else {
         State other = (State)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            label47: {
               Object this$reason = this.getReason();
               Object other$reason = other.getReason();
               if (this$reason == null) {
                  if (other$reason == null) {
                     break label47;
                  }
               } else if (this$reason.equals(other$reason)) {
                  break label47;
               }

               return false;
            }

            Object this$state = this.getState();
            Object other$state = other.getState();
            if (this$state == null) {
               if (other$state != null) {
                  return false;
               }
            } else if (!this$state.equals(other$state)) {
               return false;
            }

            Object this$reasonTtl = this.getReasonTtl();
            Object other$reasonTtl = other.getReasonTtl();
            if (this$reasonTtl == null) {
               if (other$reasonTtl != null) {
                  return false;
               }
            } else if (!this$reasonTtl.equals(other$reasonTtl)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof State;
   }

   public int hashCode() {
      final int PRIME = 31; // 选择一个质数作为乘数
      int result = 1;
      Object reason = this.getReason();
      result = result * PRIME + (reason == null ? 43 : reason.hashCode());
      Object state = this.getState();
      result = result * PRIME + (state == null ? 43 : state.hashCode());
      Object reasonTtl = this.getReasonTtl();
      result = result * PRIME + (reasonTtl == null ? 43 : reasonTtl.hashCode());
      return result;
   }

   public String toString() {
      return "State(reason=" + this.getReason() + ", state=" + this.getState() + ", reasonTtl=" + this.getReasonTtl() + ")";
   }
}
