package com.dcap.classifier.nmap.data;

public class Address {
   private String addrtype;
   private String addr;

   public String getAddrtype() {
      return this.addrtype;
   }

   public String getAddr() {
      return this.addr;
   }

   public void setAddrtype(String addrtype) {
      this.addrtype = addrtype;
   }

   public void setAddr(String addr) {
      this.addr = addr;
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Address)) {
         return false;
      } else {
         Address other = (Address)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            Object this$addrtype = this.getAddrtype();
            Object other$addrtype = other.getAddrtype();
            if (this$addrtype == null) {
               if (other$addrtype != null) {
                  return false;
               }
            } else if (!this$addrtype.equals(other$addrtype)) {
               return false;
            }

            Object this$addr = this.getAddr();
            Object other$addr = other.getAddr();
            if (this$addr == null) {
               if (other$addr != null) {
                  return false;
               }
            } else if (!this$addr.equals(other$addr)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Address;
   }

   public int hashCode() {
      int PRIME = 59;
      int result = 1;
      Object addrtype = this.getAddrtype();
      result = result * PRIME + (addrtype == null ? 43 : addrtype.hashCode());
      Object addr = this.getAddr();
      result = result * PRIME + (addr == null ? 43 : addr.hashCode());
      return result;
   }

   public String toString() {
      return "Address(addrtype=" + this.getAddrtype() + ", addr=" + this.getAddr() + ")";
   }
}
