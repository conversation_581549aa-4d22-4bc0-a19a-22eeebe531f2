package com.dcap.classifier.nmap;


import com.dcap.classifier.nmap.core.flags.Flag;
import com.dcap.classifier.nmap.core.nmap.ExecutionResults;
import com.dcap.classifier.nmap.core.nmap.NMapExecutionException;
import com.dcap.classifier.nmap.core.nmap.NMapInitializationException;
import com.dcap.classifier.nmap.core.scans.HostDiscovery;
import com.dcap.classifier.nmap.core.scans.ParameterValidationFailureException;
import com.dcap.classifier.nmap.core.scans.Scanner;
import com.dcap.classifier.nmap.core.scans.ServiceDiscovery;
import com.dcap.classifier.nmap.data.*;
import com.dcap.classifier.nmap.net.HostAddress;
import com.dcap.utils.JSON;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.zeroturnaround.process.ProcessUtil;
import org.zeroturnaround.process.Processes;

import java.io.IOException;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;

public class NMapScan {

    private static final Logger logger = LoggerFactory.getLogger(NMapScan.class);

//    public static String NMAP_PATH = "C:/Program Files (x86)/Nmap";
    public static String NMAP_PATH = "/usr";
    static {
        String nmapEnv = System.getenv("NMAP_PATH");
        if (StringUtils.isNotBlank(nmapEnv)) {
            NMAP_PATH = nmapEnv;
        }
    }
    public static void main(String[] args) throws ParameterValidationFailureException, NMapExecutionException, NMapInitializationException {
//      dbScan(Lists.newArrayList("*************","*************"), new Integer[]{8088,80,443,8080,8443,8001,6443});
        String[] addrAry = new String[]{"*************"};
        // 1 发现或者的 ip
        List<String> upHosts = getUpHost(addrAry, (pid)->{
            System.out.println(pid);
            try {
                ProcessUtil.destroyForcefullyAndWait(Processes.newPidProcess(pid.intValue()));
            } catch (IOException | InterruptedException e) {
                throw new RuntimeException(e);
            }
        });
        if (upHosts == null) {
            return;
        }

        List<Host> hosts = dbScan(upHosts, null, (pid)->{
            System.out.println(pid);
        });// new String[]{"31001-31200"}
        System.out.println(JSON.from(hosts).toString());
//        ServiceDiscovery serviceDiscovery = new ServiceDiscovery(upHosts.toArray(new String[]{}), null);
//        serviceDiscovery.setNMapPath(NMAP_PATH);
//        serviceDiscovery.removeFlag(Flag.NORMAL_TIMING);
//        serviceDiscovery.setTiming(Scanner.TimingFlag.INSANE);
//        ExecutionResults executionResults = serviceDiscovery.executeScan();
//        executionResults.getOutput()

    }

    public static List<String> getUpHost(String[] addrAry, Consumer<Long> processCallback) throws NMapExecutionException, NMapInitializationException {
        List<String> result = new ArrayList<>();
        if (addrAry == null || addrAry.length == 0){
            addrAry = getLocalAddresses().stream().map(HostAddress::getCidr).toArray(String[]::new);
        }
        HostDiscovery hostDiscovery = new HostDiscovery(addrAry, null);
        hostDiscovery.setNMapPath(NMAP_PATH);
        hostDiscovery.removeFlag(Flag.NORMAL_TIMING);
        hostDiscovery.setTiming(Scanner.TimingFlag.INSANE);
        ExecutionResults executionResults = hostDiscovery.executeScan(processCallback);
        String output = executionResults.getOutput();
        NMapRun nmapRun = JSON.fromXml(output).toObject(NMapRun.class);
        if (nmapRun == null){
            return null;
        }
        List<Host> hosts = nmapRun.getHost();
        if (hosts == null){
            return null;
        }
        for (Host host : hosts) {
            List<Address> address = host.getAddress();
            if (address == null){
                continue;
            }
            for (Address addr : address) {
                if (Objects.equals("ipv4", addr.getAddrtype())) {
                    result.add(addr.getAddr());
                }
            }
        }
        return result;
    }

    public static List<HostAddress> getLocalAddresses(){
        List<HostAddress> localAddressList = new ArrayList<>();
        try {
            // 获取所有网络接口
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();
                // 过滤掉回环接口和非活动接口
                if (networkInterface.isLoopback() || !networkInterface.isUp()) {
                    continue;
                }
                // 获取网络接口上的所有IP地址
                Enumeration<InetAddress> inetAddresses = networkInterface.getInetAddresses();
                while (inetAddresses.hasMoreElements()) {
                    InetAddress inetAddress = inetAddresses.nextElement();
                    // 只处理 IPv4 地址
                    if (inetAddress instanceof java.net.Inet4Address) {
                        // 获取本机 IP 地址
                        String ipAddress = inetAddress.getHostAddress();
                        // 获取子网掩码
                        int prefixLength = networkInterface.getInterfaceAddresses().get(0).getNetworkPrefixLength();
                        String subnetMask = prefixLengthToSubnetMask(prefixLength);
                        String cidr = ipAddress + "/" + prefixLength;
                        HostAddress hostAddress = new HostAddress(ipAddress, subnetMask, cidr);
                        localAddressList.add(hostAddress);
                    }
                }
            }
        } catch (SocketException e) {
            e.printStackTrace();
        }
        return localAddressList;
    }

    // 将前缀长度转换为子网掩码
    private static String prefixLengthToSubnetMask(int prefixLength) {
        int mask = 0xffffffff << (32 - prefixLength);
        int value = mask;
        byte[] bytes = new byte[]{(byte) (value >>> 24), (byte) (value >>> 16), (byte) (value >>> 8), (byte) value};
        try {
            InetAddress netAddr = InetAddress.getByAddress(bytes);
            return netAddr.getHostAddress();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    public static List<Host> dbScan(List<String> ips, String[] ports, Consumer<Long> processCallback) throws NMapExecutionException, NMapInitializationException {
        List<Host> result = new ArrayList<>();
        String[] ipv4 = ips.toArray(new String[]{});
        ServiceDiscovery discoverer = new ServiceDiscovery(ipv4, null);
        if (ports != null && ports.length > 0) {
            discoverer.addPorts(ports);
        }
        discoverer.setNMapPath(NMAP_PATH);
        discoverer.removeFlag(Flag.NORMAL_TIMING);
        discoverer.setTiming(Scanner.TimingFlag.AGGRESSIVE);

// try {
        ExecutionResults results = discoverer.executeScan(processCallback);
        if (results.hasErrors()) {
            logger.info("[Database scan NMap]: {}", results.getErrors());
        }

        String output = results.getOutput();

        NMapRun nmapRun = JSON.fromXml(output).toObject(NMapRun.class);
        for (Host host : nmapRun.getHost()) {
            Ports ps = host.getPorts();
            if (ps == null){
                continue;
            }
            List<Port> portList = ps.getPort();
            if (portList == null || portList.isEmpty()){
                continue;
            }
            List<Port> openList = portList.stream()
                    .filter(port -> {
                        boolean state = port.getState() != null && Objects.equals(port.getState().getState(), "open");
                        boolean service = port.getService() != null
                                && !Objects.equals(port.getService().getName(), "unknown")
//                                && Objects.equals(port.getService().getMethod(), "probed")
                                ;
                        return state && service;
                    })
                    .collect(Collectors.toList());
            if (openList.isEmpty()){
                continue;
            }
            host.getPorts().setPort(openList);
            result.add(host);
        }
        return result;
    }
}
