package com.dcap.classifier.analyzer;

import com.dcap.classifier.InitializationException;
import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.RuleContext;
import com.yd.rules.engine.result.MatchedResult;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * 基于表指纹的分析器
 */
public class FingerprintAnalyzer implements IntelligentAnalyzer {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private final RecognitionResultCache cache = RecognitionResultCache.getInstance();
    
    @Override
    public Map<String, List<MatchedResult>> analyze(RuleContext context, ContextTable table) {
        if (!table.getTableTypeName().toLowerCase().contains("table")) {
            return null;
        }
        String fingerprint;
        try {
            fingerprint = generateFingerprint(context);
            return cache.get(fingerprint);
        } catch (InitializationException e) {
            logger.error("Failed to generate fingerprint for context: {}", e.getMessage());
        }
        return null;
    }
    
    @Override
    public void cacheResults(RuleContext ruleContext, ContextTable table, Map<String, List<MatchedResult>> results) {
        String fingerprint;
        try {
            fingerprint = generateFingerprint(ruleContext);
            cache.put(fingerprint, results);
        } catch (InitializationException e) {
            logger.error("Failed to generate fingerprint for context: {}", e.getMessage());
        }
    }
    
    private String generateFingerprint(RuleContext context) throws InitializationException {
        StringBuilder sb = new StringBuilder();
        for (ContextColumn column : context.getTableColumns()) {
            sb.append(column.getColumnName()).append("|").append(column.getTypename()).append("|").append(column.getSize()).append("; ");
        }
        return DigestUtils.md5Hex(sb.toString());
    }
}