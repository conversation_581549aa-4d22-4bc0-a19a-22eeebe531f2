package com.dcap.classifier.analyzer;

import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.RuleContext;
import com.dcap.datalayer.DataSourceType;
import com.dcap.sqllineage.LineageReport;
import com.dcap.sqllineage.SQLLineageRunner;
import com.yd.rules.engine.result.MatchedResult;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 基于血缘分析的分析器
 */
public class LineageAnalyzer implements IntelligentAnalyzer {

    private static final Logger LOG = LoggerFactory.getLogger(LineageAnalyzer.class);

    private final RecognitionResultCache cache = RecognitionResultCache.getInstance();
    
    @Override
    public Map<String, List<MatchedResult>> analyze(RuleContext context, ContextTable table) {
        if (!"view".equalsIgnoreCase(table.getTableTypeName())) {
            return null;
        }
        
        try {
            String viewSql = getViewDefinition(context, table);
            if (viewSql == null) {
                return null;
            }

            Map<String,List<MatchedResult>> result = new HashMap<>();
            Map<String, Map<String, String>> dependentTables = parseViewDependencies(context, viewSql);
            // 检查所有依赖表是否都有缓存结果
            boolean allCached = true;
            for (Map.Entry<String,Map<String,String>> dependentTable : dependentTables.entrySet()) {
                String dependentTableName = dependentTable.getKey();
                Map<String, List<MatchedResult>> tableMatchedResult = cache.getIfPresent(dependentTableName);
                if (tableMatchedResult == null) {
                    allCached = false;
                    LOG.warn("【LineageAnalyzer】No cached results for dependent table [{}] for view : {}. Maybe the table does not have marked columns",
                            dependentTableName, table.getIdentityName());
                    continue;
                }
                // 保存了列名和视图列名的映射
                Map<String, String> columnsMap = dependentTable.getValue();
                for (Map.Entry<String, List<MatchedResult>> columnMatchedResult : tableMatchedResult.entrySet()) {
                    String columnName = columnMatchedResult.getKey();
                    List<MatchedResult> matchedResults = columnMatchedResult.getValue();
                    String viewColumnName = columnsMap.get(columnName);
                    // 如果视图列名不为空，说明找到缓存的结果
                    if (StringUtils.isNotBlank(viewColumnName)){
                        result.put(viewColumnName, matchedResults);
                    }
                }
            }
            
            if (!allCached) {
                LOG.warn("Not all dependent tables have cached results, analyzing view lineage for view: {}", table.getIdentityName());
            }
            return result;
        } catch (Exception e) {
            LOG.error("Error analyzing view lineage for table: {}", table.getTableName(), e);
        }
        return null;
    }
    
    @Override
    public void cacheResults(RuleContext ruleContext, ContextTable table, Map<String, List<MatchedResult>> results) {
        // 使用表名作为缓存键
        String cacheKey = table.getIdentityName();
        cache.put(cacheKey, results);
    }
    
private String getViewDefinition(RuleContext context, ContextTable table) {
    try {
        DataSourceType dataSourceType = context.getDatasource().getType();
        ContextTable view = table;
        if (table.getTableType().equals(ContextTable.SYNONYM)) {
            ContextTable referencedTable = table.getReference();
            if (referencedTable != null && "view".equalsIgnoreCase(referencedTable.getTableTypeName())) {
                view = referencedTable;
            }
        }
        return ViewDefinitionProvider.getViewDefinition(dataSourceType, context, view);
    } catch (Exception e) {
        LOG.error("Error getting view definition for: {}", table.getTableName(), e);
        return null;
    }
}
    
    private Map<String,Map<String,String>> parseViewDependencies(RuleContext context, String viewSql) {
        Map<String, Map<String, String>> result = new HashMap<>();
        // TODO: 实现解析视图 SQL 获取依赖表的逻辑
        // 对 view 定义的 sql 进行血缘分析。
        LineageReport lineageReport = new SQLLineageRunner.Builder()
                .sqlType("view")
                .vendor(context.getDatasource().getType())
                .build()
                .run(viewSql);
        for (LineageReport.LineageReportItem lineageItem : lineageReport.getLineageItems()) {
            String sourceTableName = lineageItem.getSourceTable();
            String dependentTableName = mergeStrings(context.getCurrentTable().getIdentityPrefix(), sourceTableName);
            Map<String, String> dependentTableMap = result.computeIfAbsent(dependentTableName, k -> new HashMap<>());
            String sourceColumnName = lineageItem.getSourceColumn();
            String viewColumnName = lineageItem.getTargetColumn();
            dependentTableMap.put(sourceColumnName, viewColumnName);
        }
        return result;
    }
    public static String mergeStrings(String str1, String str2) {
        // 如果两个字符串完全相同
        if (str1.equals(str2)) {
            return str1;
        }

        // 找到最大重叠部分
        int overlapIndex = 0;
        for (int i = 1; i <= Math.min(str1.length(), str2.length()); i++) {
            if (str1.endsWith(str2.substring(0, i))) {
                overlapIndex = i;
            }
        }

        return str1 + str2.substring(overlapIndex);
    }

    private void printCacheSize() {
        try {
            Map<String, Object> stats = cache.stats();
            long actualSize = (long) stats.get("actualSize");
            
            LOG.info("缓存统计 - 条目数: {}, 实际大小: {}, 命中率: {}, 命中次数: {}, 未命中次数: {}", 
                stats.get("size"), 
                formatSize(actualSize),
                String.format("%.2f%%", (double)stats.get("hitRate") * 100),
                stats.get("hitCount"),
                stats.get("missCount"));
        } catch (Exception e) {
            LOG.error("计算缓存大小时发生错误", e);
        }
    }

    private String formatSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", size / (1024.0 * 1024 * 1024));
        }
    }
}
