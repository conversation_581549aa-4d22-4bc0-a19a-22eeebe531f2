package com.dcap.classifier.analyzer;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.yd.rules.engine.result.MatchedResult;
import org.openjdk.jol.info.GraphLayout;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * 识别结果缓存,采用单例模式
 * 使用 LRU 策略
 */
public class RecognitionResultCache {
    private static final Logger LOG = LoggerFactory.getLogger(RecognitionResultCache.class);
    private static final RecognitionResultCache INSTANCE = new RecognitionResultCache();
    private static final int MAX_CACHE_SIZE = 100000;
    
    private final LoadingCache<String, Map<String, List<MatchedResult>>> cache;
    
    private RecognitionResultCache() {
        this.cache = CacheBuilder.newBuilder()
            .maximumSize(MAX_CACHE_SIZE) // 限制缓存大小为10
            .recordStats() // 开启统计
            .removalListener(notification -> 
                LOG.debug("Cache entry removed. Key: {}, Cause: {}", 
                    notification.getKey(), notification.getCause()))
            .build(new CacheLoader<String, Map<String, List<MatchedResult>>>() {
                @Override
                public Map<String, List<MatchedResult>> load(String key) {
                    return null;
                }
            });
    }
    
    public static RecognitionResultCache getInstance() {
        return INSTANCE;
    }
    
    public Map<String, List<MatchedResult>> get(String key) {
        try {
            return cache.get(key);
        } catch (Exception e) {
            LOG.debug("Cache miss for key: {}", key);
            return null;
        }
    }
    
    public void put(String key, Map<String, List<MatchedResult>> results) {
        cache.put(key, results);
        LOG.debug("Cached results for key: {}", key);
    }
    
    public Map<String, List<MatchedResult>> getIfPresent(String key) {
        return cache.getIfPresent(key);
    }

    /**
     * 清理所有缓存
     */
    public void clearAll() {
        cache.invalidateAll();
        LOG.info("All cache entries have been cleared");
    }

    /**
     * 获取缓存大小
     */
    public long size() {
        return cache.size();
    }

    /**
     * 获取缓存的实际内存占用
     * @return 返回缓存占用的字节数
     */
    public long getActualSize() {
        try {
            // 使用JOL计算整个缓存对象的内存占用
            return GraphLayout.parseInstance(cache).totalSize();
        } catch (Exception e) {
            LOG.error("计算缓存实际大小时发生错误", e);
            return -1;
        }
    }

    /**
     * 获取缓存统计信息，包含实际内存占用
     */
    public Map<String, Object> stats() {
        return Map.of(
            "size", cache.size(),
            "hitCount", cache.stats().hitCount(),
            "missCount", cache.stats().missCount(),
            "hitRate", cache.stats().hitRate(),
            "actualSize", getActualSize()
        );
    }
} 