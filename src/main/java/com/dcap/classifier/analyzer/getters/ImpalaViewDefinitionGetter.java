package com.dcap.classifier.analyzer.getters;

import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.RuleContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSet;
import java.sql.Statement;

/**
 * Impala视图定义获取器
 * 
 * 支持Apache Impala 2.x+版本的视图定义获取
 * 使用多种策略确保兼容性：
 * 1. SHOW CREATE VIEW - 标准方式（推荐）
 * 2. DESCRIBE FORMATTED - 备用方式
 * 3. 优雅降级 - 不支持的情况返回null
 * 
 * 特别处理：
 * - Impala的数据库概念对应Hive的database
 * - 支持跨数据库视图引用
 * - 处理Kerberos认证环境
 */
public class ImpalaViewDefinitionGetter implements ViewDefinitionGetter {

    private static final Logger LOG = LoggerFactory.getLogger(ImpalaViewDefinitionGetter.class);

    @Override
    public String getViewDefinition(RuleContext context, ContextTable view) {
        String databaseName = view.getCatalog() != null ? view.getCatalog() : view.getSchema();
        String viewName = view.getTableName();
        
        // 构建完整的视图名称
        String fullViewName = databaseName != null ? databaseName + "." + viewName : viewName;
        
        LOG.debug("开始获取Impala视图定义：{}", fullViewName);
        
        // 策略1：使用 SHOW CREATE VIEW（推荐方式）
        String definition = tryShowCreateView(context, fullViewName);
        if (definition != null) {
            LOG.debug("通过SHOW CREATE VIEW成功获取视图定义：{}", fullViewName);
            return definition;
        }
        
        // 策略2：使用 DESCRIBE FORMATTED（备用方式）
        definition = tryDescribeFormatted(context, fullViewName);
        if (definition != null) {
            LOG.debug("通过DESCRIBE FORMATTED成功获取视图定义：{}", fullViewName);
            return definition;
        }
        
        // 策略3：尝试查询Hive Metastore系统表（需要特殊配置）
        definition = tryMetastoreQuery(context, databaseName, viewName);
        if (definition != null) {
            LOG.debug("通过metastore查询成功获取视图定义：{}", fullViewName);
            return definition;
        }
        
        LOG.warn("未能获取Impala视图定义：{}，可能是版本不支持或权限不足", fullViewName);
        return null;
    }
    
    /**
     * 策略1：使用SHOW CREATE VIEW命令
     * 这是获取视图定义的标准方式，适用于Impala 2.x+
     */
    private String tryShowCreateView(RuleContext context, String fullViewName) {
        try (Statement stmt = context.getConnection().createStatement()) {
            String sql = String.format("SHOW CREATE VIEW %s", fullViewName);
            LOG.debug("执行Impala SHOW CREATE VIEW查询：{}", sql);
            
            try (ResultSet rs = stmt.executeQuery(sql)) {
                StringBuilder createStatement = new StringBuilder();
                while (rs.next()) {
                    // SHOW CREATE VIEW返回多行，每行是DDL的一部分
                    String line = rs.getString(1); // 通常第一列包含DDL内容
                    if (line != null) {
                        createStatement.append(line).append("\n");
                    }
                }
                
                String fullStatement = createStatement.toString().trim();
                if (!fullStatement.isEmpty()) {
                    // 提取视图定义部分（AS后面的SELECT语句）
                    return extractViewDefinitionFromCreateStatement(fullStatement);
                }
            }
        } catch (Exception e) {
            LOG.debug("SHOW CREATE VIEW查询失败：{}", e.getMessage());
        }
        return null;
    }
    
    /**
     * 策略2：使用DESCRIBE FORMATTED命令
     * 某些Impala版本或配置下，这个命令可能包含视图定义信息
     */
    private String tryDescribeFormatted(RuleContext context, String fullViewName) {
        try (Statement stmt = context.getConnection().createStatement()) {
            String sql = String.format("DESCRIBE FORMATTED %s", fullViewName);
            LOG.debug("执行Impala DESCRIBE FORMATTED查询：{}", sql);
            
            try (ResultSet rs = stmt.executeQuery(sql)) {
                boolean foundViewDefinition = false;
                StringBuilder viewDefinition = new StringBuilder();
                
                while (rs.next()) {
                    String columnName = rs.getString(1);
                    String dataType = rs.getString(2);
                    String comment = rs.getString(3);
                    
                    // 查找视图定义相关的行
                    if (columnName != null) {
                        String lowerColumnName = columnName.toLowerCase().trim();
                        if (lowerColumnName.contains("view") && lowerColumnName.contains("text")) {
                            foundViewDefinition = true;
                        }
                        if (foundViewDefinition && dataType != null && !dataType.trim().isEmpty()) {
                            viewDefinition.append(dataType.trim()).append(" ");
                        }
                    }
                }
                
                String definition = viewDefinition.toString().trim();
                if (!definition.isEmpty()) {
                    return definition;
                }
            }
        } catch (Exception e) {
            LOG.debug("DESCRIBE FORMATTED查询失败：{}", e.getMessage());
        }
        return null;
    }
    
    /**
     * 策略3：查询Hive Metastore系统表
     * 需要对底层Hive Metastore的访问权限
     * 这是最后的降级策略
     */
    private String tryMetastoreQuery(RuleContext context, String databaseName, String viewName) {
        try (Statement stmt = context.getConnection().createStatement()) {
            // 尝试查询Hive的系统表获取视图定义
            // 注意：这需要特殊的权限配置和Impala版本支持
            String sql = String.format(
                "SELECT param_value FROM " +
                "(SELECT t.tbl_id, tp.param_key, tp.param_value " +
                " FROM tbls t JOIN table_params tp ON t.tbl_id = tp.tbl_id " +
                " JOIN dbs d ON t.db_id = d.db_id " +
                " WHERE d.name = '%s' AND t.tbl_name = '%s' " +
                " AND t.tbl_type = 'VIRTUAL_VIEW') view_params " +
                "WHERE param_key IN ('impala_view_definition', 'view.definition', 'spark.sql.view.definition')",
                databaseName != null ? databaseName : "default",
                viewName
            );
            
            LOG.debug("执行Impala metastore查询：{}", sql);
            try (ResultSet rs = stmt.executeQuery(sql)) {
                if (rs.next()) {
                    String definition = rs.getString("param_value");
                    if (definition != null && !definition.trim().isEmpty()) {
                        return definition.trim();
                    }
                }
            }
        } catch (Exception e) {
            LOG.debug("metastore查询失败，可能没有访问权限或不支持：{}", e.getMessage());
        }
        return null;
    }
    
    /**
     * 从CREATE VIEW语句中提取视图定义
     * 输入：CREATE VIEW view_name AS SELECT ...
     * 输出：SELECT ...
     */
    private String extractViewDefinitionFromCreateStatement(String createStatement) {
        if (createStatement == null || createStatement.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 查找 AS 关键字的位置
            String upperStatement = createStatement.toUpperCase();
            
            // 使用正则表达式匹配CREATE VIEW ... AS模式
            int asIndex = -1;
            String[] lines = upperStatement.split("\\s+");
            
            boolean foundCreateView = false;
            for (int i = 0; i < lines.length - 1; i++) {
                if ("CREATE".equals(lines[i]) && "VIEW".equals(lines[i + 1])) {
                    foundCreateView = true;
                }
                if (foundCreateView && "AS".equals(lines[i])) {
                    // 找到AS关键字在原始字符串中的位置
                    String beforeAS = String.join(" ", java.util.Arrays.copyOfRange(lines, 0, i));
                    asIndex = findStringPosition(createStatement.toUpperCase(), beforeAS + " AS");
                    if (asIndex >= 0) {
                        asIndex += beforeAS.length() + 3; // 3是" AS"的长度
                        break;
                    }
                }
            }
            
            if (asIndex > 0 && asIndex < createStatement.length()) {
                String viewDefinition = createStatement.substring(asIndex).trim();
                return viewDefinition;
            }
            
            // 如果无法解析AS位置，尝试简单的字符串匹配
            int simpleAsIndex = upperStatement.indexOf(" AS ");
            if (simpleAsIndex > 0) {
                String viewDefinition = createStatement.substring(simpleAsIndex + 4).trim();
                if (!viewDefinition.isEmpty()) {
                    return viewDefinition;
                }
            }
            
            // 如果无法解析，返回原始语句
            LOG.warn("无法从CREATE VIEW语句中提取视图定义，返回完整语句");
            return createStatement;
            
        } catch (Exception e) {
            LOG.warn("解析CREATE VIEW语句时出错：{}", e.getMessage());
            return createStatement;
        }
    }
    
    /**
     * 辅助方法：在字符串中查找子字符串的位置
     */
    private int findStringPosition(String source, String target) {
        if (source == null || target == null) {
            return -1;
        }
        return source.indexOf(target);
    }
}
