package com.dcap.classifier.analyzer.getters;

import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.RuleContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSet;
import java.sql.Statement;

public class OracleViewDefinitionGetter implements ViewDefinitionGetter {

    private static final Logger LOG = LoggerFactory.getLogger(OracleViewDefinitionGetter.class);

    @Override
    public String getViewDefinition(RuleContext context, ContextTable view) {
        try (Statement stmt = context.getConnection().createStatement()) {
            String sql = String.format("SELECT DBMS_METADATA.GET_DDL('VIEW', '%s', '%s') AS ddl FROM dual",
                    view.getTableName(), view.getCatalog());
            try (ResultSet rs = stmt.executeQuery(sql)) {
                if (rs.next()) {
                    return rs.getString("ddl");
                }
            }
        } catch (Exception e) {
            LOG.error("错误获取 oracle 视图的视图定义：{}", view.getTableName(), e);
        }
        return null;
    }
}
