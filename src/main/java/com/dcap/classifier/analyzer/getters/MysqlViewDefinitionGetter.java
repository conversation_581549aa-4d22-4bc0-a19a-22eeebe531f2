package com.dcap.classifier.analyzer.getters;

import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.RuleContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSet;
import java.sql.Statement;

public class MysqlViewDefinitionGetter implements ViewDefinitionGetter {

    private static final Logger LOG = LoggerFactory.getLogger(MysqlViewDefinitionGetter.class);

    @Override
    public String getViewDefinition(RuleContext context, ContextTable view) {
        try (Statement stmt = context.getConnection().createStatement()) {
            String sql = String.format("SELECT TABLE_NAME, VIEW_DEFINITION FROM information_schema.views \n" +
                    "WHERE TABLE_SCHEMA = '%s' AND TABLE_NAME = '%s'", view.getCatalog(), view.getTableName());
            try (ResultSet rs = stmt.executeQuery(sql)) {
                if (rs.next()) {
                    return rs.getString("VIEW_DEFINITION");
                }
            }
        } catch (Exception e) {
            LOG.error("错误获取 MySQL 视图的视图定义：{}", view.getTableName(), e);
        }
        return null;
    }
}
