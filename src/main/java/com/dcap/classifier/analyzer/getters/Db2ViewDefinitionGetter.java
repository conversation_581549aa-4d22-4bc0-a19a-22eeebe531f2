package com.dcap.classifier.analyzer.getters;

import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.RuleContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSet;
import java.sql.Statement;

/**
 * DB2视图定义获取器
 * 
 * 支持IBM DB2数据库的视图定义获取
 * 使用多种策略确保兼容性：
 * 1. SYSCAT.VIEWS - 标准方式（DB2 9.x+）
 * 2. INFORMATION_SCHEMA.VIEWS - SQL标准方式（DB2 9.7+）
 * 3. SYSIBM.SYSVIEWS - 传统兼容方式
 */
public class Db2ViewDefinitionGetter implements ViewDefinitionGetter {

    private static final Logger LOG = LoggerFactory.getLogger(Db2ViewDefinitionGetter.class);

    @Override
    public String getViewDefinition(RuleContext context, ContextTable view) {
        String schemaName = view.getSchema();
        String viewName = view.getTableName();
        
        // DB2中，如果没有指定schema，通常使用当前用户名作为schema
        if (schemaName == null || schemaName.trim().isEmpty()) {
            schemaName = getCurrentSchema(context);
        }
        
        LOG.debug("开始获取DB2视图定义：{}.{}", schemaName, viewName);
        
        // 策略1：使用 SYSCAT.VIEWS（推荐方式，DB2 9.x+）
        String definition = trySyscatViews(context, schemaName, viewName);
        if (definition != null) {
            LOG.debug("通过SYSCAT.VIEWS成功获取视图定义：{}.{}", schemaName, viewName);
            return definition;
        }
        
        // 策略2：使用 INFORMATION_SCHEMA.VIEWS（SQL标准方式）
        definition = tryInformationSchema(context, schemaName, viewName);
        if (definition != null) {
            LOG.debug("通过INFORMATION_SCHEMA成功获取视图定义：{}.{}", schemaName, viewName);
            return definition;
        }
        
        // 策略3：使用 SYSIBM.SYSVIEWS（传统方式）
        definition = trySysibmSysviews(context, schemaName, viewName);
        if (definition != null) {
            LOG.debug("通过SYSIBM.SYSVIEWS成功获取视图定义：{}.{}", schemaName, viewName);
            return definition;
        }
        
        LOG.warn("未能获取DB2视图定义：{}.{}，可能是权限不足或视图不存在", schemaName, viewName);
        return null;
    }
    
    /**
     * 策略1：使用SYSCAT.VIEWS系统表
     * 这是DB2获取视图定义的标准方式，适用于DB2 9.x+
     */
    private String trySyscatViews(RuleContext context, String schemaName, String viewName) {
        try (Statement stmt = context.getConnection().createStatement()) {
            String sql = String.format(
                "SELECT TEXT FROM SYSCAT.VIEWS " +
                "WHERE VIEWSCHEMA = '%s' AND VIEWNAME = '%s'",
                schemaName.toUpperCase(), viewName.toUpperCase()
            );
            
            LOG.debug("执行DB2 SYSCAT.VIEWS查询：{}", sql);
            try (ResultSet rs = stmt.executeQuery(sql)) {
                if (rs.next()) {
                    String definition = rs.getString("TEXT");
                    if (definition != null && !definition.trim().isEmpty()) {
                        return definition.trim();
                    }
                }
            }
        } catch (Exception e) {
            LOG.debug("SYSCAT.VIEWS查询失败：{}", e.getMessage());
        }
        return null;
    }
    
    /**
     * 策略2：使用INFORMATION_SCHEMA.VIEWS
     * SQL标准方式，适用于DB2 9.7+
     */
    private String tryInformationSchema(RuleContext context, String schemaName, String viewName) {
        try (Statement stmt = context.getConnection().createStatement()) {
            String sql = String.format(
                "SELECT VIEW_DEFINITION FROM INFORMATION_SCHEMA.VIEWS " +
                "WHERE TABLE_SCHEMA = '%s' AND TABLE_NAME = '%s'",
                schemaName.toUpperCase(), viewName.toUpperCase()
            );
            
            LOG.debug("执行DB2 INFORMATION_SCHEMA查询：{}", sql);
            try (ResultSet rs = stmt.executeQuery(sql)) {
                if (rs.next()) {
                    String definition = rs.getString("VIEW_DEFINITION");
                    if (definition != null && !definition.trim().isEmpty()) {
                        return definition.trim();
                    }
                }
            }
        } catch (Exception e) {
            LOG.debug("INFORMATION_SCHEMA查询失败：{}", e.getMessage());
        }
        return null;
    }
    
    /**
     * 策略3：使用SYSIBM.SYSVIEWS
     * 传统的DB2系统表，用于兼容老版本
     */
    private String trySysibmSysviews(RuleContext context, String schemaName, String viewName) {
        try (Statement stmt = context.getConnection().createStatement()) {
            // SYSIBM.SYSVIEWS表的结构可能因版本而异
            String sql = String.format(
                "SELECT TEXT FROM SYSIBM.SYSVIEWS " +
                "WHERE CREATOR = '%s' AND NAME = '%s'",
                schemaName.toUpperCase(), viewName.toUpperCase()
            );
            
            LOG.debug("执行DB2 SYSIBM.SYSVIEWS查询：{}", sql);
            try (ResultSet rs = stmt.executeQuery(sql)) {
                if (rs.next()) {
                    String definition = rs.getString("TEXT");
                    if (definition != null && !definition.trim().isEmpty()) {
                        return definition.trim();
                    }
                }
            }
        } catch (Exception e) {
            LOG.debug("SYSIBM.SYSVIEWS查询失败：{}", e.getMessage());
        }
        return null;
    }
    
    /**
     * 获取当前schema名称
     * 如果没有明确指定schema，DB2通常使用当前用户名作为默认schema
     */
    private String getCurrentSchema(RuleContext context) {
        try (Statement stmt = context.getConnection().createStatement()) {
            String sql = "VALUES(CURRENT SCHEMA)";
            LOG.debug("获取DB2当前schema：{}", sql);
            
            try (ResultSet rs = stmt.executeQuery(sql)) {
                if (rs.next()) {
                    String currentSchema = rs.getString(1);
                    if (currentSchema != null && !currentSchema.trim().isEmpty()) {
                        LOG.debug("DB2当前schema：{}", currentSchema);
                        return currentSchema.trim().toUpperCase();
                    }
                }
            }
        } catch (Exception e) {
            LOG.debug("获取当前schema失败，使用默认值：{}", e.getMessage());
        }
        
        // 如果无法获取当前schema，返回一个常用的默认值
        return "DB2INST1"; // DB2的常见默认实例用户
    }
    
    /**
     * 清理和格式化视图定义
     * DB2的视图定义可能包含额外的空白字符和格式化字符
     */
    private String cleanViewDefinition(String definition) {
        if (definition == null || definition.trim().isEmpty()) {
            return null;
        }
        
        String cleaned = definition.trim();
        
        // 移除可能的CREATE VIEW前缀（如果存在）
        if (cleaned.toUpperCase().startsWith("CREATE VIEW")) {
            int asIndex = cleaned.toUpperCase().indexOf(" AS ");
            if (asIndex > 0 && asIndex + 4 < cleaned.length()) {
                cleaned = cleaned.substring(asIndex + 4).trim();
            }
        }
        
        // 移除多余的换行和空格
        cleaned = cleaned.replaceAll("\\s+", " ");
        
        return cleaned;
    }
}
