package com.dcap.classifier.analyzer.getters;

import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.RuleContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSet;
import java.sql.Statement;

/**
 * Hive视图定义获取器
 * 
 * 支持Hive 2.x+版本的视图定义获取
 * 使用多种策略确保兼容性：
 * 1. SHOW CREATE TABLE - 标准方式（Hive 2.x+）
 * 2. 查询系统表 - 备用方式
 * 3. 优雅降级 - 不支持视图的版本返回null
 */
public class HiveViewDefinitionGetter implements ViewDefinitionGetter {

    private static final Logger LOG = LoggerFactory.getLogger(HiveViewDefinitionGetter.class);

    @Override
    public String getViewDefinition(RuleContext context, ContextTable view) {
        String databaseName = view.getCatalog() != null ? view.getCatalog() : view.getSchema();
        String viewName = view.getTableName();
        
        // 构建完整的视图名称
        String fullViewName = databaseName != null ? databaseName + "." + viewName : viewName;
        
        LOG.debug("开始获取Hive视图定义：{}", fullViewName);
        
        // 策略1：使用 SHOW CREATE TABLE（推荐方式，Hive 2.x+）
        String definition = tryShowCreateTable(context, fullViewName);
        if (definition != null) {
            LOG.debug("通过SHOW CREATE TABLE成功获取视图定义：{}", fullViewName);
            return definition;
        }
        
        // 策略2：查询information_schema（如果启用）
        definition = tryInformationSchema(context, databaseName, viewName);
        if (definition != null) {
            LOG.debug("通过information_schema成功获取视图定义：{}", fullViewName);
            return definition;
        }
        
        // 策略3：直接查询metastore系统表（需要特殊权限）
        definition = tryMetastoreQuery(context, databaseName, viewName);
        if (definition != null) {
            LOG.debug("通过metastore查询成功获取视图定义：{}", fullViewName);
            return definition;
        }
        
        LOG.warn("未能获取Hive视图定义：{}，可能是版本不支持或权限不足", fullViewName);
        return null;
    }
    
    /**
     * 策略1：使用SHOW CREATE TABLE命令
     * 这是获取视图定义的标准方式，适用于Hive 2.x+
     */
    private String tryShowCreateTable(RuleContext context, String fullViewName) {
        try (Statement stmt = context.getConnection().createStatement()) {
            String sql = String.format("SHOW CREATE TABLE %s", fullViewName);
            LOG.debug("执行Hive SHOW CREATE TABLE查询：{}", sql);
            
            try (ResultSet rs = stmt.executeQuery(sql)) {
                StringBuilder createStatement = new StringBuilder();
                while (rs.next()) {
                    // SHOW CREATE TABLE返回多行，每行是DDL的一部分
                    String line = rs.getString("createtab_stmt");
                    if (line == null) {
                        // 有些Hive版本列名不同，尝试第一列
                        line = rs.getString(1);
                    }
                    if (line != null) {
                        createStatement.append(line).append("\n");
                    }
                }
                
                String fullStatement = createStatement.toString().trim();
                if (!fullStatement.isEmpty()) {
                    // 提取视图定义部分（AS后面的SELECT语句）
                    return extractViewDefinitionFromCreateStatement(fullStatement);
                }
            }
        } catch (Exception e) {
            LOG.debug("SHOW CREATE TABLE查询失败：{}", e.getMessage());
        }
        return null;
    }
    
    /**
     * 策略2：查询information_schema.views
     * 某些Hive配置启用了information_schema
     */
    private String tryInformationSchema(RuleContext context, String databaseName, String viewName) {
        try (Statement stmt = context.getConnection().createStatement()) {
            String sql = String.format(
                "SELECT view_definition FROM information_schema.views " +
                "WHERE table_schema = '%s' AND table_name = '%s'",
                databaseName != null ? databaseName : "default",
                viewName
            );
            
            LOG.debug("执行Hive information_schema查询：{}", sql);
            try (ResultSet rs = stmt.executeQuery(sql)) {
                if (rs.next()) {
                    String definition = rs.getString("view_definition");
                    if (definition != null && !definition.trim().isEmpty()) {
                        return definition.trim();
                    }
                }
            }
        } catch (Exception e) {
            LOG.debug("information_schema查询失败：{}", e.getMessage());
        }
        return null;
    }
    
    /**
     * 策略3：直接查询Hive Metastore
     * 需要对metastore数据库的访问权限
     */
    private String tryMetastoreQuery(RuleContext context, String databaseName, String viewName) {
        try (Statement stmt = context.getConnection().createStatement()) {
            // 尝试查询Hive的系统表获取视图定义
            // 注意：这需要特殊的权限配置
            String sql = String.format(
                "SELECT param_value FROM " +
                "(SELECT t.tbl_id, tp.param_key, tp.param_value " +
                " FROM tbls t JOIN table_params tp ON t.tbl_id = tp.tbl_id " +
                " JOIN dbs d ON t.db_id = d.db_id " +
                " WHERE d.name = '%s' AND t.tbl_name = '%s' " +
                " AND t.tbl_type = 'VIRTUAL_VIEW') view_params " +
                "WHERE param_key IN ('presto_view_definition', 'spark.sql.view.definition', 'view.definition')",
                databaseName != null ? databaseName : "default",
                viewName
            );
            
            LOG.debug("执行Hive metastore查询：{}", sql);
            try (ResultSet rs = stmt.executeQuery(sql)) {
                if (rs.next()) {
                    String definition = rs.getString("param_value");
                    if (definition != null && !definition.trim().isEmpty()) {
                        return definition.trim();
                    }
                }
            }
        } catch (Exception e) {
            LOG.debug("metastore查询失败，可能没有访问权限：{}", e.getMessage());
        }
        return null;
    }
    
    /**
     * 从CREATE VIEW语句中提取视图定义
     * 输入：CREATE VIEW view_name AS SELECT ...
     * 输出：SELECT ...
     */
    private String extractViewDefinitionFromCreateStatement(String createStatement) {
        if (createStatement == null || createStatement.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 查找 AS 关键字的位置
            String upperStatement = createStatement.toUpperCase();
            int asIndex = -1;
            
            // 寻找CREATE VIEW ... AS 模式中的AS
            String[] keywords = {"CREATE", "VIEW"};
            boolean foundCreateView = false;
            String[] lines = upperStatement.split("\\s+");
            
            for (int i = 0; i < lines.length - 1; i++) {
                if ("CREATE".equals(lines[i]) && "VIEW".equals(lines[i + 1])) {
                    foundCreateView = true;
                }
                if (foundCreateView && "AS".equals(lines[i])) {
                    // 计算AS在原始字符串中的位置
                    String beforeAS = String.join(" ", java.util.Arrays.copyOfRange(lines, 0, i + 1));
                    asIndex = createStatement.toUpperCase().indexOf(beforeAS) + beforeAS.length();
                    break;
                }
            }
            
            if (asIndex > 0 && asIndex < createStatement.length()) {
                String viewDefinition = createStatement.substring(asIndex).trim();
                // 移除开头的AS
                if (viewDefinition.toUpperCase().startsWith("AS ")) {
                    viewDefinition = viewDefinition.substring(3).trim();
                }
                return viewDefinition;
            }
            
            // 如果无法解析，返回原始语句
            LOG.warn("无法从CREATE语句中提取视图定义，返回完整语句");
            return createStatement;
            
        } catch (Exception e) {
            LOG.warn("解析CREATE VIEW语句时出错：{}", e.getMessage());
            return createStatement;
        }
    }
}
