package com.dcap.classifier.analyzer.getters;

import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.RuleContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSet;
import java.sql.Statement;

public class SqlServerViewDefinitionGetter implements ViewDefinitionGetter {

    private static final Logger LOG = LoggerFactory.getLogger(SqlServerViewDefinitionGetter.class);

    @Override
    public String getViewDefinition(RuleContext context, ContextTable view) {
        try (Statement stmt = context.getConnection().createStatement()) {
            String schemaName = view.getSchema() != null ? view.getSchema() : "dbo";
            String viewName = view.getTableName();

            // SQL Server 使用 sys.views 和 sys.sql_modules 联合查询
            String sql = String.format(
                    "SELECT m.definition " +
                            "FROM sys.views v " +
                            "INNER JOIN sys.sql_modules m ON v.object_id = m.object_id " +
                            "INNER JOIN sys.schemas s ON v.schema_id = s.schema_id " +
                            "WHERE s.name = '%s' AND v.name = '%s'",
                    schemaName, viewName
            );

            LOG.debug("执行 SQL Server 视图定义查询： {}", sql);
            try (ResultSet rs = stmt.executeQuery(sql)) {
                if (rs.next()) {
                    String definition = rs.getString("definition");
                    if (definition != null && !definition.trim().isEmpty()) {
                        // SQL Server 返回的定义可能包含 CREATE VIEW 语句，需要清理
                        return definition;
                    }
                }
            }

            // 如果上面的查询失败，尝试使用 INFORMATION_SCHEMA.VIEWS (如果可用)
            String fallbackSql = String.format(
                    "SELECT VIEW_DEFINITION FROM INFORMATION_SCHEMA.VIEWS " +
                            "WHERE TABLE_SCHEMA = '%s' AND TABLE_NAME = '%s'",
                    schemaName, viewName
            );

            LOG.debug("执行SQL Server后备查询：{}", fallbackSql);
            try (ResultSet rs = stmt.executeQuery(fallbackSql)) {
                if (rs.next()) {
                    String definition = rs.getString("VIEW_DEFINITION");
                    if (definition != null && !definition.trim().isEmpty()) {
                        return definition;
                    }
                }
            }

            LOG.warn("没有找到 SQL Server 视图的视图定义： {}.{}", schemaName, viewName);
        } catch (Exception e) {
            LOG.error("错误获取 SQL Server 视图的视图定义： {}.{}",
                    view.getSchema(), view.getTableName(), e);
        }
        return null;
    }
}
