package com.dcap.classifier.analyzer.getters;

import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.RuleContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSet;
import java.sql.Statement;

public class PostgreSqlViewDefinitionGetter implements ViewDefinitionGetter {

    private static final Logger LOG = LoggerFactory.getLogger(PostgreSqlViewDefinitionGetter.class);

    @Override
    public String getViewDefinition(RuleContext context, ContextTable view) {
        try (Statement stmt = context.getConnection().createStatement()) {
            // PostgreSQL 使用 information_schema.views，但需要处理 schema 概念
            String schemaName = view.getSchema() != null ? view.getSchema() : "public";
            String viewName = view.getTableName();

            // 优先尝试从 information_schema.views 获取
            String sql = String.format(
                    "SELECT view_definition FROM information_schema.views " +
                            "WHERE table_schema = '%s' AND table_name = '%s'",
                    schemaName.toLowerCase(), viewName.toLowerCase()
            );

            LOG.debug("执行 PostgreSQL 视图定义查询：{}", sql);
            try (ResultSet rs = stmt.executeQuery(sql)) {
                if (rs.next()) {
                    String definition = rs.getString("view_definition");
                    if (definition != null && !definition.trim().isEmpty()) {
                        return definition.trim();
                    }
                }
            }

            // 如果 information_schema 没有找到，尝试使用 pg_views 系统视图
            String fallbackSql = String.format(
                    "SELECT definition FROM pg_views " +
                            "WHERE schemaname = '%s' AND viewname = '%s'",
                    schemaName.toLowerCase(), viewName.toLowerCase()
            );

            LOG.debug("执行 PostgreSQL 后备查询：{}", fallbackSql);
            try (ResultSet rs = stmt.executeQuery(fallbackSql)) {
                if (rs.next()) {
                    String definition = rs.getString("definition");
                    if (definition != null && !definition.trim().isEmpty()) {
                        return definition.trim();
                    }
                }
            }

            LOG.warn("未找到 PostgreSQL 视图的视图定义：{}.{}", schemaName, viewName);
        } catch (Exception e) {
            LOG.error("错误获取 postgreSQL 视图的视图定义视图： {}.{}",
                    view.getSchema(), view.getTableName(), e);
        }
        return null;
    }
}
