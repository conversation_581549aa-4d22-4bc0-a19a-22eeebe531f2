package com.dcap.classifier.analyzer;

import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.RuleContext;
import com.yd.rules.engine.result.MatchedResult;

import java.util.List;
import java.util.Map;

/**
 * 智能分析模块接口
 */
public interface IntelligentAnalyzer {
    /**
     * 分析表是否可以复用已有的识别结果
     * @param context 规则上下文
     * @param table 待分析的表
     * @return 如果可以复用返回缓存的结果,否则返回null
     */
    Map<String, List<MatchedResult>> analyze(RuleContext context, ContextTable table);

    /**
     * 缓存识别结果
     * @param table 表信息
     * @param results 识别结果
     */
    void cacheResults(RuleContext ruleContext, ContextTable table, Map<String, List<MatchedResult>> results);
}




