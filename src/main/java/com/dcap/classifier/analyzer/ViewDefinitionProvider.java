package com.dcap.classifier.analyzer;

import com.dcap.classifier.analyzer.getters.*;
import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.RuleContext;
import com.dcap.datalayer.DataSourceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 视图定义提供者
 * 不同数据库实现各自的视图定义获取逻辑
 */
public class ViewDefinitionProvider {

    private static final Logger LOG = LoggerFactory.getLogger(ViewDefinitionProvider.class);
    
    // 存储所有数据库类型对应的视图定义获取实现
    private static final Map<DataSourceType, ViewDefinitionGetter> PROVIDERS = new HashMap<>();
    
    // 注册各数据库类型的视图定义获取实现
    static {
        register(DataSourceType.MYSQL, new MysqlViewDefinitionGetter());
        register(DataSourceType.ORACLE, new OracleViewDefinitionGetter());
        register(DataSourceType.POSTGRESQL, new PostgreSqlViewDefinitionGetter());
        register(DataSourceType.MSSQL, new SqlServerViewDefinitionGetter());
        register(DataSourceType.HIVE, new HiveViewDefinitionGetter());
        register(DataSourceType.DB2, new Db2ViewDefinitionGetter());
        register(DataSourceType.IMPALA, new ImpalaViewDefinitionGetter());
    }
    
    /**
     * 获取视图的SQL定义
     */
    public static String getViewDefinition(DataSourceType dataSourceType, RuleContext context, ContextTable view) {
        ViewDefinitionGetter getter = PROVIDERS.get(dataSourceType);
        if (getter == null) {
            LOG.warn("Database type not supported: {}", dataSourceType);
            return null;
        }
        return getter.getViewDefinition(context, view);
    }
    
    /**
     * 判断是否支持该数据库类型
     */
    public static boolean isSupported(DataSourceType dataSourceType) {
        return PROVIDERS.containsKey(dataSourceType);
    }
    
    /**
     * 注册新的数据库类型支持
     */
    public static void register(DataSourceType dataSourceType, ViewDefinitionGetter getter) {
        PROVIDERS.put(dataSourceType, getter);
    }

    // 私有构造函数防止实例化
    private ViewDefinitionProvider() {}
} 