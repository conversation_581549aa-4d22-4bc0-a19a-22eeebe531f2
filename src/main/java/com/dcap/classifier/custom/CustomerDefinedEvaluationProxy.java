package com.dcap.classifier.custom;

import java.util.HashMap;
import java.util.Map;

public class CustomerDefinedEvaluationProxy implements Evaluation {
   private static final Map<String, Evaluation> evaluationClsMap = new HashMap<String, Evaluation>();
   private final String evaluationName;

   private Evaluation evaluation;

   static {
      evaluationClsMap.put("LuhnMod10", new LuhnMod10Evaluation());
   }
   public CustomerDefinedEvaluationProxy(String evaluationName){
      this.evaluationName = evaluationName;
      this.evaluation = evaluationClsMap.get(this.evaluationName);
   }

   @Override
   public boolean evaluate(String columnValue) throws EvaluationException {
      return evaluation.evaluate(columnValue);
   }
}
/*
public class CustomerDefinedEvaluationProxy extends CustomClassProxy implements Evaluation {
   private final Method evaluateMethod;

   public CustomerDefinedEvaluationProxy(String var1) throws SecurityException, ClassNotFoundException, InstantiationException, IllegalAccessException, NoSuchMethodException {
      super(var1);
      if (!Evaluation.class.isAssignableFrom(this.impl.getClass())) {
         String var2 = "Custom Evaluation classes must implement " + Evaluation.class + ".";
         throw new SecurityException(var2);
      } else {
         this.evaluateMethod = this.impl.getClass().getMethod("evaluate", String.class);
      }
   }

   public boolean evaluate(String var1) throws EvaluationException {
      try {
         Boolean var2 = (Boolean)this.evaluateMethod.invoke(this.impl, var1);
         return var2;
      } catch (Throwable var3) {
         if (var3.getCause() instanceof EvaluationException) {
            throw (EvaluationException)var3.getCause();
         } else {
            AdHocLogger.logException(var3);
            return false;
         }
      }
   }
}
 */
