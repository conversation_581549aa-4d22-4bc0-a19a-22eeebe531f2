package com.dcap.classifier.custom;

public class LuhnMod10Evaluation implements Evaluation {
   public LuhnMod10Evaluation() {
   }

   public boolean evaluate(String columnValue) {
      if (columnValue != null && columnValue.length() != 0) {
         int var2 = calcCheckDigit(columnValue.substring(0, columnValue.length() - 1));
         String var3 = columnValue.substring(columnValue.length() - 1);
         return var3.equals(String.valueOf(var2));
      } else {
         return false;
      }
   }

   public static int calcCheckDigit(int var0) {
      return calcCheckDigit(String.valueOf(var0));
   }

   public static int calcCheckDigit(String var0) {
      if (var0 != null && var0.length() != 0) {
         char[] var1 = var0.toCharArray();
         int var2 = var1.length % 2;
         int var3 = 0;

         int var4;
         for(var4 = 0; var4 < var1.length; ++var4) {
            if ((var4 + var2) % 2 == 0) {
               var3 += Character.digit(var1[var4], 10);
            } else {
               int var5 = Character.digit(var1[var4], 10) * 2;
               var3 += var5 > 9 ? var5 - 9 : var5;
            }
         }

         var4 = var3 % 10;
         if (var4 == 0) {
            return var4;
         } else {
            return 10 - var4;
         }
      } else {
         return 0;
      }
   }
}
