package com.dcap.snapshot;

import com.dcap.snapshot.models.MysqlBuildResult;
import com.dcap.snapshot.models.SnapshotAssetMetadata;
import com.dcap.utils.JSON;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.zeroturnaround.exec.ProcessResult;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;

public class SnapshotMysqlScanner extends SnapshotAssetsScanner {


    @Getter
    private MysqlBuildResult mysqlBuildResult;

    private String location = null;

    private String targetInstanceId = null;

    private String targetRegion = null;

    private String dbVersion = null;

    public SnapshotMysqlScanner(ProbeClientTaskContext probeClientTaskContext) {
        super(probeClientTaskContext);
    }

    @Override
    public void scanning(String targetInstanceId, String targetEcsRegion) throws IOException {
        this.targetInstanceId = targetInstanceId;
        this.targetRegion = targetEcsRegion;
        if (taskContext != null){
            String msg = "开始执行 snapshot database scan，targetInstanceId["+targetInstanceId+"], targetEcsRegion["+targetRegion+"]";
            taskContext.reportExecuting(StatusRecord.Position.Snapshot, msg, null).sendToServer();
        }
        super.scanning(targetInstanceId, targetEcsRegion);
        if (this.getAssetsList() == null || this.getAssetsList().isEmpty()) {
            String msg = "Package list is empty, error calling external tool?";
            LOG.error(msg);
            if (taskContext != null){
                msg = "资产列表为空，调用工具的过程中出错了吗？targetInstanceId["+targetInstanceId+"], targetEcsRegion["+targetRegion+"]";
                taskContext.reportErrorOccurredExecuting(StatusRecord.Position.Snapshot, msg, null,null).sendToServer();
            }
            return;
        }
        Map<String, Object> mysqlPackage = getMysqlPackage();
        if (mysqlPackage == null) {
            if (taskContext != null){
                String msg = "没有从获取到的资产包列表中找到 mysql server。targetInstanceId["+targetInstanceId+"], targetEcsRegion["+targetRegion+"]";
                taskContext.reportErrorOccurredExecuting(StatusRecord.Position.Snapshot, msg, null, null).sendToServer();
            }
            LOG.warn("mysql server does not exist in the package list");
            return;
        }
        dbVersion = Objects.toString(mysqlPackage.get("version"),null);
        location = this.getTakeSnapshotResult().getOutput().getLocation();
        if (taskContext != null){
            String msg = "开始启动 mysql 实例。dbVersion["+dbVersion+"], location["+location+"], targetInstanceId["+targetInstanceId+"], targetEcsRegion["+targetRegion+"]";
            taskContext.reportExecuting(StatusRecord.Position.Snapshot, msg, null).sendToServer();
        }
        this.mysqlBuildResult = startMysqlInstance(location, dbVersion);
    }

    @Override
    public void shutdown(String targetInstanceId, String targetEcsRegion) {
        if (StringUtils.isNotBlank(location)){
            stopMysqlContainer(location);
        }
        destroyDisk(targetInstanceId, targetEcsRegion);
    }

    private void stopMysqlContainer(String location) {
        LOG.info("stop Mysql instance");
        String [] command = new String[]{
//                "C:\\Program Files\\PuTTY\\plink.exe","-ssh","root@182.92.236.128", "-pw", "1qaz#EDC",
//                "\"cd /opt/yuandian/dspm/app-build/bin " +
//                        "&& export ALIYUN_ECS_ROLE=YDScannerRole " +
//                        "&& ./app-build run mysql "+location+" -c destroy -v "+
//                        "\""
                "./app-build/bin/app-build", "run", "mysql", location, "-c", "destroy", "-v"
        };
        ProcessResult processResult = runCommand(command);
        int exitValue = processResult.getExitValue();
        if (exitValue > 0 ){
            LOG.error("The call failed. Check the error log.");
        }
        String outputText = processResult.getOutput().getString();
        if (StringUtils.isBlank(outputText)){
            LOG.error("Why doesn't the program output anything ?");
        } else {
            LOG.info(outputText);
        }
    }

    private MysqlBuildResult startMysqlInstance(String location, String dbVersion) throws IOException {
        String containerName = this.targetInstanceId+"_mysql_"+dbVersion;
        LOG.info("start Mysql instance");
        String [] command = new String[]{
//                "C:\\Program Files\\PuTTY\\plink.exe","-ssh","root@182.92.236.128", "-pw", "1qaz#EDC",
//                "\"cd /opt/yuandian/dspm/app-build/bin " +
//                        "&& export ALIYUN_ECS_ROLE=YDScannerRole " +
//                        "&& ./app-build run mysql "+location+" --version "+dbVersion+" --container-name "+containerName+" --config /opt/yuandian/dspm/app-build/app-build-conf/mysql8.0/yd.cnf -c create -v "+
//                        "\""
                "./app-build/bin/app-build", "run", "mysql", location, "--version", dbVersion, "--container-name", containerName, "--config", "/opt/yuandian/dspm/app-build/app-build-conf/mysql8.0/yd.cnf", "-c", "create", "-v"
        };
        ProcessResult processResult = runCommand(command);
        int exitValue = processResult.getExitValue();
        if (exitValue > 0 ){
            LOG.error("The call failed. Check the error log.");
            if (taskContext != null){
                String msg = "执行启动 mysql 实例失败，targetInstanceId["+targetInstanceId+"], targetEcsRegion["+targetRegion+"]";
                taskContext.reportErrorOccurredExecuting(StatusRecord.Position.Snapshot, msg, null, null).sendToServer();
            }
        }
        String outputText = processResult.getOutput().getString();
        if (StringUtils.isBlank(outputText)){
            LOG.error("Why doesn't the program output anything ?");
            if (taskContext != null){
                String msg = "执行启动 mysql 实例失败，调用工具没有任何返回。targetInstanceId["+targetInstanceId+"], targetEcsRegion["+targetRegion+"]";
                taskContext.reportErrorOccurredExecuting(StatusRecord.Position.Snapshot, msg, null, null).sendToServer();
            }
            return null;
        } else {
            LOG.info(outputText);
        }
        String successFlag = "RunResult: OK";
        if (!outputText.contains(successFlag)) {
            LOG.error("Failed to take a snapshot ?");
            if (taskContext != null){
                String msg = "执行启动 mysql 实例失败，outputText["+outputText+"], targetInstanceId["+targetInstanceId+"], targetEcsRegion["+targetRegion+"]";
                taskContext.reportErrorOccurredExecuting(StatusRecord.Position.Snapshot, msg, null, null).sendToServer();
            }
            return null;
        }
        String jsonText = outputText.substring(outputText.lastIndexOf(successFlag)+successFlag.length());
        return getMysqlBuildResult(jsonText);
    }

    private static MysqlBuildResult getMysqlBuildResult(String jsonText) throws IOException {
        Map<String, Object> buildSnapshot = JSON.from(jsonText).toObject(Map.class);
        boolean success = Boolean.parseBoolean(String.valueOf(buildSnapshot.get("Success")));
        Map<String, Object> outputMap = (Map<String, Object>) buildSnapshot.get("Output");
        com.dcap.snapshot.models.MysqlBuildResult.Output output = new MysqlBuildResult.Output();

        output.setConfigPath(Objects.toString(outputMap.get("configPath"),null));
        output.setContainerName(Objects.toString(outputMap.get("containerName"), null));
        output.setMountDir(Objects.toString(outputMap.get("mountDir"), null));
        output.setPassword(Objects.toString(outputMap.get("password"), null));
        output.setPort(Objects.toString(outputMap.get("port"), null));
        output.setUser(Objects.toString(outputMap.get("user"), null));
        output.setVersion(Objects.toString(outputMap.get("version"), null));

        MysqlBuildResult mysqlBuildResult = new MysqlBuildResult();
        mysqlBuildResult.setSuccess(success);
        mysqlBuildResult.setOutput(output);
        return mysqlBuildResult;
    }

    private static final Pattern mysqlServerPattern = Pattern.compile("mysql.*server");
    private static final Pattern mysqlVersionPattern = Pattern.compile("8[.]0.*");
    private Map<String,Object> getMysqlPackage(){
        return this.getAssetsList().stream().filter(metadata->Objects.equals(metadata.getDataType(), "PACKAGE"))
                .filter((metadata)->{
                    Map<String, Object> softwarePackage = metadata.getData();
                    String name = Objects.toString(softwarePackage.get("name"),null);
                    String version = Objects.toString(softwarePackage.get("version"), null);
                    return mysqlServerPattern.matcher(name).find() &&
                            mysqlVersionPattern.matcher(version).find();
                }
        ).map(SnapshotAssetMetadata::getData).findFirst().orElse(null);
    }

    public static void main(String[] args) throws IOException {
        SnapshotMysqlScanner snapshotMysqlScanner = new SnapshotMysqlScanner(null);
        snapshotMysqlScanner.scanning("i-2zehplm12ugpehnphcyp", "cn-beijing");
    }


}
