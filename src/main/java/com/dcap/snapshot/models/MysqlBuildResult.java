package com.dcap.snapshot.models;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class MysqlBuildResult {
    private boolean Success = false;
    private Output Output;

    @Getter
    @Setter
    @ToString
    public static class Output{
        private String configPath;
        private String containerName;
        private String mountDir;
        private String version;
        private String port;
        private String user;
        private String password;
    }
}
