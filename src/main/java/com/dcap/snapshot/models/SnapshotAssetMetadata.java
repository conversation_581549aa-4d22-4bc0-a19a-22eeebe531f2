package com.dcap.snapshot.models;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;
import java.util.Objects;

@Getter
@Setter
@ToString
public class SnapshotAssetMetadata {
    private String tenantId;
    private String providerId;
    private String targetEcsInstanceId;
    private String targetEcsRegion;
    private String scanJobHistoryId;
    private Long currentVersion;
    private String dataType;
    private Map<String,Object> data;
    private String md5;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SnapshotAssetMetadata that = (SnapshotAssetMetadata) o;
        return Objects.equals(md5, that.md5);
    }

    @Override
    public int hashCode() {
        return Objects.hash(md5);
    }
}
