package com.dcap.snapshot.taskreport;

import com.dcap.classifier.Clock;
import com.dcap.utils.JSON;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.classifier.taskreport.TaskReport;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.dcap.probe.TaskResultReporter;
import com.yd.dcap.probe.entities.ProbeClientJobResult;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.time.Duration;
import java.time.Instant;
import java.util.*;

@Getter
public class ScanSnapshotDatabaseReport implements TaskReport {

    final Instant start = Instant.now();

    private final Clock clock = new Clock();

    private final List<StatusRecord> statusRecordList = Collections.synchronizedList(new LinkedList<>());

    private final TaskResultReporter probeClient;

    private final ProbeClientTaskContext probeClientTaskContext;

    public ScanSnapshotDatabaseReport(TaskResultReporter probeClient, ProbeClientTaskContext probeClientTaskContext) {
        this.probeClient = probeClient;
        this.probeClientTaskContext = probeClientTaskContext;
    }

    @Override
    public void record(String field, Object value) {

    }

    @Override
    public List<StatusRecord> addStatusRecord(StatusRecord statusRecord) {
        statusRecord.setElapsedTime(TaskReport.humanReadableFormat(Duration.between(start, Instant.now())));
        this.statusRecordList.add(statusRecord);
        return this.statusRecordList;
    }

    @Override
    public String toJson() {
        return JSON.from(toMap()).toString();
    }

    @Override
    public Map<String, Object> toMap() {
        int reportMsgTextLen = 0;
        int reportDataTextLen = 0;
        List<StatusRecord> newRecordList = new ArrayList<>();
        List<StatusRecord> statusRecordList = this.getStatusRecordList();
        for (StatusRecord statusRecord : statusRecordList) {
            String trace = statusRecord.getTrace();
            if (StringUtils.isNotBlank(trace)){
                statusRecord.setTrace(null);
            }
            if (statusRecord.getMessage() != null){
                reportMsgTextLen += statusRecord.getMessage().length();
            }
            if (statusRecord.getData() != null){
                reportDataTextLen += statusRecord.getData().length();
            }
            if (reportMsgTextLen > 50000 || reportDataTextLen > 50000){
                break;
            }
            newRecordList.add(statusRecord);
        }
        return UtilMisc.toMap(
                "clock", clock, "statusRecordList", newRecordList
        );
    }

    @Override
    public void sendToServer() {
        ProbeClientJobResult probeClientJobResult = new ProbeClientJobResult(
                this.probeClientTaskContext.getExecId(), this.probeClientTaskContext.getTaskId(),
                this.probeClientTaskContext.getTaskType(), this.probeClientTaskContext.getTaskStatus(),
                this.probeClientTaskContext.getExecStatus(), toMap()
        );
        probeClient.updateDspmProbeClientJobResult(probeClientJobResult);
    }
}
