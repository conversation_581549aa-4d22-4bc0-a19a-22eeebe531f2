package com.dcap.snapshot;

import com.dcap.snapshot.models.TakeSnapshotResult;
import com.dcap.utils.JSON;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import lombok.Getter;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.zeroturnaround.exec.ProcessExecutor;
import org.zeroturnaround.exec.ProcessResult;
import org.zeroturnaround.exec.stream.slf4j.Slf4jStream;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

public abstract class AbstractSnapshotScanner implements SnapshotScanner {

    protected final Logger LOG = LoggerFactory.getLogger(getClass());
    protected final String installDir = "/opt/yuandian/dspm";

    // 可执行程序
    protected final String appBuild = "app-build";

    protected final String assetQuery = "asset-query";

    protected final String snapshotBuild = "snapshot-build";

    protected final String ENV_ALIYUN_ECS_ROLE = "YDScannerRole";

    protected final String ALIYUN_ECS_ROLE = "ALIYUN_ECS_ROLE";

    protected final long processTimeout;

    protected final File baseFolder;

    @Getter
    private TakeSnapshotResult takeSnapshotResult;

    protected final ProbeClientTaskContext taskContext;

    public AbstractSnapshotScanner(ProbeClientTaskContext probeClientTaskContext, long processTimeout, TimeUnit processTimeoutUnit) {
        this.taskContext = probeClientTaskContext;
        this.baseFolder = new File("/opt/yuandian/dspm");
        this.processTimeout = processTimeoutUnit.toMillis(processTimeout);
    }

    @Override
    public boolean isOperational() {
        if (checkInstall()) {
            LOG.info("Ready to install");
        }

        return true;
    }

    @Override
    public void scanning(String targetInstanceId, String targetEcsRegion) throws IOException {
//        if (!isOperational()) {
//            LOG.error("The program is not ready, please confirm that the program installation is successful and has the appropriate permissions.");
//            return;
//        }
        // 1 打快照
        takeSnapshotResult = createSnapshot(targetInstanceId, targetEcsRegion);
        if (takeSnapshotResult != null && taskContext != null){
            taskContext.reportExecuting(StatusRecord.Position.Snapshot, JSON.from(takeSnapshotResult).toString(), "创建快照成功");
        }
    }

    private boolean checkInstall(){
        Path path = Paths.get(installDir);
        if (Files.exists(path) && Files.isDirectory(path)){
            LOG.info("[{}]The installation directory exists, to continue to check whether there is a program.", installDir);
        } else {
            LOG.error("[{}]Installation directory does not exist, application installation is not successful", installDir);
            return false;
        }
        path = Paths.get(installDir+"/"+snapshotBuild+"/bin/"+snapshotBuild);
        if (!checkAppExecutable(path)) {
            return false;
        }
        path = Paths.get(installDir+"/"+assetQuery+"/bin/"+assetQuery);
        if (!checkAppExecutable(path)) {
            return false;
        }
        path = Paths.get(installDir+"/"+appBuild+"/bin/"+appBuild);
        return checkAppExecutable(path);
    }
    private boolean checkAppExecutable(Path path){
        if (Files.exists(path) && Files.isRegularFile(path) && Files.isExecutable(path)) {
            // Check if the current user has execute permission
            if (path.toFile().canExecute()) {
                LOG.info("The file [{}] exists, is a regular file, and can be executed.",path);
            } else {
                LOG.error("The file [{}] exists and is a regular file, but execute permission is missing.", path);
                return false;
            }
        } else {
            LOG.error("The file [{}] does not exist or is not a regular file.", path);
            return false;
        }
        return true;
    }

    protected static String quote(String... args) {
        StringBuilder stringBuilder = new StringBuilder();
        boolean first = true;
        for (String arg : args) {
            if (first) {
                first = false;
            } else {
                stringBuilder.append(' ');
            }
            stringBuilder.append('"').append(arg.replace("\"", "\"\"")).append('"');
        }
        return stringBuilder.toString();
    }

    protected static String doubleQuote(String... args) {
        return "\"" + quote(args) + "\"";
    }



    protected File getBaseFolder() {
        return baseFolder;
    }

    protected long getProcessTimeout() {
        return processTimeout;
    }

    protected ProcessExecutor makePresetProcessExecutor() {
        ProcessExecutor processExecutor = new ProcessExecutor();
        if (StringUtils.isNotBlank(System.getenv(ALIYUN_ECS_ROLE))){
            LOG.warn("name [{}] get env value is success. will use value as {}",ALIYUN_ECS_ROLE, System.getenv(ALIYUN_ECS_ROLE));
            processExecutor.environment(ALIYUN_ECS_ROLE, System.getenv(ALIYUN_ECS_ROLE));
        } else {
            String envValue = null;
            String url = "http://100.100.100.200/latest/meta-data/ram/security-credentials/";
            LOG.info("get role name by call http url [{}]", url);
            try(InputStream inputStream = Runtime.getRuntime()
                    .exec("curl "+url).getInputStream()) {
                String roleName = IOUtils.toString(inputStream, Charset.defaultCharset());
                LOG.info("get role name success [{}] by call http url [{}]", roleName, url);
                envValue = roleName;
            } catch (Exception e) {
                LOG.error(e.getMessage(), e);
            }
            if (StringUtils.isNotBlank(envValue)){
                processExecutor.environment(ALIYUN_ECS_ROLE, envValue);
            } else {
                LOG.warn("name [{}] get env value is null. will use default value as {}",ALIYUN_ECS_ROLE, ENV_ALIYUN_ECS_ROLE);
                processExecutor.environment(ALIYUN_ECS_ROLE, ENV_ALIYUN_ECS_ROLE);
            }
        }
        return processExecutor
                .redirectOutput(Slf4jStream.of(LOG).asInfo())
                .redirectError(Slf4jStream.of(LOG).asError())
                .readOutput(true)
                .directory(new File(installDir))
                .timeout(getProcessTimeout(), TimeUnit.MILLISECONDS)
                .exitValueAny();
    }

    protected ProcessResult runCommand(String[] command) {
        String commandText = Arrays.toString(command);
        LOG.info("Execute command {}", commandText);
        try {
            ProcessResult executeResult = makePresetProcessExecutor()
                    .command(command)
                    .execute();
            LOG.info("Got exit code {} for command {}", executeResult.getExitValue(), commandText);
            return executeResult;
        } catch (IOException e) {
            String message = String.format("Unable to run command: %s", commandText);
            LOG.error(message, e);
            throw new RuntimeException(message, e);
        } catch (InterruptedException e) {
            String message = String.format("Thread responsible for running command was interrupted: %s", commandText);
            LOG.error(message, e);
            throw new RuntimeException(message, e);
        } catch (TimeoutException e) {
            String message = String.format("Thread responsible for running command timed out: %s", commandText);
            LOG.error(message, e);
            throw new RuntimeException(message, e);
        }
    }

    private TakeSnapshotResult createSnapshot(String targetInstanceId, String targetEcsRegion) throws IOException {
        String [] command = new String[]{
//                "C:\\Program Files\\PuTTY\\plink.exe","-ssh","root@182.92.236.128", "-pw", "1qaz#EDC",
//                "\"cd /opt/yuandian/dspm/snapshot-build/bin " +
//                        "&& export ALIYUN_ECS_ROLE=YDScannerRole " +
//                        "&& ./snapshot-build run ali ecs "+targetInstanceId+" --target-region "+targetEcsRegion+" -c create -v "+
//                        "\""
                "./snapshot-build/bin/snapshot-build", "run", "ali", "ecs", targetInstanceId, "--target-region", targetEcsRegion, "-c", "create", "-v"
        };
        if (taskContext != null){
            String msg = "开始创建快照，targetInstanceId["+targetInstanceId+"], targetEcsRegion["+targetEcsRegion+"]";
            taskContext.reportExecuting(StatusRecord.Position.Snapshot, msg, null).sendToServer();
        }
        ProcessResult processResult = runCommand(command);
        int exitValue = processResult.getExitValue();
        if (exitValue > 0 ){
            LOG.error("The call failed. Check the error log.");
            if (taskContext != null){
                String msg = "创建快照失败。exitValue["+exitValue+"], targetInstanceId["+targetInstanceId+"], targetEcsRegion["+targetEcsRegion+"]";
                taskContext.reportErrorOccurredExecuting(StatusRecord.Position.Snapshot, msg, null, null).sendToServer();
            }
            return null;
        }
        if (taskContext != null){
            String msg = "创建快照成功。targetInstanceId["+targetInstanceId+"], targetEcsRegion["+targetEcsRegion+"]";
            taskContext.reportExecuting(StatusRecord.Position.Snapshot, msg, null).sendToServer();
        }
        String outputText = processResult.getOutput().getString();
        if (StringUtils.isBlank(outputText)){
            LOG.error("Why doesn't the program output anything ?");
            if (taskContext != null){
                String msg = "创建快照失败。工具没有任何输出。exitValue["+exitValue+"], targetInstanceId["+targetInstanceId+"], targetEcsRegion["+targetEcsRegion+"]";
                taskContext.reportErrorOccurredExecuting(StatusRecord.Position.Snapshot, msg, null, null).sendToServer();
            }
            return null;
        } else {
            LOG.info(outputText);
        }
        String successFlag = "RunResult: OK";
        if (!outputText.contains(successFlag)) {
            LOG.error("Failed to take a snapshot ?");
            if (taskContext != null){
                String msg = "创建快照失败。outputText["+outputText+"], exitValue["+exitValue+"], targetInstanceId["+targetInstanceId+"], targetEcsRegion["+targetEcsRegion+"]";
                taskContext.reportErrorOccurredExecuting(StatusRecord.Position.Snapshot, msg, null, null).sendToServer();
            }
            return null;
        }
        String jsonText = outputText.substring(outputText.lastIndexOf(successFlag)+successFlag.length());
        return getTakeSnapshotResult(jsonText);
    }
    protected void destroyDisk(String targetInstanceId, String targetEcsRegion) {
        LOG.info("Start recycling resources...");
        String [] command = new String[]{
//                "C:\\Program Files\\PuTTY\\plink.exe","-ssh","root@182.92.236.128", "-pw", "1qaz#EDC",
//                "\"cd /opt/yuandian/dspm/snapshot-build/bin " +
//                        "&& export ALIYUN_ECS_ROLE=YDScannerRole " +
//                        "&& ./snapshot-build run ali ecs "+targetInstanceId+" --target-region "+targetEcsRegion+" -c destroy -v "+
//                        "\""
                "./snapshot-build/bin/snapshot-build", "run", "ali", "ecs", targetInstanceId, "--target-region", targetEcsRegion, "-c", "destroy", "-v"
        };
        ProcessResult processResult = runCommand(command);
        int exitValue = processResult.getExitValue();
        if (exitValue > 0 ){
            LOG.error("The call failed. Check the error log.");
            return;
        }
        String outputText = processResult.getOutput().getString();
        if (StringUtils.isBlank(outputText)){
            LOG.error("Why doesn't the program output anything ?");
            return;
        } else {
            LOG.info(outputText);
        }
    }

    private static TakeSnapshotResult getTakeSnapshotResult(String jsonText) throws IOException {
        Map<String, Object> buildSnapshot = JSON.from(jsonText).toObject(Map.class);
        boolean success = Boolean.parseBoolean(String.valueOf(buildSnapshot.get("Success")));
        Map<String, Object> outputMap = (Map<String, Object>) buildSnapshot.get("Output");
        com.dcap.snapshot.models.TakeSnapshotResult.Output output = new TakeSnapshotResult.Output();
        output.setLocation(Objects.toString(outputMap.get("location"),null));
        output.setVolumeDevice(Objects.toString(outputMap.get("volume-device"), null));
        output.setVolumeId(Objects.toString(outputMap.get("volume-id"), null));
        output.setVolumeRegion(Objects.toString(outputMap.get("volume-region"), null));
        TakeSnapshotResult takeSnapshotResult = new TakeSnapshotResult();
        takeSnapshotResult.setSuccess(success);
        takeSnapshotResult.setOutput(output);
        return takeSnapshotResult;
    }

}
