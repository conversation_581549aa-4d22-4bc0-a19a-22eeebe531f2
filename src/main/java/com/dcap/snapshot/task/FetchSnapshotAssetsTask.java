package com.dcap.snapshot.task;


import com.dcap.classifier.writer.DataWriter;
import com.dcap.classifier.writer.DataWriterFactory;
import com.dcap.classifier.writer.RecordTypeEnum;
import com.dcap.snapshot.SnapshotAssetsScanner;
import com.dcap.snapshot.models.SnapshotAssetMetadata;
import com.dcap.snapshot.taskreport.FetchSnapshotAssetsReport;
import com.dcap.utils.Base64Util;
import com.dcap.utils.JSON;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.probe.TaskResultReporter;
import com.yd.dcap.probe.client.AbstractProbeClientProcessor;
import com.yd.dcap.probe.client.TaskType;
import com.yd.dcap.probe.entities.ProbeClientJobResult;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import com.yd.dspm.config.DspmTaskType;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;

@TaskType(DspmTaskType.FETCH_SNAPSHOT_ASSETS)
public class FetchSnapshotAssetsTask extends AbstractProbeClientProcessor {
    private final DataWriter assetsWriter;
    public FetchSnapshotAssetsTask(ProbeClientTaskContext probeClientTaskContext) {
        super(probeClientTaskContext);
        long scanJobHistoryId = probeClientTaskContext.getExecId();
        this.assetsWriter = DataWriterFactory.createDataWriter(scanJobHistoryId, RecordTypeEnum.SNAPSHOT_ASSETS_INFO);
        TaskResultReporter taskResultReporter = probeClientTaskContext.getTaskResultReporter();
        probeClientTaskContext.registerTaskReport(new FetchSnapshotAssetsReport(taskResultReporter, probeClientTaskContext));
    }

    @Override
    protected Map<String, Object> doWork(long execId, long taskId, String taskType, long tenantId,
                                         Map<String, Object> taskParam, Consumer<ProbeClientJobResult> callback) throws Exception {
        String currentVersion = Objects.toString(taskParam.get("currentVersion"), null);
        String providerId = Objects.toString(taskParam.get("providerId"), null);
        String targetInstanceId = Objects.toString(taskParam.get("targetInstanceId"), null);
        String targetEcsRegion = Objects.toString(taskParam.get("targetRegion"), null);
        SnapshotAssetsScanner assetsScanner = new SnapshotAssetsScanner(probeClientTaskContext);
        try{
            assetsScanner.scanning(targetInstanceId, targetEcsRegion);
            List<SnapshotAssetMetadata> assetsList = assetsScanner.getAssetsList();
            for (SnapshotAssetMetadata assetMetadata : assetsList) {
                String [] entries = new String [] {
                        String.valueOf(tenantId), Objects.toString(execId, null), currentVersion,
                        providerId, targetInstanceId, targetEcsRegion, assetMetadata.getDataType(),
                        Base64Util.Encrypt(JSON.from(assetMetadata.getData()).toString())
                };
                this.assetsWriter.writeEntries(entries);
            }
            String msg = "FetchSnapshotAssets 任务执行完成。";
            return probeClientTaskContext.reportSuccess(StatusRecord.Position.SnapshotFetchAssets, null, msg).toMap();
        } catch (Exception e){
            logger.error(e.getMessage(), e);
            return probeClientTaskContext.reportFailed(StatusRecord.Position.SnapshotFetchAssets, null, e).toMap();
        } finally {
            assetsScanner.shutdown(targetInstanceId, targetEcsRegion);
            if (this.assetsWriter != null){
                this.assetsWriter.close();
            }
        }
    }
}
