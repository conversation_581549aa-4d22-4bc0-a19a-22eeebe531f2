package com.dcap.snapshot.task;


import com.dcap.snapshot.SnapshotMysqlScanner;
import com.dcap.snapshot.models.MysqlBuildResult;
import com.dcap.snapshot.taskreport.ScanSnapshotDatabaseReport;
import com.dcap.utils.JSON;
import com.yd.dcap.classifier.ClassifierService;
import com.yd.dcap.classifier.config.SpringContextUtil;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.dcap.probe.TaskResultReporter;
import com.yd.dcap.probe.client.AbstractProbeClientProcessor;
import com.yd.dcap.probe.client.TaskConfig;
import com.yd.dcap.probe.client.TaskType;
import com.yd.dcap.probe.entities.ProbeClientJobResult;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import com.yd.dspm.config.DspmTaskType;

import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;

@TaskType(DspmTaskType.SCAN_SNAPSHOT_DB)
public class ScanSnapshotDatabaseTask extends AbstractProbeClientProcessor {

    public ScanSnapshotDatabaseTask(ProbeClientTaskContext probeClientTaskContext) {
        super(probeClientTaskContext);
        TaskResultReporter taskResultReporter = probeClientTaskContext.getTaskResultReporter();
        probeClientTaskContext.registerTaskReport(new ScanSnapshotDatabaseReport(taskResultReporter, probeClientTaskContext));
    }
    @Override
    protected Map<String, Object> doWork(long execId, long taskId, String taskType, long tenantId,
                                         Map<String, Object> taskConf, Consumer<ProbeClientJobResult> callback) throws Exception {
        String currentVersion = Objects.toString(taskConf.get("currentVersion"), null);
        String providerId = Objects.toString("providerId", null);
        Map<String,Object> dataSource = (Map<String, Object>) taskConf.get("datasource");
        String targetInstanceId = Objects.toString(taskConf.get("targetInstanceId"), null);
        String targetRegion = Objects.toString(taskConf.get("targetRegion"), null);
        SnapshotMysqlScanner snapshotMysqlScanner = new SnapshotMysqlScanner(probeClientTaskContext);
        try {
            snapshotMysqlScanner.scanning(targetInstanceId, targetRegion);
            MysqlBuildResult mysqlBuildResult = snapshotMysqlScanner.getMysqlBuildResult();
            if (mysqlBuildResult == null){
                String errMsg = "mysqlBuildResult 为空，调用工具的过程中出错了？ targetInstanceId ["+targetInstanceId+"], targetRegion ["+targetRegion+"]";
                return probeClientTaskContext.reportFailed(StatusRecord.Position.Snapshot, null, errMsg).toMap();
            }
            String user = mysqlBuildResult.getOutput().getUser();
            String password = mysqlBuildResult.getOutput().getPassword();
            String port = mysqlBuildResult.getOutput().getPort();
            String host = "127.0.0.1";
            dataSource.put("host", host);
            dataSource.put("port", port);
            dataSource.put("authCfg", UtilMisc.toMap("username", user, "password", password));
            String data = JSON.from(dataSource).toString();
            probeClientTaskContext.reportExecuting(StatusRecord.Position.Snapshot, data,"开始执行数据库扫描").sendToServer();
            ClassifierService classifierService = SpringContextUtil.getBean(ClassifierService.class);
            return classifierService.scan(getTaskContext().getTaskType(), JSON.from(taskConf).toObject(TaskConfig.class));
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            snapshotMysqlScanner.shutdown(targetInstanceId, targetRegion);
            probeClientTaskContext.clean();
        }
    }
}
