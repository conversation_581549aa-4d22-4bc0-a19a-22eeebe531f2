package com.dcap.snapshot;

import com.dcap.snapshot.models.SnapshotAssetMetadata;
import com.dcap.snapshot.taskreport.FetchSnapshotAssetsReport;
import com.dcap.utils.JSON;
import com.google.common.collect.Lists;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import lombok.Getter;
import org.zeroturnaround.exec.ProcessResult;

import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Getter
public class SnapshotAssetsScanner extends AbstractSnapshotScanner {

    private final List<SnapshotAssetMetadata> assetsList = new ArrayList<>();


    public SnapshotAssetsScanner(ProbeClientTaskContext probeClientTaskContext) {
        super(probeClientTaskContext, 10, TimeUnit.MINUTES);
    }

    @Override
    public void scanning(String targetInstanceId, String targetEcsRegion) throws IOException {
        if (taskContext != null){
            String msg = "开始扫描资产列表，targetInstanceId["+targetInstanceId+"], targetEcsRegion["+targetEcsRegion+"]";
            taskContext.reportExecuting(StatusRecord.Position.SnapshotFetchAssets, msg, null).sendToServer();
        }
        super.scanning(targetInstanceId, targetEcsRegion);
        if (this.getTakeSnapshotResult() == null || this.getTakeSnapshotResult().getOutput() == null){
            return;
        }
        String location = this.getTakeSnapshotResult().getOutput().getLocation();
        if (taskContext != null){
            String msg = "扫描指定资产。location["+location+"], targetInstanceId["+targetInstanceId+"], targetEcsRegion["+targetEcsRegion+"]";
            taskContext.reportExecuting(StatusRecord.Position.SnapshotFetchAssets, msg, null).sendToServer();
        }
        List<SnapshotAssetMetadata> snapshotAssetMetadata = specificAssetScanning(location, "packages");
        if (snapshotAssetMetadata != null){
            this.assetsList.addAll(snapshotAssetMetadata);
            if (taskContext != null){
                taskContext.recordField(FetchSnapshotAssetsReport.FIELD_PACKAGE_COUNT, snapshotAssetMetadata.size()).sendToServer();
            }
        }

        snapshotAssetMetadata = specificAssetScanning(location, "users.list{*}");
        if (snapshotAssetMetadata != null){
            this.assetsList.addAll(snapshotAssetMetadata);
            if (taskContext != null){
                taskContext.recordField(FetchSnapshotAssetsReport.FIELD_USER_COUNT, snapshotAssetMetadata.size()).sendToServer();
            }
        }

        snapshotAssetMetadata = specificAssetScanning(location, "sshd.config{*}");
        if (snapshotAssetMetadata != null){
            this.assetsList.addAll(snapshotAssetMetadata);
            if (taskContext != null){
                taskContext.recordField(FetchSnapshotAssetsReport.FIELD_CONFIG_COUNT, 1).sendToServer();
            }
        }

        snapshotAssetMetadata = specificAssetScanning(location, "mysqld.config{*}");
        if (snapshotAssetMetadata != null){
            this.assetsList.addAll(snapshotAssetMetadata);
            if (taskContext != null){
                taskContext.recordField(FetchSnapshotAssetsReport.FIELD_CONFIG_COUNT, 1).sendToServer();
            }
        }

        snapshotAssetMetadata = specificAssetScanning(location, "services.list{*}");
        if (snapshotAssetMetadata != null){
            this.assetsList.addAll(snapshotAssetMetadata);
            if (taskContext != null){
                taskContext.recordField(FetchSnapshotAssetsReport.FIELD_SERVICE_COUNT, snapshotAssetMetadata.size()).sendToServer();
            }
        }
    }

    @Override
    public void shutdown(String targetInstanceId, String targetEcsRegion) {
        destroyDisk(targetInstanceId, targetEcsRegion);
    }


    private List<SnapshotAssetMetadata> specificAssetScanning(String location, String specificName) throws IOException {
        LOG.info("Start scanning the asset package at this location:[{}]", location);
        String [] command = new String[]{
//                "C:\\Program Files\\PuTTY\\plink.exe","-ssh","root@182.92.236.128", "-pw", "1qaz#EDC",
//                "cd /opt/yuandian/dspm/asset-query/bin " +
//                        "&& export ALIYUN_ECS_ROLE=YDScannerRole " +
//                        "&& ./asset-query run snapshot "+location+" -c \""+specificName+"\" --json -v "
                "./asset-query/bin/asset-query", "run", "snapshot", location, "-c", specificName, "--json", "-v"
        };
        ProcessResult processResult = runCommand(command);
        String outputText = processResult.getOutput().getString();
        int exitValue = processResult.getExitValue();
        if (exitValue > 0 ){
            LOG.error("The call failed. Check the error log. output text: {}",outputText);
        }

        List<Map<String,Object>> outputResult = JSON.from(outputText).toObject(List.class);
        if (outputResult == null || outputResult.isEmpty() || outputResult.get(0) == null
                || (outputResult.get(0).get("packages.list") == null
                    && outputResult.get(0).get("users.list") == null
                    && outputResult.get(0).get("sshd.config") == null
                    && outputResult.get(0).get("services.list") == null
                    )
        ){
            LOG.error("For external tool output results be empty: {}", outputText);
            return null;
        }
        Map<String,Object> scanResult = outputResult.get(0);
        if (scanResult == null || scanResult.isEmpty() || scanResult.containsKey("error")){
            LOG.error("An error occurred in the output of the external tool: {}", outputResult);
            return null;
        }
        List<SnapshotAssetMetadata> snapshotAssetMetadata = wrapMetadata(scanResult);
        if (snapshotAssetMetadata != null && !snapshotAssetMetadata.isEmpty()) {
            return snapshotAssetMetadata;
        }
        return null;
    }
    private static String getMD5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(input.getBytes());
            byte[] digest = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        }
    }

    private List<SnapshotAssetMetadata> wrapMetadata(Map<String,Object> scanResult) {
        if (scanResult.containsKey("packages.list")) {
            List<Map<String, Object>> packageList = (List<Map<String, Object>>) scanResult.get("packages.list");
            if (packageList == null || packageList.isEmpty() || packageList.get(0).containsKey("error")) {
                LOG.error("An error occurred in the output of the external tool: {}", scanResult);
                return null;
            }
            return packageList.stream().map(packageMap -> {
                SnapshotAssetMetadata assetMetadata = new SnapshotAssetMetadata();
                assetMetadata.setDataType("PACKAGE");
                assetMetadata.setData(packageMap);
                assetMetadata.setMd5(getMD5(JSON.from(assetMetadata.getData()).toString()));
                return assetMetadata;
            }).distinct().collect(Collectors.toList());
        } else if (scanResult.containsKey("sshd.config")) {
            Map<String, Object> sshdConfig = (Map<String, Object>) scanResult.get("sshd.config");
            SnapshotAssetMetadata assetMetadata = new SnapshotAssetMetadata();
            assetMetadata.setDataType("SSHD_CONFIG");
            assetMetadata.setData(sshdConfig);
            return Lists.newArrayList(assetMetadata);
        } else if (scanResult.containsKey("users.list")) {
            List<Map<String, Object>> usersLit = (List<Map<String, Object>>) scanResult.get("users.list");
            if (usersLit == null || usersLit.isEmpty() || usersLit.get(0).containsKey("error")) {
                LOG.error("An error occurred in the output of the external tool: {}", scanResult);
                return null;
            }
            return usersLit.stream().map(userMap -> {
                SnapshotAssetMetadata assetMetadata = new SnapshotAssetMetadata();
                assetMetadata.setDataType("USER");
                assetMetadata.setData(userMap);
                assetMetadata.setMd5(getMD5(JSON.from(assetMetadata.getData()).toString()));
                return assetMetadata;
            }).distinct().collect(Collectors.toList());
        } else if (scanResult.containsKey("mysqld.config")) {
            Map<String, Object> mysqldConfig = (Map<String, Object>) scanResult.get("mysqld.config");
            SnapshotAssetMetadata assetMetadata = new SnapshotAssetMetadata();
            assetMetadata.setDataType("MYSQLD_CONFIG");
            assetMetadata.setData(mysqldConfig);
            return Lists.newArrayList(assetMetadata);
        } else if (scanResult.containsKey("services.list")) {
            List<Map<String, Object>> serviceLit = (List<Map<String, Object>>) scanResult.get("services.list");
            if (serviceLit == null || serviceLit.isEmpty() || serviceLit.get(0).containsKey("error")) {
                LOG.error("An error occurred in the output of the external tool: {}", scanResult);
                return null;
            }
            return serviceLit.stream().map(serviceMap -> {
                SnapshotAssetMetadata assetMetadata = new SnapshotAssetMetadata();
                assetMetadata.setDataType("SERVICE");
                assetMetadata.setData(serviceMap);
                assetMetadata.setMd5(getMD5(JSON.from(assetMetadata.getData()).toString()));
                return assetMetadata;
            }).distinct().collect(Collectors.toList());
        }
        LOG.error("The result of haven't support: {}", scanResult);
        return null;
    }



    public static void main(String[] args) throws IOException {
        SnapshotAssetsScanner snapshotAssetsScanner = new SnapshotAssetsScanner(null);
        snapshotAssetsScanner.scanning("i-2zehplm12ugpehnphcyp", "cn-beijing");
        List<SnapshotAssetMetadata> assetsList1 = snapshotAssetsScanner.getAssetsList();
        System.out.println(assetsList1.size());
        for (SnapshotAssetMetadata assetMetadata : assetsList1) {
            System.out.println(assetMetadata);
        }
    }
}
