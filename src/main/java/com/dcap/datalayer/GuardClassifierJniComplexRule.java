package com.dcap.datalayer;

import com.dcap.classifier.RuleEvaluator;
import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.rules.ColumnJniComplexRuleEvaluator;
import com.dcap.utils.Utils;
import com.yd.dataclassifier.jni.LibDataClassifierPointer;
import com.yd.rules.engine.ScoringModel;
import com.yd.rules.engine.parser.CustomScoreParser;
import com.yd.rules.engine.parser.ScoringModelParser;
import com.yd.rules.engine.parser.ScoringRuleParser;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

import static com.dcap.datalayer.GuardClassifierColumnDataRule.MARK_THRESHOLDS;

public class GuardClassifierJniComplexRule extends AbstractClassifierComplexRule<GuardiumClassifierRule> {
    private final Logger logger = LoggerFactory.getLogger(GuardClassifierJniComplexRule.class);
    public static final String DATA_TYPE = "DATA_TYPE";
    public static final String MINIMUM_LENGTH = "MINIMUM_LENGTH";
    public static final String MAXIMUM_LENGTH = "MAXIMUM_LENGTH";
    public static final String HIT_PERCENTAGE = "HIT_PERCENTAGE";
//    public static final String SEARCH_IN_SQL = "SEARCH_IN_SQL";
//    public static final String SEARCH_IN_GROUP = "SEARCH_IN_GROUP";

    public static final String SCORING_MODEL = "SCORING_MODEL";
    private static final ScoringModelParser scoringModelParser = new ScoringModelParser(new CustomScoreParser(new ScoringRuleParser()));
    private final TableRuleParameters tableRuleParameters;
    private final TableTypeParameter tableTypeParameter;
    private final TableNameParameters tableNameParameters;
    private final SearchValueParameters searchValueParameters;
    private boolean loadedHitPct;
    private boolean loadedMinLength;
    private boolean loadedMaxLength;

    private boolean loadedMarkThresholds;
    private boolean loadedColumnTypes;

    private boolean loadedScoringModel = false;
    private Integer minLength;
    private Integer maxLength;
    private Integer hitPct;

    private Integer markThresholds;

    private ScoringModel scoringModel;
    private ContextColumn.ColumnType[] columnTypes;

    // 数据分类器相关
    @Getter
    private LibDataClassifierPointer classifierPtr;
    private long scanJobHistoryId;
    private boolean classifierInitialized = false;

    public GuardClassifierJniComplexRule(GuardiumClassifierRule rule) {
        super(rule);
        this.tableTypeParameter = new TableTypeParameter(rule);
        this.tableRuleParameters = new TableRuleParameters(rule);
        this.tableNameParameters = new TableNameParameters(rule);
        this.searchValueParameters = new SearchValueParameters(rule);

        try {
            // 获取必要参数
            this.scanJobHistoryId = Long.parseLong(Objects.toString(rule.getParameter("scanJobHistoryId")));
            long tenantId = Long.parseLong(Objects.toString(rule.getParameter("tenantId")));
            String expr = rule.getParameter(SCORING_MODEL);
            String dictData = rule.getParameter("dataDictionary");
            if (StringUtils.isNotBlank(expr) && expr.startsWith("---\n")){
                expr = expr.substring(4);
            }
            // 初始化数据分类器
            initDataClassifier(scanJobHistoryId, expr, dictData, tenantId);
        } catch (Exception e) {
            logger.error("初始化数据分类器失败: {}", e.getMessage(), e);
            throw new RuntimeException("初始化数据分类器失败: " + e.getMessage());
        }
    }

    /**
     * 初始化数据分类器
     * @param scanJobHistoryId 扫描任务历史ID
     * @param expr 模型定义表达式
     * @param dictData 字典数据
     * @param tenantId 租户ID
     */
    private void initDataClassifier(long scanJobHistoryId, String expr, String dictData, long tenantId) {
        if (StringUtils.isBlank(expr)) {
            logger.error("模型定义为空，无法初始化数据分类器");
            return;
        }

        try {
            // 获取数据分类器工具类实例
            DataClassifierUtil util = DataClassifierUtil.getInstance();

            // 构建分类器
            classifierPtr = util.build(scanJobHistoryId, expr, dictData, tenantId);
            if (classifierPtr != null) {
                classifierInitialized = true;
                logger.info("数据分类器初始化成功，任务ID: {}", scanJobHistoryId);
            } else {
                logger.error("数据分类器初始化失败，任务ID: {}", scanJobHistoryId);
            }
        } catch (Exception e) {
            logger.error("初始化数据分类器异常: {}", e.getMessage(), e);
        }
    }

    String getParameter(String paramName) {
        return this.getRuleRecord().getParameter(paramName);
    }

    public ContextTable.TableType[] getTableTypes() {
        return this.tableTypeParameter.getTableTypes();
    }

    public String[] getExcludedSchemas() {
        return this.tableRuleParameters.getExcludedSchemas();
    }

    public String[] getExcludedTables() {
        return this.tableRuleParameters.getExcludedTables();
    }

    public TableColumn[] getExcludedTableColumns() {
        return this.tableRuleParameters.getExcludedTableColumns();
    }

    public String getTableNameLike() {
        return this.tableNameParameters.getTableNameLike();
    }

    public String getColumnNameLike() {
        return this.tableNameParameters.getColumnNameLike();
    }

    public Integer getMinimumLength() {
        if (this.loadedMinLength) {
            return this.minLength;
        }
        String var1 = this.getParameter(MINIMUM_LENGTH);
        if (var1 != null && (var1 = var1.trim()).length() > 0) {
            this.minLength = Utils.parseInteger(var1);
        }
        this.loadedMinLength = true;
        return this.minLength;
    }

    public Integer getMaximumLength() {
        if (this.loadedMaxLength) {
            return this.maxLength;
        }
        String var1 = this.getParameter(MAXIMUM_LENGTH);
        if (var1 != null && (var1 = var1.trim()).length() > 0) {
            this.maxLength = Utils.parseInteger(var1);
        }
        this.loadedMaxLength = true;
        return this.maxLength;
    }

    public Integer getMarkThresholds(){
        if (this.loadedMarkThresholds) {
            return this.markThresholds;
        }
        String var1 = this.getParameter(MARK_THRESHOLDS);
        if (var1 != null && (var1 = var1.trim()).length() > 0) {
            this.markThresholds = Utils.parseInteger(var1);
        } else{
            this.markThresholds = 60;
        }
        this.loadedMarkThresholds = true;
        return this.markThresholds;

    }

    public ContextColumn.ColumnType[] getColumnTypes() {
        if (this.loadedColumnTypes) {
            return this.columnTypes;
        }

        String dataType = this.getParameter(DATA_TYPE);
        if (StringUtils.isNotBlank(dataType)) {
            this.columnTypes = GuardiumClassifierRule.parseColumnTypes(dataType);
        }
        this.loadedColumnTypes = true;

        return this.columnTypes;
    }

    public String getSearchValueLike() {
        return this.searchValueParameters.getSearchValueLike();
    }

    public String getSearchValuePattern() {
        return this.searchValueParameters.getSearchValuePattern();
    }

    public String[] getSearchValues() {
        return null;
    }

    public boolean isValuesSearchWithin() {
        return false;
    }

    public Integer getRequiredHitPercentage() {
        if (this.loadedHitPct) {
            return this.hitPct;
        }
        String hitPercentage = this.getParameter(HIT_PERCENTAGE);
        if (StringUtils.isNotBlank(hitPercentage)) {
            try {
                this.hitPct = Integer.parseInt(hitPercentage);
            } catch (NumberFormatException ignored) {
            }
        }
        this.loadedHitPct = true;
        return this.hitPct;
    }

    @Override
    public ScoringModel getScoringModel() {
        if (this.loadedScoringModel) {
            return this.scoringModel;
        }
        String scoringModelText = this.getParameter(SCORING_MODEL);
        if (StringUtils.isBlank(scoringModelText)) {
            throw new RuntimeException("scoring model text is required");
        }
        String dataTagType = this.getDataTagTypeForDspm();
        try {
            this.scoringModel = scoringModelParser.parseScoringModel(this.getTranslatedName(), dataTagType, scoringModelText, this.record.getDataTagOrderBy());
        } catch (Exception e) {
            String errorMsg = "parsing model ["+this.getTranslatedName()+"] failure ["+e.getMessage()+"], model data is ["+scoringModelText+"]";
            logger.error(errorMsg);
            throw new IllegalArgumentException(errorMsg);
        }
        this.loadedScoringModel = true;
        return scoringModel;
    }

    public String generateParams() {
        return "";
    }

    /**
     * 释放分类器资源
     */
    public void releaseClassifier() {
        if (classifierInitialized && classifierPtr != null) {
            try {
                DataClassifierUtil util = DataClassifierUtil.getInstance();
                util.releaseTaskClassifier(scanJobHistoryId, classifierPtr);
                classifierInitialized = false;
                classifierPtr = null;
                logger.info("释放分类器资源成功，任务ID: {}", scanJobHistoryId);
            } catch (Exception e) {
                logger.error("释放分类器资源异常: {}", e.getMessage(), e);
            }
        }
    }

    @Override
    protected RuleEvaluator getRuleEvaluator() {
        return new ColumnJniComplexRuleEvaluator(this, this.getRuleRecord().getDataTagOrderBy());
    }

    /**
     * 在对象被垃圾回收时释放资源
     */
    @Override
    protected void finalize() throws Throwable {
        try {
            releaseClassifier();
        } finally {
            super.finalize();
        }
    }
}
