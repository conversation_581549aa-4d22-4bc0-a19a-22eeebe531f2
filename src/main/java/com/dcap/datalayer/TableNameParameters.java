package com.dcap.datalayer;

public class TableNameParameters {
    public static final String TABLE_NAME_LIKE = "TABLE_NAME_LIKE";
    public static final String COLUMN_NAME_LIKE = "COLUMN_NAME_LIKE";
    private final GuardiumClassifierRule record;
    private String tableNameLike;
    private String columnNameLike;
    private boolean tableNameLikePopulated;
    private boolean columnNameLikePopulated;

    protected TableNameParameters(GuardiumClassifierRule var1) {
        this.record = var1;
    }

    String getParameter(String var1) {
        return this.record.getParameter(var1);
    }

    public String getTableNameLike() {
        if (!this.tableNameLikePopulated) {
            this.tableNameLike = this.getParameter(TABLE_NAME_LIKE);
            this.tableNameLikePopulated = true;
        }
        return this.tableNameLike;
    }

    public String getColumnNameLike() {
        if (!this.columnNameLikePopulated) {
            this.columnNameLike = this.getParameter(COLUMN_NAME_LIKE);
            this.columnNameLikePopulated = true;
        }
        return this.columnNameLike;
    }
}
