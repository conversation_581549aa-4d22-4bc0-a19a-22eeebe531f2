package com.dcap.datalayer;

import com.dcap.classifier.RuleEvaluator;
import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.rules.ColumnComplexRuleEvaluator;
import com.dcap.utils.Utils;
import com.yd.rules.engine.ScoringModel;
import com.yd.rules.engine.parser.CustomScoreParser;
import com.yd.rules.engine.parser.ScoringModelParser;
import com.yd.rules.engine.parser.ScoringRuleParser;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.dcap.datalayer.GuardClassifierColumnDataRule.MARK_THRESHOLDS;

public class GuardClassifierComplexRule extends AbstractClassifierComplexRule<GuardiumClassifierRule> {
    private final Logger logger = LoggerFactory.getLogger(GuardClassifierComplexRule.class);
    public static final String DATA_TYPE = "DATA_TYPE";
    public static final String MINIMUM_LENGTH = "MINIMUM_LENGTH";
    public static final String MAXIMUM_LENGTH = "MAXIMUM_LENGTH";
    public static final String HIT_PERCENTAGE = "HIT_PERCENTAGE";
//    public static final String SEARCH_IN_SQL = "SEARCH_IN_SQL";
//    public static final String SEARCH_IN_GROUP = "SEARCH_IN_GROUP";

    public static final String SCORING_MODEL = "SCORING_MODEL";
    private static final ScoringModelParser scoringModelParser = new ScoringModelParser(new CustomScoreParser(new ScoringRuleParser()));
    private final TableRuleParameters tableRuleParameters;
    private final TableTypeParameter tableTypeParameter;
    private final TableNameParameters tableNameParameters;
    private final SearchValueParameters searchValueParameters;
    private boolean loadedHitPct;
    private boolean loadedMinLength;
    private boolean loadedMaxLength;

    private boolean loadedMarkThresholds;
    private boolean loadedColumnTypes;

    private boolean loadedScoringModel = false;
    private Integer minLength;
    private Integer maxLength;
    private Integer hitPct;

    private Integer markThresholds;

    private ScoringModel scoringModel;
    private ContextColumn.ColumnType[] columnTypes;

    public GuardClassifierComplexRule(GuardiumClassifierRule rule) {
        super(rule);
        this.tableTypeParameter = new TableTypeParameter(rule);
        this.tableRuleParameters = new TableRuleParameters(rule);
        this.tableNameParameters = new TableNameParameters(rule);
        this.searchValueParameters = new SearchValueParameters(rule);
    }

    String getParameter(String paramName) {
        return this.getRuleRecord().getParameter(paramName);
    }

    public ContextTable.TableType[] getTableTypes() {
        return this.tableTypeParameter.getTableTypes();
    }

    public String[] getExcludedSchemas() {
        return this.tableRuleParameters.getExcludedSchemas();
    }

    public String[] getExcludedTables() {
        return this.tableRuleParameters.getExcludedTables();
    }

    public TableColumn[] getExcludedTableColumns() {
        return this.tableRuleParameters.getExcludedTableColumns();
    }

    public String getTableNameLike() {
        return this.tableNameParameters.getTableNameLike();
    }

    public String getColumnNameLike() {
        return this.tableNameParameters.getColumnNameLike();
    }

    public Integer getMinimumLength() {
        if (this.loadedMinLength) {
            return this.minLength;
        }
        String var1 = this.getParameter(MINIMUM_LENGTH);
        if (var1 != null && (var1 = var1.trim()).length() > 0) {
            this.minLength = Utils.parseInteger(var1);
        }
        this.loadedMinLength = true;
        return this.minLength;
    }

    public Integer getMaximumLength() {
        if (this.loadedMaxLength) {
            return this.maxLength;
        }
        String var1 = this.getParameter(MAXIMUM_LENGTH);
        if (var1 != null && (var1 = var1.trim()).length() > 0) {
            this.maxLength = Utils.parseInteger(var1);
        }
        this.loadedMaxLength = true;
        return this.maxLength;
    }

    public Integer getMarkThresholds(){
        if (this.loadedMarkThresholds) {
            return this.markThresholds;
        }
        String var1 = this.getParameter(MARK_THRESHOLDS);
        if (var1 != null && (var1 = var1.trim()).length() > 0) {
            this.markThresholds = Utils.parseInteger(var1);
        } else{
            this.markThresholds = 60;
        }
        this.loadedMarkThresholds = true;
        return this.markThresholds;

    }

    public ContextColumn.ColumnType[] getColumnTypes() {
        if (this.loadedColumnTypes) {
            return this.columnTypes;
        }

        String dataType = this.getParameter(DATA_TYPE);
        if (StringUtils.isNotBlank(dataType)) {
            this.columnTypes = GuardiumClassifierRule.parseColumnTypes(dataType);
        }
        this.loadedColumnTypes = true;

        return this.columnTypes;
    }

    public String getSearchValueLike() {
        return this.searchValueParameters.getSearchValueLike();
    }

    public String getSearchValuePattern() {
        return this.searchValueParameters.getSearchValuePattern();
    }

    public String[] getSearchValues() {
//        if (this.loadedSearchValues) {
//            return this.searchValues;
//        }
//        String searchInGroup = this.getParameter(SEARCH_IN_GROUP);
//        if (StringUtils.isNotBlank(searchInGroup)) {
//            this.searchValues = searchInGroup.split(";"); //GuardiumClassifierRule.getGroupByPrimaryKey(searchInGroup);
//        }
//        this.loadedSearchValues = true;
        return null;// this.searchValues;
    }

    public boolean isValuesSearchWithin() {
        return false;
    }

    public Integer getRequiredHitPercentage() {
        if (this.loadedHitPct) {
            return this.hitPct;
        }
        String hitPercentage = this.getParameter(HIT_PERCENTAGE);
        if (StringUtils.isNotBlank(hitPercentage)) {
            try {
                this.hitPct = Integer.parseInt(hitPercentage);
            } catch (NumberFormatException ignored) {
            }
        }
        this.loadedHitPct = true;
        return this.hitPct;
    }

    @Override
    public ScoringModel getScoringModel() {
        if (this.loadedScoringModel) {
            return this.scoringModel;
        }
        String scoringModelText = this.getParameter(SCORING_MODEL);
        if (StringUtils.isBlank(scoringModelText)) {
            throw new RuntimeException("scoring model text is required");
        }
        String dataTagType = this.getDataTagTypeForDspm();
        try {
            this.scoringModel = scoringModelParser.parseScoringModel(this.getTranslatedName(), dataTagType, scoringModelText, this.record.getDataTagOrderBy());
        } catch (Exception e) {
            String errorMsg = "parsing model ["+this.getTranslatedName()+"] failure ["+e.getMessage()+"], model data is ["+scoringModelText+"]";
            logger.error(errorMsg);
            throw new IllegalArgumentException(errorMsg);
        }
        this.loadedScoringModel = true;
        return scoringModel;
    }

    public String generateParams() {
        return "";
    }

    protected RuleEvaluator getRuleEvaluator() {
        return new ColumnComplexRuleEvaluator(this, this.getRuleRecord().getDataTagOrderBy());
    }
}
