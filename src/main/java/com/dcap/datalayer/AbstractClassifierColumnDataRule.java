package com.dcap.datalayer;

import com.dcap.classifier.RuleEvaluator;
import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.rules.ColumnDataRuleEvaluator;

public abstract class AbstractClassifierColumnDataRule<T extends ClassifierRuleRecord> extends ClassifierRule<T> {
    protected AbstractClassifierColumnDataRule(T record) {
        super(record);
    }

    protected RuleEvaluator getRuleEvaluator() {
        return new ColumnDataRuleEvaluator(this);
    }

    public String getRuleTypeName() {
        String ruleTypeName = super.getRuleTypeName();
        if (ruleTypeName == null) {
            ruleTypeName = "Search For Data";
        }
        return ruleTypeName;
    }

    public abstract ContextTable.TableType[] getTableTypes();

    public abstract TableColumn[] getExcludedTableColumns();

    public abstract String[] getExcludedTables();

    public abstract String[] getExcludedSchemas();

    public abstract String getTableNameLike();

    public abstract String getColumnNameLike();

    public abstract Integer getMinimumLength();

    public abstract Integer getMaximumLength();

    public abstract ContextColumn.ColumnType[] getColumnTypes();

    public abstract boolean showUniqueValues();

    public abstract String getUniqueValueMask();

    public abstract String getSearchValueLike();

    public abstract String getSearchValuePattern();

    public abstract String getEvaluation();

    public abstract String getSQL();

    public abstract String[] getSearchValues();

    public abstract boolean isValuesSearchWithin();

    public abstract Integer getRequiredHitPercentage();

}
