package com.dcap.datalayer;

import com.dcap.classifier.context.AbstractInnerType;
import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.ContextColumn.ColumnType;
import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.ContextTable.TableType;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class GuardiumClassifierRule implements ClassifierRuleRecord {
    //    private static final pvilegeType[] NO_PRIVS = new PrivilegeType[0];
    public static final TableType[] NO_TABLE_TYPES = new TableType[0];
    public static final ColumnType[] NO_COLUMN_TYPES = new ColumnType[0];
    public static final String[] NO_STRINGS = new String[0];
    public static final TableColumn[] NO_COLUMNS = new TableColumn[0];
    static final int TABLE_COLUMN_DELIMITER = 43;
    private final ClsRule rule;
    private final Map<String, String> parameters;

    public GuardiumClassifierRule(ClsRule clsRule, Map<String, String> parameters) {
        if (parameters == null) {
            throw new NullPointerException("parameters is null");
        } else {
            this.rule = clsRule;
            this.parameters = parameters;
        }
    }

    static TableType[] parseTableTypes(String tableTypesText) {
        TableType[] defaultTableTypes = ContextTable.TABLE_TYPES;
        List<AbstractInnerType> var2 = parseTypes(tableTypesText, defaultTableTypes);
        return var2.size() > 0 ? var2.toArray(new TableType[0]) : NO_TABLE_TYPES;
    }

    static ColumnType[] parseColumnTypes(String dataType) {
        List<AbstractInnerType> columnTypes = parseTypes(dataType, ContextColumn.COLUMN_TYPES);
        return columnTypes.size() > 0 ? columnTypes.toArray(new ColumnType[0]) : NO_COLUMN_TYPES;
    }

    static List<AbstractInnerType> parseTypes(String typesText, AbstractInnerType[] innerTypes) {
        List<AbstractInnerType> parsedTypes = new ArrayList<>();

        if (typesText == null) {
            return parsedTypes;
        }
        for (String type : typesText.split(",")) {
            type = type.trim();
            if (StringUtils.isBlank(type)) {
                continue;
            }
            for (AbstractInnerType innerType : innerTypes) {
                if (innerType.isEquivalent(type) && !parsedTypes.contains(innerType)) {
                    parsedTypes.add(innerType);
                }
            }
        }
        return parsedTypes;
    }

    public static String[] getGroupByPrimaryKey(String var0) {
        return NO_STRINGS;
    }

    @Override
    public String getId() {
        return this.rule.getClsRuleId();
    }

    public String getName() {
        return this.rule.getName();
    }

    @Override
    public String getTranslatedName() {
        return this.rule.getTranslatedName();
    }

    @Override
    public String getDataTagTypeForDspm() {
        return this.rule.getDataTagTypeForDspm();
    }

    public String getCategoryName() {
        return this.rule.getCategoryName();
    }

    public String getClassificationName() {
        return this.rule.getClassificationName();
    }

    public ContinueOption getContinueOption() {
        return this.rule.getContinueOption();
    }

    @Override
    public int getDataTagOrderBy() {
        return this.rule.getDataTagOrderBy();
    }

    public String getParameter(String key) {
        return Objects.toString(this.parameters.get(key));
    }

}
