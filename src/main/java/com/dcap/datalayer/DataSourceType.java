package com.dcap.datalayer;

public enum DataSourceType {

   MSSQL("SQL Server"),
   MONGODB("Mongo DB"),
   DYNAMODB("Dynamo DB"),
   MYSQL("MySQL"),
   DORIS("Doris"),
   DAMENG("Da<PERSON><PERSON>"),
   HIVE("Hive"),
   SPARK_SQL("SparkSQL"),
   OCEANBASE_ORACLE("OceanbaseOracle"),
   ORACLE("Oracle"),
   HANA("Hana"),
   DB2("Db2"),
   POSTGRESQL("PostgreSQL"),
   MAXCOMPUTE("MaxCompute"),
   IMPALA("Impala"),
   TRANSWARP_INCEPTOR("TranswarpInceptor"),
   DREMIO("Drem<PERSON>"),
   SINODB("SinoDB"),
   ELASTIC_SEARCH("Elasticsearch"),
   REDIS("redis"),
   CLICKHOUSE("Clickhouse"),
   GBASE8S("gbase8s"),
   GBASE8A("gbase8a"),
   GBASE8C("gbase8c"),
   TRINO("trino"),
   PRESTO("presto"),
   CACHE("cache"),
   SYBASE("sybase"),
   REDSHIFT("Redshift"),
   ;

   private String name;


   DataSourceType(String name) {
      this.name = name;
   }

   public String getName() {
      return name;
   }
}
