package com.dcap.datalayer;

import com.dcap.classifier.RuleEvaluator;
import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.ContextTable;
import com.yd.rules.engine.ScoringModel;

public abstract class AbstractClassifierComplexRule<T extends ClassifierRuleRecord> extends ClassifierRule<T> {
    protected AbstractClassifierComplexRule(T record) {
        super(record);
    }

    protected abstract RuleEvaluator getRuleEvaluator();

    public String getRuleTypeName() {
        String ruleTypeName = super.getRuleTypeName();
        if (ruleTypeName == null) {
            ruleTypeName = "Search For Data";
        }
        return ruleTypeName;
    }

    public abstract ContextTable.TableType[] getTableTypes();

    public abstract TableColumn[] getExcludedTableColumns();

    public abstract String[] getExcludedTables();

    public abstract String[] getExcludedSchemas();

    public abstract String getTableNameLike();

    public abstract String getColumnNameLike();

    public abstract Integer getMinimumLength();

    public abstract Integer getMaximumLength();

    public abstract Integer getMarkThresholds();

    public abstract ContextColumn.ColumnType[] getColumnTypes();

    public abstract String getSearchValueLike();

    public abstract String getSearchValuePattern();


    public abstract String[] getSearchValues();

    public abstract boolean isValuesSearchWithin();

    public abstract Integer getRequiredHitPercentage();

    public abstract ScoringModel getScoringModel();

}
