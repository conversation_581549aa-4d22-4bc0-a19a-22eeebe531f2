package com.dcap.datalayer;

public enum ContinueOption {
    DEFAULT,
    CONTINUE_ON_MATCH,
    CONTINUE_WITH_UNMATCHED_COLUMNS;

    ContinueOption() {
    }

    public static ContinueOption fromConstant(int var0) {
        if (var0 == CONTINUE_ON_MATCH.getConstant()) {
            return CONTINUE_ON_MATCH;
        } else {
            return var0 == CONTINUE_WITH_UNMATCHED_COLUMNS.getConstant() ? CONTINUE_WITH_UNMATCHED_COLUMNS : DEFAULT;
        }
    }

    public static ContinueOption fromBooleans(boolean var0, boolean var1) {
        return var0 ? (var1 ? CONTINUE_WITH_UNMATCHED_COLUMNS : CONTINUE_ON_MATCH) : DEFAULT;
    }

    public int getConstant() {
        return this.ordinal();
    }

    public boolean continueToNextRule(boolean var1) {
        return !var1 || this.equals(CONTINUE_ON_MATCH) || this.equals(CONTINUE_WITH_UNMATCHED_COLUMNS);
    }

    public boolean useUnmatchedColumns() {
        return this.equals(CONTINUE_WITH_UNMATCHED_COLUMNS);
    }
}
