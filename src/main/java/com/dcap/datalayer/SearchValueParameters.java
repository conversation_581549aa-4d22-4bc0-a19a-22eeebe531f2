package com.dcap.datalayer;

public class SearchValueParameters {
    public static final String SEARCH_VALUE_LIKE = "SEARCH_VALUE_LIKE";
    public static final String SEARCH_VALUE_PATTERN = "SEARCH_VALUE_PATTERN";
    private final GuardiumClassifierRule record;
    private boolean loadedSearchValuePattern;
    private boolean loadedSearchValueLike;
    private String searchValueLike;
    private String searchValuePattern;

    protected SearchValueParameters(GuardiumClassifierRule record) {
        this.record = record;
    }

    String getParameter(String var1) {
        return this.record.getParameter(var1);
    }

    String getSearchValueLike() {
        if (this.loadedSearchValueLike) {
            return this.searchValueLike;
        }
        String var1 = this.getParameter(SEARCH_VALUE_LIKE);
        if (var1 != null && (var1 = var1.trim()).length() > 0) {
            this.searchValueLike = var1;
        }
        this.loadedSearchValueLike = true;
        return this.searchValueLike;
    }

    String getSearchValuePattern() {
        if (!this.loadedSearchValuePattern) {
            String var1 = this.getParameter(SEARCH_VALUE_PATTERN);
            if (var1 != null && (var1 = var1.trim()).length() > 0) {
                this.searchValuePattern = var1;
            }

            this.loadedSearchValuePattern = true;
        }

        return this.searchValuePattern;
    }
}
