package com.dcap.datalayer;

import java.util.Arrays;

public class TableRuleParameters {
    public static final String EXCLUDE_SCHEMA = "EXCLUDE_SCHEMA";
    public static final String EXCLUDE_TABLE = "EXCLUDE_TABLE";
    public static final String EXCLUDE_TABLE_COLUMN = "EXCLUDE_TABLE_COLUMN";
    private final GuardiumClassifierRule record;
    private TableColumn[] excludeColumnMembers;
    private String[] excludedTables;
    private String[] excludedSchemas;

    protected TableRuleParameters(GuardiumClassifierRule var1) {
        this.record = var1;
    }

    String getParameter(String var1) {
        return this.record.getParameter(var1);
    }

    public String[] getExcludedSchemas() {
        if (this.excludedSchemas != null) {
            return this.excludedSchemas;
        }
        String excludeSchema = this.getParameter(EXCLUDE_SCHEMA);
        this.excludedSchemas = excludeSchema != null ? new String[]{excludeSchema} : GuardiumClassifierRule.NO_STRINGS;
        return this.excludedSchemas;
    }

    public String[] getExcludedTables() {
        String[] var1 = this.excludedTables;
        if (var1 == null) {
            String excludeTable = this.getParameter(EXCLUDE_TABLE);
            this.excludedTables = var1 = excludeTable != null ? new String[]{excludeTable} : GuardiumClassifierRule.NO_STRINGS;
        }
        return var1;
    }

    public TableColumn[] getExcludedTableColumns() {
        TableColumn[] tableColumns = this.excludeColumnMembers;
        if (tableColumns == null) {
            String excludeColumn = this.getParameter(EXCLUDE_TABLE_COLUMN);
            if (excludeColumn == null) {
                tableColumns = GuardiumClassifierRule.NO_COLUMNS;
            } else {
                String[] var3 = new String[]{excludeColumn}; // GuardiumClassifierRule.getGroupByPrimaryKey(var2);
                int var4 = 0;
                tableColumns = new TableColumn[var3.length];
                for (String var6 : var3) {
                    int var7 = var6.indexOf(43);
                    if (var7 >= 0) {
                        String tableName = var6.substring(0, var7);
                        String columnName = var6.substring(var7 + 1);
                        tableColumns[var4++] = new TableColumn(tableName, columnName);
                    }
                }
                if (var4 != var3.length) {
                    tableColumns = Arrays.copyOf(tableColumns, var4);
                }
            }
            this.excludeColumnMembers = tableColumns;
        }
        return tableColumns;
    }
}
