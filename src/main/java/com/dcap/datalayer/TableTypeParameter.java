package com.dcap.datalayer;

import com.dcap.classifier.context.ContextTable;
import org.apache.commons.lang3.StringUtils;

public class TableTypeParameter {
    public static final String TABLE_TYPE = "TABLE_TYPE";
    private final GuardiumClassifierRule record;
    private ContextTable.TableType[] tableTypes;
    private boolean loadedTableTypes;

    protected TableTypeParameter(GuardiumClassifierRule record) {
        this.record = record;
    }

    String getParameter(String var1) {
        return this.record.getParameter(var1);
    }

    public ContextTable.TableType[] getTableTypes() {
        if (this.loadedTableTypes) {
            return this.tableTypes;
        }
        String tableType = this.getParameter(TABLE_TYPE);
        if (StringUtils.isNotBlank(tableType)) {
            this.tableTypes = GuardiumClassifierRule.parseTableTypes(tableType);
        }
        this.loadedTableTypes = true;
        return this.tableTypes;
    }
}
