package com.dcap.datalayer;

import com.dcap.classifier.RuleEvaluator;
import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.rules.ColumnNameRuleEvaluator;

public abstract class AbstractClassifierColumnNameRule<T extends ClassifierRuleRecord> extends ClassifierRule<T> {
    protected AbstractClassifierColumnNameRule(T record) {
        super(record);
    }

    protected RuleEvaluator getRuleEvaluator() {
        return new ColumnNameRuleEvaluator(this);
    }

    public abstract ContextTable.TableType[] getTableTypes();

    public abstract TableColumn[] getExcludedTableColumns();

    public abstract String[] getExcludedTables();

    public abstract String[] getExcludedSchemas();

    public abstract String getTableNameLike();

    public abstract String getColumnNameLike();

    public String getRuleTypeName() {
        String var1 = super.getRuleTypeName();
        if (var1 == null) {
            var1 = "Catalog Search";
        }

        return var1;
    }
}

