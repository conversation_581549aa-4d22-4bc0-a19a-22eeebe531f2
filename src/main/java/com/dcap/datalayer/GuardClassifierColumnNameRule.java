package com.dcap.datalayer;

import com.dcap.classifier.RuleEvaluator;
import com.dcap.classifier.context.ContextTable;

public class GuardClassifierColumnNameRule extends AbstractClassifierColumnNameRule<GuardiumClassifierRule> {
    private final TableRuleParameters tableRuleParameters;
    private final TableTypeParameter tableTypeParameter;
    private final TableNameParameters tableNameParameters;

    public GuardClassifierColumnNameRule(GuardiumClassifierRule record) {
        super(record);
        this.tableRuleParameters = new TableRuleParameters(record);
        this.tableTypeParameter = new TableTypeParameter(record);
        this.tableNameParameters = new TableNameParameters(record);
    }

    public String getParameter(String var1) {
        return (this.getRuleRecord()).getParameter(var1);
    }

    public ContextTable.TableType[] getTableTypes() {
        return this.tableTypeParameter.getTableTypes();
    }

    public String[] getExcludedSchemas() {
        return this.tableRuleParameters.getExcludedSchemas();
    }

    public String[] getExcludedTables() {
        return this.tableRuleParameters.getExcludedTables();
    }

    public TableColumn[] getExcludedTableColumns() {
        return this.tableRuleParameters.getExcludedTableColumns();
    }

    public String getTableNameLike() {
        return this.tableNameParameters.getTableNameLike();
    }

    public String getColumnNameLike() {
        return this.tableNameParameters.getColumnNameLike();
    }

    public String generateParams() {
        return "";
    }

    public RuleEvaluator getRuleEvaluator() {
        return super.getRuleEvaluator();
    }
}
