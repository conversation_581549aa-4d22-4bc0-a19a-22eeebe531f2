package com.dcap.datalayer;

import java.util.List;

public class ClsRule {
    private String clsRuleId;

    private Integer clsPolicyId;

    private Integer clsRuleTypeId;

    private String name;

    private String description;

    private Integer evalSequence;

    private String categoryName;

    private String classificationName;

    private Integer continueFlag;

    private String timestamp;

    private String localeScope;

    private String translatedName;

    private String dataTagTypeForDspm;

    private int dataTagOrderBy;

    private int continue_flag;

    protected List collClsRuleParms;

    public String getClsRuleId() {
        return clsRuleId;
    }

    public void setClsRuleId(String clsRuleId) {
        this.clsRuleId = clsRuleId;
    }

    public Integer getClsPolicyId() {
        return clsPolicyId;
    }

    public void setClsPolicyId(Integer clsPolicyId) {
        this.clsPolicyId = clsPolicyId;
    }

    public Integer getClsRuleTypeId() {
        return clsRuleTypeId;
    }

    public void setClsRuleTypeId(Integer clsRuleTypeId) {
        this.clsRuleTypeId = clsRuleTypeId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public Integer getEvalSequence() {
        return evalSequence;
    }

    public void setEvalSequence(Integer evalSequence) {
        this.evalSequence = evalSequence;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName == null ? null : categoryName.trim();
    }

    public String getClassificationName() {
        return classificationName;
    }

    public void setClassificationName(String classificationName) {
        this.classificationName = classificationName == null ? null : classificationName.trim();
    }

    public Integer getContinueFlag() {
        return continueFlag;
    }

    public void setContinueFlag(Integer continueFlag) {
        this.continueFlag = continueFlag;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp == null ? null : timestamp.trim();
    }

    public String getLocaleScope() {
        return localeScope;
    }

    public void setLocaleScope(String localeScope) {
        this.localeScope = localeScope == null ? null : localeScope.trim();
    }

    public String getTranslatedName() {
        return translatedName;
    }

    public void setTranslatedName(String translatedName) {
        this.translatedName = translatedName == null ? null : translatedName.trim();
    }

    public ContinueOption getContinueOption() {
        return ContinueOption.fromConstant(this.getContinueFlag());
    }


    public String getDataTagTypeForDspm() {
        return dataTagTypeForDspm;
    }

    public void setDataTagTypeForDspm(String dataTagTypeForDspm) {
        this.dataTagTypeForDspm = dataTagTypeForDspm;
    }

    public int getDataTagOrderBy() {
        return dataTagOrderBy;
    }

    public void setDataTagOrderBy(int dataTagOrderBy) {
        this.dataTagOrderBy = dataTagOrderBy;
    }
}
