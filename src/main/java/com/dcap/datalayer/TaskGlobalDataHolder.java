package com.dcap.datalayer;

import com.dcap.classifier.RuleEvaluator;
import com.dcap.classifier.custom.CustomerDefinedEvaluationProxy;
import com.dcap.classifier.custom.Evaluation;
import com.dcap.classifier.writer.DataWriter;
import com.dcap.classifier.writer.DataWriterFactory;
import com.dcap.classifier.writer.RecordTypeEnum;
import com.dcap.utils.JSON;
import com.dcap.utils.Utils;
import com.google.common.collect.Lists;
import com.yd.dcap.classifier.taskreport.ScanDbReport;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.dcap.common.utils.sm4.SM4Utils;
import com.yd.dcap.probe.client.ProbeClientTaskUtil;
import com.yd.dcap.probe.client.TaskConfig;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import com.yd.rules.engine.ScoringModel;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileNotFoundException;
import java.util.*;
import java.util.stream.Collectors;

public class TaskGlobalDataHolder implements AutoCloseable {

    private final Logger LOG = LoggerFactory.getLogger(getClass());
    @Getter
    private final Long scanJobHistoryId;

    @Getter
    private final String taskType;
    @Getter
    private final TaskConfig.TaskParam taskParam;

    @Getter
    private final Map<String,String> hashTables;

    @Getter
    private final TaskConfig.DataSource dataSource;

    // 当前任务的版本号。只有 DSPM 需要使用。DCAP 版本号固定是 0
    @Getter
    private final Long currentVersion;
    @Getter
    private final List<RuleEvaluator> ruleEvaluatorList;
    @Getter
    private final Map<String, Set<String>> dataMarkings;

    @Getter
    private final DataWriter inventoryWriter;

//    @Getter
//    private final DataWriter databaseUsersWriter;
//
//    @Getter
//    private final DataWriter dbPrivilegesWriter;
//
//    @Getter
//    private final DataWriter dbPrivilegesReportWriter;

    @Getter
    private final ProbeClientTaskContext probeClientTaskContext;

    private final Map<String, ScoringModel> enhancedRuleModelMap = new HashMap<>();

    private String dataDictionary;

    public TaskGlobalDataHolder(ProbeClientTaskContext probeClientTaskContext, TaskConfig taskConfig) throws FileNotFoundException {
        this.probeClientTaskContext = probeClientTaskContext;
        this.taskType = probeClientTaskContext.getTaskType();
        this.scanJobHistoryId = taskConfig.getScanJobHistoryId();
        this.currentVersion = taskConfig.getCurrentVersion();
        this.dataSource = taskConfig.getDatasource();
        Map<String, String> hashTables = taskConfig.getHashTables();
        if (hashTables == null){
            this.hashTables = new HashMap<>();
        } else {
            this.hashTables = hashTables;
        }
        String dict = taskConfig.getDataDictionary();
        if (dict != null ){
            try{
                this.dataDictionary = new String(Base64.getUrlDecoder().decode(dict));
            } catch (Exception e){
                LOG.warn("无法 Base64 解码 dataDictionary  尝试按原始字符串解析。");
                this.dataDictionary = dict;
            }
        } else {
            this.dataDictionary = null;
        }


        if (taskConfig.getTaskParam() == null) {
            taskParam = new TaskConfig.TaskParam(false, false, true, 1000, 98, true, false,
                    100, 180, false, 100,
                    1, null, null, null, "*",
                    "TABLE,VIEW,SYNONYM", "*", null, 16,
                    255, 0, "NUMBER,TEXT", new TaskConfig.TaskParam.ScanRange(),
                    60, true, true);
        } else {
            taskParam = taskConfig.getTaskParam();
            if (taskParam.getIncrementalEvaluationEnabled() == null) {
                taskParam.setIncrementalEvaluationEnabled(false);
            }

            if (taskParam.getSamplingReverseOrder() == null){
                taskParam.setSamplingReverseOrder(false);
            }

            if (taskParam.isUseRandomSampling()) {
                taskParam.setUseRandomSampling(true);
            }

            if (taskParam.getSampleCount() <= 0) {
                taskParam.setSampleCount(100);
            }
            if (taskParam.getSampleSqlTimeout() <= 0) {
                taskParam.setSampleSqlTimeout(10);
            }
            if (taskParam.getTableRowCount() <= 0) {
                taskParam.setTableRowCount(100);
            }
            if (taskParam.getTableRowCountUnit() <= 0) {
                taskParam.setTableRowCountUnit(1);
            }

            if (StringUtils.isBlank(taskParam.getTableNameLike())) {
                taskParam.setTableNameLike("*");
            }
            if (StringUtils.isBlank(taskParam.getColumnNameLike())) {
                taskParam.setColumnNameLike("*");
            }
            if (StringUtils.isBlank(taskParam.getSearchValueLike())) {
                taskParam.setSearchValueLike("*");
            }
            if (StringUtils.isBlank(taskParam.getTableType())) {
                taskParam.setTableType("TABLE");
            } else if (taskParam.getTableType().trim().startsWith("[")) {
                List<String> tableTypeList = JSON.from(taskParam.getTableType()).toObject(List.class);
                String tableTypes = String.join(",", tableTypeList);
                taskParam.setTableType(tableTypes);
            }
            if (taskParam.getMinimumLength() <= 0) {
                taskParam.setMinimumLength(0);
            }
            if (taskParam.getMaximumLength() <= 0) {
                taskParam.setMaximumLength(256);
            }
            if (taskParam.getMaximumLength() <= taskParam.getMinimumLength()) {
                taskParam.setMaximumLength(taskParam.getMinimumLength());
            }
            if (StringUtils.isBlank(taskParam.getDataType())) {
                taskParam.setDataType("NUMBER,TEXT");
            } else if (taskParam.getDataType().trim().startsWith("[")) {
                List<String> dataTypeList = JSON.from(taskParam.getDataType()).toObject(List.class);
                String dataTypes = String.join(",", dataTypeList);
                taskParam.setDataType(dataTypes);
            }
            if (taskParam.getScanRange() == null) {
                taskParam.setScanRange(new TaskConfig.TaskParam.ScanRange());
            }
            if (taskParam.getScanRange().getSelectedDatabase() == null){
                taskParam.getScanRange().setSelectedDatabase(new HashSet<>());
            }
            if (taskParam.getScanRange().getSelectedSchema() == null){
                taskParam.getScanRange().setSelectedSchema(new HashSet<>());
            }
            if (taskParam.getScanRange().getSelectedTable() == null){
                taskParam.getScanRange().setSelectedTable(new HashSet<>());
            }
            if (taskParam.getScanRange().getSelectedView() == null){
                taskParam.getScanRange().setSelectedView(new HashSet<>());
            }
            if (taskParam.getScanRange().getSelectedSynonym() == null){
                taskParam.getScanRange().setSelectedSynonym(new HashSet<>());
            }
            if (taskParam.getScanRange().getExcludedDatabase() == null){
                taskParam.getScanRange().setExcludedDatabase(new HashSet<>());
            }
            if (taskParam.getScanRange().getExcludedSchema() == null){
                taskParam.getScanRange().setExcludedSchema(new HashSet<>());
            }
            if (taskParam.getScanRange().getExcludedTable() == null){
                taskParam.getScanRange().setExcludedTable(new HashSet<>());
            }
            if (taskParam.getScanRange().getExcludedView() == null){
                taskParam.getScanRange().setExcludedView(new HashSet<>());
            }
            if (taskParam.getScanRange().getExcludedSynonym() == null){
                taskParam.getScanRange().setExcludedSynonym(new HashSet<>());
            }

            if (taskParam.getMarkThresholds() == null || taskParam.getMarkThresholds() <= 0) {
                taskParam.setMarkThresholds(60);
            }
            if (taskParam.getExcludeEmptyValues() == null) {
                taskParam.setExcludeEmptyValues(false);
            }
            // 如果没有设置或者设置为 0 时。
            if (taskParam.getEmptyPercentage() == null || taskParam.getEmptyPercentage() == 0) {
                taskParam.setEmptyPercentage(98);
            }
            if (taskParam.getIntelligentLineageEnabled() == null) {
                taskParam.setIntelligentLineageEnabled(true);
            }
            if (taskParam.getIntelligentFingerprintEnabled() == null) {
                taskParam.setIntelligentFingerprintEnabled(true);
            }
        }

        this.ruleEvaluatorList = this.getRules(taskConfig.getPolicies());

        enhancedRuleModelMap.putAll(this.ruleEvaluatorList.stream().map(evaluator -> {
                    ClassifierRule<? extends ClassifierRuleRecord> ruleDefinition = evaluator.getRuleDefinition();
                    if (ruleDefinition instanceof GuardClassifierComplexRule){
                        try {
                            ScoringModel scoringModel = ((GuardClassifierComplexRule) ruleDefinition).getScoringModel();
                            return new AbstractMap.SimpleImmutableEntry<>(scoringModel.getDataTag(), scoringModel);
                        } catch (Exception e){
                            probeClientTaskContext
                                    .reportErrorOccurredExecuting(StatusRecord.Position.Scan, null, e.getMessage(), null);
                            return null;
                        }
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (value1,value2) -> value1)));

        this.dataMarkings = getDataMarkings(taskConfig.getSensDataMarkings());
        this.probeClientTaskContext.recordField(ScanDbReport.FIELD_RULE_COUNT, this.ruleEvaluatorList.size());

        this.inventoryWriter = DataWriterFactory.createDataWriter(scanJobHistoryId, RecordTypeEnum.DB_SCAN_RESULT);
//        this.databaseUsersWriter = DataWriterFactory.createDataWriter(scanJobHistoryId, LogTypeEnum.DATABASE_USERS_INFO);
//        this.dbPrivilegesWriter = DataWriterFactory.createDataWriter(scanJobHistoryId, LogTypeEnum.DATABASE_PRIVILEGES_INFO);
//        this.dbPrivilegesReportWriter = DataWriterFactory.createDataWriter(scanJobHistoryId, LogTypeEnum.DATABASE_PRIVILEGES_REPORT_INFO);
    }

    private List<RuleEvaluator> getRules(List<TaskConfig.Policy> policies) {
        if (policies == null || policies.isEmpty()){
            return new ArrayList<>();
        }
        // 此处读取一个环境变量的值，java_engine 为 false 就使用 jni
        String useJavaEngine = System.getenv().get("java_engine");
        if (StringUtils.isBlank(useJavaEngine)) {
            useJavaEngine = System.getenv("JAVA_ENGINE");
        }
        if (!Boolean.parseBoolean(useJavaEngine)){
            List<Map<String,Object>> exprList = policies.stream()
                    .filter(Objects::nonNull)
                    .flatMap((policy) -> policy.getPatterns().stream())
                    .map(pattern -> {
                        try {
                            Map<String,Object> model = JSON.from(SM4Utils.decCheckSm4ForEcb(pattern.getExpr())).toObject(Map.class);
                            model.put("model_id", Math.abs(model.get("model_id").hashCode()));
                            return model;
                        } catch (Exception e){
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            Map<String,Object> classifyModels = UtilMisc.toMap(
                    "classify_models", exprList
            );

            TaskConfig.Pattern pattern = new TaskConfig.Pattern("rule", JSON.from(classifyModels).convertToYaml());
            TaskConfig.Policy policy = new TaskConfig.Policy("id", "name", "builtin", 1L, "dataTag", "dataTagTypeForDspm", 50, 1L, "levelName", Lists.newArrayList(pattern));
            return Lists.newArrayList(getRuleEvaluator(policy, pattern));
        }
        return policies.stream()
                .flatMap(policy -> {
                            // 如果敏感数据类型定义中，存在 rule 类型的 pattern，那么就只使用 rule 类型的 pattern
                            if (policy.getPatterns().stream().filter(Objects::nonNull)
                                    .anyMatch(pattern -> "rule".equalsIgnoreCase(pattern.getType()))
                            ) {
                                return policy.getPatterns().stream().filter(Objects::nonNull)
                                        .filter(pattern -> "rule".equalsIgnoreCase(pattern.getType()))
                                        .map(pattern -> getRuleEvaluator(policy, pattern));
                            }
                            return policy.getPatterns().stream().filter(Objects::nonNull)
                                    .map(pattern -> getRuleEvaluator(policy, pattern));
                        }
                )
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    private Map<String, Set<String>> getDataMarkings(List<TaskConfig.SensDataMarking> sensDataMarkings) {
        if (sensDataMarkings == null){
            return new HashMap<>();
        }
        return sensDataMarkings.stream()
                .flatMap((sensDataMarking) -> {
                    String manualTagsKey = sensDataMarking.getTenantId() +
                            "_" + sensDataMarking.getDatasourceId() +
                            "_" + sensDataMarking.getQualifiedName() + "_ManualTags";
                    Set<String> manualTagsSet = JSON.from(sensDataMarking.getManualTags()).toObject(HashSet.class);
                    AbstractMap.SimpleImmutableEntry<String, Set<String>> manualTagsEntry = new AbstractMap.SimpleImmutableEntry<>(manualTagsKey, manualTagsSet);

                    String deletedAutoTagsKey = sensDataMarking.getTenantId() +
                            "_" + sensDataMarking.getDatasourceId() +
                            "_" + sensDataMarking.getQualifiedName() + "_DeletedAutoTags";
                    Set<String> deletedAutoTagsSet = JSON.from(sensDataMarking.getDeletedAutoTags()).toObject(HashSet.class);
                    AbstractMap.SimpleImmutableEntry<String, Set<String>> deletedAutoTagsEntry = new AbstractMap.SimpleImmutableEntry<>(deletedAutoTagsKey, deletedAutoTagsSet);
                    return Lists.newArrayList(manualTagsEntry, deletedAutoTagsEntry).stream();
                })
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (v1, v2) -> {
                    return v1;
                }));
    }

    private RuleEvaluator getRuleEvaluator(TaskConfig.Policy policy,
                                           TaskConfig.Pattern pattern) {
        ClsRule clsRule = new ClsRule();
        // clsRule 对应 validator 所以这里要调整一下，传递一个其他值来指定 validator
        clsRule.setName(policy.getName());
        clsRule.setTranslatedName(policy.getDataTag());
        clsRule.setClsRuleId(policy.getId());

        // 是否继续匹配的标记。数字对应枚举的 ContinueOption 序数。
        clsRule.setContinueFlag(1);
        clsRule.setCategoryName(String.valueOf(policy.getLevelName()));
        clsRule.setClassificationName(String.valueOf(policy.getLevel()));
        // todo: dataTagType 用于 dspm ，同时 dspm 有一个排序字段，排序字段是为了实现 基础类型->业务类型->复合类型这样的执行顺序。
        clsRule.setDataTagTypeForDspm(policy.getDataTagTypeForDspm());
        clsRule.setDataTagOrderBy(policy.getDataTagOrderBy());

        RuleEvaluator ruleEvaluator = null;
        if ("data".equalsIgnoreCase(pattern.getType())) {
            Map<String, String> params = Utils.toMap(
                    TableRuleParameters.EXCLUDE_SCHEMA, taskParam.getExcludeSchema(),
                    TableRuleParameters.EXCLUDE_TABLE, taskParam.getExcludeTable(),
                    TableRuleParameters.EXCLUDE_TABLE_COLUMN, taskParam.getExcludeColumn(),
                    TableTypeParameter.TABLE_TYPE, taskParam.getTableType(),
                    GuardClassifierColumnDataRule.DATA_TYPE, taskParam.getDataType(),
                    GuardClassifierColumnDataRule.MINIMUM_LENGTH, String.valueOf(taskParam.getMinimumLength()),
                    GuardClassifierColumnDataRule.MAXIMUM_LENGTH, String.valueOf(taskParam.getMaximumLength()),

                    TableNameParameters.TABLE_NAME_LIKE, taskParam.getTableNameLike(),
                    TableNameParameters.COLUMN_NAME_LIKE, taskParam.getColumnNameLike(),

                    // 命中率
                    GuardClassifierColumnDataRule.HIT_PERCENTAGE, String.valueOf(taskParam.getHitPercentage()),
                    // 当开启 SHOW_UNIQUE_VALUES 时，不能使用命中率  HIT_PERCENTAGE
                    // Show Unique Values 是否显示一些比较敏感数据，比如身份账号，这个选项需要配置  UNIQUE_VALUE_MASK 一起用
                    GuardClassifierColumnDataRule.SHOW_UNIQUE_VALUES, "false",
                    // 这个为匹配成功的列值进行脱敏的正则，只有当 SHOW_UNIQUE_VALUES 为 true 时生效。
//                    GuardiumClassifierRule.GuardClassifierColumnDataRule.UNIQUE_VALUE_MASK, uniqueValueMaskPattern,

//                    GuardiumClassifierRule.GuardClassifierColumnDataRule.EVALUATE_GROUP_MARKER, null,

//                    GuardiumClassifierRule.GuardClassifierColumnDataRule.EVALUATION_NAME, null,

//                    GuardiumClassifierRule.GuardClassifierColumnDataRule.SEARCH_IN_SQL, null,

                    // 传递一组数据，使用 ; 号分隔，从这一组里进行匹配，相当于关键字
                    GuardClassifierColumnDataRule.SEARCH_IN_GROUP, null,

                    SearchValueParameters.SEARCH_VALUE_LIKE, pattern.getExpr() // taskParam.getSearchValueLike()
//                    , GuardiumClassifierRule.SearchValueParameters.SEARCH_VALUE_PATTERN, pattern.getExpr()
            );
            GuardClassifierColumnDataRule rule = new GuardClassifierColumnDataRule(new GuardiumClassifierRule(clsRule, params));
            ruleEvaluator = rule.getRuleEvaluator();
        } else if ("catalog".equalsIgnoreCase(pattern.getType())) {
            Map<String, String> params = Utils.toMap(
                    TableRuleParameters.EXCLUDE_SCHEMA, taskParam.getExcludeSchema(),
                    TableRuleParameters.EXCLUDE_TABLE, taskParam.getExcludeTable(),
                    TableRuleParameters.EXCLUDE_TABLE_COLUMN, taskParam.getExcludeColumn(),
                    TableTypeParameter.TABLE_TYPE, taskParam.getTableType(),
                    TableNameParameters.TABLE_NAME_LIKE, taskParam.getTableNameLike(),
                    TableNameParameters.COLUMN_NAME_LIKE, pattern.getExpr()
            );

            GuardClassifierColumnNameRule rule = new GuardClassifierColumnNameRule(new GuardiumClassifierRule(clsRule, params));
            ruleEvaluator = rule.getRuleEvaluator();
        } else if ("rule".equalsIgnoreCase(pattern.getType())) {
            Map<String, String> params = Utils.toMap(
                    TableRuleParameters.EXCLUDE_SCHEMA, taskParam.getExcludeSchema(),
                    TableRuleParameters.EXCLUDE_TABLE, taskParam.getExcludeTable(),
                    TableRuleParameters.EXCLUDE_TABLE_COLUMN, taskParam.getExcludeColumn(),
                    TableTypeParameter.TABLE_TYPE, taskParam.getTableType(),
                    GuardClassifierColumnDataRule.DATA_TYPE, taskParam.getDataType(),
                    GuardClassifierColumnDataRule.MINIMUM_LENGTH, String.valueOf(taskParam.getMinimumLength()),
                    GuardClassifierColumnDataRule.MAXIMUM_LENGTH, String.valueOf(taskParam.getMaximumLength()),
                    GuardClassifierColumnDataRule.MARK_THRESHOLDS, String.valueOf(taskParam.getMarkThresholds()),

                    TableNameParameters.TABLE_NAME_LIKE, taskParam.getTableNameLike(),
                    TableNameParameters.COLUMN_NAME_LIKE, taskParam.getColumnNameLike(),

                    // 命中率
                    GuardClassifierColumnDataRule.HIT_PERCENTAGE, String.valueOf(taskParam.getHitPercentage()),
                    GuardClassifierColumnDataRule.SHOW_UNIQUE_VALUES, "false",
                    GuardClassifierComplexRule.SCORING_MODEL, pattern.getExpr(),
                    "scanJobHistoryId", this.scanJobHistoryId,
                    "tenantId", this.probeClientTaskContext.getTenantId(),
                    "dataDictionary", dataDictionary
            );

            AbstractClassifierComplexRule<GuardiumClassifierRule> rule;
            // 此处读取一个环境变量的值，java_engine 为 false 就使用 jni
            String useJavaEngine = System.getenv().get("java_engine");
            if (StringUtils.isBlank(useJavaEngine)) {
                useJavaEngine = System.getenv("JAVA_ENGINE");
            }
            if (!Boolean.parseBoolean(useJavaEngine)){
                rule = new GuardClassifierJniComplexRule(new GuardiumClassifierRule(clsRule, params));
            } else {
                rule = new GuardClassifierComplexRule(new GuardiumClassifierRule(clsRule, params));
            }
            ruleEvaluator = rule.getRuleEvaluator();
        } else {
            String errorMsg ="Not supported type: " + pattern.getType()+", will ignore the current rule evaluator："+policy.getName()+"["+policy.getDataTag()+"]";
            LOG.error(errorMsg);
            try {
                ProbeClientTaskContext probeClientTaskContext = ProbeClientTaskUtil.getInstance().getProbeClientTaskContext(this.taskType, this.scanJobHistoryId);
                probeClientTaskContext.reportErrorOccurredExecuting(StatusRecord.Position.Scan, JSON.from(policy).toString(), errorMsg,null);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return ruleEvaluator;
    }

    @Override
    public void close() throws Exception {
        if (this.inventoryWriter != null) {
            this.inventoryWriter.close();
        }
//        if (this.databaseUsersWriter != null){
//            this.databaseUsersWriter.close();
//        }
//        if (this.dbPrivilegesWriter != null){
//            this.dbPrivilegesWriter.close();
//        }
//        if (this.dbPrivilegesReportWriter != null){
//            this.dbPrivilegesReportWriter.close();
//        }
    }

    public Evaluation loadEvaluationClass(String evaluationName) throws SecurityException, ReflectiveOperationException {
        return new CustomerDefinedEvaluationProxy(evaluationName);
    }

    public Map<String, ScoringModel> getEnhancedRuleModelMap() {
        return enhancedRuleModelMap;
    }
}
