package com.dcap.datalayer;

import com.dcap.classifier.RuleEvaluator;
import com.dcap.classifier.context.ContextColumn;
import com.dcap.classifier.context.ContextTable;
import com.dcap.utils.Utils;
import org.apache.commons.lang3.StringUtils;

public class GuardClassifierColumnDataRule extends AbstractClassifierColumnDataRule<GuardiumClassifierRule> {
    public static final String DATA_TYPE = "DATA_TYPE";
    public static final String MINIMUM_LENGTH = "MINIMUM_LENGTH";
    public static final String MAXIMUM_LENGTH = "MAXIMUM_LENGTH";

    public static final String MARK_THRESHOLDS = "MARK_THRESHOLDS";
    public static final String EVALUATION_NAME = "EVALUATION_NAME";
    public static final String EVALUATE_GROUP_MARKER = "EVALUATE_GROUP_MARKER";
    public static final String HIT_PERCENTAGE = "HIT_PERCENTAGE";
    public static final String SEARCH_IN_SQL = "SEARCH_IN_SQL";
    public static final String SEARCH_IN_GROUP = "SEARCH_IN_GROUP";
    public static final String EVALUATE_MARKER = "EVALUATE_GROUP_MARKER";
    public static final String SHOW_UNIQUE_VALUES = "SHOW_UNIQUE_VALUES";
    public static final String UNIQUE_VALUE_MASK = "UNIQUE_VALUE_MASK";
    private final TableRuleParameters tableRuleParameters;
    private final TableTypeParameter tableTypeParameter;
    private final TableNameParameters tableNameParameters;
    private final SearchValueParameters searchValueParameters;
    private boolean loadedSQL;
    private boolean loadedHitPct;
    private boolean loadedShowUniqueValues;
    private boolean loadedEvaluation;
    private boolean loadedMinLength;
    private boolean loadedMaxLength;
    private boolean loadedColumnTypes;
    private boolean loadedUniqueValueMask;
    private boolean loadedSearchValues;
    private String[] searchValues;
    private Integer minLength;
    private Integer maxLength;
    private Integer hitPct;
    private ContextColumn.ColumnType[] columnTypes;
    private boolean showUniqueValues;
    private String uniqueValueMask;
    private String evaluation;
    private String sql;

    public GuardClassifierColumnDataRule(GuardiumClassifierRule rule) {
        super(rule);
        this.tableTypeParameter = new TableTypeParameter(rule);
        this.tableRuleParameters = new TableRuleParameters(rule);
        this.tableNameParameters = new TableNameParameters(rule);
        this.searchValueParameters = new SearchValueParameters(rule);
    }

    String getParameter(String paramName) {
        return this.getRuleRecord().getParameter(paramName);
    }

    public ContextTable.TableType[] getTableTypes() {
        return this.tableTypeParameter.getTableTypes();
    }

    public String[] getExcludedSchemas() {
        return this.tableRuleParameters.getExcludedSchemas();
    }

    public String[] getExcludedTables() {
        return this.tableRuleParameters.getExcludedTables();
    }

    public TableColumn[] getExcludedTableColumns() {
        return this.tableRuleParameters.getExcludedTableColumns();
    }

    public String getTableNameLike() {
        return this.tableNameParameters.getTableNameLike();
    }

    public String getColumnNameLike() {
        return this.tableNameParameters.getColumnNameLike();
    }

    public Integer getMinimumLength() {
        if (this.loadedMinLength) {
            return this.minLength;
        }
        String var1 = this.getParameter(MINIMUM_LENGTH);
        if (var1 != null && !(var1 = var1.trim()).isEmpty()) {
            this.minLength = Utils.parseInteger(var1);
        }
        this.loadedMinLength = true;
        return this.minLength;
    }

    public Integer getMaximumLength() {
        if (this.loadedMaxLength) {
            return this.maxLength;
        }
        String var1 = this.getParameter(MAXIMUM_LENGTH);
        if (var1 != null && (var1 = var1.trim()).length() > 0) {
            this.maxLength = Utils.parseInteger(var1);
        }
        this.loadedMaxLength = true;
        return this.maxLength;
    }

    public ContextColumn.ColumnType[] getColumnTypes() {
        if (this.loadedColumnTypes) {
            return this.columnTypes;
        }

        String dataType = this.getParameter(DATA_TYPE);
        if (StringUtils.isNotBlank(dataType)) {
            this.columnTypes = GuardiumClassifierRule.parseColumnTypes(dataType);
        }
        this.loadedColumnTypes = true;

        return this.columnTypes;
    }

    public boolean showUniqueValues() {
        if (this.loadedShowUniqueValues) {
            return this.showUniqueValues;
        }
        // 如果未加载 showUniqueValues 参数，此时先加载
        this.loadedShowUniqueValues = true;
        this.showUniqueValues = Utils.isTrue(this.getParameter(SHOW_UNIQUE_VALUES));
        return this.showUniqueValues;
    }

    public String getUniqueValueMask() {
        if (this.loadedUniqueValueMask) {
            return this.uniqueValueMask;
        }

        String uniqueValueMask = this.getParameter(UNIQUE_VALUE_MASK);
        if (StringUtils.isNotBlank(uniqueValueMask)) {
            this.uniqueValueMask = uniqueValueMask;
        }
        this.loadedUniqueValueMask = true;
        return this.uniqueValueMask;
    }

    public String getSearchValueLike() {
        return this.searchValueParameters.getSearchValueLike();
    }

    public String getSearchValuePattern() {
        return this.searchValueParameters.getSearchValuePattern();
    }

    public String getEvaluation() {
        if (this.loadedEvaluation) {
            return this.evaluation;
        }
        String evaluationName = this.getParameter(EVALUATION_NAME);
        if (evaluationName != null && (evaluationName = evaluationName.trim()).length() > 0) {
            this.evaluation = evaluationName;
        }
        this.loadedEvaluation = true;
        return this.evaluation;
    }

    public String getSQL() {
        if (this.loadedSQL) {
            return this.sql;
        }
        String searchInSql = this.getParameter(SEARCH_IN_SQL);
        if (StringUtils.isNotBlank(searchInSql)) {
            this.sql = searchInSql;
        }
        this.loadedSQL = true;
        return this.sql;
    }

    public String[] getSearchValues() {
        if (this.loadedSearchValues) {
            return this.searchValues;
        }
        String searchInGroup = this.getParameter(SEARCH_IN_GROUP);
        if (StringUtils.isNotBlank(searchInGroup)) {
            this.searchValues = searchInGroup.split(";"); //GuardiumClassifierRule.getGroupByPrimaryKey(searchInGroup);
        }
        this.loadedSearchValues = true;
        return this.searchValues;
    }

    public boolean isValuesSearchWithin() {
        return false;
    }

    public Integer getRequiredHitPercentage() {
        if (this.loadedHitPct) {
            return this.hitPct;
        }
        String hitPercentage = this.getParameter(HIT_PERCENTAGE);
        if (StringUtils.isNotBlank(hitPercentage)) {
            try {
                this.hitPct = Integer.parseInt(hitPercentage);
            } catch (NumberFormatException ignored) {
            }
        }
        this.loadedHitPct = true;
        return this.hitPct;
    }

    public String generateParams() {
        return "";
    }

    public RuleEvaluator getRuleEvaluator() {
        return super.getRuleEvaluator();
    }
}
