package com.dcap.datalayer;

import com.dcap.classifier.RuleEvaluator;
import com.dcap.classifier.context.ContextTable;

public abstract class ClassifierRule<T extends ClassifierRuleRecord> {
    protected final T record;
    private String cachedParams;

    protected ClassifierRule(T record) {
        this.record = record;
    }

    public T getRuleRecord() {
        return this.record;
    }

    protected abstract RuleEvaluator getRuleEvaluator();

    public String getClassificationName() {
        return this.record.getClassificationName();
    }

    public String getName() {
        return this.record.getName();
    }

    public String getTranslatedName() {
        return this.record.getTranslatedName();
    }

    public String getDataTagTypeForDspm(){
        return this.record.getDataTagTypeForDspm();
    }

    public String getId() {
        return this.record.getId();
    }

    public String getCategoryName() {
        return this.record.getCategoryName();
    }

    public String getRuleTypeName() {
        return this.record.getRuleTypeName();
    }

    public ContinueOption getContinueOption() {
        return this.record.getContinueOption();
    }

    public abstract ContextTable.TableType[] getTableTypes();

    public abstract String generateParams();

    public String describeParams() {
        if (this.cachedParams == null) {
            this.cachedParams = this.generateParams();
        }

        return this.cachedParams;
    }




}
