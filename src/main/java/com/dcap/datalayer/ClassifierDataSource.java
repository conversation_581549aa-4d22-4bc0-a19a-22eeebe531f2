package com.dcap.datalayer;

import java.sql.Connection;
import java.util.Set;

public abstract class ClassifierDataSource {
   public ClassifierDataSource() {
   }

   public abstract Long getTenantId();

   public abstract String getId();

   public abstract String getName();

   public abstract String getDescription();

   public abstract String getAttributes();

   public abstract String getDbName();

   public abstract String getInstanceName();

   public abstract String getConnectionDescriptor();

   public abstract Connection connect() throws ConnectionException;

   public abstract DatasourceDriver getDriverInterface();

   public abstract DataSourceType getType();

   public String getTypeName() {
      return getType().name();
   }

   public abstract boolean isCatalog();

   public String getDescriptor() {
      return this.getTypeName() + ' ' + this.getName();
   }

   public String getInventoryDbName() {
      return null;
   }

   public abstract Set<String> getEmptyCatalogs();

   public void release(){

   }

   public abstract boolean supportedQueryTimeout();
}
