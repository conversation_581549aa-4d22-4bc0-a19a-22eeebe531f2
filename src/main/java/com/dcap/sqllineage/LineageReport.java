package com.dcap.sqllineage;

import gudusoft.gsqlparser.dlineage.dataflow.model.json.Dataflow;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * 输出 - LineageReport
 * 定义：
 *
 * LineageReport 类负责存储和展示最终的血缘分析结果。
 * 它结构化地表示了解析的血缘信息，如源表、目标表及其关系。
 * 功能：
 *
 * 存储从 SQLLineageRunner 获取的血缘数据。
 * 提供格式化输出，例如文本、图形表示或其他格式，以便用户易于理解。
 * 工作流程：
 *
 * SQLLineageRunner 完成分析后，生成 LineageReport。
 * LineageReport 可以输出到不同的媒介（如控制台、文件或图形界面）。
 */
public interface LineageReport {

    public static LineageReport parse(String sqlType, Dataflow dataflow){
        if ("view".equalsIgnoreCase(sqlType)){
            return new ViewReport(dataflow);
        }
        throw new IllegalArgumentException("sqlType is not supported");
    }
    List<LineageReportItem> getLineageItems();

    Map<String,List<String>> getTableAndColumns();

    @Data
    @ToString
    public static class LineageReportItem{
        private String sourceTable;
        private String sourceColumn;
        private String sourceTableType;

        private String targetTable;
        private String targetColumn;
        private String targetTableType;

    }
}
