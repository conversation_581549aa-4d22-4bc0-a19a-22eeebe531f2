package com.dcap.sqllineage;


import gudusoft.gsqlparser.dlineage.dataflow.model.json.Dataflow;
import gudusoft.gsqlparser.dlineage.dataflow.model.json.Relationship;
import gudusoft.gsqlparser.dlineage.dataflow.model.json.RelationshipElement;
import gudusoft.gsqlparser.dlineage.metadata.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

public class ViewReport implements LineageReport {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    private final Dataflow dataflow;

    private final List<LineageReportItem> items;

    private final Map<String, List<String>> tableAndColumns;

    private final List<String> viewColumns;

    public ViewReport(Dataflow dataflow) {
        this.dataflow = dataflow;
        this.items = parseDataflow();
        this.tableAndColumns = new HashMap<>();
        viewColumns = new ArrayList<>();
        for (LineageReportItem item : items) {
            if (item.getSourceTableType().equals("table")) {
                List<String> columns;
                if (tableAndColumns.get(item.getSourceTable()) != null){
                    columns = tableAndColumns.get(item.getSourceTable());
                } else {
                    columns = new ArrayList<>();
                    tableAndColumns.put(item.getSourceTable(), columns);
                }
                columns.add(item.getSourceColumn());
                viewColumns.add(item.getTargetColumn());
            }
        }
    }

    @Override
    public List<LineageReportItem> getLineageItems() {
        return items;
    }

    @Override
    public Map<String, List<String>> getTableAndColumns() {
        return tableAndColumns;
    }

    public List<String> getViewColumns() {
        return viewColumns;
    }

    private List<LineageReportItem> parseDataflow() {
        Sqlflow dbobjs = dataflow.getDbobjs();
        List<Server> servers = dbobjs.getServers();
        List<Table> tables = new ArrayList<>();
        for (Server server : servers) {
            if (!server.isSupportsSchemas()) {
                for (Database database : server.getDatabases()) {
                    List<Table> tbs = database.getTables();
                    if (tbs != null && !tbs.isEmpty()){
                        tables.addAll(tbs);
                    }
                    List<Table> others = database.getOthers();
                    if (others != null && !others.isEmpty()){
                        tables.addAll(others);
                    }
                }
            } else {
                List<Schema> schemas = server.getDatabases().stream().flatMap(database -> database.getSchemas().stream()).collect(Collectors.toList());
                for (Schema schema : schemas) {
                    List<Table> tbs = schema.getTables();
                    if (tbs != null && !tbs.isEmpty()){
                        tables.addAll(tbs);
                    }
                    List<Table> others = schema.getOthers();
                    if (others != null && !others.isEmpty()){
                        tables.addAll(others);
                    }
                }
            }
        }
        Map<String,Table> tableMap = tables.stream().map((table)->{
            String tableName = getTableName(table);
            if (StringUtils.isBlank(tableName)){
                return null;
            }
            return new AbstractMap.SimpleEntry<String,Table>(tableName, table);
        }).filter(Objects::nonNull)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,  (v1, v2)->{
                    logger.warn("Duplicate key [{}] for values {} and {} , use {} as value", getTableName(v1), v1, v2, v1);
                    return v1;
                }));
        List<LineageReportItem> items = new ArrayList<>();
        for (Relationship relationship : dataflow.getRelationships()) {
            // fdd 直接关系
            if (!"fdd".equalsIgnoreCase(relationship.getType())) {
                continue;
            }
            RelationshipElement source = relationship.getSources()[0];
            RelationshipElement target = relationship.getTarget();
            String sourceTableName = source.getParentName();
            String targetTableName = target.getParentName(); // 就是 view name
            if (StringUtils.isBlank(sourceTableName) || StringUtils.isBlank(targetTableName)){
                continue;
            }
            sourceTableName = sourceTableName.replaceAll("`", "").replaceAll("\"","");
            targetTableName = targetTableName.replaceAll("`", "").replaceAll("\"","");
            Table sourceTable = tableMap.get(sourceTableName);
            Table targetTable = tableMap.get(targetTableName);
            if (sourceTable == null || targetTable == null){
                continue;
            }
            LineageReportItem lineageReportItem = new LineageReportItem();
            lineageReportItem.setSourceTable(sourceTableName);
            lineageReportItem.setSourceTableType(sourceTable.getType());
            lineageReportItem.setSourceColumn(source.getColumn().replaceAll("`","").replaceAll("\"",""));
            lineageReportItem.setTargetTable(targetTableName);
            lineageReportItem.setTargetTableType(targetTable.getType());
            lineageReportItem.setTargetColumn(target.getColumn().replaceAll("`","").replaceAll("\"",""));
            items.add(lineageReportItem);
        }
        return items;
    }

    private static String getTableName(Table table){
        String tableName = table.fullName();
        if (StringUtils.isBlank(tableName)){
            tableName = table.getDisplayName();
        }
        if (StringUtils.isBlank(tableName)){
            tableName = table.getName();
        }
        if (StringUtils.isNotBlank(tableName)){
            tableName = tableName.replaceAll("`", "").replaceAll("\"","");
        }
        return tableName;
    }

    @Override
    public String toString() {
        return "ViewReport{" +
                "items=" + items +
                '}';
    }
}
