package com.dcap.sqllineage;

import com.dcap.datalayer.DataSourceType;
import gudusoft.gsqlparser.EDbVendor;
import gudusoft.gsqlparser.TGSqlParser;
import gudusoft.gsqlparser.dlineage.dataflow.model.ErrorInfo;
import gudusoft.gsqlparser.dlineage.dataflow.model.json.Dataflow;
import gudusoft.gsqlparser.dlineage.dataflow.model.xml.dataflow;
import gudusoft.gsqlparser.dlineage.dataflow.sqlenv.SQLEnvParser;
import gudusoft.gsqlparser.dlineage.util.RemoveDataflowFunction;
import gudusoft.gsqlparser.sqlenv.TSQLEnv;
import gudusoft.gsqlparser.sqlenv.parser.TSQLEnvParser;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 系统的主要执行类。
 * 协调 SQLParser 和 LineageExtractor 的工作。
 * 将 DataModel 和 LineageModel 结合，提供最终的血缘分析结果。
 */
public class SQLLineageRunner {

    private LineageConfiguration configuration;

    /**
     * 从 SQLParser 获取解析结果。
     * 从 LineageExtractor 获取血缘关系。
     * 将 DataModel 和 LineageModel 结合，提供最终的血缘分析结果。
     * @param sql
     * @return
     */
    public LineageReport run(String sql) {
        Dataflow dataflow = SQLLineageRunner.analyzeDataFlow(configuration.getVendor(), configuration.getDatabaseDdl(), sql);
        return LineageReport.parse(configuration.getSqlType(), dataflow);
    }
    // builder 模式
    public static class Builder {
        private final LineageConfiguration configuration = new LineageConfiguration();

        // 数据库类型
        // 支持 access,bigquery,couchbase,dax,db2,greenplum,hana,hive,impala,informix,mdx,mssql,
        // sqlserver,mysql,netezza,odbc,openedge,oracle,postgresql,postgres,redshift,snowflake,
        // sybase,teradata,soql,vertica, the default value is oracle
        public Builder vendor(DataSourceType dataSourceType) {
            String vendor = "oracle";
            if (DataSourceType.POSTGRESQL == dataSourceType){
                vendor = "postgresql";
            } else if (DataSourceType.ORACLE == dataSourceType){
                vendor = "oracle";
            } else if (DataSourceType.MSSQL == dataSourceType){
                vendor = "mssql";
            } else if (DataSourceType.MYSQL == dataSourceType){
                vendor = "mysql";
            } else if (DataSourceType.HIVE == dataSourceType){
                vendor = "hive";
            } else if (DataSourceType.DB2 == dataSourceType){
                vendor = "db2";
            } else if (DataSourceType.HANA == dataSourceType){
                vendor = "hana";
            } else if (DataSourceType.IMPALA == dataSourceType){
                vendor = "impala";
            } else if (DataSourceType.GBASE8A == dataSourceType){
                vendor = "mysql";
            } else {
                throw new IllegalArgumentException("Unsupported database type: " + dataSourceType);
            }
            this.configuration.setVendor(vendor);
            return this;
        }

        // ddl
        public Builder databaseDdl(String databaseDdl) {
            this.configuration.setDatabaseDdl(databaseDdl);
            return this;
        }
        // sql type
        public Builder sqlType(String sqlType) {
            this.configuration.setSqlType(sqlType);
            return this;
        }

        public SQLLineageRunner build() {
            SQLLineageRunner runner = new SQLLineageRunner();
            runner.configuration = this.configuration;
            return runner;
        }
    }

    public static void main(String[] args) {
        String sql = "  CREATE OR REPLACE FORCE EDITIONABLE VIEW \"TAIPINGLIFEMAIN\".\"STF_HEAD_AGREE_ORG_MONTH_ALL\" (\"ORGAN_ID\", \"T_MONTH\", \"AGREE_ID\", \"HALL_ID\") AS \n" +
                "  select ORGAN_ID,T_MONTH,AGREE_ID,HALL_ID\n" +
                "  from STF_HEAD_AGREE_ORG_MONTH\n" +
                "union\n" +
                "select distinct da.organ_id,\n" +
                "                trunc(da.apply_date, 'mm'),\n" +
                "                ao.agree_id,\n" +
                "                da.HALL_ID\n" +
                "  from v_cmm_app_hall da, dm_blueprint db, stf_cf_agreement_org ao\n" +
                " where ao.agree_id = db.agree_id\n" +
                "   and da.blue_id = db.blue_id";
        SQLLineageRunner runner = new Builder()
                .vendor(DataSourceType.ORACLE)
                .sqlType("view")
                .build();
        LineageReport report = runner.run(sql);
        for (Map.Entry<String, List<String>> tableAndColumns : report.getTableAndColumns().entrySet()) {
            String tableName = tableAndColumns.getKey();
            List<String> columnsValue = tableAndColumns.getValue();
            System.out.println("tableName："+tableName+", columnsValue："+columnsValue);
        }
        for (LineageReport.LineageReportItem lineageItem : report.getLineageItems()) {
            String viewColumn = lineageItem.getTargetColumn();
            String sourceTable = lineageItem.getSourceTable();
            String sourceColumn = lineageItem.getSourceColumn();
            System.out.println("视图列："+viewColumn+", 来源表："+sourceTable+", 来源列："+sourceColumn);
        }
//        for (Map.Entry<String, List<String>> stringListEntry : report.getTableAndColumns().entrySet()) {
//            System.out.println(stringListEntry.getKey() + " : " + stringListEntry.getValue());
//        }
    }

    private static Dataflow analyzeDataFlow(String databaseType, String env, String sql) {
        EDbVendor vendor = EDbVendor.dbvoracle;
        if (StringUtils.isNotBlank(databaseType)) {
            vendor = TGSqlParser.getDBVendorByName(databaseType);
        }

        boolean ignoreTemporaryTable = true;
        boolean ignoreResultSets = false;
        boolean showJoin = false;
        boolean transform = false;
        boolean transformCoordinate = false;
        boolean textFormat = false;
        boolean jsonFormat = true;
        boolean linkOrphanColumnToFirstTable = false;
        boolean ignoreCoordinate = true;
        boolean showImplicitSchema = true;
        boolean simple = false;
        boolean traceView = false;

        boolean stat = true; // argList.contains("/stat");
        boolean ignoreFunction = true;// argList.contains("/if");
        boolean topselectlist = false;// argList.contains("/topselectlist");

        boolean tableLineage = false;// argList.contains("/tableLineage");
        boolean csv = false;// argList.contains("/csv");
        String delimiter = ",";

        // 如果传递是创建表的 ddl ，分析 view 时，就可以分析出来没有指明路径的 view 列名。
        TSQLEnv sqlenv = null;
        if (StringUtils.isNotBlank(env)) {
            TSQLEnvParser sqlEnvParser = new SQLEnvParser(null, null, null);
            TSQLEnv[] envs = sqlEnvParser.parseSQLEnv(vendor, env);
            if(envs!=null && envs.length>0) {
                sqlenv = envs[0];
            }
        }

        gudusoft.gsqlparser.dlineage.DataFlowAnalyzer dlineage = new gudusoft.gsqlparser.dlineage.DataFlowAnalyzer(sql, vendor, simple);

        if (sqlenv != null) {
            dlineage.setSqlEnv(sqlenv);
        }

        dlineage.setTransform(transform);
        dlineage.setTransformCoordinate(transformCoordinate);
        dlineage.setShowJoin(showJoin);
        dlineage.setIgnoreRecordSet(ignoreResultSets);
        dlineage.setLinkOrphanColumnToFirstTable(linkOrphanColumnToFirstTable);
        dlineage.setIgnoreCoordinate(ignoreCoordinate);
        dlineage.setSimpleShowTopSelectResultSet(topselectlist);
        dlineage.setShowImplicitSchema(showImplicitSchema);
        dlineage.setIgnoreTemporaryTable(ignoreTemporaryTable);

        dlineage.setShowConstantTable(false);
        dlineage.setShowCountTableColumn(false);

//        if (argList.contains("/defaultDatabase")) {
//            dlineage.getOption().setDefaultDatabase(args[argList.indexOf("/defaultDatabase") + 1]);
//        }
//        if (argList.contains("/defaultSchema")) {
//            dlineage.getOption().setDefaultSchema(args[argList.indexOf("/defaultSchema") + 1]);
//        }


        if (simple && !jsonFormat) {
            dlineage.setTextFormat(textFormat);
        }

        String result = dlineage.generateDataFlow();
        dataflow dataflow = dlineage.getDataFlow();
        if (ignoreFunction) {
            dataflow = new RemoveDataflowFunction().removeFunction(dataflow, vendor);
        }
        Dataflow model = gudusoft.gsqlparser.dlineage.DataFlowAnalyzer.getSqlflowJSONModel(dataflow, vendor);
//        result = JSON.toJSONString(model);

//        if (result != null) {
//            System.out.println(result);
//        }

        List<ErrorInfo> errors = dlineage.getErrorMessages();
        if (!errors.isEmpty()) {
            System.err.println("Error log:\n");
            for (ErrorInfo error : errors) {
                System.err.println(error.getErrorMessage());
            }
        }
        return model;
    }
}
