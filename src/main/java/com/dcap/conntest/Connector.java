package com.dcap.conntest;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import com.dcap.conntest.entities.ConnTestParams;
import com.dcap.datalayer.ConnectionException;
import com.dcap.utils.JSON;
import com.dcap.utils.UtilDB;
import com.mongodb.MongoClient;
import com.yd.dcap.classifier.RedisDatasource;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.dcap.platform.model.ProbeClientTask;
import com.yd.dcap.probe.client.AbstractProbeClientProcessor;
import com.yd.dcap.probe.client.TaskType;
import com.yd.dcap.probe.entities.ProbeClientJobResult;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import org.apache.commons.lang3.StringUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.model.ListTablesResponse;

import java.net.InetSocketAddress;
import java.net.Socket;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

@TaskType(ProbeClientTask.TASK_TYPE_CONN_TEST)
public class Connector extends AbstractProbeClientProcessor {

    private static final int CONNECTIONS = 1000000;
    private static final String URL = "********************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "password";
    private static final int MAX_POOL_SIZE = 200;
    public Connector(ProbeClientTaskContext probeClientTaskContext) {
        super(probeClientTaskContext);
    }

    public static void main(String[] args) throws ConnectionException, SQLException {
        //testdb:GBASEDBTSERVER=gbase01;DB_LOCALE=zh_CN.utf8;CLIENT_LOCALE=zh_CN.utf8;IFX_LOCK_MODE_WAIT=30;
        Connection connection = UtilDB.buildDatabaseConnection("gbase8s",
                            "************", // 1qaz2wsx
                            19088, "gbasedbt", "GBase123", "testdb:GBASEDBTSERVER=gbase01;DB_LOCALE=zh_CN.utf8;CLIENT_LOCALE=zh_CN.utf8;IFX_LOCK_MODE_WAIT=30;");
        System.out.println(connection);
    }

    public boolean checkIpPort(String ip, int port) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(ip, port), 2000);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    protected Map<String, Object> doWork(long execId, long taskId, String taskType, long tenantId,
                                Map<String, Object> taskParam, Consumer<ProbeClientJobResult> callback) throws Exception {
        ConnTestParams connTestParams = JSON.from(taskParam).toObject(ConnTestParams.class);
        if (StringUtils.isBlank(connTestParams.getUsername()) || StringUtils.isBlank(connTestParams.getPassword())) {
            if (checkIpPort(connTestParams.getHost(), connTestParams.getPort())) {
                return UtilMisc.toMap("data", "success");
            }
            throw new RuntimeException("地址和端口号不可用");
        }
        String dbType = connTestParams.getDbType();
        if (UtilDB.useMongo(dbType)) {
            try (MongoClient mongoClient = UtilDB.buildMongoClient(connTestParams.getHost(),
                    connTestParams.getPort(), connTestParams.getUsername(),
                    connTestParams.getPassword(), connTestParams.getExtraCfg()
            )) {
                List<String> databaseNames = mongoClient.getDatabaseNames();
                return UtilMisc.toMap("data", "success");
            }
        } else if (UtilDB.useRedis(dbType)) {
            JedisPool jedisPool = RedisDatasource.createJedisPool(connTestParams.getHost(), connTestParams.getPort(), connTestParams.getUsername(), connTestParams.getPassword());
            Jedis resource = jedisPool.getResource();
            resource.connect();
            jedisPool.close();
            return UtilMisc.toMap("data", "success");
        } else if (UtilDB.useJDBC(dbType)) {
            try (Connection connection = UtilDB.buildDatabaseConnection(dbType, connTestParams.getHost(),
                    connTestParams.getPort(), connTestParams.getUsername(), connTestParams.getPassword(), connTestParams.getExtraCfg())) {
                DatabaseMetaData metaData = connection.getMetaData();
                return UtilMisc.toMap("data", "success");
            }
        }else if (UtilDB.useElasticsearch(dbType)){
            ElasticsearchClient elasticsearchClient = UtilDB.buildElasticsearchClient(connTestParams.getHost(), connTestParams.getPort(),
                    connTestParams.getUsername(), connTestParams.getPassword());
            System.out.println(elasticsearchClient.info().toString());
            return UtilMisc.toMap("data", "success");
        } else if (UtilDB.useDynamodb(dbType)){
            try (DynamoDbClient dynamoDbClient =
                         UtilDB.buildDynamoDbClient(connTestParams.getUsername(), connTestParams.getPassword(), connTestParams.getExtraCfg())){
                ListTablesResponse listTablesResponse = dynamoDbClient.listTables();
                return UtilMisc.toMap("data", "success");
            }
        }
        throw new RuntimeException("数据库类型[" + dbType + "]不支持");
    }
}
