package com.dcap.conntest.entities;

public class ConnTestParams {

    private String dbType;

    private String host;

    private Integer port;

    private String username;

    private String password;

    private String extraCfg;

    public String getDbType() {
        return dbType;
    }

    public void setDbType(String dbType) {
        this.dbType = dbType;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getExtraCfg() {
        return extraCfg;
    }

    public void setExtraCfg(String extraCfg) {
        this.extraCfg = extraCfg;
    }
}
