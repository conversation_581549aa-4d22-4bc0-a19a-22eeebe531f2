package com.dcap.cloud;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;

public class Discovery {

    private static final Logger LOGGER = LoggerFactory.getLogger(Discovery.class);
    private static final ObjectMapper MAPPER = new ObjectMapper(new YAMLFactory());
    private static final String DEFAULT_CONFIG_FILE = "config.yaml";

//    public static final Map<Long, Pair<List<Orchestrator.LayerCallable>, ExecutorService>> RUNNING_TASKS = new ConcurrentHashMap<>();

    public static String humanReadableFormat(Duration duration) {
        return duration.toString().substring(2).replaceAll("(\\d[HMS])(?!$)", "$1 ").toLowerCase();
    }

//  public static List<Orchestrator.LayerCallable> doWork(RDSWatchLogsDiscovery.RDSWatchLogsConfig rdsWatchLogsConfig) throws IOException{
////    final Instant start = Instant.now();
//    try(InputStream is = Thread.currentThread().getContextClassLoader().getResourceAsStream(DEFAULT_CONFIG_FILE)) {
//      final CloudConfig config = ConfigUtils.merge(MAPPER.readValue(is, CloudConfig.class), System.getenv());
//      LOGGER.info("OSS Discovery. Classpath={}", System.getProperties().get("java.class.path"));
//      return new Orchestrator(rdsWatchLogsConfig, config, new Session()).scan();
//    }
////    LOGGER.info("Discovery completed in {}", Discovery.humanReadableFormat(Duration.between(start, Instant.now())));
//  }

    public static void doWork(String bodyData) throws Exception {
//        final Instant start = Instant.now();
//        CloudDiscovery discovery = JSON.from(bodyData).toObject(CloudDiscovery.class);
//        Long discoveryId = discovery.getId();
//        CloudDiscoveryPlugin discoveryPlugin = discovery.getDiscoveryPlugin();
//        if (discoveryPlugin == null) {
//            throw new RuntimeException("discovery plugin is null");
//        }
//        // 获取云服务的配置
//        CloudService cloudService = discoveryPlugin.getCloudService();
//        if (cloudService == null) {
//            throw new RuntimeException("cloud service is null");
//        }
//        CloudProvider provider = cloudService.getProvider();
//        if (provider == null) {
//            throw new RuntimeException("cloud provider is null");
//        }
//
//        String providerId = provider.getId();
//        String cloudServiceId = cloudService.getId();
//        String pluginName = discoveryPlugin.getPluginName();
//        // example：cloud/aliyun/rds/describe_sql_log_records
//        String configFilePath = "cloud" + File.separator + providerId + File.separator + cloudServiceId +
//                File.separator + pluginName + File.separator + DEFAULT_CONFIG_FILE;
//        try (InputStream is = Thread.currentThread().getContextClassLoader().getResourceAsStream(configFilePath.toLowerCase())) {
//            final CloudConfig config = ConfigUtils.merge(MAPPER.readValue(is, CloudConfig.class), System.getenv());
//            LOGGER.info("Start Cloud Discovery. discoveryId={}, pluginName={}", discoveryId, pluginName);
//            Pair<List<Orchestrator.LayerCallable>, ExecutorService> scan = new Orchestrator(discovery, config, new Session()).scan();
//        } finally {
//            LOGGER.info("Discovery completed in {}", Discovery.humanReadableFormat(Duration.between(start, Instant.now())));
//        }
    }
}
