package com.dcap.cloud.aliyun;

import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.dialect.hive.parser.HiveStatementParser;
import com.alibaba.druid.sql.dialect.hive.visitor.HiveSchemaStatVisitor;
import com.alibaba.druid.sql.dialect.mysql.parser.MySqlStatementParser;
import com.alibaba.druid.sql.dialect.mysql.visitor.MySqlSchemaStatVisitor;
import com.alibaba.druid.sql.dialect.oracle.parser.OracleStatementParser;
import com.alibaba.druid.sql.dialect.oracle.visitor.OracleSchemaStatVisitor;
import com.alibaba.druid.sql.dialect.postgresql.parser.PGSQLStatementParser;
import com.alibaba.druid.sql.dialect.postgresql.visitor.PGSchemaStatVisitor;
import com.alibaba.druid.sql.dialect.sqlserver.parser.SQLServerStatementParser;
import com.alibaba.druid.sql.dialect.sqlserver.visitor.SQLServerSchemaStatVisitor;
import com.alibaba.druid.sql.parser.SQLStatementParser;
import com.alibaba.druid.sql.visitor.SchemaStatVisitor;
import com.alibaba.druid.stat.TableStat;
import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.Emitter;
import com.dcap.cloud.core.api.IntermediatePlugin;
import com.dcap.utils.Base64Util;
import com.dcap.utils.RC4Util;
import com.dcap.utils.UtilDateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SqlParserPlugin implements IntermediatePlugin<Map<String, Object>> {
    private static final Logger LOG = LoggerFactory.getLogger(SqlParserPlugin.class);
    private final String KEY = "FIRST@YD";

    private Map<String, Object> cloudDiscoveryConfig;

    public void accept(CloudEnvelope<Map<String, Object>> env, Emitter emitter) {
        try {
            // timestamp,user_name,datasource_id,db_proto,app_name,data_tags,cmd_type,row_count,response_length,query_id,
            // orig_sql,objects,fields,tenant_id,action,appuser,appuri,user_group,ipuser,dataset,rule_id
            Map<String, Object> content = env.getContents();
            String executeTime = String.valueOf(UtilDateTime.convertUTCToCSTDate(String.valueOf(content.get("ExecuteTime"))).getTime());
            String acctName = (String) content.get("AccountName");
            String userName = acctName;
            if (acctName == null || acctName.contains("null")) {
                userName = "";
            }
            String datasourceId = (String) this.cloudDiscoveryConfig.get("datasourceId");
            String tenantId = (String) this.cloudDiscoveryConfig.get("tenantId");
            String datasourceType = ((String) this.cloudDiscoveryConfig.get("datasourceType")).toUpperCase();
            String appName = null;
            String dataTags = null;
            String cmdType = "OTHER";
            String rowCount = content.get("ReturnRowCounts").toString();
            String responseLength = "0";
            String origSql = content.get("SQLText").toString().replaceAll("\"", "");
            if (origSql.contains("logout") || origSql.contains("login failed")) {
                return;
            }

            String queryId = String.valueOf(origSql.hashCode());
            String dbName = (String) content.get("DBName");
            String objects = dbName;
            if (dbName.contains("null")) {
                objects = "";
            }
            String fields = null;
            String action = null;
            String appuser = userName;
            String appuri = null;
            String userGroup = null;
            String ipuser = (String) content.get("HostAddress");
            String dataset = null;
            String ruleId = "0";
            if (origSql.startsWith("00000: execute <unnamed>:")) {
                origSql = origSql.replaceAll("00000: execute <unnamed>:", "");
            }
            try {
                SQLStatementParser sqlParser = createSQLParser(datasourceType, origSql);
                if (sqlParser != null) {
                    sqlParser.setKeepComments(true);
                    SQLStatement statement = sqlParser.parseStatement();
                    SchemaStatVisitor sqlVisitor = createSQLVisitor(datasourceType);
                    statement.accept(sqlVisitor);
                    Map<TableStat.Name, TableStat> tables = sqlVisitor.getTables();
                    if (!tables.isEmpty()) {
                        StringBuilder objectsBuilder = new StringBuilder();
                        for (Map.Entry<TableStat.Name, TableStat> nameTableStatEntry : tables.entrySet()) {
                            if (!"MYSQL".equals(datasourceType)) {
                                objectsBuilder.append(dbName).append(".");
                            } else {
                                String tableName = nameTableStatEntry.getKey().toString();
                                if (!tableName.contains(dbName) && !dbName.contains("null")) {
                                    objectsBuilder.append(dbName).append(".");
                                }
                            }

                            objectsBuilder.append(nameTableStatEntry.getKey()).append(";");
                            cmdType = nameTableStatEntry.getValue().toString().toUpperCase();
                        }
                        objects = objectsBuilder.substring(0, objectsBuilder.length() - 1);
                    }
                    try {
                        List<String> comments = getComments(origSql);
                        if (!comments.isEmpty()) {
                            // /*YD-INFO:dWi6WK0NDA==:d2arVKFKBkO1UFgrzB9fTMvijjAiG/GcnxyElHSImh3cfQ==*/
                            String comment = comments.get(0);
                            String[] commentSplit = comment.split(":");
                            appuser = RC4Util.decry_RC4(Base64Util.Decrypt_Bytes(commentSplit[1].getBytes(StandardCharsets.UTF_8)), KEY);
                            appuri = RC4Util.decry_RC4(Base64Util.Decrypt_Bytes(commentSplit[2].getBytes(StandardCharsets.UTF_8)), KEY);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    Collection<TableStat.Column> columns = sqlVisitor.getColumns();
                    if (!columns.isEmpty()) {
                        StringBuilder fieldsBuilder = new StringBuilder();
                        for (TableStat.Column column : columns) {
                            // todo: 这里直接拼接 :; 是因为原本 fields 拼接的应该是 datatag，目前暂时没有 datatag，就直接拼上连接字符
                            if (column.getName().intern() == "*") {
                                continue;
                            }
                            fieldsBuilder.append(column.getName() + ":;");
                        }
                        fields = fieldsBuilder.toString();
                    }
                }
            } catch (Exception e) {
//        e.printStackTrace();
            }

            String[] entries = {executeTime, userName, datasourceId, datasourceType, appName, dataTags, cmdType,
                    rowCount, responseLength, queryId, origSql, objects, fields, tenantId, action, appuser, appuri,
                    userGroup, ipuser, dataset, ruleId};
            for (int i = 0; i < entries.length; i++) {
                String entry = entries[i];
                if (entry == null) {
                    continue;
                }
                entry = entry.replaceAll("\n\t", " ");
                entry = entry.replaceAll("\n", " ");
                entry = entry.replaceAll("\t", " ");
                entries[i] = entry;
            }
            emitter.emit(CloudEnvelope.of(env, PluginConstant.CLOUD_SQL_PARSER_ID, entries));
        } catch (Exception e) {
            LOG.warn("Couldn't process envelope contents", e);
        }
    }

    private final Pattern pattern = Pattern.compile("/\\*YD-INFO:.+:.+\\*/");

    private List<String> getComments(String sql) {
        List<String> comments = new ArrayList<>();
        try {
            Matcher matcher = pattern.matcher(sql);
            while (matcher.find()) {
                String comment = matcher.group();
                comment = comment.substring(2, comment.length() - 2);
                comments.add(comment);
            }
        } catch (Exception ignored) {

        }

//    if (sqlObject.hasBeforeComment()) {
//      comments.addAll(sqlObject.getBeforeCommentsDirect());
//    }
//    if(sqlObject.hasAfterComment()){
//      comments.addAll(sqlObject.getAfterCommentsDirect());
//    }
//
//    if(sqlObject instanceof SQLStatement){
//      SQLStatement statement = (SQLStatement) sqlObject;
//      List<SQLObject> children = statement.getChildren();
//      if(children != null && !children.isEmpty()){
//        for (SQLObject child : children) {
//          List<String> cs = getComments(child);
//          if(!cs.isEmpty()){
//            comments.addAll(cs);
//          }
//        }
//      }
//    }

        return comments;
    }

    private SQLStatementParser createSQLParser(String datasourceType, String sql) {
        switch (datasourceType) {
            case "MYSQL":
                return new MySqlStatementParser(sql);
            case "PGSQL":
                return new PGSQLStatementParser(sql);
            case "ORACLE":
                return new OracleStatementParser(sql);
            case "MSSQL":
                return new SQLServerStatementParser(sql);
            case "HIVE":
                return new HiveStatementParser(sql);
            default:
                return null;
        }
    }

    private SchemaStatVisitor createSQLVisitor(String datasourceType) {
        switch (datasourceType) {
            case "MYSQL":
                return new MySqlSchemaStatVisitor();
            case "PGSQL":
                return new PGSchemaStatVisitor();
            case "ORACLE":
                return new OracleSchemaStatVisitor();
            case "MSSQL":
                return new SQLServerSchemaStatVisitor();
            case "HIVE":
                return new HiveSchemaStatVisitor();
            default:
                return null;
        }
    }

    @Override
    public String id() {
        return PluginConstant.CLOUD_SQL_PARSER_ID;
    }

    @Override
    public void shutdown() {
    }

    @Override
    public void init(Map<String,Object> config) {
        this.cloudDiscoveryConfig = config;
    }
}
