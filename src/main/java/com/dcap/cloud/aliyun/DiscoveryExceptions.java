package com.dcap.cloud.aliyun;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DiscoveryExceptions {

  private static final Logger logger = LoggerFactory.getLogger(DiscoveryExceptions.class);

  static public void onDiscoveryException(String resourceType, String resourceName, String region, Exception ex) {
    logger.error("{} - Exception on {} in {}, with error {}", resourceType, resourceName, region, ex.getMessage());
  }
}
