package com.dcap.cloud.aliyun;


public class ClientCreators {

  // todo: assumeRoleCreator 应该传递 secret key 或者 rolearn
  public static AliyunClientCreator assumeRoleCreator(final String accessKeyId, final String accessKeySecret,
                                                      final String region, final String roleArn) {
    return null;
//    return new AliyunClientCreator(){
//      @Override
//      public <BuilderT extends AliyunClientBuilder<BuilderT, ClientT>, ClientT> BuilderT apply(AliyunClientBuilder<BuilderT, ClientT> builder) {
//        Config config = new Config()
//                .setAccessKeyId(accessKeyId)
//                .setAccessKeySecret(accessKeySecret);
//        // 访问的域名
//        config.endpoint = "sts." + region + ".aliyuncs.com";
//        try {
//          Client client = new Client(config);
//          AssumeRoleRequest assumeRoleRequest = new AssumeRoleRequest();
//          assumeRoleRequest.setRoleArn(roleArn);
//          AssumeRoleResponse assumeRoleResponse = client.assumeRole(assumeRoleRequest);
//          if(assumeRoleResponse != null && assumeRoleResponse.body != null && assumeRoleResponse.body.credentials != null){
//            String keyId = assumeRoleResponse.body.credentials.accessKeyId;
//            String keySecret = assumeRoleResponse.body.credentials.accessKeySecret;
//            return builder.region(region).credentialsProvider(keyId,keySecret);
//          }
//        } catch (Exception e) {
//          e.printStackTrace();
//        }
//        return  null;
//      }
//
//      @Override
//      public String getKey() {
//        return accessKeyId;
//      }
//      @Override
//      public String getSecret() {
//        return accessKeySecret;
//      }
//
//      @Override
//      public String getRegion() {
//        return region;
//      }
//    };
  }

  public static AliyunClientCreator ramClientCreator(final String accessKeyId, final String accessKeySecret, final String region) {
    return new AliyunClientCreator(){
      @Override
      public <BuilderT extends AliyunClientBuilder<BuilderT, ClientT>, ClientT> BuilderT apply(AliyunClientBuilder<BuilderT, ClientT> builder) {
        return builder.region(region).credentialsProvider(accessKeyId, accessKeySecret);
      }
      @Override
      public String getKey() {
        return accessKeyId;
      }
      @Override
      public String getSecret() {
        return accessKeySecret;
      }

      @Override
      public String getRegion() {
        return region;
      }
    };
  }
}
