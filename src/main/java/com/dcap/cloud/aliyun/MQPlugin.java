package com.dcap.cloud.aliyun;

import com.dcap.classifier.writer.RecordTypeEnum;
import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.TerminalPlugin;
import com.dcap.utils.JSON;
import com.yd.dcap.classifier.config.SpringContextUtil;
import com.yd.dcap.probe.TaskResultReporter;
import com.yd.dcap.probe.mq.MqManager;
import com.yd.dspm.config.LogTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * oss的对象扫描结果保存输出插件
 * 想做成通用插件，感觉在目前已有代码结构上很难实现：
 * 任务类型、消息类型混杂，同1个任务输出多种消息类型，而Plugin又无法得到消息具体是什么类型的
 */
@Deprecated
public class MQPlugin implements TerminalPlugin<String[]> {

    private static final Logger LOG = LoggerFactory.getLogger(MQPlugin.class);

    private TaskResultReporter reportWriter;

    /**
     * 20231122 经分析代码，目前env的值类型是String[]数组的只有两个地方:
     * OssIdentifyContentPlugin
     * SqlParserPlugin
     *
     * @param env
     */
    @Override
    public void accept(CloudEnvelope<String[]> env) {
        long jobId = env.getSession().getJobHistoryId();
        List<String> pluginPath = env.getPluginPath();
        String lastPluginId = CollectionUtils.isEmpty(pluginPath) ? null : pluginPath.get(pluginPath.size() - 1);
        LogTypeEnum logTypeEnum = getLogTypeByDataPluginId(lastPluginId);
        String[] entities = env.getContents();
        try {
            String dataJson = JSON.from(entities).toString();
            reportWriter.clientReportData(jobId, RecordTypeEnum.VFS_ASSETS_DATA, dataJson);
        } catch (Exception ex) {
            LOG.warn("Couldn't process envelope contents", ex);
        }
    }

    private LogTypeEnum getLogTypeByDataPluginId(String dataPluginId) {
        if (PluginConstant.OSS_IDENTIFY_CONTENT_ID.equals(dataPluginId)) {
            return LogTypeEnum.OSS_ASSETS_INFO;
        }
        //未来补充其他的机制
        return null;
    }

    @Override
    public String id() {
        return PluginConstant.CLOUD_MQ_OUT_ID;
    }

    @Override
    public void init(Map<String, Object> config) {
        reportWriter = SpringContextUtil.getBean(MqManager.class);
        //参见 AbstractCloudDiscovery的 doWork 代码，顺藤摸瓜可知此处的config就是那里的 taskParam
//        long jobId = MapUtils.getLong(config, "execId");
//        String taskType = MapUtils.getString(config, "taskType");
    }

    @Override
    public void shutdown() {
    }

}
