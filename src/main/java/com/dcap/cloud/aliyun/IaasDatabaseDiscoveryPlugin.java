package com.dcap.cloud.aliyun;

import com.aliyuncs.ecs.model.v20140526.DescribeInstancesResponse;
import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.Emitter;
import com.dcap.cloud.core.api.IntermediatePlugin;
import com.yd.dcap.platform.model.CloudAssetsDatabase;
import com.yd.dcap.platform.model.query.QCloudAssetsDatabase;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutableTriple;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.HttpHost;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

public class IaasDatabaseDiscoveryPlugin implements IntermediatePlugin<DescribeInstancesResponse.Instance> {

    private static final Logger LOG = LoggerFactory.getLogger(IaasDatabaseDiscoveryPlugin.class);

    private Map<String, Object> cloudDiscoveryConfig;

    private Integer portScanThreads;

    private Integer fromPort;

    private Integer toPort;

    private Integer portScanTimeout;

    // 测试的线程数
    private Integer testHostThreads;

    private Integer testOneHostTimeout;

    @Override
    public void accept(CloudEnvelope<DescribeInstancesResponse.Instance> envelope, Emitter emitter) {
        try {
            DescribeInstancesResponse.Instance instance = envelope.getContents();
            CloudEnvelope.MetaData metadata = envelope.getMetadata();
            String resourceType = metadata.getResourceDiscoveryStartingPath();
            String service = metadata.getCloudServiceId();

            MutableTriple<String,String, List<Integer>> hostAndPorts = getHostAndPorts(instance);
            if (hostAndPorts == null) {
                return;
            }
            String netType = hostAndPorts.getLeft();
            String address = hostAndPorts.getMiddle();
            List<Integer> hostPorts = hostAndPorts.getRight();

            // 获取扫描到的数据库服务列表
            ConcurrentLinkedQueue<MutableTriple<String, Integer, Pair<String,String>>> databaseQueue = new ConcurrentLinkedQueue<>();
            // 对扫描到的每个端口，都测试一遍数据库
            for (Integer hostPort : hostPorts) {
                Pair<String,String> engineAndVersion = testOneHost(address, hostPort, this.testOneHostTimeout);
                if (engineAndVersion != null) {
                    databaseQueue.add(MutableTriple.of(address, hostPort, engineAndVersion));
                }
            }
            if (databaseQueue.isEmpty()) {
                return;
            }

            for (MutableTriple<String, Integer, Pair<String,String>> hostPortDatabase : databaseQueue) {
                String host = hostPortDatabase.getLeft();
                int port = hostPortDatabase.getMiddle();
                Pair<String,String> engineAndVersion = hostPortDatabase.getRight();
                CloudAssetsDatabase cloudAssetsDatabase = buildCloudAssetsDatabase(netType, host, port, engineAndVersion.getLeft(), engineAndVersion.getRight(),
                        service, resourceType, instance);
                emitter.emit(CloudEnvelope.of(envelope, PluginConstant.CLOUD_ECS_DATABASE_DISCOVERY_ID, cloudAssetsDatabase));
            }
        } catch (Exception ex) {
            LOG.warn("Couldn't process envelope contents", ex);
        }
    }


    private MutableTriple<String,String,List<Integer>> getHostAndPorts(DescribeInstancesResponse.Instance instance){
        // todo: 先扫描内部 ip ，如果 内部 ip 已经扫描到端口，就停止扫描 外部 ip
        // 优先级是 内部 ip > 外部 ip，只要有一个 ip 地址扫描到端口，就停止，然后使用这个 ip 地址。
//        final String address = getAliveIpAddress(instance);
//        if (address == null) {
//            // 不存在可达的 IP 地址，记录日志并返回。
//            return;
//        }
        List<String> innerIpAddress = instance.getInnerIpAddress();
        if(innerIpAddress == null){
            innerIpAddress = new ArrayList<>();
        }
        for (String ipAddress : innerIpAddress) {
            // 对地址进行端口扫描
            List<Integer> hostPorts  = portScan(this.fromPort, this.toPort, ipAddress, portScanThreads, this.portScanTimeout);
            if (!hostPorts.isEmpty()) {
                LOG.info("在内网主机地址 ["+ipAddress+"] 识别到开放端口["+StringUtils.join(hostPorts.iterator(),",")+"]，" +
                        "端口扫描范围["+this.fromPort+"-"+this.toPort+"]");
                return MutableTriple.of("Intranet", ipAddress,hostPorts);
            }
        }

        List<String> publicIpAddress = instance.getPublicIpAddress();
        if(publicIpAddress == null){
            publicIpAddress = new ArrayList<>();
        }
        for (String ipAddress : publicIpAddress) {
            // 对地址进行端口扫描
            List<Integer> hostPorts  = portScan(this.fromPort, this.toPort, ipAddress, portScanThreads, this.portScanTimeout);
            if (!hostPorts.isEmpty()) {
                LOG.info("在外网主机地址 ["+ipAddress+"] 识别到开放端口["+StringUtils.join(hostPorts.iterator(),",")+"]，" +
                        "端口扫描范围["+this.fromPort+"-"+this.toPort+"]");
                return MutableTriple.of("Internet", ipAddress,hostPorts);
            }
        }

        LOG.info("内网主机 ["+StringUtils.join(innerIpAddress.iterator(),"; ")+"]，外网主机 ["+StringUtils.join(publicIpAddress.iterator(),"; ")+"] 全都未识别到开放端口，" +
                "端口扫描范围["+this.fromPort+"-"+this.toPort+"]");
        return null;
    }

    private String getAliveIpAddress(DescribeInstancesResponse.Instance instance) {
        // 实例公网 IP 地址。
        List<String> publicIpAddress = instance.getPublicIpAddress();
        if (publicIpAddress == null) {
            publicIpAddress = new ArrayList<>();
        }

        // 经典网络类型实例的内网 IP 地址。
        List<String> innerIpAddress = instance.getInnerIpAddress();
        if (innerIpAddress == null) {
            innerIpAddress = new ArrayList<>();
        }

        if (publicIpAddress.isEmpty() && innerIpAddress.isEmpty()) {
            return null;
        }

        innerIpAddress = innerIpAddress.parallelStream().map(ads -> {
            try {
                if (InetAddress.getByName(ads).isReachable(1000)) {
                    return ads;
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (!innerIpAddress.isEmpty()) {
            return innerIpAddress.get(0);
        } else {
            publicIpAddress = publicIpAddress.parallelStream().map(ads -> {
                try {
                    if (InetAddress.getByName(ads).isReachable(1000)) {
                        return ads;
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
                return null;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            if (!publicIpAddress.isEmpty()) {
                return publicIpAddress.get(0);
            } else {
                return null;
            }
        }
    }

    private CloudAssetsDatabase buildCloudAssetsDatabase(String netType, String host, int port, String database,
                                                         String version, String serviceId, String resourceType,
                                                         DescribeInstancesResponse.Instance instance) {
        // todo: 查询该数据库是否已经存在了
        CloudAssetsDatabase cloudAssetsDatabase = new QCloudAssetsDatabase()
                .instanceId.eq(instance.getInstanceId())
                .engine.eq(database)
                .serviceId.eq(serviceId)
                .resourceType.eq(resourceType)
                .connectionString.eq(host)
//                .tenantId.eq(this.cloudDiscovery.getTenantId())
                .port.eq(port)
                .findOne();
        if (cloudAssetsDatabase == null) {
            cloudAssetsDatabase = new CloudAssetsDatabase();
        }
//        cloudAssetsDatabase.setProvider(this.cloudDiscovery.getCloudAccess().getProvider());
//        cloudAssetsDatabase.setResourceId(this.cloudDiscovery.getDiscoveryPlugin().getPluginName());
//        cloudAssetsDatabase.setLastUpdateTime(new Date().getTime());
//        cloudAssetsDatabase.setServiceId(serviceId);
//        cloudAssetsDatabase.setResourceType(resourceType);
//        cloudAssetsDatabase.setEngine(database);
//        cloudAssetsDatabase.setEngineVersion(version);
//        // todo: 暂时硬编码，为了匹配 RDS
//        cloudAssetsDatabase.setConnectionMode("Standard");
//        cloudAssetsDatabase.setInstanceClassType("x");
//        cloudAssetsDatabase.setInstanceNetType(netType);
//        cloudAssetsDatabase.setConnectionString(host);
//        cloudAssetsDatabase.setPort(port);
//        cloudAssetsDatabase.setTenantId(this.cloudDiscovery.getTenantId());
//
//        //实例 ID。
//        String instanceId = instance.getInstanceId();
//        cloudAssetsDatabase.setInstanceId(instanceId);
//        // 实例运行的镜像 ID。
//        String imageId = instance.getImageId();
//
//        // 实例所属的地域 ID。您可以调用 DescribeRegions 查看最新的阿里云地域列表。
//        String regionId = instance.getRegionId();
//        cloudAssetsDatabase.setRegionId(regionId);
//        // 可用区 ID。cn-hangzhou-g
//        String zoneId = instance.getZoneId();
//        cloudAssetsDatabase.setZoneId(zoneId);
//        // 实例序列号。
//        String serialNumber = instance.getSerialNumber();
//        // 实例名称，支持使用通配符*进行模糊搜索。
//        String instanceName = instance.getInstanceName();
//        cloudAssetsDatabase.setInstanceDescription(instanceName);
//        // 实例描述。
//        String description = instance.getDescription();
//        // 实例规格。例如: ecs.g5.large
//        String instanceType = instance.getInstanceType();
//        cloudAssetsDatabase.setInstanceType(instanceType);
//        //实例规格族。 	ecs.g5
//        String instanceTypeFamily = instance.getInstanceTypeFamily();
//        cloudAssetsDatabase.setInstanceClass(instanceTypeFamily);
//        //实例状态。
//        String status = instance.getStatus();
//        cloudAssetsDatabase.setInstanceStatus(status);
//
//        //vCPU数。
//        Integer cpu = instance.getCpu();
//        cloudAssetsDatabase.setInstanceCpu(String.valueOf(cpu));
//        // 内存大小，单位为 MiB。
//        Integer memory = instance.getMemory();
//        cloudAssetsDatabase.setInstanceMemory(Long.valueOf(memory));
//        // 实例挂载的本地存储数量。
//        Integer localStorageAmount = instance.getLocalStorageAmount();
//
//        // 实例挂载的本地存储容量。
//        Long localStorageCapacity = instance.getLocalStorageCapacity();
//        if(localStorageCapacity != null){
//            cloudAssetsDatabase.setInstanceStorage(Math.toIntExact(localStorageCapacity));
//        }
//
//        // 实例是否可以挂载数据盘。
//        Boolean deviceAvailable = instance.getDeviceAvailable();
//        // 是否为I/O优化型实例。
//        Boolean ioOptimized = instance.getIoOptimized();
//        // 实例主机名。
//        String hostName = instance.getHostName();
//        // 实例的操作系统名称。CentOS 7.4 64 位
//        String osName = instance.getOSName();
//
//        // 实例操作系统的英文名称。 CentOS 7.4 64 bit
//        String osNameEn = instance.getOSNameEn();
//        // 实例的操作系统类型，分为 Windows Server和 Linux两种。可能值：windows。linux。
//        String osType = instance.getOSType();
//        // 实例是否可以回收。
//        Boolean recyclable = instance.getRecyclable();
//
//        // 实例公网 IP 地址。
//        List<String> publicIpAddress = instance.getPublicIpAddress();
//        // 经典网络类型实例的内网 IP 地址。
//        List<String> innerIpAddress = instance.getInnerIpAddress();
//        // HPC实例的 Rdma 网络IP。
//        List<String> rdmaIpAddress = instance.getRdmaIpAddress();
//        // 实例的计费方式。取值范围：PostPaid：按量付费。PrePaid：包年包月。
//        String instanceChargeType = instance.getInstanceChargeType();
//        cloudAssetsDatabase.setPayType(instanceChargeType);
//
//        // 实例网络类型。可能值：classic：经典网络。vpc：专有网络VPC。
//        String instanceNetworkType = instance.getInstanceNetworkType();
//        cloudAssetsDatabase.setInstanceNetworkType(instanceNetworkType.toUpperCase());
//
//        // 公网带宽计费方式。取值范围：PayByBandwidth：按固定带宽计费。PayByTraffic：按使用流量计费。
//        String internetChargeType = instance.getInternetChargeType();
//
//        // 公网入带宽最大值，单位为 Mbit/s。
//        Integer internetMaxBandwidthIn = instance.getInternetMaxBandwidthIn();
//        // 公网出带宽最大值，单位为 Mbit/s。
//        Integer internetMaxBandwidthOut = instance.getInternetMaxBandwidthOut();
//        // 实例使用的SSH密钥对名称。
//        String keyPairName = instance.getKeyPairName();
//
//        // 实例创建时间。以 ISO8601为标准，并使用 UTC+0 时间，格式为 yyyy-MM-ddTHH:mmZ。更多信息，请参见ISO8601。
//        String creationTime = instance.getCreationTime();
//        cloudAssetsDatabase.setCreateTime(UtilDateTime.convertUTCToCSTDate(creationTime).getTime());
//        // 实例最近一次的启动时间。以 ISO8601为标准，并使用 UTC+0 时间，格式为 yyyy-MM-ddTHH:mmZ。更多信息，请参见ISO8601。
//        String startTime = instance.getStartTime();
//
//        // 过期时间。以ISO8601为标准，并使用 UTC+0时间，格式为 yyyy-MM-ddTHH:mmZ。更多信息，请参见ISO8601。
//        String expiredTime = instance.getExpiredTime();
//        cloudAssetsDatabase.setExpireTime(UtilDateTime.convertUTCToCSTDate(expiredTime).getTime());
//
//        // 按量付费实例的自动释放时间。
//        String autoReleaseTime = instance.getAutoReleaseTime();

//                // 实例所在的企业资源组ID。使用该参数过滤资源时，资源数量不能超过1000个。
//                String resourceGroupId = instance.getResourceGroupId();
//                // 实例计费周期。 month
//                String saleCycle = instance.getSaleCycle();
//                // 实例所属安全组集合。
//                List<String> securityGroupIds = instance.getSecurityGroupIds();
//
//                // 实例规格附带的 GPU数量。
//                Integer gpuAmount = instance.getGPUAmount();
//                // 实例规格附带的 GPU类型。
//                String gpuSpec = instance.getGPUSpec();
//
//                //实例所在的集群 ID。
//                String clusterId = instance.getClusterId();
//                // 实例所属的HPC集群 ID。
//                String hpcClusterId = instance.getHpcClusterId();
//                // 抢占式实例的保留时长，单位为小时。可能值为0~6。
//                // 保留时长2~6正在邀测中，如需开通请提交工单。
//                // 值为0，则为无保护期模式。
//                Integer spotDuration = instance.getSpotDuration();
//                // 实例的每小时最高价格。支持最大3位小数，参数SpotStrategy=SpotWithPriceLimit时，该参数生效。
//                Float spotPriceLimit = instance.getSpotPriceLimit();
//                // 抢占式实例的抢占策略。可能值：NoSpot：正常按量付费实例。SpotWithPriceLimit：设置上限价格的抢占式实例。SpotAsPriceGo：系统自动出价，最高按量付费价格。
//                String spotStrategy = instance.getSpotStrategy();
//                //实例的 VLAN ID。
//                String vlanId = instance.getVlanId();
//
//                // 实例停机后是否继续收费。可能值： KeepCharging：停机后继续收费，为您继续保留库存资源。
//                // StopCharging：停机后不收费。停机后，我们释放实例对应的资源，例如vCPU、内存和公网IP等资源。重启是否成功依赖于当前地域中是否仍有资源库存。
//                // Not-applicable：本实例不支持停机不收费功能。
//                String stoppedMode = instance.getStoppedMode();
//
//                // 修改突发性能实例的运行模式。可能值：
//                // Standard：标准模式。有关实例性能的更多信息，请参见什么是突发性能实例中的性能约束模式章节。
//                // Unlimited：无性能约束模式，有关实例性能的更多信息，请参见什么是突发性能实例中的无性能约束模式章节。
//                String creditSpecification = instance.getCreditSpecification();
//                // 实例释放保护属性，指定是否支持通过控制台或API（DeleteInstance）释放实例。true：已开启实例释放保护。false：未开启实例释放保护。
//                Boolean deletionProtection = instance.getDeletionProtection();
//                // ECS实例绑定部署集分散部署时，实例在部署集中的分组位置。
//                Integer deploymentSetGroupNo = instance.getDeploymentSetGroupNo();
//                // 部署集ID。
//                String deploymentSetId = instance.getDeploymentSetId();
//
//                // 实例的标签集合。
//                List<DescribeInstancesResponse.Instance.Tag> tags = instance.getTags();
//                // 由专有宿主机集群ID（DedicatedHostClusterId）、专有宿主机ID（DedicatedHostId）和名称（DedicatedHostName）组成的宿主机属性数组。
//                // DedicatedHostId 专有宿主机ID。DedicatedHostName 专有宿主机名称。DedicatedHostClusterId 专有宿主机集群ID。
//                DescribeInstancesResponse.Instance.DedicatedHostAttribute dedicatedHostAttribute = instance.getDedicatedHostAttribute();
//                // CPU配置详情。 Numa 分配的线程数。可能值为2。CoreCount 物理CPU核心数。 ThreadsPerCore CPU线程数。
//                DescribeInstancesResponse.Instance.CpuOptions cpuOptions = instance.getCpuOptions();
//                // 专有宿主机实例的属性。
//                DescribeInstancesResponse.Instance.DedicatedInstanceAttribute dedicatedInstanceAttribute = instance.getDedicatedInstanceAttribute();
//                //云服务器ECS的容量预留相关参数。
//                DescribeInstancesResponse.Instance.EcsCapacityReservationAttr ecsCapacityReservationAttr = instance.getEcsCapacityReservationAttr();
//                // 实例的弹性公网 IP列表。当 InstanceNetworkType = vpc 时该参数生效，取值可以由多个IP组成一个JSON数组，最多支持100个IP，IP之间用半角逗号（,）隔开。
//                DescribeInstancesResponse.Instance.EipAddress eipAddress = instance.getEipAddress();
//                //元数据选项集合。
//                DescribeInstancesResponse.Instance.MetadataOptions metadataOptions = instance.getMetadataOptions();
//                // 实例包含的弹性网卡集合。
//                List<DescribeInstancesResponse.Instance.NetworkInterface> networkInterfaces = instance.getNetworkInterfaces();
//                //实例的锁定原因。
//                List<DescribeInstancesResponse.Instance.LockReason> operationLocks = instance.getOperationLocks();
//                // 专有网络VPC属性。
//                DescribeInstancesResponse.Instance.VpcAttributes vpcAttributes = instance.getVpcAttributes();
//                // 暂未开放
//                DescribeInstancesResponse.Instance.ImageOptions imageOptions = instance.getImageOptions();
//                // 暂未开放
//                DescribeInstancesResponse.Instance.HibernationOptions hibernationOptions = instance.getHibernationOptions();
//                // 该参数正在邀测中，暂未开放使用。
//                String isp = instance.getISP();
        return cloudAssetsDatabase;
    }

    @Override
    public String id() {
        return PluginConstant.CLOUD_ECS_DATABASE_DISCOVERY_ID;
    }

    @Override
    public void init(Map<String,Object> cloudDiscoveryConfig) {
        this.cloudDiscoveryConfig = cloudDiscoveryConfig;

        this.portScanThreads = (Integer) this.cloudDiscoveryConfig.getOrDefault("port.scan.threads", 100);
        try {
            String portRange = (String) this.cloudDiscoveryConfig.getOrDefault("port.scan.range", "22-65535");
            String[] split = portRange.split("-");
            this.fromPort = Integer.parseInt(split[0]);
            this.toPort = Integer.parseInt(split[1]);
        } catch (Exception e) {
            e.printStackTrace();
            this.fromPort = 22;
            this.toPort = 65535;
        }
        this.portScanTimeout = (Integer) this.cloudDiscoveryConfig.getOrDefault("port.scan.timeout.milliseconds", 100);
        this.testHostThreads = (Integer) this.cloudDiscoveryConfig.getOrDefault("test.host.threads", 100);
        this.testOneHostTimeout = (Integer) this.cloudDiscoveryConfig.getOrDefault("test.one.host.timeout.milliseconds", 100);
    }

    @Override
    public void shutdown() {
    }


    public static void main(String[] args) {
        String ip = "alpha.demo.yuandiansec.net";
//        System.out.println(testHive(ip, 10000, 3000));
//        System.out.println(testPostgres(ip, 5432, 30000));
//        System.out.println(testOracle(ip, 1522, 30000));
//        System.out.println(testMssql(ip, 1433, 30000));
//        System.out.println(testMysql("", 3306, 30000));
//        List<Integer> localhostPorts = portScan(22,65535,ip,100,200);
//        for (Integer port : localhostPorts) {
//            System.out.println("ip " + ip + ", Port " + port + " is open");
//        }
    }

    public static List<Integer> portScan(int fromPort, int toPort, String ip, int portScanThreads, int timeoutMilliseconds) {
        final ExecutorService portScanExecutorService = Executors.newFixedThreadPool(portScanThreads);
        final ConcurrentLinkedQueue<Integer> openPorts = new ConcurrentLinkedQueue<>();
        final AtomicInteger port = new AtomicInteger(fromPort);
        while (port.get() < toPort) {
            final int currentPort = port.getAndIncrement();
            portScanExecutorService.submit(() -> {
                try {
                    Socket socket = new Socket();
                    socket.connect(new InetSocketAddress(ip, currentPort), timeoutMilliseconds);
                    socket.close();
                    openPorts.add(currentPort);
                } catch (IOException ignored) {
                }
            });
        }
        portScanExecutorService.shutdown();
        try {
            portScanExecutorService.awaitTermination(10, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        List<Integer> openPortList = new ArrayList<>();
        while (!openPorts.isEmpty()) {
            openPortList.add(openPorts.poll());
        }
        return openPortList;
    }

    public static Pair<String,String> testOneHost(String host, int port, int timeoutMilliseconds) {
        Pair<String,String> result = null;
        try {
            Pair<Boolean,String> testResult;
            if ((testResult = testMysql(host, port, timeoutMilliseconds)).getLeft()) {
                result = Pair.of("MySQL",testResult.getRight());
            } else if ((testResult = testMssql(host, port, timeoutMilliseconds)).getLeft()) {
                result = Pair.of("SQLServer",testResult.getRight());
            } else if ((testResult = testPostgres(host, port, timeoutMilliseconds)).getLeft()) {
                result = Pair.of("PostgreSQL",testResult.getRight());
            } else if ((testResult = testOracle(host, port, timeoutMilliseconds)).getLeft()) {
                result = Pair.of("Oracle",testResult.getRight());
            } else if ((testResult = testMongodb(host, port, timeoutMilliseconds)).getLeft()) {
                result = Pair.of("MongoDB",testResult.getRight());
            } else if ((testResult = testHive(host, port, timeoutMilliseconds)).getLeft()) {
                result = Pair.of("Hive",testResult.getRight());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if(result == null){
            LOG.info("在主机["+host+"]，端口["+port+"] 上未识别到任何数据库服务");
        } else{
            LOG.info("在主机["+host+"]，端口["+port+"] 上识别到["+result.getLeft()+"]数据库服务，数据库版本号为["+result.getRight()+"]");
        }
        return result;
    }

    static final byte ff = (byte) 0xff;

    public static Pair<Boolean,String> testMysql(String host, int port, int timeoutMilliseconds) {
        try {
            Socket socket = new Socket();
            socket.setSoTimeout(timeoutMilliseconds);
            socket.connect(new InetSocketAddress(host, port), timeoutMilliseconds);
            byte[] bytes = new byte[1024];
            int read = socket.getInputStream().read(bytes, 0, 1024);
            String version = new String(bytes, 5, 6);
            if (bytes[3] == 0x00 && (bytes[4] == 0x0a || bytes[4] == 0x09 || bytes[4] == ff)) {
                return Pair.of(true,version);
            }
        } catch (IOException ignored) {
        }
        return Pair.of(false,null);
    }

    static final byte[] testMssqlMsg = new byte[]{
            0x12, 0x01, 0x00, 0x34, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x00, 0x06, 0x01,
            0x00, 0x1b,
            0x00, 0x01, 0x02, 0x00, 0x1c, 0x00, 0x0c,
            0x03, 0x00, 0x28, 0x00, 0x04, ff, 0x08, 0x00, 0x01,
            0x55, 0x00, 0x00, 0x00, 0x4d, 0x53, 0x53, 0x51, 0x4c,
            0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x00, 0x20, 0x12, 0x00, 0x00};

    public static Pair<Boolean,String> testMssql(String host, int port, int timeoutMilliseconds) {
        try {
            Socket socket = new Socket();
            socket.setSoTimeout(timeoutMilliseconds);
            socket.connect(new InetSocketAddress(host, port), timeoutMilliseconds);
            socket.getOutputStream().write(testMssqlMsg);
            byte[] bytes = new byte[16];
            socket.getInputStream().read(bytes, 0, 16);
            if ((bytes[0] == 0x04)
                    && (bytes[1] == 0x01 || bytes[1] == 0x00)
                    && (bytes[4] == 0x00)
                    && (bytes[5] == 0x00)
                    && (bytes[6] == 0x01)
                    && (bytes[7] == 0x00)
                    && (bytes[8] == 0x00)
                    && (bytes[9] == 0x00)
                    && (bytes[10] == 0x15)
                    && (bytes[11] == 0x00)
                    && (bytes[12] == 0x06)
                    && (bytes[13] == 0x01)
                    && (bytes[14] == 0x00)
                    && (bytes[15] == 0x1b)
            ) {
                return Pair.of(true,null);
            }
        } catch (IOException ignored) {
        }
        return Pair.of(false,null);
    }

    static final byte[] testOracleMsg = new byte[]{
            0x00, 0x5a, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x36, 0x01, 0x2c, 0x00, 0x00, 0x08, 0x00,
            0x7f, (byte) 0xff, 0x7f, 0x08, 0x00, 0x00, 0x00, 0x01, 0x00, 0x20, 0x00, 0x3a, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x34, (byte) 0xe6, 0x00, 0x00, 0x00,
            0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54,
            0x5f, 0x44, 0x41, 0x54, 0x41, 0x3d, 0x28, 0x43, 0x4f, 0x4d, 0x4d, 0x41, 0x4e, 0x44, 0x3d, 0x76, 0x65, 0x72,
            0x73, 0x69, 0x6f, 0x6e, 0x29, 0x29};

    public static Pair<Boolean,String> testOracle(String host, int port, int timeoutMilliseconds) {
        try {
            Socket socket = new Socket();
            socket.setSoTimeout(timeoutMilliseconds);
            socket.connect(new InetSocketAddress(host, port), timeoutMilliseconds);
            socket.getOutputStream().write(testOracleMsg);
            byte[] bytes = new byte[100];
            socket.getInputStream().read(bytes, 0, 100);
            String text = new String(bytes);
            if ((bytes[0] == 0x00) && (bytes[2] == 0x00) && (bytes[3] == 0x00) && (bytes[4] == 0x02 || bytes[4] == 0x04)) {
                return Pair.of(true,null);
            }
        } catch (Throwable ignored) {
            ignored.printStackTrace();
        }
        return Pair.of(false,null);
    }

//    static final byte [] testTeradataMsg = new byte []{0x03, 0x01, 0x0a, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
//            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
//            0x00, 0x00, 0x00, 0x00, 0x00, (byte) 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
//            0x00, 0x00, 0x00, 0x00, 0x00, (byte) 0xa6, 0x00, 0x10, 0x00, 0x00, 0x00, 0x01, 0x00, 0x02, 0x00, 0x04,
//            0x0c, 0x00, 0x00, 0x00};
//
//    public static boolean testTeradata(String host, int port, int timeout){
//        try {
//            Socket socket = new Socket();
//            socket.connect(new InetSocketAddress(host, port), timeout);
//            socket.getOutputStream().write(testTeradataMsg);
//            byte[] bytes = new byte[4];
//            socket.getInputStream().read(bytes, 0, 4);
//            if (bytes[0] == 0x03 && bytes[1] == 0x02 ) {
//                return true;
//            }
//        } catch (IOException ignored) {
//            ignored.printStackTrace();
//        }
//        return false;
//    }
//
//    static final byte [] test_netezza_msg = new byte[]{0x00, 0x00, 0x00, 0x08, 0x00, 0x01, 0x00, 0x03} ;
//    public static boolean testNetezza(String host, int port, int timeout){
//        try {
//            Socket socket = new Socket();
//            socket.connect(new InetSocketAddress(host, port), timeout);
//            socket.getOutputStream().write(test_netezza_msg);
//            byte[] bytes = new byte[63];
//            socket.getInputStream().read(bytes, 0, 63);
//            if (bytes[0] == 0x03 && bytes[1] == 0x02 ) {
//                return true;
//            }
//        } catch (IOException ignored) {
//            ignored.printStackTrace();
//        }
//        return false;
//    }

    static final byte[] test_postgres_msg = new byte[]{0x00, 0x00, 0x00, 0x08, 0x04, (byte) 0xd2, 0x16, 0x2f};

    public static Pair<Boolean,String> testPostgres(String host, int port, int timeoutMilliseconds) {
        try {
            Socket socket = new Socket();
            socket.setSoTimeout(timeoutMilliseconds);
            socket.connect(new InetSocketAddress(host, port), timeoutMilliseconds);
            socket.getOutputStream().write(test_postgres_msg);
            socket.getOutputStream().flush();
            byte[] bytes = new byte[24];
            socket.getInputStream().read(bytes, 0, 24);
            if ((bytes[0] == 0x4e) || (bytes[0] == 0x45)) {
                return Pair.of(true,null);
            }
        } catch (IOException ignored) {
//            ignored.printStackTrace();
        }
        return Pair.of(false,null);
    }

    public static Pair<Boolean,String> testMongodb(String host, int port, int timeout) {
        try{
            RequestConfig.Builder requestBuilder = RequestConfig.custom();
            requestBuilder.setConnectTimeout(timeout);
            requestBuilder.setConnectionRequestTimeout(timeout);
            CloseableHttpClient client = HttpClientBuilder.create().setDefaultRequestConfig(requestBuilder.build()).build();
            HttpGet httpGet = new HttpGet();
            HttpHost httpHost = new HttpHost(host, port);
            CloseableHttpResponse execute = client.execute(httpHost, httpGet);
            InputStream content = execute.getEntity().getContent();
            StringBuilder sb = new StringBuilder();
            String line;
            BufferedReader br = new BufferedReader(new InputStreamReader(content));
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }
            br.close();
            return Pair.of(sb.toString().toLowerCase().contains("mongodb"),null);
        } catch (Throwable ignored){
//            ignored.printStackTrace();
        }
        return Pair.of(false,null);
    }

    static final byte[] test_hive_msg1 = new byte[]{
            0x01,0x00,0x00,0x00,0x05,0x50,0x4c,0x41,0x49,0x4e
    };
    static final byte[] test_hive_msg2 = new byte[]{
            0x05, 0x00, 0x00, 0x00, 0x09, 0x00, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x00, 0x31, 0x31
    };


    public static Pair<Boolean,String> testHive(String host, int port, int timeoutMilliseconds) {
        try {
            Socket socket = new Socket();
            socket.setSoTimeout(timeoutMilliseconds);
            socket.connect(new InetSocketAddress(host, port), timeoutMilliseconds);
            socket.getOutputStream().write(test_hive_msg1);
            socket.getOutputStream().flush();
            socket.getOutputStream().write(test_hive_msg2);
            socket.getOutputStream().flush();
            byte[] bytes = new byte[5];
            socket.getInputStream().read(bytes, 0, 5);
            if ((bytes[0] == 0x03 && bytes[1] == 0x00 && bytes[2] == 0x00 && bytes[3] == 0x00 && bytes[4] == 0x1a) ||
            (bytes[0] == 0x05 && bytes[1] == 0x00 && bytes[2] == 0x00 && bytes[3] == 0x00 && bytes[4] == 0x00)) {
                return Pair.of(true,null);
            }
        } catch (Throwable ignored) {
//            ignored.printStackTrace();
        }
        return Pair.of(false,null);
    }


}
