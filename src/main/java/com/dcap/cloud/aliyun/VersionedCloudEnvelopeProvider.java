package com.dcap.cloud.aliyun;

import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.Session;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.List;

public class VersionedCloudEnvelopeProvider {

  public static CloudEnvelope create(Session session, List<String> pluginPath, ObjectNode contents) {
//    return new CloudEnvelope(session, pluginPath, contents);
    return null;
  }
}
