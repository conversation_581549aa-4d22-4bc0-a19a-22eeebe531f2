package com.dcap.cloud.aliyun.intermediate.oss;

import com.dcap.cloud.aliyun.PluginConstant;
import com.dcap.cloud.aliyun.services.oss.OssObject;
import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.Emitter;
import com.dcap.cloud.core.api.IntermediatePlugin;
import com.dcap.utils.Base64Util;
import com.dcap.utils.JSON;
import com.google.common.collect.ImmutableList;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.dcap.probe.client.ProbeClientTaskUtil;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import com.yd.dspm.config.DspmTaskType;
import com.yd.rules.engine.ScoringModel;
import com.yd.rules.engine.models.ScoringModelInput;
import com.yd.rules.engine.models.ScoringModelOutput;
import com.yd.rules.engine.parser.CustomScoreParser;
import com.yd.rules.engine.parser.ScoringModelParser;
import com.yd.rules.engine.parser.ScoringRuleParser;
import com.yd.rules.engine.result.MatchedResult;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

import static com.yd.dcap.classifier.taskreport.ScanDbReport.FIELD_RULE_COUNT;

public class OssIdentifyContentPlugin implements IntermediatePlugin<OssObject<String>> {
    private static final Logger LOG = LoggerFactory.getLogger(OssIdentifyContentPlugin.class);

    private static final ScoringModelParser scoringModelParser = new ScoringModelParser(new CustomScoreParser(new ScoringRuleParser()));

    private Map<String, Object> cloudDiscoveryConfig;

    private List<ScoringModel> modelList = null;

    private String currentVersion;

    private Integer markThresholds = 80;

    private Long taskId;


    private final Map<String, List<MatchedResult>> matchedResultMap = new HashMap<>();



    @Override
    public String id() {
        return PluginConstant.OSS_IDENTIFY_CONTENT_ID;
    }

    @Override
    public void init(Map<String, Object> cloudDiscoveryConfig) {
        this.cloudDiscoveryConfig = cloudDiscoveryConfig;
        try{
            String currentVersionText = Objects.toString(this.cloudDiscoveryConfig.get("currentVersion"),null);
            if (StringUtils.isNotBlank(currentVersionText)){
                this.currentVersion = currentVersionText;
            }
            String markThresholdsText = Objects.toString(this.cloudDiscoveryConfig.get("markThresholds"),null);
            if (StringUtils.isNotBlank(markThresholdsText)){
                this.markThresholds = Integer.parseInt(markThresholdsText);
            }
            String taskIdText = Objects.toString(this.cloudDiscoveryConfig.get("taskId"), null);
            if (StringUtils.isNotBlank(taskIdText)){
                this.taskId = Long.parseLong(taskIdText);
            }
        } catch (Exception e){
            e.printStackTrace();
//            this.currentVersion = String.valueOf(System.currentTimeMillis());
        }

        //  todo: 这里还应该收集手动打标和手动删除的列表，用于匹配时的输入数据。
        LOG.info("Start parsing the rule model");
        List<Map<String,Object>> dataTags = (List<Map<String,Object>>) this.cloudDiscoveryConfig.get("dataTags");
        if (dataTags == null || dataTags.isEmpty()){
            modelList = ImmutableList.of();
            return;
        }
        modelList = new ArrayList<>();
        for (Map<String, Object> dataTagMap : dataTags) {
            try {
                String dataTagName = (String) dataTagMap.get("name");
                String dataTagType = (String) dataTagMap.get("dataTagType");
                String dataTagText = (String) dataTagMap.get("dataTag");
                String dataTagOrderByText = String.valueOf(dataTagMap.get("dataTagOrderBy"));
                int dataTagOrderBy = 50;
                if (StringUtils.isNotBlank(dataTagOrderByText) && !Objects.equals("null", dataTagOrderByText)){
                    dataTagOrderBy = Integer.parseInt(dataTagOrderByText);
                }
                String modelData = (String) dataTagMap.get("modelData");
                if (StringUtils.isBlank(modelData)) {
                    continue;
                }
                String decrypt;
                try {
                    decrypt = Base64Util.Decrypt(modelData);
                } catch (Exception e){
                    decrypt = modelData;
                }
                ScoringModel scoringModel = scoringModelParser.parseScoringModel(dataTagText, dataTagType, decrypt, dataTagOrderBy);
                modelList.add(scoringModel);
            } catch (Exception e){
                LOG.error(e.getMessage(), e);
                LOG.error("Error [{}] encountered while initializing the build data tag model, the data tag [{}] will be skipped", e.getMessage(), dataTagMap);
            }
        }
        // 对模型排序，要求 基础类型，业务类型先执行，复合类型会依赖 基础类型和业务类型的执行结果。
        modelList.sort(null);
        if(taskId != null){
            ProbeClientTaskContext probeClientTaskContext = ProbeClientTaskUtil.getInstance().getProbeClientTaskContext(DspmTaskType.ALIYUN_OSS_SCAN, taskId);
            probeClientTaskContext.recordField(FIELD_RULE_COUNT, modelList.size());
        }
    }

    @Override
    public void accept(CloudEnvelope<OssObject<String>> envelope, Emitter emitter) {
        long jobHistoryId = envelope.getSession().getJobHistoryId();
        OssObject<String> ossObject = envelope.getContents();
        for (ScoringModel scoringModel : this.modelList) {
            try {
                String dataTag = scoringModel.getDataTag();
                // 创建输入对象。根据对象名称获取该对象已经打标的结果。
                List<MatchedResult> results = matchedResultMap.getOrDefault(ossObject.getFullObjectName(), new ArrayList<>());
                Map<String, Object> modelInputData = buildModelInputData(ossObject, results);
                modelInputData.put("dataTag", dataTag);
                ScoringModelOutput interpret = scoringModel.interpret(new ScoringModelInput(modelInputData));
                MatchedResult execResults = interpret.getMatchedResult(markThresholds);
                if (execResults.getEvaluation() != null && execResults.isConfirmed()) {
                    results.add(execResults);
                    matchedResultMap.put(ossObject.getFullObjectName(), results);
                }
            } catch (Exception e){
                e.printStackTrace();
            }
        }
        List<MatchedResult> matchedResults = matchedResultMap.get(ossObject.getFullObjectName());
        String matchedResultText = null;
        String metadataText = null;
        if (matchedResults != null){
            matchedResultText = Base64Util.Encrypt(JSON.from(matchedResults).toString());
        }
        if (ossObject.getMetadata() != null){
            metadataText = Base64Util.Encrypt(JSON.from(ossObject.getMetadata()).toString());
        }

        long objectLastModified = ossObject.getObjectLastModified();

        String[] ossInventoryArr = new String[]{
                String.valueOf(jobHistoryId), ossObject.getRegion(), String.valueOf(ossObject.getTenantId()), ossObject.getObjectDir(),
                ossObject.getBucketOwnerId(), ossObject.getBucketArn(), ossObject.getObjectKey(), ossObject.getFullObjectName(),
                ossObject.getShortName(), ossObject.getBucketName(), ossObject.getEtag(), ossObject.getObjectType(), ossObject.getDataType(),
                String.valueOf(ossObject.getObjectSize()), String.valueOf(ossObject.getContentLimit()), ossObject.getContentLanguage(),
                metadataText, matchedResultText,String.valueOf(this.currentVersion),ossObject.getBucketId(), String.valueOf(objectLastModified)
        };
        emitter.emit(CloudEnvelope.of(envelope, id(), ossInventoryArr));

    }

    private Map<String,Object> buildModelInputData(OssObject<String> ossObject, List<MatchedResult> matchedResults){
        Map<String,Integer> currentMarked = null;
        if (matchedResults != null && !matchedResults.isEmpty()) {
            currentMarked = matchedResults.stream()
                    .filter(item -> Objects.equals(item.getEvaluation(), MatchedResult.CONFIRMED))
                    .map((matchedResult) ->
                        new AbstractMap.SimpleImmutableEntry<>(matchedResult.getDataTag(), matchedResult.getScore())
                    )
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,(v1,v2)->{
                        return v1;
                    }));
        }

        Map<String, Object> modelInputData = UtilMisc.toMap(
                "dataStore", UtilMisc.toMap(
                        "name", ossObject.getBucketName()
                ),
                "container", UtilMisc.toMap(
                        "name", ossObject.getShortName(),
                        "fullName", ossObject.getFullObjectName(),
                        "type", ossObject.getObjectType(),
                        "content", ossObject.getContent(),
                        // 该列 当前已经标记的
                        "currentMarked", currentMarked
//                        // 该列 已经手动打标的
//                        "manualTags", columnManualTagsSet,
//                        // 该列 已经排除的标签
//                        "deletedAutoTags", columnDeletedAutoTagsSet
                ),
                "item", UtilMisc.toMap(
                        "type", ossObject.getDataType(),
                        "size", ossObject.getObjectSize()
//                        "values", ossObject.getContent()
//                        // 该列 当前已经标记的
//                        "currentMarked", currentColumnMarked,
//                        // 该列 已经手动打标的
//                        "manualTags", columnManualTagsSet,
//                        // 该列 已经排除的标签
//                        "deletedAutoTags", columnDeletedAutoTagsSet
                )
        );

        return modelInputData;
    }
}
