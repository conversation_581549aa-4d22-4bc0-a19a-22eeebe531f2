package com.dcap.cloud.aliyun.intermediate.oss;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.dcap.cloud.aliyun.PluginConstant;
import com.dcap.cloud.aliyun.services.oss.OssObject;
import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.Emitter;
import com.dcap.cloud.core.api.IntermediatePlugin;
import com.dcap.cloud.core.api.Session;
import com.dcap.utils.UtilTika;
import com.yd.dcap.common.utils.UtilDateTime;
import com.yd.dcap.probe.client.ProbeClientTaskUtil;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.mime.MediaType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.yd.dcap.classifier.taskreport.CloudOssScanReport.FIELD_EXTRACTED_COUNT;
import static com.yd.dcap.classifier.taskreport.StatusRecord.Position.OssExtracted;

public class FromPathExtractContentPlugin implements IntermediatePlugin<OssObject<String>> {
    private static final Logger LOG = LoggerFactory.getLogger(FromPathExtractContentPlugin.class);

    private long lastScanTimestamp = -1;
    private Date lastScanDate = null;

    @Override
    public String id() {
        return PluginConstant.OSS_EXTRACT_CONTENT_ID;
    }

    @Override
    public void init(Map<String, Object> pluginConfig) {
        String lastScanTimestampText = String.valueOf(pluginConfig.get("lastScanTimestamp"));
        if (StringUtils.isNotBlank(lastScanTimestampText) && !Objects.equals("null", lastScanTimestampText)){
            lastScanTimestamp = Long.parseLong(lastScanTimestampText);
            lastScanDate = new Date(lastScanTimestamp);
        } else {
            lastScanDate = new Date();
            lastScanTimestamp = lastScanDate.getTime();
        }
    }

    @Override
    public void accept(CloudEnvelope<OssObject<String>> envelope, Emitter emitter) {
        Session session = envelope.getSession();
        ProbeClientTaskContext probeClientTaskContext = ProbeClientTaskUtil.getInstance().getProbeClientTaskContext(session.getTaskType(), session.getTaskId());
        OssObject<String> ossObject = envelope.getContents();
        // 如果最近一次更新时间，小于最后扫描的时间，说明已经是扫描过的，不需要再扫描了。
//        if (ossObject.getObjectLastModified() < this.lastScanTimestamp){
//            String objectKey = ossObject.getObjectKey();
//            LOG.info("object key [{}], last modified[{}], last scan timestamp[{}]", objectKey, UtilDateTime.datetimeToString(new Date(ossObject.getObjectLastModified())), UtilDateTime.datetimeToString(this.lastScanDate));
//            return;
//        }
        String bucketName = ossObject.getBucketName();
//        Path path = ossObject.getPath();
//        if (path == null){
//            return;
//        }
        OSSClient ossClient = ossObject.getClient();
        String objectKey = ossObject.getObjectKey();
        ossObject.setFullObjectName(objectKey);
        GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, objectKey);
        try (OSSObject ossObj = ossClient.getObject(getObjectRequest)){
            ObjectMetadata objectMetadata = ossObj.getObjectMetadata();
            ossObject.setEtag(objectMetadata.getETag());
            String contentMD5 = objectMetadata.getContentMD5();
            Metadata metadata = getObjectMetadata(objectMetadata);
            String resourceName;
            int lastPathSeparatorIndex = ossObj.getKey().lastIndexOf("/");
            if (lastPathSeparatorIndex != -1) {
                resourceName = ossObj.getKey().substring(lastPathSeparatorIndex + 1);
            } else {
                resourceName = ossObj.getKey();
            }
            metadata.set("resourceName", resourceName);
            ossObject.setShortName(resourceName);
            ossObject.setObjectDir(ossObject.getFullObjectName().substring(0,ossObject.getFullObjectName().lastIndexOf(resourceName)));
            if (objectKey.equals(ossObject.getObjectDir())){
                return;
            }
            MediaType objectMediaType = MediaType.parse(metadata.get(Metadata.CONTENT_TYPE));
            ossObject.setDataType(objectMediaType.toString());
            String objectFileExName = resourceName.lastIndexOf(".") != -1? resourceName.substring(resourceName.lastIndexOf(".")+1):"none";
            // 对象扩展名
            ossObject.setObjectType(objectFileExName);
            Map<String,Object> metadataMap = new HashMap<>();
            for (int i = 0; i < metadata.names().length; i++) {
                String metadataKey = metadata.names()[i];
                String value = metadata.get(metadataKey);
                metadataMap.put(metadataKey, value);
            }
            ossObject.setMetadata(metadataMap);
            long objectSize = ossObj.getObjectMetadata().getContentLength();
            if (objectSize == 0){
                return;
            }
            ossObject.setObjectSize(objectSize);
            // 根据文件大小，文件类型，文件名称，决定是否提取文件内容。
            // 如果不需要提取文件内容，直接添加到识别队列即可
            if (!needExtracted(ossObj, metadata, objectMediaType)){
                emitter.emit(CloudEnvelope.of(envelope, PluginConstant.OSS_EXTRACT_CONTENT_ID, ossObject));
                return;
            }

            LOG.info("started with tika to extract object content:" + objectKey);
            Pair<Metadata, String> parse = UtilTika.parse(ossObj.getObjectContent(), metadata, 63325);
            LOG.info("extracting content using Tika is done:" + objectKey);
            String content = parse.getRight();
            if (StringUtils.isNotBlank(content)){
                content = clearText(content);
                String objectLanguage = UtilTika.detectLanguage(content);
                ossObject.setContentLanguage(objectLanguage);
                ossObject.setContent(content);
            }
            probeClientTaskContext.recordField(FIELD_EXTRACTED_COUNT, 1).sendToServer();
            emitter.emit(CloudEnvelope.of(envelope, PluginConstant.OSS_EXTRACT_CONTENT_ID, ossObject));
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            probeClientTaskContext.reportErrorOccurredExecuting(OssExtracted,objectKey, null,e).sendToServer();
        }
    }

    private boolean needExtracted(OSSObject ossObject, Metadata metadata, MediaType objectMediaType){
        return true;
    }

    /**
     * 清洗数据
     * @return
     */
    private String clearText(String content){
        return content.replaceAll("[\r\n\t]|\r\n"," ");
    }

    private Metadata getObjectMetadata(ObjectMetadata objectMetadata) throws ParseException {
        Metadata metadata = new Metadata();
        Map<String, Object> rawMetadata = objectMetadata.getRawMetadata();
        if (rawMetadata != null) {
            rawMetadata.forEach((key, value) -> {
                if (value != null) {
                    metadata.set(key, value.toString());
                }
            });
        }

        Map<String, String> userMetadata = objectMetadata.getUserMetadata();
        if (userMetadata != null) {
            userMetadata.forEach((key, value) -> {
                if (value != null) {
                    metadata.set(key, value);
                }
            });
        }
        if (objectMetadata.getCacheControl() != null) {
            metadata.set("cacheControl", objectMetadata.getCacheControl());
        }

        if (objectMetadata.getRawExpiresValue() != null) {
            metadata.set("rawExpiresValue", objectMetadata.getRawExpiresValue());
        }

        if (objectMetadata.getObjectType() != null) {
            metadata.set("x-oss-object-type", objectMetadata.getObjectType());
        }

        if (objectMetadata.getContentDisposition() != null) {
            metadata.set("contentDisposition", objectMetadata.getContentDisposition());
        }

        if (objectMetadata.getObjectRawRestore() != null) {
            metadata.set("objectRawRestore", objectMetadata.getObjectRawRestore());
        }

        if (objectMetadata.getContentMD5() != null) {
            metadata.set(Metadata.CONTENT_MD5, objectMetadata.getContentMD5());
        }

        if (objectMetadata.getContentEncoding() != null) {
            metadata.set(Metadata.CONTENT_ENCODING, objectMetadata.getContentEncoding());
        }

        metadata.set(Metadata.CONTENT_LENGTH, String.valueOf(objectMetadata.getContentLength()));

        if (objectMetadata.getContentType() != null) {
            metadata.set(Metadata.CONTENT_TYPE, objectMetadata.getContentType());
        }

        if (objectMetadata.getETag() != null) {
            metadata.set("ETag", objectMetadata.getETag());
        }

        if (objectMetadata.getRequestId() != null) {
            metadata.set("x-oss-request-id", objectMetadata.getRequestId());
        }

        if (objectMetadata.getServerSideDataEncryption() != null) {
            metadata.set("serverSideDataEncryption", objectMetadata.getServerSideDataEncryption());
        }

        if (objectMetadata.getServerSideEncryption() != null) {
            metadata.set("serverSideEncryption", objectMetadata.getServerSideEncryption());
        }

        if (objectMetadata.getServerSideEncryptionKeyId() != null) {
            metadata.set("serverSideEncryptionKeyId", objectMetadata.getServerSideEncryptionKeyId());
        }

        if (objectMetadata.getVersionId() != null) {
            metadata.set("versionId", objectMetadata.getVersionId());
        }

        if (objectMetadata.getExpirationTime() != null) {
            metadata.set("expirationTime", UtilDateTime.datetimeToString(objectMetadata.getExpirationTime()));
        }

        if (objectMetadata.getLastModified() != null) {
            metadata.set("Last-Modified", UtilDateTime.datetimeToString(objectMetadata.getLastModified()));
        }

        if (objectMetadata.getServerCRC() != null) {
            metadata.set("serverCRC", String.valueOf(objectMetadata.getServerCRC()));
        }

        if (objectMetadata.getObjectStorageClass() != null) {
            metadata.set("x-oss-storage-class", objectMetadata.getObjectStorageClass().toString());
        }
        return metadata;
    }
}
