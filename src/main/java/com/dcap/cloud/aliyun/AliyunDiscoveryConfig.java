package com.dcap.cloud.aliyun;

import com.google.common.collect.ImmutableList;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class AliyunDiscoveryConfig {
    private Map<String,String> ramUser = new HashMap<>();
    private Map<String, Object> stsUser = new HashMap<>();
    private List<String> services = ImmutableList.of();
    private List<String> regions = ImmutableList.of();
    private List<String> ignoredRegions = ImmutableList.of();

}
