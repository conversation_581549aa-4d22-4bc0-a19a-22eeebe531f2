package com.dcap.cloud.aliyun;

/**
 * 插件id定义
 */
public interface PluginConstant {
    String ALIYUN_ID = "cloud.aliyun.discovery";

    String CLOUD_DB_OUT_ID = "cloud.db.out";

    String CLOUD_CSV_OUT_ID = "cloud.csv.out";

    String CLOUD_MQ_OUT_ID = "cloud.mq.out";

    String CLOUD_SQL_PARSER_ID = "cloud.sql.parser";


    String CLOUD_ECS_DATABASE_DISCOVERY_ID = "cloud.ecs.database.discovery";


    String CLOUD_IDENTITY_ID = "cloud.identity";

    String OSS_EXTRACT_CONTENT_ID = "oss.extract.content.from.path";

    String OSS_IDENTIFY_CONTENT_ID = "oss.identify.content";
}
