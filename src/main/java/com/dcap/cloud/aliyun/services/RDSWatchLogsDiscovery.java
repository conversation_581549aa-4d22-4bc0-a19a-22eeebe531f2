package com.dcap.cloud.aliyun.services;

import com.aliyun.rds20140815.Client;
import com.aliyun.rds20140815.models.*;
import com.aliyun.teaopenapi.models.Config;
import com.dcap.cloud.aliyun.AliyunClientBuilder;
import com.dcap.cloud.aliyun.AliyunClientCreator;
import com.dcap.cloud.aliyun.DiscoveryExceptions;
import com.dcap.cloud.aliyun.exception.AliyunDiscoveryException;
import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.Emitter;
import com.dcap.cloud.core.api.Session;
import com.dcap.utils.UtilDateTime;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import com.yd.dcap.probe.client.ProbeClientTaskUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.List;
import java.util.Map;

public class RDSWatchLogsDiscovery extends AbstractAliyunDiscovery {

    private static final Logger LOG = LoggerFactory.getLogger(RDSWatchLogsDiscovery.class);


    public RDSWatchLogsDiscovery(Map<String, Object> cloudDiscoveryConfig) {
        super(cloudDiscoveryConfig);
    }


    @Override
    public void discover(ObjectMapper mapper, Session session, String region, Emitter emitter, AliyunClientCreator clientCreator) {
        try {
            final Client client = clientCreator.apply(new RDSClientBuilder()).build();
            List<String> dbInstanceIds = getRdsInstanceIds(region, client);
            discoverLogs(mapper, session, region, emitter, dbInstanceIds, client);
        } catch (Exception ex) {
            ex.printStackTrace();
            DiscoveryExceptions.onDiscoveryException(RESOURCE_DISCOVERY_PATH, null, region, ex);
        }
    }

    private List<String> getRdsInstanceIds(String region, Client client) throws Exception {
        // todo: 暂时硬编码，正确的方式应该是根据配置中的 id，逐一检查并获取开启 SQL 洞察的 dbinstance
        String dbInstanceId = (String) cloudDiscoveryConfig.get("instanceId");
        if (enabledSQLLog(dbInstanceId, client)) {
            return ImmutableList.of(dbInstanceId);
        }
        return ImmutableList.of();
    }

    private void discoverLogs(ObjectMapper mapper, Session session, String region, Emitter emitter, List<String> dbInstanceIds,
                              Client client) throws Exception {
        if (dbInstanceIds.isEmpty()) {
            String dbInstanceId = (String) cloudDiscoveryConfig.get("instanceId");
            throw new AliyunDiscoveryException(dbInstanceId + " 没有开启 sql 洞察");
        }
        // todo: 暂时只存在一个 dbinstance
        String dbInstanceId = dbInstanceIds.get(0);

        String datasourceId = String.valueOf(cloudDiscoveryConfig.get("datasourceId"));
        String datasourceType = (String) cloudDiscoveryConfig.get("datasourceType");
        String tenantId = String.valueOf(cloudDiscoveryConfig.get("tenantId"));
        String startTime = (String) cloudDiscoveryConfig.get("startTime");
        int durationMinute = 10;
        if (cloudDiscoveryConfig.get("durationMinute") != null) {
            durationMinute = Integer.parseInt(cloudDiscoveryConfig.get("durationMinute").toString());
        }

        // 如果参数不存在，就从一小时前开始扫描
        startTime = startTime == null ? "" : UtilDateTime.convertDateToUTC(new Date(startTime));
        long timeMillis = System.currentTimeMillis();
        if (StringUtils.isBlank(startTime)) {
            startTime = UtilDateTime.convertDateToUTC(new Date(timeMillis - 1000 * 60 * 60));
        }
        String endTime = UtilDateTime.convertDateToUTC(new Date(timeMillis));
        int pageNumber = 1;
        long taskId = Long.parseLong((String) this.cloudDiscoveryConfig.get("taskId"));
        do {
            DescribeSQLLogRecordsRequest logRecordsRequest = new DescribeSQLLogRecordsRequest();
            logRecordsRequest.setDBInstanceId(dbInstanceId);
            logRecordsRequest.setStartTime(startTime);
            logRecordsRequest.setEndTime(endTime);
            logRecordsRequest.setPageSize(100);
            logRecordsRequest.setPageNumber(pageNumber);
            DescribeSQLLogRecordsResponse describeSQLLogRecordsResponse = client.describeSQLLogRecords(logRecordsRequest);
            if (describeSQLLogRecordsResponse == null ||
                    describeSQLLogRecordsResponse.getBody() == null ||
                    describeSQLLogRecordsResponse.getBody().getItems() == null ||
                    describeSQLLogRecordsResponse.getBody().getItems().getSQLRecord() == null ||
                    describeSQLLogRecordsResponse.getBody().getItems().getSQLRecord().isEmpty()) {
                while (System.currentTimeMillis() - timeMillis < 1000L * 60 * durationMinute) {
                    Thread.sleep(100);
                }
                startTime = UtilDateTime.convertDateToUTC(new Date(timeMillis - 5000));
                timeMillis = System.currentTimeMillis();
                endTime = UtilDateTime.convertDateToUTC(new Date(timeMillis));
                pageNumber = 1;
                continue;
            }
            int size = describeSQLLogRecordsResponse.getBody().items.SQLRecord.size();
            LOG.warn("SQLRecord:" + size);
            for (DescribeSQLLogRecordsResponseBody.DescribeSQLLogRecordsResponseBodyItemsSQLRecord sqlRecord : describeSQLLogRecordsResponse.getBody().getItems().getSQLRecord()) {
                CloudEnvelope.MetaData metaData = new CloudEnvelope.MetaData(region, this.CLOUD_SERVICE_ID, this.CLOUD_SERVICE_NAME, this.CLOUD_PROVIDER_ID, this.RESOURCE_DISCOVERY_PATH);
                CloudEnvelope<Map<String, Object>> cloudEnvelope =
                        new CloudEnvelope<>(session, ImmutableList.of(RESOURCE_DISCOVERY_PATH), metaData, sqlRecord.toMap());
//                cloudEnvelope.getMetadata().put("datasourceId", datasourceId);
//                cloudEnvelope.getMetadata().put("tenantId", tenantId);
//                cloudEnvelope.getMetadata().put("datasourceType", datasourceType);
                emitter.emit(cloudEnvelope);
            }
            pageNumber++;
        } while (ProbeClientTaskUtil.getInstance().checkTaskRunning(session.getTaskType(),taskId));// todo: 判断是否中断
    }

    private boolean enabledSQLLog(String dbInstanceId, Client client) {
        try {
            DescribeSQLCollectorPolicyRequest request = new DescribeSQLCollectorPolicyRequest();
            request.setDBInstanceId(dbInstanceId);
            DescribeSQLCollectorPolicyResponse describeSQLCollectorPolicyResponse = client.describeSQLCollectorPolicy(request);
            return describeSQLCollectorPolicyResponse != null &&
                    describeSQLCollectorPolicyResponse.body != null &&
                    describeSQLCollectorPolicyResponse.body.getSQLCollectorStatus() != null &&
                    describeSQLCollectorPolicyResponse.body.getSQLCollectorStatus().intern().equals("Enable");
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    private static class RDSClientBuilder implements AliyunClientBuilder<RDSClientBuilder, Client> {

        private final Config config = new Config();

        @Override
        public Client build() {
            try {
                return new Client(config);
            } catch (Exception e) {
                throw new AliyunDiscoveryException(e.getMessage(), e);
            }
        }

        @Override
        public RDSClientBuilder credentialsProvider(String accessKeyId, String accessKeySecret) {
            config.setAccessKeyId(accessKeyId);
            config.setAccessKeySecret(accessKeySecret);
            return this;
        }

        @Override
        public RDSClientBuilder region(String region) {
            config.setRegionId(region);
            config.setEndpoint("rds.aliyuncs.com");
            return this;
        }
    }

    @Data
    public static class RDSWatchLogsConfig {
        private Long taskId;
        private Long tenantId;
        private Map<String, Object> datasource;
        // ENABLED RUNNING DISABLED FINISHED
        private String status;
        // ALIYUN TENCENT
        private String cloudType;
        private String accessKeyId;
        private String accessKeySecret;
        private String region;
        private String dbInstanceId;
        private Long startTime;
        private Integer durationMinute;
        private String extConfig;
    }
}
