package com.dcap.cloud.aliyun.services.oss;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.ListObjectsV2Request;
import com.aliyun.oss.model.ListObjectsV2Result;
import com.aliyun.oss.model.OSSObjectSummary;
import com.dcap.cloud.aliyun.AliyunClientBuilder;
import com.dcap.cloud.aliyun.AliyunClientCreator;
import com.dcap.cloud.aliyun.DiscoveryExceptions;
import com.dcap.cloud.aliyun.exception.AliyunDiscoveryException;
import com.dcap.cloud.aliyun.services.AbstractAliyunDiscovery;
import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.Emitter;
import com.dcap.cloud.core.api.Session;
import com.dcap.utils.command.DefaultCommonExecs;
import com.dcap.utils.command.ExecResult;
import com.dcap.utils.command.TimeoutCommonExecs;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.probe.client.ProbeClientTaskUtil;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.Map;

import static com.yd.dcap.classifier.taskreport.CloudOssScanReport.FIELD_OBJECT_COUNT;

public class OssBucketOriginDiscovery extends AbstractAliyunDiscovery {

    private static final Logger LOG = LoggerFactory.getLogger(OssBucketOriginDiscovery.class);


    public OssBucketOriginDiscovery(Map<String, Object> cloudDiscoveryConfig) {
        super(cloudDiscoveryConfig);
    }


    @Override
    public void discover(ObjectMapper mapper, Session session, String region, Emitter emitter, AliyunClientCreator clientCreator) {
        try {
            final String bucketId = (String) this.cloudDiscoveryConfig.get("bucketId");
            final String bucketName = (String) this.cloudDiscoveryConfig.get("bucketName");
            String logMsg = "Start scanning bucket["+bucketName+"]";
            LOG.info(logMsg);
            ProbeClientTaskContext probeClientTaskContext = ProbeClientTaskUtil.getInstance().getProbeClientTaskContext(session.getTaskType(), session.getTaskId());
            probeClientTaskContext.reportExecuting(StatusRecord.Position.OssScanStart, null, logMsg).sendToServer();
            CloudEnvelope.MetaData metaData = new CloudEnvelope.MetaData(region, this.CLOUD_SERVICE_ID, this.CLOUD_SERVICE_NAME, this.CLOUD_PROVIDER_ID, this.RESOURCE_DISCOVERY_PATH);

            String bucketArnPrefix = "acs:oss:"+region+":"+bucketName;
            final OSSClient client = clientCreator.apply(new OssClientBuilder()).build();
            String continuationToken = null;
            ListObjectsV2Result listObjectsV2Result = null;
            do {
                ListObjectsV2Request listObjectsV2Request = new ListObjectsV2Request(bucketName);
                listObjectsV2Request.setMaxKeys(10);
                if (continuationToken != null) {
                    listObjectsV2Request.setContinuationToken(continuationToken);
                }
                listObjectsV2Result = client.listObjectsV2(listObjectsV2Request);
                int count=0;
                for (OSSObjectSummary objectSummary : listObjectsV2Result.getObjectSummaries()) {
//                    String objectKey = objectSummary.getKey();
//                    String objectSize = String.valueOf(objectSummary.getSize());
//                    String objectStorageClass = objectSummary.getStorageClass();
//                    String objectOwner = objectSummary.getOwner().getDisplayName();
//                    String objectOwnerId = objectSummary.getOwner().getId();
//                    String objectOwnerDisplayName = objectSummary.getOwner().getDisplayName();
//                    String objectETag = objectSummary.getETag();
                    long objectLastModified = objectSummary.getLastModified().getTime();

                    if (!ProbeClientTaskUtil.getInstance().checkTaskRunning(session.getTaskType(), session.getTaskId())
                        || probeClientTaskContext.checkInterrupt()) {
                        break;
                    }

                    long size = objectSummary.getSize();
                    if (size == 0){
                        LOG.warn("the object[{}] size is 0, it will be skipped",objectSummary.getKey());
                        continue;
                    }
                    OssObject<String> ossObject = new OssObject<>();
                    ossObject.setClient(client);
                    ossObject.setObjectKey(objectSummary.getKey());
                    ossObject.setBucketId(bucketId);
                    ossObject.setBucketName(bucketName);
                    ossObject.setRegion(region);
                    ossObject.setTenantId(session.getTenantId());
                    ossObject.setBucketArn(bucketArnPrefix+"/"+ossObject.getObjectKey());
                    ossObject.setObjectLastModified(objectLastModified);
                    count++;
                    // 传递到后续的处理器。
                    CloudEnvelope<OssObject<String>> cloudEnvelope = new CloudEnvelope<>(session, ImmutableList.of(RESOURCE_DISCOVERY_PATH), metaData, ossObject);
                    emitter.emit(cloudEnvelope);
                }
                probeClientTaskContext.recordField(FIELD_OBJECT_COUNT, count).sendToServer();
                continuationToken = listObjectsV2Result.getNextContinuationToken();
            } while (listObjectsV2Result.isTruncated());
        } catch (Exception ex) {
            ex.printStackTrace();
            DiscoveryExceptions.onDiscoveryException(RESOURCE_DISCOVERY_PATH, null, region, ex);
        }
    }


    /**
     * 返回 mount 成功的路径。
     * @return
     */
    private Pair<Boolean, String> mountOssBucket(final String region, final String bucketName, final String accessKeyId, final String accessKeySecret) {
        long tenantId = Long.parseLong(String.valueOf(this.cloudDiscoveryConfig.get("tenantId")));
        // 1 写入密码到文件
        Pair<Boolean, String> execResult = writePasswordToFile(tenantId, bucketName, accessKeyId, accessKeySecret);
        if (!execResult.getLeft()){
            LOG.error("Failed to write password to file: {}", execResult.getRight());
            return Pair.of(false, execResult.getRight());
        }
        String passwordFilePath = execResult.getRight();
        // 2 挂载 oss 目录
        Pair<Boolean, String> mountResult = execMountCommand(tenantId, bucketName, region, passwordFilePath);
        if (!mountResult.getLeft()) {
            LOG.error("Failed to mount oss bucket: {}", mountResult.getRight());
            return Pair.of(false, mountResult.getRight());
        }

        return Pair.of(true,mountResult.getRight());
    }

    private Pair<Boolean, String> execMountCommand(long tenantId, String bucketName, String region, String passwordFile){
        try {
            String mountDir = "/tmp/ossfs/t_"+tenantId+"/"+bucketName;
            File mountDirFile = new File(mountDir);
            if (!mountDirFile.exists()){
                if (!mountDirFile.mkdirs()) {
                    return Pair.of(false, "Failed to create directory for mounting oss buckets: " + mountDir);
                }
            } else {
                // fusermount -u your_mountpoint
                String unmountCommand = "fusermount -u " + mountDir;
                TimeoutCommonExecs unmountBucket = new TimeoutCommonExecs("bash", Lists.newArrayList(
                        "-c", unmountCommand
                ));
                ExecResult execResult = unmountBucket.exec();
                if (!execResult.execSuccess()) {
                    String errorMsg = "stdout:"+execResult.getStdout()+"; stderr: "+execResult.getStderr();
                    LOG.error("An error occurred while executing unmount bucket; unmount command:{}, {}", unmountCommand, errorMsg);
                    // 尝试使用 umount -f 卸载
                    unmountCommand = "umount -f" + mountDir;
                    unmountBucket = new TimeoutCommonExecs("bash", Lists.newArrayList(
                            "-c", unmountCommand
                    ));
                    execResult = unmountBucket.exec();
                    if (!execResult.execSuccess()) {
                        errorMsg = "stdout:"+execResult.getStdout()+"; stderr: "+execResult.getStderr();
                        LOG.error("An error occurred while executing unmount bucket; unmount command:{}, {}", unmountCommand, errorMsg);
                        return Pair.of(false, errorMsg);
                    }
                }
            }
            String endpoint = "http://oss-" + region + ".aliyuncs.com";
            String mountCommand = "ossfs " + bucketName + " " + mountDir +
                    " -o url=" + endpoint +
                    " -o passwd_file=" + passwordFile +
                    " -o nonempty";

            // ossfs ydtestbkt /tmp/ossfs -o url=http://oss-cn-beijing.aliyuncs.com -o passwd_file=/usr/local/src/passwd-ossfs
            TimeoutCommonExecs mountBucket = new TimeoutCommonExecs("bash", Lists.newArrayList(
                    "-c", mountCommand
            ));
            ExecResult execResult = mountBucket.exec();
            if (!execResult.execSuccess()) {
                String errorMsg = "stdout:"+execResult.getStdout()+"; stderr: "+execResult.getStderr();
                LOG.error("An error occurred while executing oss mount; mount command:{}, {}", mountCommand, errorMsg);
                return Pair.of(false, errorMsg);
            }
            return Pair.of(true, mountDir+"/");
        } catch (Exception e){
            e.printStackTrace();
            return Pair.of(false, e.getMessage());
        }
    }


    /**
     * 将 oss 认证相关的信息写入到一个文件，返回值是 pair，如果成功，左边值为 true，右边值是完整的密码文件路径。
     * 如果失败，左边值是 false，右边值是异常信息。
     * @param tenantId
     * @param bucketName
     * @param accessKeyId
     * @param accessKeySecret
     * @return
     */
    private Pair<Boolean, String> writePasswordToFile(long tenantId, String bucketName, String accessKeyId, String accessKeySecret){
        try {
            String bucketInfo = bucketName + ":" + accessKeyId + ":" + accessKeySecret;
            String passwordDirs = "/var/oss/pwd/t_"+tenantId+"/"+bucketName+"/";
            File pwdDirs = new File(passwordDirs);
            if (!pwdDirs.exists()){
                if (!pwdDirs.mkdirs()) {
                    return Pair.of(false, "create password dirs failed");
                }
            }
            String passwordFile = passwordDirs+"passwd-ossfs";
            String writePasswordCommand = "echo "+bucketInfo+" > "+passwordFile;
            DefaultCommonExecs writePassword = new DefaultCommonExecs("bash", Lists.newArrayList(
                    "-c", writePasswordCommand
            ));
            ExecResult execResult = writePassword.exec();
            if (!execResult.execSuccess()) {
                String errorMsg = "stdout:"+execResult.getStdout()+"; stderr: "+execResult.getStderr();
                LOG.error("An error occurred while writing the password to a file; {}", errorMsg);
                return Pair.of(false, errorMsg);
            }
            LOG.info("Writing password to file successfully:{}", passwordFile);
            DefaultCommonExecs changeModeCommand = new DefaultCommonExecs("bash", Lists.newArrayList(
                    "-c",
                    "chmod 400 "+passwordFile
            ));
            execResult = changeModeCommand.exec();
            if (!execResult.execSuccess()){
                String errorMsg = "stdout:" + execResult.getStdout() + "; stderr: " + execResult.getStderr();
                LOG.error("An error occurred while setting permissions for the password file; {}", errorMsg);
                return Pair.of(false, errorMsg);
            }
            return Pair.of(true, passwordFile);
        } catch (Exception e){
            e.printStackTrace();
            return Pair.of(false, e.getMessage());
        }
    }


    private static class OssClientBuilder implements AliyunClientBuilder<OssClientBuilder, OSSClient> {

        private String region;
        private String key;
        private String secret;

        @Override
        public OSSClient build() {
            try {
                String endpoint = "oss-"+region+".aliyuncs.com";
                return (OSSClient) new OSSClientBuilder().build(endpoint, key, secret);
            } catch (Exception e) {
                throw new AliyunDiscoveryException(e.getMessage(), e);
            }
        }

        @Override
        public OssClientBuilder credentialsProvider(String accessKeyId, String accessKeySecret) {
            this.key = accessKeyId;
            this.secret = accessKeySecret;
            return this;
        }

        @Override
        public OssClientBuilder region(String region) {
            this.region = region;
            return this;
        }
    }
}
