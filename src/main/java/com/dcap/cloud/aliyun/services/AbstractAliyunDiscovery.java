package com.dcap.cloud.aliyun.services;

import com.dcap.cloud.aliyun.AliyunClientCreator;
import com.dcap.cloud.aliyun.AliyunDiscoveryPlugin;
import com.dcap.cloud.core.api.Emitter;
import com.dcap.cloud.core.api.Session;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

public abstract class AbstractAliyunDiscovery implements AliyunDiscovery {

    private static final Logger LOG = LoggerFactory.getLogger(AbstractAliyunDiscovery.class);

    protected final Map<String,Object> cloudDiscoveryConfig;

    protected final String RESOURCE_DISCOVERY_PATH;

    protected final String CLOUD_SERVICE_ID;

    protected final String CLOUD_SERVICE_NAME;

    protected final String CLOUD_PROVIDER_ID;


    public AbstractAliyunDiscovery(Map<String,Object> cloudDiscoveryData) {
        this.cloudDiscoveryConfig = cloudDiscoveryData;
        CLOUD_SERVICE_ID = (String) cloudDiscoveryConfig.get("cloudServiceId");
        CLOUD_SERVICE_NAME = (String) cloudDiscoveryConfig.get("cloudServiceName");
        CLOUD_PROVIDER_ID = (String) cloudDiscoveryConfig.get("cloudProviderId");
        RESOURCE_DISCOVERY_PATH = CLOUD_PROVIDER_ID + ":" + CLOUD_SERVICE_ID ;
    }

    @Override
    public String service() {
        return CLOUD_SERVICE_ID;
    }

    @Override
    public void discoverWrapper(ObjectMapper mapper, Session session, String region, Emitter emitter,
                                AliyunClientCreator clientCreator) {
        LOG.debug("Starting {} discovery in {}", RESOURCE_DISCOVERY_PATH, region);
        discover(mapper, session, region, emitter, clientCreator);
        LOG.debug("Completed {} discovery in {}", RESOURCE_DISCOVERY_PATH, region);
    }

    public abstract void discover(ObjectMapper mapper, Session session, String region, Emitter Emitter, AliyunClientCreator clientCreator);

    @Override
    public String fullService() {
        return AliyunDiscoveryPlugin.ID + ":" + service();
    }

    @Override
    public List<String> getSupportedRegions() {
        // todo: 应该是传递的参数或者是查询数据库
        return ImmutableList.of("cn-qingdao","cn-beijing","cn-zhangjiakou","cn-huhehaote","cn-wulanchabu","cn-hangzhou","cn-shanghai"
                ,"cn-shenzhen","cn-heyuan","cn-guangzhou","cn-chengdu","cn-nanjing","cn-hongkong","ap-southeast-1","ap-southeast-2"
                ,"ap-southeast-3","ap-southeast-5","ap-southeast-6","ap-south-1","ap-northeast-1","us-west-1","us-east-1","eu-central-1"
                ,"eu-west-1","me-east-1");
    }
}
