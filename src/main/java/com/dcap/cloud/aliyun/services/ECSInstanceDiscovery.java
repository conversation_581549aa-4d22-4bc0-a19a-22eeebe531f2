package com.dcap.cloud.aliyun.services;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.ecs.model.v20140526.DescribeInstancesRequest;
import com.aliyuncs.ecs.model.v20140526.DescribeInstancesResponse;
import com.aliyuncs.profile.DefaultProfile;
import com.dcap.cloud.aliyun.AliyunClientBuilder;
import com.dcap.cloud.aliyun.AliyunClientCreator;
import com.dcap.cloud.aliyun.DiscoveryExceptions;
import com.dcap.cloud.aliyun.exception.AliyunDiscoveryException;
import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.Emitter;
import com.dcap.cloud.core.api.Session;
import com.dcap.utils.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.dcap.platform.model.CloudDiscovery;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Map;

public class ECSInstanceDiscovery extends AbstractAliyunDiscovery {

    private static final Logger LOG = LoggerFactory.getLogger(ECSInstanceDiscovery.class);

    public ECSInstanceDiscovery(Map<String, Object> configuration) {
        super(configuration);
    }


    @Override
    public void discover(ObjectMapper mapper, Session session, String region, Emitter emitter, AliyunClientCreator clientCreator) {
        try {
            final IAcsClient client = clientCreator.apply(new IaaSClientBuilder()).build();
            discoveryECSInstances(session, region, client, emitter);
        } catch (Exception ex) {
            ex.printStackTrace();
            DiscoveryExceptions.onDiscoveryException(RESOURCE_DISCOVERY_PATH, null, region, ex);
        }
    }

    private void discoveryECSInstances(Session session, String region, IAcsClient client, Emitter emitter) throws Exception {
        int pageNumber = 1;
        String nextToken = null;
        // 创建 API 请求并设置参数。
        do {
            DescribeInstancesRequest request = new DescribeInstancesRequest();
            request.setRegionId(region);
            request.setPageSize(30);
            request.setPageNumber(pageNumber);
            if (nextToken != null) {
                request.setNextToken(nextToken);
            }
            // 发起请求并处理应答或异常。
            DescribeInstancesResponse response = client.getAcsResponse(request);
            if (response == null || response.getInstances() == null || response.getInstances().isEmpty()) {
                break;
            }else {
                LOG.info("获取到 ECS 实例");
            }
            nextToken = response.getNextToken();
            pageNumber++;
            for (DescribeInstancesResponse.Instance instance : response.getInstances()) {
                CloudEnvelope.MetaData metaData = new CloudEnvelope.MetaData(region, this.CLOUD_SERVICE_ID, this.CLOUD_SERVICE_NAME, this.CLOUD_PROVIDER_ID, this.RESOURCE_DISCOVERY_PATH);
                CloudEnvelope<DescribeInstancesResponse.Instance> envelope =
                        new CloudEnvelope<>(session, ImmutableList.of(RESOURCE_DISCOVERY_PATH), metaData, instance);
                emitter.emit(envelope);
            }
        } while (true);
    }

    private static class IaaSClientBuilder implements AliyunClientBuilder<IaaSClientBuilder, IAcsClient> {
        private String region;
        private String accessKeyId;
        private String accessKeySecret;

        @Override
        public IAcsClient build() {
            try {
                DefaultProfile profile = DefaultProfile.getProfile(region, accessKeyId, accessKeySecret);
                return new DefaultAcsClient(profile);
            } catch (Exception e) {
                throw new AliyunDiscoveryException(e.getMessage(), e);
            }
        }

        @Override
        public IaaSClientBuilder credentialsProvider(String accessKeyId, String accessKeySecret) {
            this.accessKeyId = accessKeyId;
            this.accessKeySecret = accessKeySecret;
            return this;
        }

        @Override
        public IaaSClientBuilder region(String region) {
            this.region = region;
            return this;
        }
    }

    @Data
    public static class RDSWatchLogsConfig {
        private Long taskId;
        private Long tenantId;
        private Map<String, Object> datasource;
        // ENABLED RUNNING DISABLED FINISHED
        private String status;
        // ALIYUN TENCENT
        private String cloudType;
        private String accessKeyId;
        private String accessKeySecret;
        private String region;
        private String dbInstanceId;
        private Long startTime;
        private Integer durationMinute;
        private String extConfig;
    }
}
