package com.dcap.cloud.aliyun.services.oss;

import com.aliyun.oss.OSSClient;
import com.yd.rules.engine.result.MatchedResult;

import java.util.List;
import java.util.Map;

public class OssObject<T> {

    private String region;

    private long tenantId;

    private OSSClient client;
//    private Path path;

    private T content;


    // 如果是采用 mount 到目录的方式，就会有值。
//    private String bucketMountDir;

    // 对象所属的目录
    private String objectDir;


    // 创建Bucket的用户ID
    private String bucketOwnerId;

    private String bucketArn;

    private String objectKey;

    private String fullObjectName;

    private String shortName;

    private String bucketId;
    private String bucketName;


    private String eTag;


    // 对象文件的扩展名
    private String objectType;

    // 对象文件的实际类型 MediaType MIME types
    private String dataType;

    // 对象实际大小。
    private long objectSize;

    // 获取对象文件的内容上限。字节
    private int contentLimit;

    private String contentLanguage;

    private long objectLastModified = -1L;
    private Map<String,Object> metadata;

    private List<MatchedResult> matchedResults;


    public OSSClient getClient() {
        return client;
    }

    public void setClient(OSSClient client) {
        this.client = client;
    }

    public T getContent() {
        return content;
    }

    public void setContent(T content) {
        this.content = content;
    }

    public String getBucketName() {
        return bucketName;
    }

    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    public String getFullObjectName() {
        return fullObjectName;
    }

    public void setFullObjectName(String fullObjectName) {
        this.fullObjectName = fullObjectName;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public long getTenantId() {
        return tenantId;
    }

    public void setTenantId(long tenantId) {
        this.tenantId = tenantId;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getObjectType() {
        return objectType;
    }

    public void setObjectType(String objectType) {
        this.objectType = objectType;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public long getObjectSize() {
        return objectSize;
    }

    public void setObjectSize(long objectSize) {
        this.objectSize = objectSize;
    }

//    public String getBucketMountDir() {
//        return bucketMountDir;
//    }
//
//    public void setBucketMountDir(String bucketMountDir) {
//        this.bucketMountDir = bucketMountDir;
//    }

    public String getObjectKey() {
        return objectKey;
    }

    public void setObjectKey(String objectKey) {
        this.objectKey = objectKey;
    }

//    public Path getPath() {
//        return path;
//    }
//
//    public void setPath(Path path) {
//        this.path = path;
//    }

    public String getContentLanguage() {
        return contentLanguage;
    }

    public void setContentLanguage(String contentLanguage) {
        this.contentLanguage = contentLanguage;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    public String getObjectDir() {
        return objectDir;
    }

    public void setObjectDir(String objectDir) {
        this.objectDir = objectDir;
    }

    public String getBucketOwnerId() {
        return bucketOwnerId;
    }

    public void setBucketOwnerId(String bucketOwnerId) {
        this.bucketOwnerId = bucketOwnerId;
    }

    public String getBucketArn() {
        return bucketArn;
    }

    public void setBucketArn(String bucketArn) {
        this.bucketArn = bucketArn;
    }

    public int getContentLimit() {
        return contentLimit;
    }

    public void setContentLimit(int contentLimit) {
        this.contentLimit = contentLimit;
    }

    public String getEtag() {
        return eTag;
    }

    public void setEtag(String eTag) {
        this.eTag = eTag;
    }

    public List<MatchedResult> getMatchedResults() {
        return matchedResults;
    }

    public void setMatchedResults(List<MatchedResult> matchedResults) {
        this.matchedResults = matchedResults;
    }

    public String getBucketId() {
        return bucketId;
    }

    public void setBucketId(String bucketId) {
        this.bucketId = bucketId;
    }

    public void setObjectLastModified(long objectLastModified) {
        this.objectLastModified = objectLastModified;
    }

    public long getObjectLastModified() {
        return objectLastModified;
    }
}
