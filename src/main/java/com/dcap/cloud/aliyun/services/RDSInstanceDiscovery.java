package com.dcap.cloud.aliyun.services;

import com.aliyun.rds20140815.Client;
import com.aliyun.rds20140815.models.*;
import com.aliyun.teaopenapi.models.Config;
import com.dcap.cloud.aliyun.AliyunClientBuilder;
import com.dcap.cloud.aliyun.AliyunClientCreator;
import com.dcap.cloud.aliyun.DiscoveryExceptions;
import com.dcap.cloud.aliyun.exception.AliyunDiscoveryException;
import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.Emitter;
import com.dcap.cloud.core.api.Session;
import com.dcap.utils.UtilDateTime;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import com.yd.dcap.platform.model.CloudAssetsDatabase;
import com.yd.dcap.platform.model.CloudService;
import com.yd.dcap.platform.model.query.QCloudAssetsDatabase;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.AbstractMap;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class RDSInstanceDiscovery extends AbstractAliyunDiscovery {

    private final Logger LOG = LoggerFactory.getLogger(RDSInstanceDiscovery.class);


    public RDSInstanceDiscovery(Map<String, Object> configuration) {
        super(configuration);
    }

    @Override
    public void discover(ObjectMapper mapper, Session session, String region, Emitter emitter, AliyunClientCreator clientCreator) {
        try {
            final Client client = clientCreator.apply(new RDSClientBuilder()).build();
            discover(mapper, session, region, emitter, client);
        } catch (Exception ex) {
            ex.printStackTrace();
            DiscoveryExceptions.onDiscoveryException(RESOURCE_DISCOVERY_PATH, null, region, ex);
        }
    }

    private void discover(ObjectMapper mapper, Session session, String region, Emitter emitter, Client client) throws Exception {
        int pageNumber = 1;
        String nextToken = null;
        do {
            DescribeDBInstancesRequest instancesRequest = new DescribeDBInstancesRequest();
            instancesRequest.setRegionId(region);
//            instancesRequest.setExpired("False");
//            instancesRequest.setDBInstanceType("Primary");
//            instancesRequest.setDBInstanceStatus("Running");

            // 必须是 30，因为还要调用 DescribeDBInstanceAttribute 接口获取更详细的信息，
            // 但是 DescribeDBInstanceAttribute 接口只支持一次最多传递 30 个 id
            instancesRequest.setPageSize(30);
            instancesRequest.setPageNumber(pageNumber);
            if (nextToken != null) {
                instancesRequest.setNextToken(nextToken);
            }

            DescribeDBInstancesResponse describeDBInstancesResponse = client.describeDBInstances(instancesRequest);
            if (describeDBInstancesResponse == null || describeDBInstancesResponse.getBody() == null ||
                    describeDBInstancesResponse.getBody().items == null || describeDBInstancesResponse.getBody().items.DBInstance == null ||
                    describeDBInstancesResponse.getBody().getItems().getDBInstance().isEmpty()) {
                break;
            }
            nextToken = describeDBInstancesResponse.getBody().nextToken;
//            CloudService cloudService = this.cloudDiscoveryData.getDiscoveryPlugin().getCloudService();
            List<DescribeDBInstancesResponseBody.DescribeDBInstancesResponseBodyItemsDBInstance> dbInstanceList =
                    describeDBInstancesResponse.getBody().getItems().getDBInstance();
            String instanceIds = dbInstanceList.stream().map(DescribeDBInstancesResponseBody.DescribeDBInstancesResponseBodyItemsDBInstance::getDBInstanceId)
                    .collect(Collectors.joining(","));
            // 调用 DescribeDBInstanceAttribute 接口获取详细信息，
            DescribeDBInstanceAttributeRequest describeDBInstanceAttributeRequest = new DescribeDBInstanceAttributeRequest();
            describeDBInstanceAttributeRequest.setDBInstanceId(instanceIds);
            DescribeDBInstanceAttributeResponse describeDBInstanceAttributeResponse = client.describeDBInstanceAttribute(describeDBInstanceAttributeRequest);
            // describeDBInstanceAttributeResponse 应该一定有结果才对。
            if (describeDBInstanceAttributeResponse == null || describeDBInstanceAttributeResponse.getBody() == null ||
                    describeDBInstanceAttributeResponse.getBody().items == null || describeDBInstanceAttributeResponse.getBody().items.DBInstanceAttribute == null ||
                    describeDBInstanceAttributeResponse.getBody().getItems().getDBInstanceAttribute().isEmpty()) {
                break;
            }
            Map<String, DescribeDBInstanceAttributeResponseBody.DescribeDBInstanceAttributeResponseBodyItemsDBInstanceAttribute> dbInstanceAttrMap = describeDBInstanceAttributeResponse.getBody().getItems().getDBInstanceAttribute().stream().map(attr ->
                    new AbstractMap.SimpleImmutableEntry<>(attr.getDBInstanceId(), attr)
            ).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            for (DescribeDBInstancesResponseBody.DescribeDBInstancesResponseBodyItemsDBInstance dbInstance : describeDBInstancesResponse.getBody().getItems().getDBInstance()) {
                String dbInstanceId = dbInstance.getDBInstanceId();
                DescribeDBInstanceAttributeResponseBody.DescribeDBInstanceAttributeResponseBodyItemsDBInstanceAttribute instanceAttribute = dbInstanceAttrMap.get(dbInstanceId);
                CloudAssetsDatabase cloudAssetsDatabase = new QCloudAssetsDatabase().instanceId.eq(dbInstanceId).findOne();
                if (cloudAssetsDatabase == null) {
                    cloudAssetsDatabase = new CloudAssetsDatabase();
                }
//                fillCloudAssetsDatabase(cloudAssetsDatabase, cloudService, dbInstance, instanceAttribute);

                CloudEnvelope<CloudAssetsDatabase> cloudEnvelope =
                        new CloudEnvelope<>(session, ImmutableList.of(RESOURCE_DISCOVERY_PATH),
                                new CloudEnvelope.MetaData(region, this.CLOUD_SERVICE_ID, this.CLOUD_SERVICE_NAME, this.CLOUD_PROVIDER_ID, this.RESOURCE_DISCOVERY_PATH),
                                cloudAssetsDatabase);
                emitter.emit(cloudEnvelope);
            }
            pageNumber++;
        } while (true);
    }

    private void fillCloudAssetsDatabase(CloudAssetsDatabase cloudAssetsDatabase, CloudService cloudService,
                                         DescribeDBInstancesResponseBody.DescribeDBInstancesResponseBodyItemsDBInstance dbInstance,
                                         DescribeDBInstanceAttributeResponseBody.DescribeDBInstanceAttributeResponseBodyItemsDBInstanceAttribute instanceAttribute) {
//        cloudAssetsDatabase.setResourceId(this.cloudDiscoveryData.getDiscoveryPlugin().getPluginName());
//        cloudAssetsDatabase.setTenantId(this.cloudDiscoveryData.getTenantId());
        cloudAssetsDatabase.setResourceType(RESOURCE_DISCOVERY_PATH);
        cloudAssetsDatabase.setLastUpdateTime(new Date().getTime());
        cloudAssetsDatabase.setProvider(cloudService.getProvider());
        cloudAssetsDatabase.setServiceId(cloudService.getId());
        cloudAssetsDatabase.setInstanceId(dbInstance.getDBInstanceId());

        // 实例规格 https://help.aliyun.com/document_detail/26312.html?spm=api-workbench.API%20Document.0.0.2ef51e0fvfnmlg
        String dbInstanceClass = dbInstance.getDBInstanceClass();
        cloudAssetsDatabase.setInstanceClass(dbInstanceClass);

        // 实例规格族，取值：s：共享型 x：通用型 d：独享套餐 h：独占物理机
        String dbInstanceClassType = instanceAttribute.getDBInstanceClassType();
        cloudAssetsDatabase.setInstanceClassType(dbInstanceClassType);

        // 实例类型，取值：Primary：主实例 ReadOnly：只读实例 Guard：灾备实例 Temp：临时实例
        String dbInstanceType = dbInstance.getDBInstanceType();
        cloudAssetsDatabase.setInstanceType(dbInstanceType);

        // 实例状态 https://help.aliyun.com/document_detail/26315.html?spm=api-workbench.API%20Document.0.0.2ef51e0fvfnmlg
        String dbInstanceStatus = dbInstance.getDBInstanceStatus();
        cloudAssetsDatabase.setInstanceStatus(dbInstanceStatus);

        String dbInstanceDescription = dbInstance.getDBInstanceDescription();
        cloudAssetsDatabase.setInstanceDescription(dbInstanceDescription);
        if (StringUtils.isBlank(dbInstanceDescription)) {
            cloudAssetsDatabase.setInstanceDescription(dbInstance.getDBInstanceId());
        }

        // 实例系列，取值 Basic：基础版 HighAvailability：高可用版 Finance：三节点企业版
        String category = instanceAttribute.getCategory();
        cloudAssetsDatabase.setCategory(category);

        // 实例访问模式，Standard：标准访问模式 Safe：数据库代理模式
        String connectionMode = dbInstance.getConnectionMode();
        cloudAssetsDatabase.setConnectionMode(connectionMode);

        // 实例连接地址 内网连接地址。
        String connectionString = dbInstance.getConnectionString();
        cloudAssetsDatabase.setConnectionString(connectionString);

        // 实例内网端口
        String port = instanceAttribute.getPort();
        cloudAssetsDatabase.setPort(Integer.valueOf(port));

        // 创建时间 格式：yyyy-MM-ddTHH:mm:ssZ（UTC时间）。
        String createTime = dbInstance.getCreateTime();
        cloudAssetsDatabase.setCreateTime(UtilDateTime.convertUTCToCSTDate(createTime).getTime());

        // 实例是内网或外网，取值：Internet：外网 Intranet：内网
        String dbInstanceNetType = dbInstance.getDBInstanceNetType();
        cloudAssetsDatabase.setInstanceNetType(dbInstanceNetType);

        // 实例的网络类型，取值 VPC：专有网络下的实例 Classic：经典网络下的实例
        String instanceNetworkType = dbInstance.getInstanceNetworkType();
        cloudAssetsDatabase.setInstanceNetworkType(instanceNetworkType);

        Integer dbInstanceStorage = instanceAttribute.getDBInstanceStorage();
        cloudAssetsDatabase.setInstanceStorage(dbInstanceStorage);

        // 实例存储类型
        String dbInstanceStorageType = instanceAttribute.getDBInstanceStorageType();
        cloudAssetsDatabase.setInstanceStorageType(dbInstanceStorageType);

        // 专属集群ID。
        String dedicatedHostGroupId = dbInstance.getDedicatedHostGroupId();
        cloudAssetsDatabase.setDedicatedHostGroupId(dedicatedHostGroupId);

        // 专属集群名称。
        String dedicatedHostGroupName = dbInstance.getDedicatedHostGroupName();
        cloudAssetsDatabase.setDedicatedHostGroupName(dedicatedHostGroupName);

        // Log节点所在主机的ID。
        String dedicatedHostIdForLog = dbInstance.getDedicatedHostIdForLog();
        cloudAssetsDatabase.setDedicatedHostIdForLog(dedicatedHostIdForLog);

        // Log节点所在主机的名称。
        String dedicatedHostNameForLog = dbInstance.getDedicatedHostNameForLog();
        cloudAssetsDatabase.setDedicatedHostNameForLog(dedicatedHostNameForLog);

        // Master节点所在主机的ID。
        String dedicatedHostIdForMaster = dbInstance.getDedicatedHostIdForMaster();
        cloudAssetsDatabase.setDedicatedHostIdForMaster(dedicatedHostIdForMaster);

        // Master节点所在主机的名称。
        String dedicatedHostNameForMaster = dbInstance.getDedicatedHostNameForMaster();
        cloudAssetsDatabase.setDedicatedHostNameForMaster(dedicatedHostNameForMaster);

        // Slave节点所在主机的ID。
        String dedicatedHostIdForSlave = dbInstance.getDedicatedHostIdForSlave();
        cloudAssetsDatabase.setDedicatedHostIdForSlave(dedicatedHostIdForSlave);

        // Slave节点所在主机的名称。
        String dedicatedHostNameForSlave = dbInstance.getDedicatedHostNameForSlave();
        cloudAssetsDatabase.setDedicatedHostNameForSlave(dedicatedHostNameForSlave);

        // Log节点所在主机的可用区ID。
        String dedicatedHostZoneIdForLog = dbInstance.getDedicatedHostZoneIdForLog();
        cloudAssetsDatabase.setDedicatedHostZoneIdForLog(dedicatedHostZoneIdForLog);

        // Master节点所在主机的可用区ID。
        String dedicatedHostZoneIdForMaster = dbInstance.getDedicatedHostZoneIdForMaster();
        cloudAssetsDatabase.setDedicatedHostZoneIdForMaster(dedicatedHostZoneIdForMaster);

        // Slave节点所在主机的可用区ID。
        String dedicatedHostZoneIdForSlave = dbInstance.getDedicatedHostZoneIdForSlave();
        cloudAssetsDatabase.setDedicatedHostZoneIdForSlave(dedicatedHostZoneIdForSlave);

        // 销毁时间。格式：yyyy-MM-ddTHH:mm:ssZ（UTC时间）。
        String destroyTime = dbInstance.getDestroyTime();
        if (StringUtils.isNotBlank(destroyTime)) {
            cloudAssetsDatabase.setDestroyTime(UtilDateTime.convertUTCToCSTDate(destroyTime).getTime());
        }

        // 数据库类型。
        String engine = dbInstance.getEngine();
        cloudAssetsDatabase.setEngine(engine);


        //数据库版本。
        String engineVersion = dbInstance.getEngineVersion();
        cloudAssetsDatabase.setEngineVersion(engineVersion);

        // 到期时间。格式：yyyy-MM-ddTHH:mm:ssZ（UTC时间）。
        String expireTime = dbInstance.getExpireTime();
        if (StringUtils.isNotBlank(expireTime)) {
            cloudAssetsDatabase.setExpireTime(UtilDateTime.convertUTCToCSTDate(expireTime).getTime());
        }

        // 主实例如果有灾备实例，该参数即为灾备实例的ID。
        String guardDBInstanceId = dbInstance.getGuardDBInstanceId();
        cloudAssetsDatabase.setGuardInstanceId(guardDBInstanceId);

        // 专属集群MySQL通用版实例所属的组名。
        String generalGroupName = dbInstance.getGeneralGroupName();
        cloudAssetsDatabase.setGeneralGroupName(generalGroupName);

        // 实例的锁定状态。取值 Unlock：正常。 ** ManualLock**：手动触发锁定。 LockByExpiration：实例过期自动锁定。
        // LockByRestoration：实例回滚前自动锁定。LockByDiskQuota：实例空间满自动锁定。
        // Released：实例已释放。此时实例无法进行解锁，只能使用备份数据重新创建新实例，重建时间较长，请耐心等待。
        String lockMode = dbInstance.getLockMode();
        cloudAssetsDatabase.setLockMode(lockMode);

        // 实例被锁定的原因。
        String lockReason = dbInstance.getLockReason();
        cloudAssetsDatabase.setLockReason(lockReason);

        // 主实例的ID，如果没有返回此参数（即为null）则表示该实例是主实例。
        String masterInstanceId = dbInstance.getMasterInstanceId();
        cloudAssetsDatabase.setMasterInstanceId(masterInstanceId);

        // 增量数据来源的实例ID，如灾备实例的增量数据来源是主实例。只读实例的增量数据来源是主实例，如果没有返回此参数则表示该实例是主实例。
        String incrementSourceDBInstanceId = instanceAttribute.getIncrementSourceDBInstanceId();
        cloudAssetsDatabase.setIncrementSourceInstanceId(incrementSourceDBInstanceId);

        // 一个实例下可创建最大数据库数量。
        Integer dbMaxQuantity = instanceAttribute.getDBMaxQuantity();
        cloudAssetsDatabase.setDbMaxQuantity(dbMaxQuantity);

        //可创建账号的最大数量。
        Integer accountMaxQuantity = instanceAttribute.getAccountMaxQuantity();
        cloudAssetsDatabase.setAccountMaxQuantity(accountMaxQuantity);

        // 实例可维护时间段，是UTC时间，+8小时才是控制台上显示的可维护时间段。 00:00Z-02:00Z
        String maintainTime = instanceAttribute.getMaintainTime();
        cloudAssetsDatabase.setMaintainTime(maintainTime);

        // 实例可用性状态，单位：百分比。 100.0%
        String availabilityValue = instanceAttribute.getAvailabilityValue();
        cloudAssetsDatabase.setAvailabilityValue(availabilityValue);

        // 是否是组合可用区。取值：true | false
        Boolean mutriORsignle = dbInstance.getMutriORsignle();
        cloudAssetsDatabase.setMutriORsignle(mutriORsignle.toString());

        // 付费类型，取值：Postpaid：按量付费 Prepaid：包年包月
        String payType = dbInstance.getPayType();
        cloudAssetsDatabase.setPayType(payType);

        // 主实例下如果有只读实例，该参数为只读实例的ID列表。
        if (dbInstance.getReadOnlyDBInstanceIds() != null &&
                dbInstance.getReadOnlyDBInstanceIds().getReadOnlyDBInstanceId() != null) {
            List<String> readOnlyDBInstanceIds = dbInstance.getReadOnlyDBInstanceIds().getReadOnlyDBInstanceId().stream()
                    .map(DescribeDBInstancesResponseBody.DescribeDBInstancesResponseBodyItemsDBInstanceReadOnlyDBInstanceIdsReadOnlyDBInstanceId::getDBInstanceId)
                    .collect(Collectors.toList());
            cloudAssetsDatabase.setReadOnlyInstanceIds(StringUtils.join(readOnlyDBInstanceIds, ","));
        }
        // 地域ID，可以通过接口DescribeRegions查看。
        String regionId = dbInstance.getRegionId();
        cloudAssetsDatabase.setRegionId(regionId);

        // 资源组ID。
        String resourceGroupId = dbInstance.getResourceGroupId();
        cloudAssetsDatabase.setResourceGroupId(resourceGroupId);

        // 当前专属集群MySQL通用版实例是否支持高可用权重切换。返回值： 100：支持切换。0：不支持切换。
        Integer switchWeight = dbInstance.getSwitchWeight();
        if (switchWeight != null) {
            cloudAssetsDatabase.setSwitchWeight(switchWeight);
        }

        // 主实例如果有临时实例，该参数即为临时实例的ID。
        String tempDBInstanceId = dbInstance.getTempDBInstanceId();
        cloudAssetsDatabase.setTempInstanceId(tempDBInstanceId);

        // 专属集群MySQL通用版实例的异常提示信息。
        String tips = dbInstance.getTips();
        cloudAssetsDatabase.setTips(tips);

        // 专属集群MySQL通用版实例的异常提示等级。返回值： 1：正常。 2：只读实例和主实例规格不对齐，可能影响可用性，请按需调整实例规格。
        Integer tipsLevel = dbInstance.getTipsLevel();
        cloudAssetsDatabase.setTipsLevel(String.valueOf(tipsLevel));

        // 专有网络实例ID。
        String vpcCloudInstanceId = dbInstance.getVpcCloudInstanceId();
        cloudAssetsDatabase.setVpcCloudInstanceId(vpcCloudInstanceId);

        // VPC ID。
        String vpcId = dbInstance.getVpcId();
        cloudAssetsDatabase.setVpcId(vpcId);

        // VPC名称。
        String vpcName = dbInstance.getVpcName();
        cloudAssetsDatabase.setVpcName(vpcName);

        // 交换机ID。
        String vSwitchId = dbInstance.getVSwitchId();
        cloudAssetsDatabase.setVSwitchId(vSwitchId);

        // 可用区ID。
        String zoneId = dbInstance.getZoneId();
        cloudAssetsDatabase.setZoneId(zoneId);

        String masterZone = instanceAttribute.getMasterZone();
        cloudAssetsDatabase.setMasterZoneId(masterZone);

        // 实例内存，单位：M。
        Long dbInstanceMemory = instanceAttribute.getDBInstanceMemory();
        cloudAssetsDatabase.setInstanceMemory(dbInstanceMemory);

        // 实例CPU数量。
        String dbInstanceCPU = instanceAttribute.getDBInstanceCPU();
        cloudAssetsDatabase.setInstanceCpu(dbInstanceCPU);

        // 系统字符集排序规则。
        String collation = instanceAttribute.getCollation();
        cloudAssetsDatabase.setCollation(collation);

        // 实例升级小版本的方式，取值：Auto：自动升级小版本 Manual：不自动升级，仅在当前版本下线时才强制升级
        String autoUpgradeMinorVersion = instanceAttribute.getAutoUpgradeMinorVersion();
        cloudAssetsDatabase.setAutoUpgradeMinorVersion(autoUpgradeMinorVersion);

        // 时区。
        String timeZone = instanceAttribute.getTimeZone();
        cloudAssetsDatabase.setTimeZone(timeZone);

        // 当前实例是否可以开放SA账号、AD域、主机账号等高权限功能。取值：Enable：开放 Disabled：不开放
        String superPermissionMode = instanceAttribute.getSuperPermissionMode();
        cloudAssetsDatabase.setSuperPermissionMode(superPermissionMode);

        // 白名单模式。取值：normal：通用模式 safety：高安全模式
        String securityIPMode = instanceAttribute.getSecurityIPMode();
        cloudAssetsDatabase.setSecurityIpMode(securityIPMode);

        // 最大每秒IO请求次数。
        Integer maxIOPS = instanceAttribute.getMaxIOPS();
        cloudAssetsDatabase.setMaxIops(maxIOPS);

        // 最大并发连接数。
        Integer maxConnections = instanceAttribute.getMaxConnections();
        cloudAssetsDatabase.setMaxConnections(maxConnections);

        //当前内核版本。
        String currentKernelVersion = instanceAttribute.getCurrentKernelVersion();
        cloudAssetsDatabase.setCurrentKernelVersion(currentKernelVersion);

        // 当前实例支持的最新内核版本。
        String latestKernelVersion = instanceAttribute.getLatestKernelVersion();
        cloudAssetsDatabase.setLatestKernelVersion(latestKernelVersion);

        // 实例支持的代理类型，取值： 0：表示不支持开通代理 1：表示支持开通共享代理（多租户模式） 2：表示支持开通独享代理（单租户模式）
        Integer proxyType = instanceAttribute.getProxyType();
        cloudAssetsDatabase.setProxyType(proxyType);
    }

    private static class RDSClientBuilder implements AliyunClientBuilder<RDSClientBuilder, Client> {

        private final Config config = new Config();

        @Override
        public Client build() {
            try {
                return new Client(config);
            } catch (Exception e) {
                throw new AliyunDiscoveryException(e.getMessage(), e);
            }
        }

        @Override
        public RDSClientBuilder credentialsProvider(String accessKeyId, String accessKeySecret) {
            config.setAccessKeyId(accessKeyId);
            config.setAccessKeySecret(accessKeySecret);
            return this;
        }

        @Override
        public RDSClientBuilder region(String region) {
            config.setRegionId(region);
            config.setEndpoint("rds.aliyuncs.com");
            return this;
        }
    }
}
