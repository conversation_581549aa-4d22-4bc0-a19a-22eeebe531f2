package com.dcap.cloud.aliyun.services;

import com.dcap.cloud.aliyun.AliyunClientCreator;
import com.dcap.cloud.core.api.Emitter;
import com.dcap.cloud.core.api.Session;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.List;

public interface AliyunDiscovery {

    String service();

    void discoverWrapper(ObjectMapper mapper, Session session, String region, Emitter emitter, AliyunClientCreator clientCreator);

//  default void discoverBackupJobs(String arn, String region, CloudResource data, AliyunClientCreator clientCreator) {
//    final var backups = BackupUtils.listBackupJobs(arn, region, clientCreator);
//    AWSUtils.update(data.supplementaryConfiguration, Map.of("awsBackupJobs", backups));
//  }

    String fullService();

    List<String> getSupportedRegions();
}
