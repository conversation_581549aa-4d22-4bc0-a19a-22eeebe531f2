package com.dcap.cloud.aliyun.task;


import com.dcap.utils.JSON;
import com.yd.dcap.classifier.ClassifierService;
import com.yd.dcap.classifier.config.SpringContextUtil;
import com.yd.dcap.classifier.taskreport.CloudDbScanReport;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.dcap.probe.TaskResultReporter;
import com.yd.dcap.probe.client.AbstractProbeClientProcessor;
import com.yd.dcap.probe.client.TaskConfig;
import com.yd.dcap.probe.client.TaskType;
import com.yd.dcap.probe.entities.ProbeClientJobResult;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import com.yd.dspm.config.DspmTaskType;

import java.util.Map;
import java.util.function.Consumer;

@TaskType(DspmTaskType.ALIYUN_DATABASE_SCAN)
public class AliyunDatabaseScanTask extends AbstractProbeClientProcessor {
    public AliyunDatabaseScanTask(ProbeClientTaskContext probeClientTaskContext) {
        super(probeClientTaskContext);
        TaskResultReporter taskResultReporter = probeClientTaskContext.getTaskResultReporter();
        probeClientTaskContext.registerTaskReport(new CloudDbScanReport(taskResultReporter, probeClientTaskContext));
    }

    @Override
    protected Map<String, Object> doWork(long jobHistoryId, long taskId, String taskType, long tenantId,
                                         Map<String, Object> taskConf, Consumer<ProbeClientJobResult> callback) throws Exception {
        ClassifierService classifierService = SpringContextUtil.getBean(ClassifierService.class);
        try {
            return classifierService.scan(probeClientTaskContext.getTaskType(), JSON.from(taskConf).toObject(TaskConfig.class));
        } catch (Exception e) {
            String causeMessage = getCauseMessage(e);
            if (causeMessage != null && causeMessage.contains("Unable to parse URL")) {
                probeClientTaskContext.setResult(UtilMisc.toMap("errMsg", "是否指定了数据库？" + causeMessage));
            } else if (causeMessage != null && causeMessage.contains("doesn't exist")) {
                probeClientTaskContext.setResult(UtilMisc.toMap("errMsg", causeMessage + "; 数据库是否开启了大小写敏感？"));
            } else {
                probeClientTaskContext.setResult(UtilMisc.toMap("errMsg", causeMessage));
            }
            throw e;
        } finally {
            probeClientTaskContext.clean();
        }
    }
}
