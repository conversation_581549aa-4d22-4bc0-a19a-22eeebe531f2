package com.dcap.cloud.aliyun.task;


import com.yd.dcap.classifier.taskreport.CloudOssScanReport;
import com.yd.dcap.probe.TaskResultReporter;
import com.yd.dcap.probe.client.TaskType;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import com.yd.dspm.config.DspmTaskType;

@TaskType(DspmTaskType.ALIYUN_OSS_SCAN)
public class AliyunOssScanTask extends AbstractCloudDiscovery {
    public AliyunOssScanTask(ProbeClientTaskContext probeClientTaskContext) {
        super(probeClientTaskContext);
        TaskResultReporter taskResultReporter = probeClientTaskContext.getTaskResultReporter();
        probeClientTaskContext.registerTaskReport(new CloudOssScanReport(taskResultReporter, probeClientTaskContext));
    }
}
