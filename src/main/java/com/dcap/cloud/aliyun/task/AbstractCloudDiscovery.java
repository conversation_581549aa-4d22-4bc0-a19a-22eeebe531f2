package com.dcap.cloud.aliyun.task;

import com.dcap.cloud.Discovery;
import com.dcap.cloud.core.Orchestrator;
import com.dcap.cloud.core.api.Session;
import com.dcap.cloud.core.config.ConfigUtils;
import com.dcap.cloud.core.config.ServiceConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.probe.client.AbstractProbeClientProcessor;
import com.yd.dcap.probe.entities.ProbeClientJobResult;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import org.apache.commons.lang3.tuple.Pair;

import java.io.File;
import java.io.InputStream;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.function.Consumer;

import static com.yd.dcap.classifier.taskreport.StatusRecord.Position.JobInterrupt;
import static com.yd.dcap.classifier.taskreport.StatusRecord.Position.OssScanStart;

public abstract class AbstractCloudDiscovery extends AbstractProbeClientProcessor {

    private static final ObjectMapper MAPPER = new ObjectMapper(new YAMLFactory());

    private static final String DEFAULT_CONFIG_FILE = "config.yaml";

    private Pair<List<Orchestrator.LayerCallable>, ExecutorService> pair;

    public AbstractCloudDiscovery(ProbeClientTaskContext probeClientTaskContext) {
        super(probeClientTaskContext);
    }



    @Override
    protected Map<String, Object> doWork(long execId, long taskId, String taskType, long tenantId,
                                        Map<String, Object> taskParam, Consumer<ProbeClientJobResult> callback) {
        final Instant start = Instant.now();
        if (taskParam == null) {
            throw new IllegalArgumentException("云数据扫描必须提供参数");
        }
        taskParam.put("execId", execId);
        taskParam.put("taskId", taskId);
        taskParam.put("taskType", taskType);
        taskParam.put("completedPosition", StatusRecord.Position.OssTaskEnd);

        String cloudProviderId = (String) taskParam.get("cloudProviderId");
        String cloudServiceId = (String) taskParam.get("cloudServiceId");
        // example：orchestrator/aliyun/rds/describe_sql_log_records
        String configFilePath = "orchestrator" + File.separator + cloudProviderId + File.separator + cloudServiceId +
                File.separator + taskType.toLowerCase() + File.separator + DEFAULT_CONFIG_FILE;
        try (InputStream is = Thread.currentThread().getContextClassLoader().getResourceAsStream(configFilePath.toLowerCase())) {
            if (is == null){
                probeClientTaskContext.reportFailed(OssScanStart, null, "配置文件查找失败["+configFilePath+"]").sendToServer();
                probeClientTaskContext.clean();
                return null;
            }
            final ServiceConfig serviceConfig = ConfigUtils.merge(MAPPER.readValue(is, ServiceConfig.class), System.getenv());
            String logMsg = "Start Cloud Discovery. execId="+execId+", taskId="+taskId+", taskType="+taskType;
            logger.info(logMsg);
            probeClientTaskContext.reportExecuting(OssScanStart,null, logMsg).sendToServer();
            Session session = new Session();
            session.setTaskId(taskId);
            session.setJobHistoryId(execId);
            session.setTaskType(taskType);
            session.setTenantId(tenantId);
            pair = new Orchestrator(taskId, taskParam, serviceConfig, session).scan();
        } catch (Exception e){
            String errMsg = "Discovery task has error["+e.getMessage()+"], end in "+Discovery.humanReadableFormat(Duration.between(start, Instant.now()));
            logger.error(errMsg,e);
            // 这是开始阶段，有异常时，应该上报并结束任务
            probeClientTaskContext.reportFailed(OssScanStart,null,errMsg).sendToServer();
            probeClientTaskContext.clean();
        }
        // 这是异步任务，所以这里返回 null
        return null;
    }

    @Override
    protected void onInterrupt(long execId, long taskId, String taskType,
                               long tenantId, Map<String, Object> taskParam) {
        if (pair == null) {
            logger.warn("The task may not have successfully started , so it is considered to be interrupted.");
            return;
        }
        List<Orchestrator.LayerCallable> callables = pair.getLeft();
        if (callables == null || callables.isEmpty()) {
            return;
        }
        String logMsg = "Forced shutting down task type ["+taskType+"], task id ["+taskId+"], exec id ["+execId+"] ";
        logger.info(logMsg);
        probeClientTaskContext.reportInterrupted(JobInterrupt, logMsg).sendToServer();
        // todo: 只需要关闭第一层
        callables.get(0).shutdown();
    }
}
