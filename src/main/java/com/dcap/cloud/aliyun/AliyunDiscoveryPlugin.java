package com.dcap.cloud.aliyun;

import com.dcap.cloud.aliyun.services.AliyunDiscovery;
import com.dcap.cloud.aliyun.services.oss.OssBucketOriginDiscovery;
import com.dcap.cloud.core.api.Emitter;
import com.dcap.cloud.core.api.OriginPlugin;
import com.dcap.cloud.core.api.Session;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.common.collect.ImmutableList;
import com.yd.dcap.common.utils.UtilMisc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Constructor;
import java.util.*;
import java.util.stream.Collectors;

public class AliyunDiscoveryPlugin implements OriginPlugin{
    public final static String ID = PluginConstant.ALIYUN_ID;
    private static final Logger LOG = LoggerFactory.getLogger(AliyunDiscoveryPlugin.class);

    protected static final ObjectMapper MAPPER = new ObjectMapper()
            .configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
            .findAndRegisterModules();

    private List<AliyunDiscovery> DISCOVERY_SERVICE_LIST;

    private Set<String> REGIONS;

    private Map<String,Object> discoveryConfig;

    private static final Map<String, Class<? extends AliyunDiscovery>> aliyunDiscoveryMap =
            UtilMisc.toMap(
//                    "describe_sql_log_records", RDSWatchLogsDiscovery.class,
//                    "describe_rds_instance_records", RDSInstanceDiscovery.class,
//                    "describe_db_instance_on_ecs", ECSInstanceDiscovery.class,
                    "dspm_aliyun_oss_scan", OssBucketOriginDiscovery.class
            );

    @Override
    public String id() {
        return ID;
    }

    @Override
    public void init(Map<String, Object> discoveryConfig) {
        this.discoveryConfig = discoveryConfig;
        String taskType = (String) discoveryConfig.get("taskType");
        if (taskType == null){
            throw new IllegalArgumentException("taskType name is empty");
        }
        taskType = taskType.toLowerCase();
        Class<? extends AliyunDiscovery> pluginClass = aliyunDiscoveryMap.get(taskType);
        if (pluginClass == null) {
            throw new RuntimeException("discovery plugin [" + taskType + "] is null");
        }
        try {
            Constructor<? extends AliyunDiscovery> declaredConstructor = pluginClass.getDeclaredConstructor(Map.class);
            this.DISCOVERY_SERVICE_LIST = ImmutableList.of(declaredConstructor.newInstance(this.discoveryConfig));
        } catch (Exception e) {
            e.printStackTrace();
        }
        String regions = (String) discoveryConfig.get("region");
        if (regions == null){
            regions = "";
        }
        this.REGIONS = new HashSet<>(Arrays.asList(regions.split(",")));
    }

    @Override
    public void discover(Session session, Emitter emitter) {
        final List<AliyunDiscovery> enabledPlugins = DISCOVERY_SERVICE_LIST.stream()
                .filter(plugin -> isEnabled(plugin.service())).collect(Collectors.toList());
        String cloudAccessType = (String) discoveryConfig.get("cloudAccessType");

        if (Objects.equals(cloudAccessType, "STS")) {
            LOG.warn("Unsupported access methods for Alibaba Cloud resources [{}]", cloudAccessType);
            return;
        }
        String accessKeyId = (String) discoveryConfig.get("key");
        String accessKeySecret = (String) discoveryConfig.get("secret");
        for (AliyunDiscovery enabledPlugin : enabledPlugins) {
            final List<String> regions = getRegionsForDiscovery(enabledPlugin);
            for (String region : regions) {
                try {
                    final AliyunClientCreator clientCreator = ClientCreators.ramClientCreator(accessKeyId, accessKeySecret, region);
                    enabledPlugin.discoverWrapper(MAPPER, session, region, emitter, clientCreator);
                } catch (Exception ex) {
                    LOG.error("Discovery error  in {} - {}", region, ex.getMessage());
                    LOG.debug("Details", ex);
                }
            }
        }
//        else {
//            Object assumedRoles = config.getStsUser().get("assumedRoles");
//            if(assumedRoles == null){
//                throw new AliyunDiscoveryException("assumedRoles is required");
//            }
//            if(!(assumedRoles instanceof List)){
//                throw new AliyunDiscoveryException("assumedRoles is not a list");
//            }
//            String accessKeyId = (String) config.getStsUser().get("AccessKeyID");
//            String accessKeySecret = (String) config.getStsUser().get("AccessKeySecret");
//            List<String> roleArns = (List<String>) assumedRoles;
//            roleArns.forEach(roleArn -> {
//                enabledPlugins.forEach(plugin -> {
//                final List<String> regions = getRegionsForDiscovery(plugin);
//                    regions.forEach(region -> {
//                        final AliyunClientCreator clientCreator = ClientCreators.assumeRoleCreator(accessKeyId, accessKeySecret, region, roleArn);
//                        try {
//                            logger.info("Discovering cross-account {}:{} using role {}", plugin.service(), region, roleArn);
//                            plugin.discoverWrapper(MAPPER, session, region, emitter, logger, clientCreator);
//                        } catch (Exception ex) {
//                            logger.error("Discovery error  in {} - {}", region, ex.getMessage());
//                            logger.debug("Details", ex);
//                        }
//                    });
//                });
//            });
//        }
    }

    protected List<String> getRegionsForDiscovery(AliyunDiscovery plugin) {
        final List<String> regions = plugin.getSupportedRegions()
                .stream()
                .filter(this::isDiscoveryEnabledIn)
                .filter(this::isAllowedRegion)
                .collect(Collectors.toList());
        if (regions.isEmpty()) {
            LOG.warn("{} is enabled but no supported regions are configured.", plugin.fullService());
        }
        return regions;
    }

    private boolean isEnabled(String service) {
        Map<String,Object> discoveryPlugin = (Map<String, Object>) this.discoveryConfig.get("discoveryPlugin");
        if (discoveryPlugin == null){
            return true;
        }
        Map<String, Object> cloudService = (Map<String, Object>) discoveryPlugin.get("cloudService");
        if (cloudService == null){
            return true;
        }
        String status = (String) cloudService.get("status");
        // 判断当前云服务插件是否开启
        return status == null || Objects.equals(status.trim(),"ENABLED");
//    boolean enabled = config.getServices().isEmpty() || config.getServices().contains(service);
//    logger.debug("{} {} per config", enabled ? "Enabling" : "Disabling", service);
    }

    private boolean isDiscoveryEnabledIn(String region) {
//    boolean enabled = config.getRegions().isEmpty() || config.getRegions().contains(region);
//    logger.debug("{} {} per config", enabled ? "Enabling" : "Disabling", region);
        return this.REGIONS.contains(region);
    }

    private boolean isAllowedRegion(String region) {
        // todo: 这应该是在 cloudService 记录中配置，某个 region 是否开启，只有明确指定 DISABLED 才是 false
//    boolean regionAllowed = config.getIgnoredRegions()
//            .stream()
//            .noneMatch(pattern -> Pattern.matches(pattern, region));
//    logger.debug("{} {} per ignore region config", regionAllowed ? "Enabling" : "Disabling", region);
        return true;
    }


}
