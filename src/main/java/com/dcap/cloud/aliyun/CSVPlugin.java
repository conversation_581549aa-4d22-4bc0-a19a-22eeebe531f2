package com.dcap.cloud.aliyun;

import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.TerminalPlugin;
import com.opencsv.CSVWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Map;

public class CSVPlugin implements TerminalPlugin<String[]> {

    private final Object SYNC = new Object();

    private static final Logger LOG = LoggerFactory.getLogger(CSVPlugin.class);

    private CSVWriter reportWriter;

    @Override
    public void accept(CloudEnvelope<String[]> env) {
        try {
            synchronized (SYNC) {
                this.reportWriter.writeNext(env.getContents(), false);
                this.reportWriter.flush();
            }
        } catch (Exception ex) {
            LOG.warn("Couldn't process envelope contents", ex);
        }
    }

    @Override
    public String id() {
        return PluginConstant.CLOUD_CSV_OUT_ID;
    }

    @Override
    public void init(Map<String,Object> config) {
        try {
            String id = String.valueOf(config.get("taskId"));
            String tenantIdText = String.valueOf(config.get("tenantId"));
            String taskType = String.valueOf(config.get("taskType"));

            if (tenantIdText == null || tenantIdText.equals("null")){
                throw new IllegalArgumentException("tenant id is null");
            }
            long tenantId = Long.parseLong(tenantIdText);
            String reportFilePath = "/var/log/dcap/" + taskType.toLowerCase()+"/";
            File logDir = new File(reportFilePath);
            if (!logDir.exists()) {
                logDir.mkdirs();
            }
            String reportFileName = "tenant_"+ tenantId + "_task_" + id + "_"+taskType.toLowerCase()+"_assets.csv";
            reportWriter = new CSVWriter(new OutputStreamWriter(Files.newOutputStream(Paths.get(reportFilePath + reportFileName)), StandardCharsets.UTF_8));
        } catch (IOException ex) {
            throw new RuntimeException("JSON generator error", ex);
        }
    }

    @Override
    public void shutdown() {
        synchronized (SYNC) {
            try {
                if (this.reportWriter != null) {
                    this.reportWriter.flush();
                    this.reportWriter.close();
                }
            } catch (IOException ignored) {
            }
        }
    }

}
