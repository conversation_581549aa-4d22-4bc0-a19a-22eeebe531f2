package com.dcap.cloud.core.plugins;

import com.dcap.cloud.core.api.CloudPlugin;
import com.dcap.cloud.core.config.ConfigException;
import com.dcap.cloud.core.config.PluginConfig;
import com.dcap.cloud.core.config.ServiceConfig;
import com.dcap.utils.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Constructor;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

public class PluginManager {

    private static final ObjectMapper MAPPER = new ObjectMapper();

    private static final Logger LOGGER = LoggerFactory.getLogger(PluginManager.class);

    private final Map<String,Object> discoveryConfig;

    private final ServiceConfig config;

    private final Map<Class<? extends CloudPlugin>, List<CloudPlugin>> plugins = new HashMap<>();

    public PluginManager(Map<String,Object> discoveryConfig, ServiceConfig config) {
        this.discoveryConfig = discoveryConfig;
        this.config = config;
    }

    public void loadPlugins(List<Class<? extends CloudPlugin>> pluginClasses) {
        final AtomicLong pluginsFound = new AtomicLong();
        for (Class<? extends CloudPlugin> pluginClass : pluginClasses) {
            final ServiceLoader<? extends CloudPlugin> loader = ServiceLoader.load(pluginClass);
            for(CloudPlugin plugin:loader){
                pluginsFound.addAndGet(1);
                try {
                    final Class<?> configType = plugin.configType();
                    final PluginConfig pluginConfigParent = config.getPlugins().get(plugin.id());
                    if (pluginConfigParent == null) {
                        LOGGER.debug("No configuration found for {}, ignoring.", plugin.id());
                    } else if (!pluginConfigParent.isEnabled()) {
                        LOGGER.debug("{} found but is disabled via config. Ignoring}", plugin.id());
                    } else {
                        final Map<String,Object> pluginConfig = buildPluginConfig(plugin.id(), configType, pluginConfigParent.getConfig());
                        plugin.init(pluginConfig);
                        List<CloudPlugin> pluginList = plugins.getOrDefault(pluginClass, new ArrayList<>());
                        pluginList.add(plugin);
                        plugins.put(pluginClass, pluginList);
                        LOGGER.debug("Loaded {}", plugin.id());
                    }
                } catch (Exception ex) {
                    throw new PluginLoaderException(ex);
                }
            }
        }

        if (pluginsFound.get() == 0) {
            throw new ConfigException("No plugins found" );
        }
    }

    private Map<String,Object> buildPluginConfig(String pluginId, Class<?> configType, Object config) throws JsonProcessingException {
        if (configType == null || "Void".equals(configType.getSimpleName())) {
            return null;
        }

        if (config == null) {
            try {
                LOGGER.debug("No config section found for {}:{}, attempting to instantiate a default", pluginId, configType.getName());
                // The plugin configuration had no defined constructor, attempt to instantiate a no-args one.
                Optional<Constructor<?>> constructor = Arrays.stream(configType.getDeclaredConstructors())
                        .filter(c -> c.getParameterCount() == 0).findFirst();
                if (!constructor.isPresent()) {
                    LOGGER.warn("No plugin configuration found for {} and no suitable constructor found to create a default.", pluginId);
                    return null;
                }
                Map<String, Object> params = JSON.from(constructor.get().newInstance()).toObject(Map.class);
                params.putAll(discoveryConfig);
                discoveryConfig.clear();
                discoveryConfig.putAll(params);
                return discoveryConfig;
            } catch (Exception ex) {
                throw new ConfigException(String.format("Cannot instantiate config for %s with type %s", pluginId, configType.getName()), ex);
            }
        }
        Map<String,Object> yamlConfig = MAPPER.treeToValue(MAPPER.valueToTree(config), Map.class);
        if(yamlConfig != null && !yamlConfig.isEmpty()){
            if(discoveryConfig != null && !discoveryConfig.isEmpty() ){
                yamlConfig.putAll(discoveryConfig);
                discoveryConfig.clear();
                discoveryConfig.putAll(yamlConfig);
            }
        }
        return discoveryConfig;
    }

    public Optional<CloudPlugin> byId(String id) {
        List<CloudPlugin> matches = plugins.values().stream()
                .flatMap(Collection::stream)
                .filter(plugin -> plugin.id().equals(id))
                .collect(Collectors.toList());
        assert (matches.size() <= 1);
        return matches.isEmpty() ? Optional.empty() : Optional.of(matches.get(0));
    }
}
