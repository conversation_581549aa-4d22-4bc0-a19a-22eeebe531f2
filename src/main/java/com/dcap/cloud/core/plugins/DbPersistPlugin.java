package com.dcap.cloud.core.plugins;

import com.dcap.cloud.aliyun.PluginConstant;
import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.TerminalPlugin;
import com.yd.dcap.classifier.config.SpringContextUtil;
import io.ebean.Database;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class DbPersistPlugin implements TerminalPlugin<Object> {

    private static final Logger LOG = LoggerFactory.getLogger(DbPersistPlugin.class);

    private Database database;

    @Override
    public String id() {
        return PluginConstant.CLOUD_DB_OUT_ID;
    }

    @Override
    public void init(Map<String,Object> pluginConfig) {
        database = SpringContextUtil.getBean(Database.class);
    }

    @Override
    public void accept(CloudEnvelope<Object> envelope) {
        try {
            database.save(envelope.getContents());
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }
}
