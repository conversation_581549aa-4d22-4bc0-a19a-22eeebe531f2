package com.dcap.cloud.core.plugins;

import com.dcap.cloud.aliyun.PluginConstant;
import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.Emitter;
import com.dcap.cloud.core.api.IntermediatePlugin;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class IdentityPlugin implements IntermediatePlugin<Map<String, Object>> {

    private static final Logger LOG = LoggerFactory.getLogger(IdentityPlugin.class);


    @Override
    public void accept(CloudEnvelope<Map<String, Object>> ngEnvelope, Emitter emitter) {
        LOG.debug("Consumed {}", ngEnvelope.getContents());
        emitter.emit(CloudEnvelope.of(ngEnvelope, PluginConstant.CLOUD_IDENTITY_ID, ngEnvelope.getContents()));
    }

    @Override
    public String id() {
        return PluginConstant.CLOUD_IDENTITY_ID;
    }


    @Override
    public void init(Map<String,Object> unused) {

    }
}
