package com.dcap.cloud.core;

import com.dcap.cloud.core.api.*;
import com.dcap.cloud.core.config.ServiceConfig;
import com.dcap.cloud.core.fifos.FifoManager;
import com.dcap.cloud.core.layers.Layer;
import com.dcap.cloud.core.layers.LayerManager;
import com.dcap.cloud.core.layers.LayerType;
import com.dcap.cloud.core.plugins.PluginManager;
import com.google.common.collect.ImmutableList;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.probe.client.ProbeClientTaskUtil;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import io.netty.util.concurrent.DefaultEventExecutorGroup;
import io.netty.util.concurrent.EventExecutorGroup;
import io.netty.util.concurrent.Future;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

public class Orchestrator {

    private static final List<Class<? extends CloudPlugin>> DISCOVERY_PLUGIN_CLASSES =
            ImmutableList.of(OriginPlugin.class, IntermediatePlugin.class, TerminalPlugin.class);

    public static class LayerCallable implements Callable<LayerType> {

        private final Layer layer;
        private volatile boolean repeat;
        private final ExecutorService executors;

        public LayerCallable(ExecutorService executors, Layer layer, Boolean repeat) {
            this.executors = executors;
            this.layer = layer;
            this.repeat = repeat;
        }

        @Override
        public LayerType call() {
            do {
                try {
                    if (!layer.exec(repeat)) {
                        break;
                    } else {
                        Thread.sleep(100L);
                    }
                } catch (InterruptedException ex) {
                    LOGGER.warn("Layer exec wait interrupted for {}", layer.getName(), ex);
                } catch (Exception ex) {
                    LOGGER.warn("Layer exception", ex);
                } catch (Error err) {
                    LOGGER.error("Severe error in layer execution", err);
                    repeat = false;
                    break;
                }
            } while (repeat);
            this.shutdown();
            layer.shutdown();
            return layer.getType();
        }

        public void shutdown() {
            repeat = false;
        }

        public LayerType getLayerType() {
            return layer.getType();
        }
    }

    private static final Logger LOGGER = LoggerFactory.getLogger(Orchestrator.class);
    private final ServiceConfig config;
    private final Map<String, Object> discoveryConfig;
    private final Session session;
    private final long taskId;

    private final StatusRecord.Position completedPosition;

    public Orchestrator(long taskId, Map<String, Object> discoveryConfig, ServiceConfig config, Session session) {
        this.taskId = taskId;
        this.discoveryConfig = discoveryConfig;
        this.config = config;
        this.session = session;
        this.completedPosition = (StatusRecord.Position) discoveryConfig.get("completedPosition");
    }

    public Pair<List<LayerCallable>, ExecutorService> scan() {
        final FifoManager fifoManager = new FifoManager(config);
        final PluginManager pluginManager = new PluginManager(discoveryConfig, config);
        pluginManager.loadPlugins(DISCOVERY_PLUGIN_CLASSES);

        final LayerManager layerManager = new LayerManager(session, config, fifoManager, pluginManager);
        final Map<String, Layer> layers = layerManager.getLayers();
        final EventExecutorGroup executors = new DefaultEventExecutorGroup(layers.size());

        final Set<Layer> originLayers = layers.values().stream().filter(l -> l.getType() == LayerType.ORIGIN).collect(Collectors.toSet());
        final Set<Layer> otherLayers = layers.values().stream().filter(l -> !originLayers.contains(l)).collect(Collectors.toSet());

        List<LayerCallable> callables = new ArrayList<LayerCallable>();

        for (Layer originLayer : originLayers) {
            LayerCallable layerCallable = new LayerCallable(executors, originLayer, false);
            callables.add(layerCallable);
            LOGGER.trace("Submitting callable {}", layerCallable.layer.getName());
            executors.submit(layerCallable);
        }

        for (Layer otherLayer : otherLayers) {
            LayerCallable layerCallable = new LayerCallable(executors, otherLayer, true);
            callables.add(layerCallable);
            LOGGER.trace("Submitting callable {}", layerCallable.layer.getName());
            Future<LayerType> layerTypeFuture = executors.submit(layerCallable);
            layerTypeFuture.addListener((layerType) -> {
                if (layerType.get() == LayerType.TERMINAL) {
                    LOGGER.info("TerminalLayer layer execute end, The task[{}] has been executed, remove running task [{}]", this.taskId, this.taskId);
                    ProbeClientTaskContext probeClientTaskContext = ProbeClientTaskUtil.getInstance()
                            .getProbeClientTaskContext(session.getTaskType(), session.getTaskId());
                    probeClientTaskContext.reportSuccess(completedPosition, null, "任务已经执行完成")
                            .sendToServer();
                    probeClientTaskContext.clean();
                }
            });
        }
        return Pair.of(callables, executors);
    }
}
