package com.dcap.cloud.core;

import com.dcap.cloud.Discovery;
import com.dcap.cloud.core.api.Session;
import com.dcap.cloud.core.config.ConfigUtils;
import com.dcap.cloud.core.config.ServiceConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.probe.client.AbstractProbeClientProcessor;
import com.yd.dcap.probe.entities.ProbeClientJobResult;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import org.apache.commons.lang3.tuple.Pair;

import java.io.File;
import java.io.InputStream;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.function.Consumer;

public abstract class OrchestratorTrigger extends AbstractProbeClientProcessor {

    private static final ObjectMapper MAPPER = new ObjectMapper(new YAMLFactory());

    private static final String DEFAULT_CONFIG_FILE = "config.yaml";

    private Pair<List<Orchestrator.LayerCallable>, ExecutorService> pair;

    public OrchestratorTrigger(ProbeClientTaskContext probeClientTaskContext) {
        super(probeClientTaskContext);
    }


    @Override
    protected Map<String, Object> doWork(long execId, long taskId, String taskType, long tenantId,
                                         Map<String, Object> taskConf, Consumer<ProbeClientJobResult> callback) {
        final Instant start = Instant.now();
        if (taskConf == null) {
            throw new IllegalArgumentException("taskConf is null");
        }
        taskConf.put("execId", execId);
        StatusRecord.Position position = (StatusRecord.Position) taskConf.get("startedPosition");


        // example：orchestrator/vfs/vfs_file_scan/describe_sql_log_records
        String configFilePath = "orchestrator" + File.separator + "vfs" +
                File.separator + taskType.toLowerCase() + File.separator + DEFAULT_CONFIG_FILE;
        try (InputStream is = Thread.currentThread().getContextClassLoader().getResourceAsStream(configFilePath.toLowerCase())) {
            if (is == null){
                probeClientTaskContext.reportFailed(position, null, "配置文件查找失败["+configFilePath+"]").sendToServer();
                probeClientTaskContext.clean();
                return null;
            }
            final ServiceConfig serviceConfig = ConfigUtils.merge(MAPPER.readValue(is, ServiceConfig.class), System.getenv());
            String logMsg = "Start Cloud Discovery. execId="+execId+", taskId="+taskId+", taskType="+taskType;
            logger.info(logMsg);
            probeClientTaskContext.reportExecuting(position,null, logMsg).sendToServer();
            Session session = new Session();
            session.setTaskId(taskId);
            session.setJobHistoryId(execId);
            session.setTaskType(taskType);
            session.setTenantId(tenantId);
            pair = new Orchestrator(taskId, taskConf, serviceConfig, session).scan();
        } catch (Exception e){
            String errMsg = "Discovery task has error["+e.getMessage()+"], end in "+ Discovery.humanReadableFormat(Duration.between(start, Instant.now()));
            logger.error(errMsg,e);
            // 这是开始阶段，有异常时，应该上报并结束任务
            probeClientTaskContext.reportFailed(position,null,errMsg).sendToServer();
            probeClientTaskContext.clean();
        }
        // 这是异步任务，所以这里返回 null
        return null;
    }

    @Override
    protected void onInterrupt(long execId, long taskId, String taskType, long tenantId, Map<String, Object> taskParam) {
        if (pair == null) {
            logger.warn("The task may not have successfully started , so it is considered to be interrupted.");
            return;
        }
        List<Orchestrator.LayerCallable> callables = pair.getLeft();
        if (callables == null || callables.isEmpty()) {
            return;
        }
        String logMsg = "Forced shutting down task type ["+taskType+"], task id ["+taskId+"], exec id ["+execId+"] ";
        logger.info(logMsg);
        StatusRecord.Position position = (StatusRecord.Position) taskParam.get("interruptedPosition");
        probeClientTaskContext.reportInterrupted(position, logMsg).sendToServer();
        // todo: 只需要关闭第一层
        callables.get(0).shutdown();
    }
}
