package com.dcap.cloud.core.fifos;

import com.dcap.cloud.core.api.CloudEnvelope;

import java.util.Optional;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

public class LocalQueue implements FifoQueue, FifoDequeue{

  private final Queue<CloudEnvelope> queue = new ConcurrentLinkedQueue<>();

  @Override
  public Optional<CloudEnvelope> poll() throws FifoException {
    return Optional.ofNullable(queue.poll());
  }

  @Override
  public void add(CloudEnvelope env) throws FifoException {
    if (!queue.add(env)) {
      throw new FifoException("Couldn't enqueue " + env.toString());
    }
  }
}
