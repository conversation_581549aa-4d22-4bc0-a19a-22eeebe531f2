package com.dcap.cloud.core.fifos;

import com.dcap.cloud.core.config.ConfigException;
import com.dcap.cloud.core.config.FifoConfig;
import com.dcap.cloud.core.config.LayerConfig;
import com.dcap.cloud.core.config.ServiceConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class FifoManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(FifoManager.class);

    private final Map<String, FifoQueue> queues = new HashMap<>();
    private final Map<String, FifoDequeue> dequeues = new HashMap<>();
    private final ServiceConfig config;

    public FifoManager(ServiceConfig config) {
        this.config = config;
        List<String> buildQueues = config.getLayers().values().stream()
                .map(LayerConfig::getQueue)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<String> buildDequeues = config.getLayers().values().stream()
                .map(LayerConfig::getDequeue)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        buildQueues(buildQueues);
        buildDequeues(buildDequeues);
    }

    private void buildQueues(List<String> buildQueues) {
        buildQueues.forEach(name -> {
            if (queues.containsKey(name)) {
                throw new ConfigException("Duplicate queue name: " + name);
            }

            FifoConfig fifoConfig = config.getFifos().get(name);
            if (fifoConfig == null) {
                throw new ConfigException("No fifo definition found for " + name);
            }
            final QueueType queueType = QueueType.valueOf(fifoConfig.getType().toUpperCase());
            switch (queueType) {
                case LOCAL:
                    LocalQueue q = new LocalQueue();
                    // A LocalQueue implements both Queue and Dequeue, so it must be placed in both
                    // collections.
                    queues.put(name, q);
                    dequeues.put(name, q);
                    break;
                case KAFKA:
//          var qk = new KafkaQueue(fifoConfig.getProperties());
//          // A LocalQueue implements both Queue and Dequeue, so it must be placed in both
//          // collections.
//          queues.put(name, qk);
                    break;
                default:
                    throw new ConfigException("Invalid queue type: " + queueType);
            }
            LOGGER.debug("Created {}:{}", name, queueType);
        });
    }

    private void buildDequeues(List<String> buildDequeues) {
        buildDequeues.stream()
                .filter(name -> !dequeues.containsKey(name))
                .forEach(name -> {

                    FifoConfig fifoConfig = config.getFifos().get(name);
                    if (fifoConfig == null) {
                        throw new ConfigException("No fifo definition found for " + name);
                    }

                    final QueueType queueType = QueueType.valueOf(fifoConfig.getType().toUpperCase());
                    switch (queueType) {
                        case LOCAL:
                            // Local queues are handled by the buildQueues method.
                            break;
                        case KAFKA:
//            var dk = new KafkaDequeue(fifoConfig.getProperties());
//            dequeues.put(name, dk);
//            break;
                        default:
                            throw new ConfigException("Invalid queue type: " + queueType);
                    }
                });
    }


    public FifoQueue getQueue(String name) {
        return queues.get(name);
    }

    public FifoDequeue getDequeue(String name) {
        return dequeues.get(name);
    }
}
