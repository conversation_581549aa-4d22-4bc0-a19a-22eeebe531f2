package com.dcap.cloud.core.api;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public class CloudEnvelope<T> {

    private final Session session;

    // 表示了执行的路径，就是说当前 cloudEnvelope 经过了多少个 插件处理器，每个插件处理的 id，都在里面记录。按照顺序记录。
    private final List<String> pluginPath;

    private final MetaData metadata;

    private final T contents;

    private final String contentClass;

    public static <T> CloudEnvelope<T> of(CloudEnvelope<?> current, String pluginId, T contents) {
        List<String> newPath = null;
        if(current.pluginPath != null){
            newPath = new ArrayList<>(current.pluginPath);
            newPath.add(pluginId);
        }
        return new CloudEnvelope<T>(current.getSession(), newPath, current.metadata, contents, contents.getClass().getName());
    }

    public CloudEnvelope(Session session, List<String> pluginPath, MetaData metadata, T contents) {
        this(session, pluginPath, metadata, contents, contents.getClass().getName());
    }

    private CloudEnvelope(Session session, List<String> pluginPath, MetaData metadata, T contents, String contentClass) {
        this.session = session;
        this.pluginPath = pluginPath;
        this.metadata = metadata;
        this.contents = contents;
        this.contentClass = contentClass;
    }

    @Getter
    public static class MetaData {

        private final String region;
        private final String cloudServiceId;
        private final String cloudServiceName;
        private final String cloudProviderId;
        // 资源发现起始路径
        private final String resourceDiscoveryStartingPath;

        public MetaData(String region, String cloudServiceId, String cloudServiceName, String cloudProviderId, String resourceDiscoveryStartingPath) {
            this.region = region;
            this.cloudServiceId = cloudServiceId;
            this.cloudServiceName = cloudServiceName;
            this.cloudProviderId = cloudProviderId;
            this.resourceDiscoveryStartingPath = resourceDiscoveryStartingPath;
        }


    }
}
