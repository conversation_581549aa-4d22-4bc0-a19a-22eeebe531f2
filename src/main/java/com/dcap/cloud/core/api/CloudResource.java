package com.dcap.cloud.core.api;

import com.dcap.cloud.core.api.utils.EncodedNamedUUIDGenerator;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import java.time.Instant;

public class CloudResource {

    private final ObjectMapper mapper;
    public String assetId;
    public String resourceName;
    public String resourceId;
    public String resourceType;
    public String region;
    public String projectId;
    public String accountId;
    public Instant createdIso;
    public Instant updatedIso = Instant.now();
    public String discoverySessionId;
    public Long maxSizeInBytes = null;
    public Long sizeInBytes = null;
    public JsonNode configuration;
    public JsonNode supplementaryConfiguration;
    public JsonNode tags;
    public JsonNode discoveryMeta;

    private CloudResource(CloudResource.CloudResourceBuilder builder) {
        this.assetId = builder.assetId;
        this.resourceName = builder.resourceName;
        this.resourceId = builder.resourceId;
        this.resourceType = builder.resourceType;
        this.region = builder.region;
        this.projectId = builder.projectId;
        this.accountId = builder.accountId;
        this.createdIso = builder.createdIso;
        this.updatedIso = builder.updatedIso;
        this.discoverySessionId = builder.discoverySessionId;
        this.maxSizeInBytes = builder.maxSizeInBytes;
        this.sizeInBytes = builder.sizeInBytes;
        this.configuration = builder.configuration;
        this.supplementaryConfiguration = builder.supplementaryConfiguration;
        this.tags = builder.tags;
        this.discoveryMeta = builder.discoveryMeta;
        this.mapper = builder.mapper;
    }

    public ObjectNode toJsonNode() {
        ObjectNode data = this.mapper.createObjectNode();
        data.put("documentId", EncodedNamedUUIDGenerator.getEncodedNamedUUID(this.assetId.concat(this.resourceType).concat(this.accountId)));
        data.put("assetId", this.assetId);
        data.put("resourceName", this.resourceName);
        data.put("resourceId", this.resourceId);
        data.put("resourceType", this.resourceType);
        data.put("region", this.region);
        data.put("accountId", this.accountId);
        data.put("projectId", this.projectId);
        data.put("createdIso", this.createdIso == null ? null : this.createdIso.toString());
        data.put("updatedIso", this.updatedIso == null ? null : this.updatedIso.toString());
        data.put("discoverySessionId", this.discoverySessionId);
        data.put("maxSizeInBytes", this.maxSizeInBytes);
        data.put("sizeInBytes", this.sizeInBytes);
        data.set("configuration", this.configuration);
        data.set("supplementaryConfiguration", this.supplementaryConfiguration);
        data.set("tags", this.tags);
        data.set("discoveryMeta", this.discoveryMeta);
        return data;
    }

    public String getAssetId() {
        return this.assetId;
    }

    public void setAssetId(String assetId) {
        this.assetId = assetId;
    }

    public String getResourceName() {
        return this.resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getResourceId() {
        return this.resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public String getResourceType() {
        return this.resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public String getRegion() {
        return this.region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getProjectId() {
        return this.projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getAccountId() {
        return this.accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public Instant getCreatedIso() {
        return this.createdIso;
    }

    public void setCreatedIso(Instant createdIso) {
        this.createdIso = createdIso;
    }

    public Instant getUpdatedIso() {
        return this.updatedIso;
    }

    public void setUpdatedIso(Instant updatedIso) {
        this.updatedIso = updatedIso;
    }

    public String getDiscoverySessionId() {
        return this.discoverySessionId;
    }

    public void setDiscoverySessionId(String discoverySessionId) {
        this.discoverySessionId = discoverySessionId;
    }

    public Long getMaxSizeInBytes() {
        return this.maxSizeInBytes;
    }

    public void setMaxSizeInBytes(Long maxSizeInBytes) {
        this.maxSizeInBytes = maxSizeInBytes;
    }

    public Long getSizeInBytes() {
        return this.sizeInBytes;
    }

    public void setSizeInBytes(Long sizeInBytes) {
        this.sizeInBytes = sizeInBytes;
    }

    public JsonNode getConfiguration() {
        return this.configuration;
    }

    public void setConfiguration(JsonNode configuration) {
        this.configuration = configuration;
    }

    public JsonNode getSupplementaryConfiguration() {
        return this.supplementaryConfiguration;
    }

    public void setSupplementaryConfiguration(JsonNode supplementaryConfiguration) {
        this.supplementaryConfiguration = supplementaryConfiguration;
    }

    public JsonNode getTags() {
        return this.tags;
    }

    public void setTags(JsonNode tags) {
        this.tags = tags;
    }

    public JsonNode getDiscoveryMeta() {
        return this.discoveryMeta;
    }

    public void setDiscoveryMeta(JsonNode discoveryMeta) {
        this.discoveryMeta = discoveryMeta;
    }

    public static class CloudResourceBuilder {
        private final ObjectMapper mapper;
        private String assetId;
        private String resourceName;
        private String resourceId;
        private String resourceType;
        private String region;
        private String projectId;
        private String accountId;
        private Instant createdIso;
        private Instant updatedIso = Instant.now();
        private String discoverySessionId;
        private Long maxSizeInBytes = null;
        private Long sizeInBytes = null;
        private JsonNode configuration;
        private JsonNode supplementaryConfiguration;
        private JsonNode tags;
        private JsonNode discoveryMeta;

        public CloudResourceBuilder(ObjectMapper mapper, String assetId) {
            this.assetId = assetId;
            this.mapper = mapper;
            this.configuration = mapper.createObjectNode();
            this.supplementaryConfiguration = mapper.createObjectNode();
            this.tags = mapper.createObjectNode();
            this.discoveryMeta = mapper.createObjectNode();
        }

        public CloudResource.CloudResourceBuilder withResourceName(String resourceName) {
            this.resourceName = resourceName;
            return this;
        }

        public CloudResource.CloudResourceBuilder withResourceId(String resourceId) {
            this.resourceId = resourceId;
            return this;
        }

        public CloudResource.CloudResourceBuilder withResourceType(String resourceType) {
            this.resourceType = resourceType;
            return this;
        }

        public CloudResource.CloudResourceBuilder withRegion(String region) {
            this.region = region;
            return this;
        }

        public CloudResource.CloudResourceBuilder withProjectId(String projectId) {
            this.projectId = projectId;
            return this;
        }

        public CloudResource.CloudResourceBuilder withAccountId(String accountId) {
            this.accountId = accountId;
            return this;
        }

        public CloudResource.CloudResourceBuilder withCreatedIso(Instant createdIso) {
            this.createdIso = createdIso;
            return this;
        }

        public CloudResource.CloudResourceBuilder withUpdatedIso(Instant updatedIso) {
            this.updatedIso = updatedIso;
            return this;
        }

        public CloudResource.CloudResourceBuilder withDiscoverySessionId(String discoverySessionId) {
            this.discoverySessionId = discoverySessionId;
            return this;
        }

        public CloudResource.CloudResourceBuilder withMaxSizeInBytes(Long maxSizeInBytes) {
            this.maxSizeInBytes = maxSizeInBytes;
            return this;
        }

        public CloudResource.CloudResourceBuilder withSizeInBytes(Long sizeInBytes) {
            this.sizeInBytes = sizeInBytes;
            return this;
        }

        public CloudResource.CloudResourceBuilder withConfiguration(JsonNode configuration) {
            this.configuration = configuration;
            return this;
        }

        public CloudResource.CloudResourceBuilder withSupplementaryConfiguration(JsonNode supplementaryConfiguration) {
            this.supplementaryConfiguration = supplementaryConfiguration;
            return this;
        }

        public CloudResource.CloudResourceBuilder withTags(JsonNode tags) {
            this.tags = tags;
            return this;
        }

        public CloudResource.CloudResourceBuilder withDiscoveryMeta(JsonNode discoveryMeta) {
            this.discoveryMeta = discoveryMeta;
            return this;
        }

        public CloudResource build() {
            return new CloudResource(this);
        }
    }
}
