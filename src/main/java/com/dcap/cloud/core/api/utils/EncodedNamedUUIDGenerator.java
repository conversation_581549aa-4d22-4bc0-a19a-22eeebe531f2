package com.dcap.cloud.core.api.utils;

import java.nio.ByteBuffer;
import java.util.Base64;
import java.util.UUID;

public class EncodedNamedUUIDGenerator {
    public EncodedNamedUUIDGenerator() {
    }

    public static String getEncodedNamedUUID(String target) {
        if (target.isEmpty()) {
            throw new IllegalStateException("Target for Encoded Named UUID is empty.");
        } else {
            byte[] targetBytes = target.toLowerCase().getBytes();
            UUID uuid = UUID.nameUUIDFromBytes(targetBytes);
            ByteBuffer uuidBytes = ByteBuffer.wrap(new byte[16]);
            uuidBytes.putLong(uuid.getMostSignificantBits());
            uuidBytes.putLong(uuid.getLeastSignificantBits());
            return Base64.getUrlEncoder().withoutPadding().encodeToString(uuidBytes.array());
        }
    }
}
