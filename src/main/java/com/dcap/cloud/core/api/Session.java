package com.dcap.cloud.core.api;

import java.time.Instant;
import java.util.UUID;

public class Session {

    private String id = UUID.randomUUID().toString();

    private Instant createdAt = Instant.now();

    private long taskId;

    private long tenantId;

    // 对应 jobHistoryId
    private long jobHistoryId;

    // 任务类型，不同的发现任务有不同的类型
    private String taskType;


    public Session() {
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public long getTaskId() {
        return taskId;
    }

    public void setTaskId(long taskId) {
        this.taskId = taskId;
    }

    public long getTenantId() {
        return tenantId;
    }

    public void setTenantId(long tenantId) {
        this.tenantId = tenantId;
    }

    public long getJobHistoryId() {
        return jobHistoryId;
    }

    public void setJobHistoryId(long jobHistoryId) {
        this.jobHistoryId = jobHistoryId;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }


    @Override
    public String toString() {
        return "Session{" +
                "id='" + id + '\'' +
                ", createdAt=" + createdAt +
                ", taskId=" + taskId +
                ", tenantId=" + tenantId +
                ", jobHistoryId=" + jobHistoryId +
                ", taskType='" + taskType + '\'' +
                '}';
    }
}
