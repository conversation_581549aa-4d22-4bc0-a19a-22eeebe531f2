package com.dcap.cloud.core.layers;


import com.dcap.cloud.core.api.*;
import com.dcap.cloud.core.config.ConfigException;
import com.dcap.cloud.core.config.LayerConfig;
import com.dcap.cloud.core.config.ServiceConfig;
import com.dcap.cloud.core.fifos.FifoDequeue;
import com.dcap.cloud.core.fifos.FifoManager;
import com.dcap.cloud.core.fifos.FifoQueue;
import com.dcap.cloud.core.plugins.PluginManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

public class LayerManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(LayerManager.class);

    private final ServiceConfig config;
//    private final FifoManager fifoManager;
    private final Map<String, Layer> layers = new LinkedHashMap<>();  // Preserve insertion order


    public LayerManager(Session session, ServiceConfig config, FifoManager fifoManager, PluginManager pluginManager) {
        this.config = config;
//        this.fifoManager = fifoManager;
        buildLayers(session, fifoManager, pluginManager);
    }

    private void buildLayers(Session session, FifoManager fifoManager, PluginManager pluginManager) {
        config.getLayers().forEach((name, layerConfig) -> {
            List<CloudPlugin> plugins = layerConfig.getPlugins().stream()
                    .map(pluginManager::byId)
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .collect(Collectors.toList());

            final LayerType layerType = LayerType.valueOf(layerConfig.getType().toUpperCase());
            switch (layerType) {
                case ORIGIN:
                    layers.put(name, new OriginLayer(name, session,
                            plugins.stream().map(p -> (OriginPlugin) p).collect(Collectors.toList()),
                            getOrThrowQueue(name, fifoManager, layerConfig)));
                    break;
                case INTERMEDIATE:
                    layers.put(name, new IntermediateLayer(name,
                            getOrThrowDequeue(name, fifoManager, layerConfig),
                            plugins.stream().map(p -> (IntermediatePlugin) p).collect(Collectors.toList()),
                            getOrThrowQueue(name, fifoManager, layerConfig)));
                    break;
                case TERMINAL:
                    layers.put(name, new TerminalLayer(name,
                            getOrThrowDequeue(name, fifoManager, layerConfig),
                            plugins.stream().map(p -> (TerminalPlugin) p).collect(Collectors.toList())));
                    break;
                default:
                    throw new ConfigException(String.format("Illegal type for layer %s: %s", name, layerConfig.getType()));
            }
            LOGGER.debug("Built layer {}", name);
        });
    }

    private FifoQueue getOrThrowQueue(String layerName, FifoManager fifoManager, LayerConfig layerConfig) {
        final String queueName = layerConfig.getQueue();
        if (Objects.isNull(queueName)) {
            throw new ConfigException("No fifo queue defined for " + layerName);
        }
        FifoQueue queue = fifoManager.getQueue(queueName);
        if (Objects.isNull(queue)) {
            throw new ConfigException("Couldn't find queue " + queueName);
        }

        return queue;
    }

    private FifoDequeue getOrThrowDequeue(String layerName, FifoManager fifoManager, LayerConfig layerConfig) {
        final String dequeueName = layerConfig.getDequeue();
        if (Objects.isNull(dequeueName)) {
            throw new ConfigException("No fifo dequeue defined for " + layerName);
        }

        FifoDequeue dequeue = fifoManager.getDequeue(dequeueName);
        if (Objects.isNull(dequeue)) {
            throw new ConfigException("Couldn't find dequeue " + dequeueName);
        }
        return dequeue;
    }

    public Map<String, Layer> getLayers() {
        return Collections.unmodifiableMap(layers);
    }
}
