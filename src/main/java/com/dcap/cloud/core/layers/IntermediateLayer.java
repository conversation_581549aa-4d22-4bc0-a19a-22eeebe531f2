package com.dcap.cloud.core.layers;

import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.CloudPlugin;
import com.dcap.cloud.core.api.IntermediatePlugin;
import com.dcap.cloud.core.fifos.FifoDequeue;
import com.dcap.cloud.core.fifos.FifoException;
import com.dcap.cloud.core.fifos.FifoQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

public class IntermediateLayer implements Layer {

  private final static Logger LOGGER = LoggerFactory.getLogger(IntermediateLayer.class);

  private final FifoDequeue dequeue;
  private final Collection<IntermediatePlugin> plugins;
  private final FifoQueue queue;
  private final String name;

  public IntermediateLayer(String name, FifoDequeue dequeue, Collection<IntermediatePlugin> plugins, FifoQueue queue) {
    this.dequeue = dequeue;
    this.plugins = plugins;
    this.queue = queue;
    this.name = name;
  }

  public boolean exec(boolean repeat) throws FifoException {
    final Optional<CloudEnvelope> opt = dequeue.poll();
    if (!opt.isPresent()) {
      return repeat;
    }
    final CloudEnvelope env = opt.get();
    if(env.getContents() != null && Objects.equals(env.getContents().toString(),"_THE_END")){
      this.emit(env);
      LOGGER.info("Finish for layer [{}]",this.name);
      return false;
    }
    final List<String> pluginPath = env.getPluginPath();
    final String lastPlugin = pluginPath == null || pluginPath.isEmpty() ? null : pluginPath.get(pluginPath.size()-1);
    if(plugins == null || plugins.isEmpty()){
      LOGGER.warn("plugins of intermediate layer is empty");
      return repeat;
    }
    for (IntermediatePlugin plugin : plugins) {
      try {
        plugin.accept(env, this::emit);
      } catch (Exception ex) {
        LOGGER.warn("Plugin exception: {}", plugin.id(), ex);
      }
    }
    return repeat;
  }

  @Override
  public String getName() {
    return name;
  }

  private void emit(CloudEnvelope env) {
    try {
      queue.add(env);
    } catch (FifoException e) {
      LOGGER.warn("Emitter exception", e);
    }
  }

  @Override
  public LayerType getType() {
    return LayerType.INTERMEDIATE;
  }

  @Override
  public void shutdown() {
    plugins.forEach(CloudPlugin::shutdown);
  }
}
