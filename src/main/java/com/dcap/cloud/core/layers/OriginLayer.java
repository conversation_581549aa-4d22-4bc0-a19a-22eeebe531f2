package com.dcap.cloud.core.layers;

import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.CloudPlugin;
import com.dcap.cloud.core.api.OriginPlugin;
import com.dcap.cloud.core.api.Session;
import com.dcap.cloud.core.fifos.FifoException;
import com.dcap.cloud.core.fifos.FifoQueue;
import com.google.common.collect.ImmutableList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;


public class OriginLayer implements Layer {

  private final static Logger LOGGER = LoggerFactory.getLogger(OriginLayer.class);

  private final Session session;
  private final Collection<OriginPlugin> plugins;
  private final FifoQueue queue;
  private final String name;

  public OriginLayer(String name, Session session, Collection<OriginPlugin> plugins, FifoQueue queue) {
    this.session = session;
    this.plugins = plugins;
    this.queue = queue;
    this.name = name;
  }

  @Override
  public boolean exec(boolean repeat) throws FifoException {
    for (OriginPlugin plugin : plugins) {
      try {
        LOGGER.info("Running discover for plugin {}", plugin.id());
        plugin.discover(session, this::emit);
      } catch (Exception ex) {
        LOGGER.warn("Plugin exception: {}", plugin.id(), ex);
      } finally {
        LOGGER.info("Finish discover for plugin {}", plugin.id());
      }
    }
    return repeat;
  }

  @Override
  public String getName() {
    return name;
  }

  private void emit(CloudEnvelope env) {
    try {
      queue.add(env);
    } catch (FifoException e) {
      LOGGER.warn("Emitter exception", e);
    }
  }

  @Override
  public LayerType getType() {
    return LayerType.ORIGIN;
  }

  @Override
  public void shutdown() {
    LOGGER.info("shutdown origin layer, name [{}]", this.name);
    plugins.forEach(CloudPlugin::shutdown);
    this.emit(new CloudEnvelope<>(session, ImmutableList.of("ORIGIN_DO_END"),null, "_THE_END"));
  }
}
