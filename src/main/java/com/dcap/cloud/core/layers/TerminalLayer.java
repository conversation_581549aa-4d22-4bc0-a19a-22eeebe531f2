package com.dcap.cloud.core.layers;

import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.CloudPlugin;
import com.dcap.cloud.core.api.TerminalPlugin;
import com.dcap.cloud.core.fifos.FifoDequeue;
import com.dcap.cloud.core.fifos.FifoException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

public class TerminalLayer implements Layer {

  private final static Logger LOGGER = LoggerFactory.getLogger(TerminalLayer.class);

  private final FifoDequeue dequeue;
  private final Collection<TerminalPlugin> plugins;
  private final String name;

  public TerminalLayer(String name, FifoDequeue dequeue, Collection<TerminalPlugin> plugins) {
    this.dequeue = dequeue;
    this.plugins = plugins;
    this.name = name;
  }

  @Override
  public boolean exec(boolean repeat) throws FifoException {
    final Optional<CloudEnvelope> opt = dequeue.poll();
    if (!opt.isPresent()) {
      return repeat;
    }
    final CloudEnvelope env = opt.get();
    if(env.getContents() != null && env.getContents().toString().intern() == "_THE_END"){
      LOGGER.info("Finish for layer [{}]", this.name);
      return false;
    }

    final List<String> pluginPath = env.getPluginPath();
    final String lastPlugin = pluginPath == null || pluginPath.isEmpty() ? null : pluginPath.get(pluginPath.size()-1);
    if(plugins == null || plugins.isEmpty()){
      LOGGER.warn("plugins of terminal layer is empty");
      return repeat;
    }
    for (TerminalPlugin plugin : plugins) {
      try {
        plugin.accept(env);
      } catch (Exception ex) {
        LOGGER.warn("Plugin exception: {}", plugin.id(), ex);
      }
    }
    return repeat;
  }

  @Override
  public String getName() {
    return name;
  }

  @Override
  public LayerType getType() {
    return LayerType.TERMINAL;
  }

  @Override
  public void shutdown() {
    plugins.forEach(CloudPlugin::shutdown);
  }
}
