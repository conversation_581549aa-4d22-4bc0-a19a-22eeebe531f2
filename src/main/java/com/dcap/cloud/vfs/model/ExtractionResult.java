package com.dcap.cloud.vfs.model;

import java.util.List;

/**
 * 文件内容提取结果统一数据结构
 * 支持文本内容和结构化表格数据的提取结果
 */
public class ExtractionResult {

    /**
     * 提取类型枚举
     */
    public enum ExtractionType {
        TEXT,           // 纯文本提取
        STRUCTURED,     // 结构化数据提取  
        MIXED           // 混合类型（既有文本又有结构化数据）
    }

    private ExtractionType type;
    private String textContent;
    private List<TableData> tableDataList;
    private String contentLanguage;

    public ExtractionResult() {
    }

    public ExtractionResult(ExtractionType type) {
        this.type = type;
    }

    // 静态工厂方法 - 创建文本提取结果
    public static ExtractionResult createTextResult(String textContent, String contentLanguage) {
        ExtractionResult result = new ExtractionResult(ExtractionType.TEXT);
        result.textContent = textContent;
        result.contentLanguage = contentLanguage;
        return result;
    }

    // 静态工厂方法 - 创建结构化数据提取结果
    public static ExtractionResult createStructuredResult(List<TableData> tableDataList) {
        ExtractionResult result = new ExtractionResult(ExtractionType.STRUCTURED);
        result.tableDataList = tableDataList;
        return result;
    }

    // 静态工厂方法 - 创建混合结果
    public static ExtractionResult createMixedResult(String textContent, String contentLanguage, List<TableData> tableDataList) {
        ExtractionResult result = new ExtractionResult(ExtractionType.MIXED);
        result.textContent = textContent;
        result.contentLanguage = contentLanguage;
        result.tableDataList = tableDataList;
        return result;
    }

    // 判断是否包含文本内容
    public boolean hasTextContent() {
        return textContent != null && !textContent.trim().isEmpty();
    }

    // 判断是否包含结构化数据
    public boolean hasTableData() {
        return tableDataList != null && !tableDataList.isEmpty();
    }

    // Getters and Setters
    public ExtractionType getType() {
        return type;
    }

    public void setType(ExtractionType type) {
        this.type = type;
    }

    public String getTextContent() {
        return textContent;
    }

    public void setTextContent(String textContent) {
        this.textContent = textContent;
    }

    public List<TableData> getTableDataList() {
        return tableDataList;
    }

    public void setTableDataList(List<TableData> tableDataList) {
        this.tableDataList = tableDataList;
    }

    public String getContentLanguage() {
        return contentLanguage;
    }

    public void setContentLanguage(String contentLanguage) {
        this.contentLanguage = contentLanguage;
    }

    @Override
    public String toString() {
        return "ExtractionResult{" +
                "type=" + type +
                ", hasTextContent=" + hasTextContent() +
                ", hasTableData=" + hasTableData() +
                ", contentLanguage='" + contentLanguage + '\'' +
                '}';
    }
}
