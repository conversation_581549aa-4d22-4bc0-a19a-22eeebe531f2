package com.dcap.cloud.vfs.model;

import java.util.List;

/**
 * 表格数据结构
 * 用于存储从Excel等结构化文件中提取的表格信息
 */
public class TableData {

    private String sheetName;           // 工作表名称
    private int sheetIndex;             // 工作表索引
    private List<String> columnNames;   // 列名列表
    private List<List<String>> rows;    // 行数据，每行是一个字符串列表
    private int totalRows;              // 原始总行数（采样前）
    private int sampledRows;            // 采样行数
    private boolean isSampled;          // 是否进行了采样

    public TableData() {
    }

    public TableData(String sheetName, int sheetIndex) {
        this.sheetName = sheetName;
        this.sheetIndex = sheetIndex;
    }

    // 获取列数
    public int getColumnCount() {
        return columnNames != null ? columnNames.size() : 0;
    }

    // 获取实际行数
    public int getRowCount() {
        return rows != null ? rows.size() : 0;
    }

    // 获取指定列的所有数据值
    public List<String> getColumnValues(int columnIndex) {
        if (rows == null || columnIndex < 0 || columnIndex >= getColumnCount()) {
            return null;
        }

        return rows.stream()
                .map(row -> columnIndex < row.size() ? row.get(columnIndex) : "")
                .collect(java.util.stream.Collectors.toList());
    }

    // 获取指定列的所有数据值（按列名）
    public List<String> getColumnValues(String columnName) {
        if (columnNames == null || columnNames.isEmpty()) {
            return null;
        }

        int columnIndex = columnNames.indexOf(columnName);
        if (columnIndex == -1) {
            return null;
        }

        return getColumnValues(columnIndex);
    }

    // Getters and Setters
    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public int getSheetIndex() {
        return sheetIndex;
    }

    public void setSheetIndex(int sheetIndex) {
        this.sheetIndex = sheetIndex;
    }

    public List<String> getColumnNames() {
        return columnNames;
    }

    public void setColumnNames(List<String> columnNames) {
        this.columnNames = columnNames;
    }

    public List<List<String>> getRows() {
        return rows;
    }

    public void setRows(List<List<String>> rows) {
        this.rows = rows;
    }

    public int getTotalRows() {
        return totalRows;
    }

    public void setTotalRows(int totalRows) {
        this.totalRows = totalRows;
    }

    public int getSampledRows() {
        return sampledRows;
    }

    public void setSampledRows(int sampledRows) {
        this.sampledRows = sampledRows;
    }

    public boolean isSampled() {
        return isSampled;
    }

    public void setSampled(boolean sampled) {
        isSampled = sampled;
    }

    @Override
    public String toString() {
        return "TableData{" +
                "sheetName='" + sheetName + '\'' +
                ", sheetIndex=" + sheetIndex +
                ", columnCount=" + getColumnCount() +
                ", rowCount=" + getRowCount() +
                ", totalRows=" + totalRows +
                ", isSampled=" + isSampled +
                '}';
    }
}
