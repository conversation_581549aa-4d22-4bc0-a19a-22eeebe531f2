package com.dcap.cloud.vfs.util;

import com.dcap.cloud.vfs.model.TableData;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * Excel文件解析工具类
 * 支持.xlsx和.xls格式文件的结构化数据提取
 */
public class ExcelParser {

    private static final Logger LOGGER = LoggerFactory.getLogger(ExcelParser.class);

    // 最大采样行数
    private static final int MAX_SAMPLE_ROWS = 100;

    /**
     * 解析Excel文件并提取结构化数据
     *
     * @param inputStream Excel文件输入流
     * @param fileName    文件名（用于判断文件类型）
     * @return 表格数据列表
     * @throws IOException 解析异常
     */
    public static List<TableData> parseExcel(InputStream inputStream, String fileName) throws IOException {
        List<TableData> tableDataList = new ArrayList<>();

        try (Workbook workbook = createWorkbook(inputStream, fileName)) {
            int numberOfSheets = workbook.getNumberOfSheets();
            LOGGER.debug("Excel文件包含 {} 个工作表", numberOfSheets);

            for (int sheetIndex = 0; sheetIndex < numberOfSheets; sheetIndex++) {
                Sheet sheet = workbook.getSheetAt(sheetIndex);
                String sheetName = sheet.getSheetName();

                LOGGER.debug("解析工作表: {} (索引: {})", sheetName, sheetIndex);

                TableData tableData = parseSheet(sheet, sheetName, sheetIndex);
                if (tableData != null && tableData.getRowCount() > 0) {
                    tableDataList.add(tableData);
                    LOGGER.debug("工作表 {} 解析完成，提取 {} 行数据", sheetName, tableData.getRowCount());
                } else {
                    LOGGER.debug("工作表 {} 无有效数据，跳过", sheetName);
                }
            }
        }

        LOGGER.info("Excel解析完成，共提取 {} 个有效工作表的数据", tableDataList.size());
        return tableDataList;
    }

    /**
     * 根据文件名创建对应的Workbook实例
     */
    private static Workbook createWorkbook(InputStream inputStream, String fileName) throws IOException {
        if (fileName != null && fileName.toLowerCase().endsWith(".xlsx")) {
            return new XSSFWorkbook(inputStream);
        } else {
            // 默认尝试.xls格式
            return new HSSFWorkbook(inputStream);
        }
    }

    /**
     * 解析单个工作表
     */
    private static TableData parseSheet(Sheet sheet, String sheetName, int sheetIndex) {
        if (sheet == null) {
            return null;
        }

        TableData tableData = new TableData(sheetName, sheetIndex);

        // 获取总行数（包括空行）
        int lastRowNum = sheet.getLastRowNum();
        int physicalNumberOfRows = sheet.getPhysicalNumberOfRows();

        LOGGER.debug("工作表 {} - LastRowNum: {}, PhysicalRows: {}", sheetName, lastRowNum, physicalNumberOfRows);

        if (physicalNumberOfRows == 0) {
            return null; // 空工作表
        }

        // 查找第一个非空行作为表头
        Row headerRow = findFirstNonEmptyRow(sheet);
        if (headerRow == null) {
            LOGGER.debug("工作表 {} 未找到有效的表头行", sheetName);
            return null;
        }

        // 解析列名
        List<String> columnNames = parseColumnNames(headerRow);
        if (columnNames.isEmpty()) {
            LOGGER.debug("工作表 {} 未找到有效的列名", sheetName);
            return null;
        }

        tableData.setColumnNames(columnNames);
        int columnCount = columnNames.size();

        // 收集所有数据行
        List<Row> dataRows = collectDataRows(sheet, headerRow.getRowNum() + 1);
        tableData.setTotalRows(dataRows.size());

        // 采样数据行
        List<Row> sampledRows = sampleRows(dataRows, MAX_SAMPLE_ROWS);
        boolean isSampled = sampledRows.size() < dataRows.size();

        // 解析采样后的数据行
        List<List<String>> parsedRows = new ArrayList<>();
        for (Row row : sampledRows) {
            List<String> rowData = parseDataRow(row, columnCount);
            if (!isEmptyRow(rowData)) {
                parsedRows.add(rowData);
            }
        }

        tableData.setRows(parsedRows);
        tableData.setSampledRows(parsedRows.size());
        tableData.setSampled(isSampled);

        return tableData;
    }

    /**
     * 查找第一个非空行
     */
    private static Row findFirstNonEmptyRow(Sheet sheet) {
        for (Row row : sheet) {
            if (row != null && !isEmptyRow(row)) {
                return row;
            }
        }
        return null;
    }

    /**
     * 解析列名
     * 如果第一行内容缺失，则使用 "第n列" 作为列名
     */
    private static List<String> parseColumnNames(Row headerRow) {
        List<String> columnNames = new ArrayList<>();

        if (headerRow == null) {
            return columnNames;
        }

        // 获取最后一个非空单元格的索引
        int lastCellNum = headerRow.getLastCellNum();

        for (int cellIndex = 0; cellIndex < lastCellNum; cellIndex++) {
            Cell cell = headerRow.getCell(cellIndex);
            String columnName = getCellStringValue(cell);

            // 如果列名为空，使用默认名称
            if (columnName == null || columnName.trim().isEmpty()) {
                columnName = "第" + (cellIndex + 1) + "列";
            }

            columnNames.add(columnName.trim());
        }

        return columnNames;
    }

    /**
     * 收集数据行（排除表头行）
     */
    private static List<Row> collectDataRows(Sheet sheet, int startRowIndex) {
        List<Row> dataRows = new ArrayList<>();

        for (Row row : sheet) {
            if (row != null && row.getRowNum() >= startRowIndex) {
                dataRows.add(row);
            }
        }

        return dataRows;
    }

    /**
     * 随机采样行数据
     * 如果总行数小于等于maxSampleRows，返回全部行
     * 否则随机采样maxSampleRows行
     */
    private static List<Row> sampleRows(List<Row> allRows, int maxSampleRows) {
        if (allRows.size() <= maxSampleRows) {
            return allRows;
        }

        // 随机采样
        List<Row> sampledRows = new ArrayList<>(allRows);
        Collections.shuffle(sampledRows, new Random());
        return sampledRows.subList(0, maxSampleRows);
    }

    /**
     * 解析数据行
     */
    private static List<String> parseDataRow(Row row, int expectedColumnCount) {
        List<String> rowData = new ArrayList<>();

        for (int cellIndex = 0; cellIndex < expectedColumnCount; cellIndex++) {
            Cell cell = row.getCell(cellIndex);
            String cellValue = getCellStringValue(cell);
            rowData.add(cellValue != null ? cellValue : "");
        }

        return rowData;
    }

    /**
     * 获取单元格的字符串值
     * 统一将所有数据类型转换为字符串
     */
    private static String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        try {
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue();
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        // 日期格式
                        Date dateCellValue = cell.getDateCellValue();
                        return dateCellValue != null ? dateCellValue.toString() : "";
                    } else {
                        // 数字格式
                        double numericCellValue = cell.getNumericCellValue();
                        // 如果是整数，不显示小数点
                        if (numericCellValue == Math.floor(numericCellValue)) {
                            return String.valueOf((long) numericCellValue);
                        } else {
                            return String.valueOf(numericCellValue);
                        }
                    }
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    // 尝试获取计算结果
                    try {
                        return getCellStringValue(cell); // 递归调用可能有风险，这里简化处理
                    } catch (Exception e) {
                        return cell.getCellFormula();
                    }
                case BLANK:
                    return "";
                case ERROR:
                    return "#ERROR#";
                default:
                    return "";
            }
        } catch (Exception e) {
            LOGGER.debug("读取单元格值异常: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 判断行是否为空
     */
    private static boolean isEmptyRow(Row row) {
        if (row == null) {
            return true;
        }

        for (Cell cell : row) {
            String cellValue = getCellStringValue(cell);
            if (cellValue != null && !cellValue.trim().isEmpty()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断行数据是否为空
     */
    private static boolean isEmptyRow(List<String> rowData) {
        if (rowData == null || rowData.isEmpty()) {
            return true;
        }

        return rowData.stream().allMatch(cell -> cell == null || cell.trim().isEmpty());
    }
}
