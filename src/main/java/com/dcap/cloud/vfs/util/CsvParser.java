package com.dcap.cloud.vfs.util;

import com.dcap.cloud.vfs.model.TableData;
import com.opencsv.CSVReader;
import com.opencsv.CSVReaderBuilder;
import com.opencsv.exceptions.CsvException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringReader;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * CSV文件解析工具类
 * 支持多种编码和分隔符的CSV文件结构化数据提取
 */
public class CsvParser {

    private static final Logger LOGGER = LoggerFactory.getLogger(CsvParser.class);

    // 最大采样行数，与Excel保持一致
    private static final int MAX_SAMPLE_ROWS = 100;

    // 支持的字符编码列表
    private static final List<Charset> SUPPORTED_CHARSETS = Arrays.asList(
            StandardCharsets.UTF_8,
            StandardCharsets.UTF_16,
            Charset.forName("GBK"),
            Charset.forName("GB2312"),
            StandardCharsets.ISO_8859_1
    );

    // 支持的分隔符列表
    private static final char[] SUPPORTED_SEPARATORS = {',', ';', '\t', '|'};

    /**
     * 解析CSV文件并提取结构化数据
     *
     * @param inputStream CSV文件输入流
     * @param fileName    文件名
     * @return 包含单个TableData的列表
     * @throws IOException 解析异常
     */
    public static List<TableData> parseCsv(InputStream inputStream, String fileName) throws IOException {
        LOGGER.debug("开始解析CSV文件: {}", fileName);

        try {
            // 1. 读取输入流数据到字节数组，用于编码检测和后续解析
            byte[] fileBytes = readInputStreamToBytes(inputStream);
            if (fileBytes.length == 0) {
                LOGGER.debug("CSV文件为空: {}", fileName);
                return Collections.emptyList();
            }

            // 2. 检测文件编码
            Charset detectedCharset = detectCharset(fileBytes, fileName);
            LOGGER.debug("CSV文件 {} 检测到编码: {}", fileName, detectedCharset.name());

            // 3. 转换为字符串
            String csvContent = new String(fileBytes, detectedCharset);

            // 4. 检测分隔符
            char separator = detectSeparator(csvContent, fileName);
            LOGGER.debug("CSV文件 {} 检测到分隔符: '{}'", fileName, separator);

            // 5. 解析CSV内容
            TableData tableData = parseCsvContent(csvContent, separator, fileName);
            if (tableData == null) {
                LOGGER.debug("CSV文件 {} 解析失败", fileName);
                return Collections.emptyList();
            }

            LOGGER.info("CSV文件 {} 解析完成，提取 {} 行数据，{} 列", 
                       fileName, tableData.getRowCount(), tableData.getColumnCount());
            return Collections.singletonList(tableData);

        } catch (Exception e) {
            LOGGER.error("解析CSV文件 {} 时出错: {}", fileName, e.getMessage(), e);
            throw new IOException("CSV解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将输入流读取为字节数组
     */
    private static byte[] readInputStreamToBytes(InputStream inputStream) throws IOException {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        byte[] data = new byte[8192];
        int bytesRead;
        
        while ((bytesRead = inputStream.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, bytesRead);
        }
        
        return buffer.toByteArray();
    }

    /**
     * 检测文件编码
     * 通过尝试不同编码解析文件前1KB内容来判断最合适的编码
     */
    private static Charset detectCharset(byte[] fileBytes, String fileName) {
        // 检测BOM标记
        Charset bomCharset = detectBom(fileBytes);
        if (bomCharset != null) {
            LOGGER.debug("CSV文件 {} 检测到BOM标记，使用编码: {}", fileName, bomCharset.name());
            return bomCharset;
        }

        // 取前1KB用于编码检测
        int detectLength = Math.min(1024, fileBytes.length);
        byte[] detectBytes = Arrays.copyOf(fileBytes, detectLength);

        // 尝试各种编码
        for (Charset charset : SUPPORTED_CHARSETS) {
            try {
                String decoded = new String(detectBytes, charset);
                // 检查是否包含乱码字符
                if (!containsGarbledChars(decoded)) {
                    LOGGER.debug("CSV文件 {} 编码检测成功: {}", fileName, charset.name());
                    return charset;
                }
            } catch (Exception e) {
                // 编码失败，尝试下一个
                LOGGER.debug("CSV文件 {} 尝试编码 {} 失败: {}", fileName, charset.name(), e.getMessage());
            }
        }

        // 默认使用UTF-8
        LOGGER.debug("CSV文件 {} 编码检测失败，使用默认UTF-8编码", fileName);
        return StandardCharsets.UTF_8;
    }

    /**
     * 检测BOM标记
     */
    private static Charset detectBom(byte[] bytes) {
        if (bytes.length >= 3) {
            // UTF-8 BOM: EF BB BF
            if (bytes[0] == (byte) 0xEF && bytes[1] == (byte) 0xBB && bytes[2] == (byte) 0xBF) {
                return StandardCharsets.UTF_8;
            }
        }
        if (bytes.length >= 2) {
            // UTF-16 BE BOM: FE FF
            if (bytes[0] == (byte) 0xFE && bytes[1] == (byte) 0xFF) {
                return StandardCharsets.UTF_16BE;
            }
            // UTF-16 LE BOM: FF FE
            if (bytes[0] == (byte) 0xFF && bytes[1] == (byte) 0xFE) {
                return StandardCharsets.UTF_16LE;
            }
        }
        return null;
    }

    /**
     * 检查字符串是否包含乱码字符
     */
    private static boolean containsGarbledChars(String text) {
        // 检查替换字符（通常表示编码错误）
        if (text.contains("\uFFFD")) {
            return true;
        }
        
        // 检查过多的控制字符
        long controlCharCount = text.chars()
                .filter(ch -> Character.isISOControl(ch) && ch != '\n' && ch != '\r' && ch != '\t')
                .count();
        
        return controlCharCount > text.length() * 0.1; // 如果控制字符超过10%，认为是乱码
    }

    /**
     * 检测CSV分隔符
     * 通过统计前几行中各种分隔符的出现频率来判断
     */
    private static char detectSeparator(String csvContent, String fileName) {
        String[] sampleLines = csvContent.split("\n", 5); // 取前5行用于分隔符检测
        
        Map<Character, Integer> separatorCounts = new HashMap<>();
        
        for (char separator : SUPPORTED_SEPARATORS) {
            int totalCount = 0;
            int validLineCount = 0;
            
            for (String line : sampleLines) {
                if (StringUtils.isNotBlank(line)) {
                    int count = countOccurrences(line, separator);
                    if (count > 0) {
                        totalCount += count;
                        validLineCount++;
                    }
                }
            }
            
            // 如果多行都包含此分隔符，且数量相对稳定，则认为是有效分隔符
            if (validLineCount >= 2) {
                separatorCounts.put(separator, totalCount);
            }
        }
        
        if (separatorCounts.isEmpty()) {
            LOGGER.debug("CSV文件 {} 未检测到明确的分隔符，使用默认逗号", fileName);
            return ',';
        }
        
        // 选择出现频率最高的分隔符
        char bestSeparator = separatorCounts.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(',');
        
        LOGGER.debug("CSV文件 {} 分隔符检测完成，选择: '{}' (出现次数: {})", 
                    fileName, bestSeparator, separatorCounts.get(bestSeparator));
        return bestSeparator;
    }

    /**
     * 统计字符在字符串中的出现次数（忽略引号内的字符）
     */
    private static int countOccurrences(String text, char target) {
        int count = 0;
        boolean inQuotes = false;
        
        for (int i = 0; i < text.length(); i++) {
            char ch = text.charAt(i);
            if (ch == '"') {
                inQuotes = !inQuotes;
            } else if (!inQuotes && ch == target) {
                count++;
            }
        }
        
        return count;
    }

    /**
     * 解析CSV内容为TableData
     */
    private static TableData parseCsvContent(String csvContent, char separator, String fileName) {
        List<String[]> allRows = new ArrayList<>();
        
        try (StringReader stringReader = new StringReader(csvContent);
             CSVReader csvReader = new CSVReaderBuilder(stringReader)
                     .withCSVParser(new com.opencsv.CSVParserBuilder()
                             .withSeparator(separator)
                             .withIgnoreLeadingWhiteSpace(true)
                             .build())
                     .build()) {
            
            allRows = csvReader.readAll();
            
        } catch (IOException | CsvException e) {
            LOGGER.error("CSV内容解析失败: {}", e.getMessage(), e);
            return null;
        }
        
        if (allRows.isEmpty()) {
            LOGGER.debug("CSV文件 {} 解析后无数据行", fileName);
            return null;
        }
        
        // 创建TableData对象
        TableData tableData = new TableData(fileName, 0); // CSV文件只有一个"工作表"
        
        // 解析列名（第一行）
        String[] headerRow = allRows.get(0);
        List<String> columnNames = parseColumnNames(headerRow);
        tableData.setColumnNames(columnNames);
        int columnCount = columnNames.size();
        
        // 解析数据行（从第二行开始）
        List<String[]> dataRows = allRows.subList(1, allRows.size());
        tableData.setTotalRows(dataRows.size());
        
        // 采样数据行
        List<String[]> sampledRows = sampleRows(dataRows, MAX_SAMPLE_ROWS);
        boolean isSampled = sampledRows.size() < dataRows.size();
        
        // 转换为TableData格式
        List<List<String>> parsedRows = new ArrayList<>();
        for (String[] row : sampledRows) {
            List<String> rowData = parseDataRow(row, columnCount);
            if (!isEmptyRow(rowData)) {
                parsedRows.add(rowData);
            }
        }
        
        tableData.setRows(parsedRows);
        tableData.setSampledRows(parsedRows.size());
        tableData.setSampled(isSampled);
        
        LOGGER.debug("CSV文件 {} 解析完成 - 总行数: {}, 采样行数: {}, 列数: {}", 
                    fileName, dataRows.size(), parsedRows.size(), columnCount);
        
        return tableData;
    }

    /**
     * 解析列名
     * 如果列名为空，使用"第n列"作为默认名称
     */
    private static List<String> parseColumnNames(String[] headerRow) {
        List<String> columnNames = new ArrayList<>();
        
        for (int i = 0; i < headerRow.length; i++) {
            String columnName = headerRow[i];
            
            // 如果列名为空或只包含空白字符，使用默认名称
            if (StringUtils.isBlank(columnName)) {
                columnName = "第" + (i + 1) + "列";
            } else {
                columnName = columnName.trim();
            }
            
            columnNames.add(columnName);
        }
        
        return columnNames;
    }

    /**
     * 随机采样行数据
     * 如果总行数小于等于maxSampleRows，返回全部行
     * 否则随机采样maxSampleRows行
     */
    private static List<String[]> sampleRows(List<String[]> allRows, int maxSampleRows) {
        if (allRows.size() <= maxSampleRows) {
            return allRows;
        }
        
        // 随机采样
        List<String[]> sampledRows = new ArrayList<>(allRows);
        Collections.shuffle(sampledRows, new Random());
        return sampledRows.subList(0, maxSampleRows);
    }

    /**
     * 解析数据行，确保与期望的列数一致
     */
    private static List<String> parseDataRow(String[] row, int expectedColumnCount) {
        List<String> rowData = new ArrayList<>();
        
        for (int i = 0; i < expectedColumnCount; i++) {
            String cellValue = "";
            if (i < row.length && row[i] != null) {
                cellValue = row[i].trim();
            }
            rowData.add(cellValue);
        }
        
        return rowData;
    }

    /**
     * 判断行数据是否为空
     */
    private static boolean isEmptyRow(List<String> rowData) {
        if (rowData == null || rowData.isEmpty()) {
            return true;
        }
        
        return rowData.stream().allMatch(cell -> StringUtils.isBlank(cell));
    }
}
