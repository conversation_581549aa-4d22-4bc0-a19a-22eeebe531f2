package com.dcap.cloud.vfs.plugin;

import com.dcap.classifier.writer.DataWriter;
import com.dcap.classifier.writer.DataWriterFactory;
import com.dcap.classifier.writer.RecordTypeEnum;
import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.Emitter;
import com.dcap.cloud.core.api.IntermediatePlugin;
import com.dcap.cloud.core.api.Session;
import com.dcap.utils.JSON;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.dcap.probe.client.ProbeClientTaskUtil;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.dcap.cloud.vfs.report.VfsScanTaskReport.FILE_METADATA_KAFKA_SENT;

/**
 * 中间件插件，将元数据发送到 Kafka Topic，但不转发给下游。
 */
public class MetadataKafkaSenderPlugin implements IntermediatePlugin<Map<String, Object>> {

    private static final Logger LOGGER = LoggerFactory.getLogger(MetadataKafkaSenderPlugin.class);

    private final String id = "metadata-kafka-sender";

    private DataWriter writer;

    private DataWriter completedWriter;

    private ProbeClientTaskContext probeClientTaskContext;

    private Map<String, Object> pluginConfig;

    @Override
    public String id() {
        return id;
    }

    /**
     * 初始化插件，从配置中加载参数并创建 Kafka Producer。
     * @param pluginConfig 插件配置 (从YAML加载)
     */
    @Override
    public void init(Map<String, Object> pluginConfig) {
        LOGGER.info("初始化元数据 Kafka 发送插件 [{}]...", id);
        this.pluginConfig = pluginConfig;
        long jobId = (long) pluginConfig.get("execId");
        writer = DataWriterFactory.createDataWriter(jobId, RecordTypeEnum.VFS_ASSETS_METADATA);
        completedWriter = DataWriterFactory.createDataWriter(jobId, RecordTypeEnum.VFS_METADATA_COMPLETED);
        String taskType = Objects.toString(pluginConfig.get("taskType"), "");
        long taskId = Long.parseLong(Objects.toString(pluginConfig.get("taskId"), ""));
        probeClientTaskContext = ProbeClientTaskUtil.getInstance().getProbeClientTaskContext(taskType, taskId);
        LOGGER.info("元数据 Kafka 发送插件 [{}] 初始化完成。", id);
    }

    @Override
    public void accept(CloudEnvelope<Map<String, Object>> envelope, Emitter emitter) {
        Session session = envelope.getSession();
        long execId = session.getJobHistoryId();
        long taskId = session.getTaskId();

        if (envelope.getContents() == null) {
            LOGGER.warn("插件 [{}] 收到空的 envelope, execId: {}, taskId: {}", id, execId, taskId);
            return; // 不处理也不转发
        }

        Map<String, Object> fileObjectInfo = envelope.getContents();
        Map<String, Object> metadata = (Map<String, Object>) fileObjectInfo.get("fileObjectMetadata");
        String fileUri = String.valueOf(metadata.get("vfsUri"));

        try {
            writer.writeContent(JSON.from(metadata).toString());
            probeClientTaskContext.recordField(FILE_METADATA_KAFKA_SENT, 1);
        } catch (Exception e) {
            String errorMsg = String.format("处理或发送元数据失败, execId: %d, taskId: %d, file: %s: %s", 
                execId, taskId, fileUri, e.getMessage());
            LOGGER.error("插件 [{}] {}", id, errorMsg, e);
            reportFileProcessingError(probeClientTaskContext, execId, taskId, fileUri, errorMsg);
        }
        
        // **关键：不调用 emitter.accept(envelope);**
        // 这个插件的任务只是发送到 Kafka，不将数据传递给 Orchestrator 的下一层。
    }

    /**
     * 报告文件处理错误。
     */
    private void reportFileProcessingError(ProbeClientTaskContext context, long execId, long taskId, String vfsUri, String errorMessage) {
        if (context != null) {
            try {
                context.reportFailed(StatusRecord.Position.VfsMetadataPhaseComplete, vfsUri, errorMessage).sendToServer();
            } catch (Exception e) {
                LOGGER.error("插件 [{}] 报告文件处理错误失败, execId={}, taskId={}, file={}: {}",
                             id, execId, taskId, vfsUri, e.getMessage(), e);
            }
        }
    }

    @Override
    public void shutdown() {
        LOGGER.info("关闭元数据 Kafka 发送插件: {}", id);
        try {
            // 只有任务状态不是失败的时候，才发送完成的消息到 kafka，否则会导致管理控制台调用清理数据接口。
            if (probeClientTaskContext.getTaskStatus() != 3){
                probeClientTaskContext.reportExecuting(StatusRecord.Position.VfsMetadataPhaseComplete, "", "元数据已经发送完成")
                        .sendToServer();
                completedWriter.writeContent(JSON.from(UtilMisc.toMap(
                        "jobId", probeClientTaskContext.getExecId(),
                        "execStatus", probeClientTaskContext.getExecStatus(),
                        "execId", probeClientTaskContext.getExecId(),
                        "taskId", probeClientTaskContext.getTaskId(),
                        "config", this.pluginConfig
                )).toString());
            }
        } catch (Exception e) {
            LOGGER.error("元数据 Kafka 发送插件 [{}] 关闭失败: {}", id, e.getMessage(), e);
        }
    }

    /**
     * 指定配置类型为 Map。
     * @return Map class
     */
    @Override
    public Class<? extends Map> configType() {
        return HashMap.class;
    }
}
