package com.dcap.cloud.vfs.plugin;

import com.dcap.cloud.core.api.Emitter;
import com.dcap.cloud.core.api.OriginPlugin;
import com.dcap.cloud.core.api.Session;
import com.dcap.cloud.vfs.service.*;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.probe.client.ProbeClientTaskUtil;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import org.apache.commons.vfs2.FileSystemOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Map;

/**
 * VFS 源扫描插件，负责发现文件并提取元数据。
 */
public class VfsOriginPlugin implements OriginPlugin {

    private static final Logger LOGGER = LoggerFactory.getLogger(VfsOriginPlugin.class);
    private static final Map<String, Class<? extends FileServiceDiscovery>> VFS_DISCOVERY_MAP = new HashMap<>();

    // 静态注册服务发现实现
    static {
        // 注册各种 VFS 服务发现实现
        VFS_DISCOVERY_MAP.put("s3", S3FileServiceDiscovery.class);
        VFS_DISCOVERY_MAP.put("minio", S3FileServiceDiscovery.class);
        VFS_DISCOVERY_MAP.put("oss", S3FileServiceDiscovery.class);
        VFS_DISCOVERY_MAP.put("ftp", FtpFileServiceDiscovery.class);
        VFS_DISCOVERY_MAP.put("ftps", FtpFileServiceDiscovery.class);
        VFS_DISCOVERY_MAP.put("sftp", SftpFileServiceDiscovery.class);
        VFS_DISCOVERY_MAP.put("cifs", CifsFileServiceDiscovery.class);
        VFS_DISCOVERY_MAP.put("smb", CifsFileServiceDiscovery.class); // 别名支持
    }

    public static final String ID = "file-service-vfs-discovery"; // 插件 ID
    private String fileServiceType; // 从配置中读取的服务 ID (e.g., "s3")
    private FileServiceDiscovery discoveryService; // 实例化的服务发现实现
    private FileSystemOptions fileSystemOptions; // VFS 文件系统选项

    @Override
    public String id() {
        return ID;
    }

    /**
     * 初始化插件，从配置中加载参数。
     * @param taskParams 插件配置 也是运行时的任务参数。会将 yaml 合并，运行时参数会覆盖 yaml 的同名项
     */
    @Override
    public void init(Map<String, Object> taskParams) {
        LOGGER.info("初始化 VFS 源扫描插件 [{}]...", ID);
        try {
            // 1. 获取并验证 fileServiceType 和 rootUri
            Object fileServiceType = taskParams.get("fileServiceType");

            if (!(fileServiceType instanceof String) || ((String) fileServiceType).isEmpty()) {
                throw new IllegalArgumentException("配置缺少有效的 fileServiceType (String)");
            }
            this.fileServiceType = ((String) fileServiceType).toLowerCase(); // 统一转小写

            // 2. 查找服务发现实现类
            Class<? extends FileServiceDiscovery> serviceClass = VFS_DISCOVERY_MAP.get(this.fileServiceType);
            if (serviceClass == null) {
                throw new IllegalArgumentException("不支持的服务类型: " + this.fileServiceType + ". 可用服务: " + VFS_DISCOVERY_MAP.keySet());
            }

            // 3. 实例化服务发现实现 (假设构造函数接受 Map<String, Object>)
            try {
                Constructor<? extends FileServiceDiscovery> constructor = serviceClass.getDeclaredConstructor(Map.class);
                this.discoveryService = constructor.newInstance(taskParams);
                LOGGER.info("成功实例化服务发现实现: {}", serviceClass.getName());
            } catch (NoSuchMethodException | InstantiationException | IllegalAccessException | InvocationTargetException e) {
                LOGGER.error("无法实例化服务发现实现类: {}", serviceClass.getName(), e);
                throw new RuntimeException("实例化服务发现实现失败: " + serviceClass.getName(), e);
            }

            // 4. 创建基础 FileSystemOptions (具体配置由服务实现处理)
            this.fileSystemOptions = new FileSystemOptions();

            LOGGER.info("VFS 源扫描插件 [{}] 初始化完成。文件服务类型: {}", ID, this.fileServiceType);
        } catch (Exception e) {
            LOGGER.error("VFS 源扫描插件 [{}] 初始化失败", ID, e);
            // 清理可能已部分初始化的状态
            this.discoveryService = null;
            this.fileSystemOptions = null;
            this.fileServiceType = null;
            throw new RuntimeException("插件初始化失败: " + e.getMessage(), e);
        }
    }


    @Override
    public void discover(Session session, Emitter emitter) {
        long execId = session.getJobHistoryId();
        long taskId = session.getTaskId();
        LOGGER.info("插件 [{}] 开始 VFS 发现任务 (服务: {}), Task ID: {}, Exec ID: {}", ID, fileServiceType, taskId, execId);

        // 获取上下文用于报告（如果服务实现内部不报告）
        ProbeClientTaskContext probeClientTaskContext = ProbeClientTaskUtil.getInstance()
                .getProbeClientTaskContext(session.getTaskType(), session.getTaskId());
        probeClientTaskContext.reportExecuting(StatusRecord.Position.VfsScanStart, null, "VFS 发现任务开始").sendToServer();
        if (discoveryService == null || fileSystemOptions == null) {
            String errorMsg = "插件未正确初始化，无法执行发现任务。";
            LOGGER.error("插件 [{}] {}", ID, errorMsg);
            // 尝试报告失败
            if (probeClientTaskContext != null) {
                probeClientTaskContext.reportFailed(StatusRecord.Position.VfsScanStart, null, errorMsg);
            } else {
                LOGGER.warn("无法获取 ProbeClientTaskContext 来报告初始化失败。");
            }
            return;
        }

        try {
            // 调用具体服务实现的 discover 方法
            discoveryService.discover(session, emitter, fileSystemOptions);
            LOGGER.info("插件 [{}] 委托给服务 [{}] 的发现任务已调用完成。", ID, fileServiceType);
        } catch (Exception e) {
            // 捕获调用服务实现时可能发生的顶层异常
            String errorMsg = String.format("调用服务 [%s] 的发现方法时发生意外错误: %s", fileServiceType, e.getMessage());
            LOGGER.error("插件 [{}] {}", ID, errorMsg, e);
            // 尝试报告失败
            if (probeClientTaskContext != null) {
                probeClientTaskContext.reportFailed(StatusRecord.Position.VfsScanStart, null, errorMsg); // 选择合适的 Position
            } else {
                LOGGER.warn("无法获取 ProbeClientTaskContext 来报告服务调用失败。");
            }
        } finally {
            // 插件级别的结束日志
            LOGGER.info("插件 [{}] VFS 发现任务调用结束, Task ID: {}, Exec ID: {}", ID, taskId, execId);
            // 注意：最终状态报告应由 FileServiceDiscovery 实现负责
        }
    }

    /**
     * 指定配置类型为 Map。
     * @return Map class
     */
    @Override
    public Class<? extends Map> configType() {
        return HashMap.class;
    }
}
