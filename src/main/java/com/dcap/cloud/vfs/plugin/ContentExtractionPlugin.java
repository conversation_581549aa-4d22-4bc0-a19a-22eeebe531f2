package com.dcap.cloud.vfs.plugin;

import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.Emitter;
import com.dcap.cloud.core.api.IntermediatePlugin;
import com.dcap.cloud.core.api.Session;
import com.dcap.cloud.vfs.model.ExtractionResult;
import com.dcap.cloud.vfs.model.TableData;
import com.dcap.cloud.vfs.report.VfsScanTaskReport;
import com.dcap.cloud.vfs.util.CsvParser;
import com.dcap.cloud.vfs.util.ExcelParser;
import com.dcap.utils.UtilTika;
import com.yd.dcap.probe.client.ProbeClientTaskUtil;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.vfs2.*;
import org.apache.tika.metadata.Metadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.yd.dcap.classifier.taskreport.StatusRecord.Position.VfsContentExtraction;


/**
 * 中间件插件，负责读取 VFS 文件内容并提取文本或结构化数据。
 * 支持Excel文件的结构化数据提取和其他文件的文本内容提取。
 */
public class ContentExtractionPlugin implements IntermediatePlugin<Map<String, Object>> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ContentExtractionPlugin.class);

    private final String id = "vfs-content-extractor"; // 插件 ID

    private FileSystemManager fsManager;

    // 可选：从配置加载 Tika 文本长度限制等
    private int tikaTextLimit = 65535; // 默认限制

    private ProbeClientTaskContext probeClientTaskContext;

    @Override
    public String id() {
        return id;
    }

    @Override
    public void init(Map<String, Object> pluginConfig) {
        LOGGER.info("初始化内容提取插件 [{}]...", id);
        try {
            // 获取共享的 VFS 管理器
            this.fsManager = VFS.getManager();
            // 可选：从配置加载 Tika 限制
            Object limitObj = pluginConfig.get("textLimit");
            if (limitObj instanceof Number) {
                this.tikaTextLimit = ((Number) limitObj).intValue();
            } else if (limitObj instanceof String) {
                try {
                    this.tikaTextLimit = Integer.parseInt((String) limitObj);
                } catch (NumberFormatException e) {
                    LOGGER.warn("无效的 tikaTextLimit 配置值: {}", limitObj);
                }
            }
            LOGGER.info("内容提取插件 [{}] 初始化完成。Tika 文本限制: {}", id, this.tikaTextLimit);
        } catch (FileSystemException e) {
            LOGGER.error("VFS 文件系统管理器初始化失败", e);
            throw new RuntimeException("VFS 初始化失败", e);
        } catch (Exception e) {
            LOGGER.error("内容提取插件 [{}] 初始化失败", id, e);
            throw new RuntimeException("插件初始化失败", e);
        }
    }

    @Override
    public void accept(CloudEnvelope<Map<String, Object>> envelope, Emitter emitter) {
        Session session = envelope.getSession();
        probeClientTaskContext = ProbeClientTaskUtil.getInstance()
                .getProbeClientTaskContext(session.getTaskType(), session.getTaskId());
        long execId = session.getJobHistoryId();
        long taskId = session.getTaskId();

        // 1. 检查插件和输入 Envelope 的有效性
        if (fsManager == null) {
            LOGGER.error("插件 [{}] 未正确初始化，无法处理消息。", id);
            reportFileProcessingError(probeClientTaskContext, execId, taskId, "N/A", "plugin_initialization", "插件未初始化");
            return;
        }

        // 2. 从 Envelope Contents 中提取必要信息
        Map<String, Object> contents = envelope.getContents();
        FileSystemOptions fsOptions = (FileSystemOptions) contents.get("vfsOptions");
        Map<String, Object> fileObjectMetadata = (Map<String, Object>) contents.get("fileObjectMetadata");
        Map<String, Object> extractedResult = new HashMap<>();
        extractedResult.put("vfsOptions", fsOptions);
        extractedResult.put("fileObjectMetadata", fileObjectMetadata);
        String vfsUri = Objects.toString(fileObjectMetadata.get("vfsUri"), null);
        if (vfsUri == null) {
            String errorMsg = String.format("无法从 envelope contents 中提取 vfsUri, execId: %d, taskId: %d", execId, taskId);
            LOGGER.error("插件 [{}] {}", id, errorMsg);
            reportFileProcessingError(probeClientTaskContext, execId, taskId, "未知 URI", "uri_extraction", errorMsg);
            return;
        }

        // 3. 处理文件
        ExtractionResult extractionResult;
        Map<String, Object> processedMetadata = new HashMap<>(); // 用于存储处理后的元数据
        String errorMessage = "文件处理失败";
        try (FileObject fileObject = fsManager.resolveFile(vfsUri, fsOptions)) {
            if (!fileObject.exists()) {
                errorMessage = String.format("VFS 文件不存在: %s", vfsUri);
                LOGGER.warn("插件 [{}] {}", id, errorMessage);
                reportFileProcessingError(probeClientTaskContext, execId, taskId, vfsUri, "content_extraction", errorMessage);
                return;
            }
            if (!fileObject.isFile()) {
                errorMessage = String.format("VFS URI 不是文件: %s", vfsUri);
                LOGGER.warn("插件 [{}] {}", id, errorMessage);
                return;
            }

            FileContent fileContent = fileObject.getContent();
            long fileSize = fileContent.getSize();

            // 5. 提取和处理元数据
            Metadata tikaMetadata = getVfsMetadata(fileObject, fileContent, processedMetadata);
            fileObjectMetadata.putAll(processedMetadata);

            // 6. 判断是否需要提取
            if (fileSize == 0) {
                LOGGER.info("插件 [{}] 文件大小为 0，跳过内容提取: {}", id, vfsUri);
                return;
            }

            if (!needExtracted(fileSize, fileObject, tikaMetadata)) {
                LOGGER.info("插件 [{}] 根据规则跳过内容提取: {}", id, vfsUri);
                return;
            }

            // 7. 根据文件类型选择提取策略
            String fileName = fileObject.getName().getBaseName();
            FileType fileType = detectFileType(fileName, tikaMetadata);

            LOGGER.debug("插件 [{}] 检测到文件类型: {}, 文件: {}", id, fileType, vfsUri);

            // 7.1 复制文件到测试目录（用于测试和调试）
            String ftpCharset = System.getenv().get("ftp_charset");
            if (StringUtils.isBlank(ftpCharset)) {
                ftpCharset = System.getenv("FTP_CHARSET");
            }
            if (StringUtils.isNotBlank(ftpCharset)){
                copyFileForTesting(fileObject, fileName, execId, taskId);
            }


            try (InputStream inputStream = fileContent.getInputStream()) {
                extractionResult = extractContent(inputStream, fileName, fileType, tikaMetadata);
            }

            if (extractionResult == null) {
                LOGGER.info("插件 [{}] 提取结果为空，跳过处理: {}", id, vfsUri);
                return;
            }

            // 8. 构建结果数据
            extractedResult.put("extractionResult", extractionResult);

            emitter.emit(CloudEnvelope.of(envelope, id, extractedResult));
            LOGGER.debug("插件 [{}] 发射处理后的 envelope, VFS URI: {}", id, vfsUri);
            probeClientTaskContext.recordField(VfsScanTaskReport.FILE_CONTENT_EXTRACTED_COUNT, 1);

        } catch (Exception e) {
            errorMessage = String.format("提取内容时出错 VFS 文件 %s: %s", vfsUri, e.getMessage());
            LOGGER.error("插件 [{}] {}", id, errorMessage, e);
            reportFileProcessingError(probeClientTaskContext, execId, taskId, vfsUri, "content_extraction", errorMessage);
        }
    }

    /**
     * 文件类型枚举
     */
    private enum FileType {
        EXCEL,      // Excel文件 (.xlsx, .xls)
        CSV,        // CSV文件
        OTHER       // 其他文件类型
    }

    /**
     * 检测文件类型
     */
    private FileType detectFileType(String fileName, Metadata metadata) {
        if (fileName == null) {
            return FileType.OTHER;
        }

        String lowerFileName = fileName.toLowerCase();

        // 检测Excel文件
        if (lowerFileName.endsWith(".xlsx") || lowerFileName.endsWith(".xls")) {
            return FileType.EXCEL;
        }

        // 检测CSV文件
        if (lowerFileName.endsWith(".csv")) {
            return FileType.CSV;
        }

        // 通过MIME类型进一步判断
        if (metadata != null) {
            String contentType = metadata.get(Metadata.CONTENT_TYPE);
            if (contentType != null) {
                String lowerContentType = contentType.toLowerCase();
                if (lowerContentType.contains("spreadsheet") ||
                        lowerContentType.contains("excel") ||
                        lowerContentType.contains("vnd.ms-excel") ||
                        lowerContentType.contains("vnd.openxmlformats-officedocument.spreadsheetml")) {
                    return FileType.EXCEL;
                }
                if (lowerContentType.contains("csv") || lowerContentType.contains("comma-separated")) {
                    return FileType.CSV;
                }
            }
        }

        return FileType.OTHER;
    }

    /**
     * 根据文件类型提取内容
     */
    private ExtractionResult extractContent(InputStream inputStream, String fileName, FileType fileType, Metadata tikaMetadata) {
        switch (fileType) {
            case EXCEL:
                return extractExcelContent(inputStream, fileName);
            case CSV:
                return extractCsvContent(inputStream, fileName);
            case OTHER:
            default:
                return extractTextContent(inputStream, tikaMetadata);
        }
    }

    /**
     * 提取Excel结构化内容
     */
    private ExtractionResult extractExcelContent(InputStream inputStream, String fileName) {
        try {
            LOGGER.debug("开始解析Excel文件: {}", fileName);
            List<TableData> tableDataList = ExcelParser.parseExcel(inputStream, fileName);

            if (tableDataList == null || tableDataList.isEmpty()) {
                LOGGER.debug("Excel文件 {} 未提取到有效的表格数据", fileName);
                return null;
            }

            LOGGER.info("Excel文件 {} 解析完成，提取到 {} 个工作表的数据", fileName, tableDataList.size());
            return ExtractionResult.createStructuredResult(tableDataList);

        } catch (Exception e) {
            LOGGER.error("解析Excel文件 {} 时出错: {}", fileName, e.getMessage(), e);
            // Excel解析失败时，尝试用Tika提取文本内容作为降级处理
            LOGGER.info("Excel解析失败，尝试提取文本内容作为降级处理: {}", fileName);
            return extractTextContent(inputStream, new Metadata());
        }
    }

    /**
     * 提取CSV结构化内容
     */
    private ExtractionResult extractCsvContent(InputStream inputStream, String fileName) {
        try {
            LOGGER.debug("开始解析CSV文件: {}", fileName);
            List<TableData> tableDataList = CsvParser.parseCsv(inputStream, fileName);

            if (tableDataList == null || tableDataList.isEmpty()) {
                LOGGER.debug("CSV文件 {} 未提取到有效的表格数据", fileName);
                return null;
            }

            LOGGER.info("CSV文件 {} 解析完成，提取到数据表", fileName);
            return ExtractionResult.createStructuredResult(tableDataList);

        } catch (Exception e) {
            LOGGER.error("解析CSV文件 {} 时出错: {}", fileName, e.getMessage(), e);
            // CSV解析失败时，尝试用Tika提取文本内容作为降级处理
            LOGGER.info("CSV解析失败，尝试提取文本内容作为降级处理: {}", fileName);
            return extractTextContent(inputStream, new Metadata());
        }
    }

    /**
     * 提取文本内容
     */
    private ExtractionResult extractTextContent(InputStream inputStream, Metadata tikaMetadata) {
        try {
            LOGGER.debug("开始使用Tika提取文本内容");
            Pair<Metadata, String> parseResult = UtilTika.parse(inputStream, tikaMetadata, this.tikaTextLimit);
            String extractedText = parseResult.getRight();

            // 清洗文本
            extractedText = clearText(extractedText);
            if (StringUtils.isBlank(extractedText)) {
                LOGGER.debug("清洗后文本内容为空");
                return null;
            }

            // 检测语言
            String objectLanguage = UtilTika.detectLanguage(extractedText);

            LOGGER.debug("Tika文本提取完成，内容长度: {}, 语言: {}", extractedText.length(), objectLanguage);
            return ExtractionResult.createTextResult(extractedText, objectLanguage);

        } catch (Exception e) {
            LOGGER.error("Tika文本提取失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从 VFS FileObject 和 FileContent 提取元数据。
     * 模仿 Aliyun 插件的 getObjectMetadata 方法。
     *
     * @param fileObject           VFS 文件对象
     * @param fileContent          VFS 文件内容对象
     * @param processedMetadataMap 用于存储提取出的键值对元数据
     * @return Tika Metadata 对象，用于传递给 Tika 解析器
     * @throws FileSystemException 如果获取元数据失败
     */
    private Metadata getVfsMetadata(FileObject fileObject, FileContent fileContent, Map<String, Object> processedMetadataMap) throws FileSystemException {
        Metadata tikaMetadata = new Metadata();
        FileName fileName = fileObject.getName();
        String baseName = fileName.getBaseName();
        String extension = fileName.getExtension();

        // 基础信息
        processedMetadataMap.put("vfsUri", fileName.getURI());
        processedMetadataMap.put("fileName", baseName);
        processedMetadataMap.put("fileExtension", extension);
        processedMetadataMap.put("filePath", fileName.getPath()); // 文件路径（不含协议和主机）
        processedMetadataMap.put("fileScheme", fileName.getScheme()); // 协议 (e.g., "s3")

        tikaMetadata.set("resourceName", baseName);
        // Tika 标准元数据
        if (StringUtils.isNotBlank(extension)) {
            // Tika 可以根据扩展名猜测 Content-Type，但我们也可以显式设置
            // String guessedType = TikaConfig.getDefaultConfig().getMimeRepository().getMimeType("." + extension).getName();
            // tikaMetadata.set(Metadata.CONTENT_TYPE, guessedType);
        }

        // 文件内容信息
        long fileSize = fileContent.getSize();
        long lastModifiedTime = fileContent.getLastModifiedTime();
        processedMetadataMap.put("fileSize", fileSize);
        processedMetadataMap.put("lastModifiedTime", lastModifiedTime);
        tikaMetadata.set(Metadata.CONTENT_LENGTH, String.valueOf(fileSize));
        tikaMetadata.add("lastModifiedTime", String.valueOf(lastModifiedTime));

        // 尝试获取 Content-Type 和 Encoding (如果可用)
        String contentType = fileContent.getContentInfo().getContentType();
        String contentEncoding = fileContent.getContentInfo().getContentEncoding();
        if (StringUtils.isNotBlank(contentType)) {
            processedMetadataMap.put("contentType", contentType);
            tikaMetadata.set(Metadata.CONTENT_TYPE, contentType);
        }
        if (StringUtils.isNotBlank(contentEncoding)) {
            processedMetadataMap.put("contentEncoding", contentEncoding);
            tikaMetadata.set(Metadata.CONTENT_ENCODING, contentEncoding);
        }

        // 可选：添加其他 VFS 提供的属性
        Map<String, Object> attributes = fileContent.getAttributes();
        if (attributes != null && !attributes.isEmpty()) {
            processedMetadataMap.put("vfsAttributes", new HashMap<>(attributes)); // 放入副本
            attributes.forEach((key, value) -> {
                if (value != null) {
                    // 将 VFS 属性也放入 Tika Metadata (可选)
                    tikaMetadata.add("vfs:" + key, value.toString());
                }
            });
        }

        return tikaMetadata;
    }

    /**
     * 判断是否需要提取文件内容。
     * 模仿 Aliyun 插件的 needExtracted 方法。
     *
     * @param fileObject   VFS 文件对象
     * @param tikaMetadata Tika 元数据
     * @return 如果需要提取则返回 true，否则返回 false
     */
    private boolean needExtracted(long fileSize, FileObject fileObject, Metadata tikaMetadata) {
        // TODO: 实现具体的判断逻辑，例如基于文件大小、文件类型、文件名等
        // 示例：默认提取所有内容
        return fileSize <= 1024 * 1024 * 100;
    }

    /**
     * 清洗文本数据，移除换行符和制表符。
     * 模仿 Aliyun 插件的 clearText 方法。
     *
     * @param content 原始文本
     * @return 清洗后的文本
     */
    private String clearText(String content) {
        if (content == null) {
            return null;
        }
        // 替换换行符、回车符、制表符为空格
        return content.replaceAll("[\r\n\t]+", " ").trim();
    }

    /**
     * 报告文件处理错误。
     */
    private void reportFileProcessingError(ProbeClientTaskContext context, long execId, long taskId, String vfsUri, String stage, String errorMessage) {
        if (context != null) {
            try {
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("fileUri", vfsUri != null ? vfsUri : "未知 URI");
                errorDetails.put("error", errorMessage);
                errorDetails.put("stage", stage); // 标记错误发生的阶段
                // 使用 recordField 记录错误详情
                context.recordField("fileProcessingError", errorDetails);
                // 也可以调用 reportFailed 或 reportErrorOccurredExecuting
                context.reportErrorOccurredExecuting(VfsContentExtraction, vfsUri, errorMessage, null);
            } catch (Exception e) {
                LOGGER.error("插件 [{}] 报告文件处理错误失败, execId={}, taskId={}, file={}: {}", id, execId, taskId, vfsUri, e.getMessage(), e);
            }
        } else {
            LOGGER.warn("插件 [{}] 无法获取 ProbeClientTaskContext 来报告错误, execId={}, taskId={}, file={}", id, execId, taskId, vfsUri);
        }
    }

    @Override
    public void shutdown() {
        LOGGER.info("关闭内容提取插件: {}", id);
    }

    /**
     * 复制文件到测试目录（用于测试和调试）
     *
     * @param fileObject 源文件对象
     * @param fileName   文件名
     * @param execId     执行ID
     * @param taskId     任务ID
     */
    private void copyFileForTesting(FileObject fileObject, String fileName, long execId, long taskId) {
        try {
            // 创建目标目录
            String targetDir = "/var/log/files";
            FileObject targetDirObject = fsManager.resolveFile(targetDir);
            if (!targetDirObject.exists()) {
                targetDirObject.createFolder();
                LOGGER.info("创建测试目录: {}", targetDir);
            }

            // 生成目标文件名，包含执行ID和任务ID以避免冲突
            String timestamp = String.valueOf(System.currentTimeMillis());
            String targetFileName = String.format("%d_%d_%s_%s", execId, taskId, timestamp, fileName);
            String targetPath = targetDir + "/" + targetFileName;

            // 执行文件复制
            FileObject targetFileObject = fsManager.resolveFile(targetPath);
            targetFileObject.copyFrom(fileObject, Selectors.SELECT_SELF);

            LOGGER.info("文件复制到测试目录成功: {} -> {}", fileObject.getName().getURI(), targetPath);

        } catch (Exception e) {
            // 复制失败不影响主业务逻辑，只记录警告日志
            LOGGER.warn("复制文件到测试目录失败，但不影响主业务逻辑: {} -> /var/log/files, 错误: {}",
                    fileObject.getName().getURI(), e.getMessage());
        }
    }

    /**
     * 指定配置类型为 Map。
     *
     * @return Map class
     */
    @Override
    public Class<? extends Map> configType() {
        return HashMap.class;
    }
}
