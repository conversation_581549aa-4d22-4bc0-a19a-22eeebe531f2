package com.dcap.cloud.vfs.plugin;

import com.dcap.classifier.writer.DataWriter;
import com.dcap.classifier.writer.DataWriterFactory;
import com.dcap.classifier.writer.RecordTypeEnum;
import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.Session;
import com.dcap.cloud.core.api.TerminalPlugin;
import com.dcap.utils.JSON;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.probe.client.ProbeClientTaskUtil;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 终端插件，负责将最终的扫描结果发送到 Kafka Topic。
 */
public class ResultKafkaSenderPlugin implements TerminalPlugin<Map<String, Object>> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ResultKafkaSenderPlugin.class);

    private final String id = "file-identity-result-kafka-sender";

    private DataWriter writer;

    @Override
    public String id() {
        return id;
    }

    /**
     * 初始化插件，从配置中加载参数并创建 Kafka Producer。
     * @param pluginConfig 插件配置 (从YAML加载)
     */
    @Override
    public void init(Map<String, Object> pluginConfig) {
        long jobId = (long) pluginConfig.get("execId");
        LOGGER.info("初始化结果 Kafka 发送插件 [{}]...jobId [{}]", id, jobId);
        writer = DataWriterFactory.createDataWriter(jobId, RecordTypeEnum.VFS_ASSETS_DATA);
        LOGGER.info("结果 Kafka 发送插件 [{}] 初始化完成。", id);
    }

    @Override
    public void accept(CloudEnvelope<Map<String, Object>> envelope) {
        Session session = envelope.getSession();
        ProbeClientTaskContext probeClientTaskContext = ProbeClientTaskUtil.getInstance()
                .getProbeClientTaskContext(session.getTaskType(), session.getTaskId());
        long execId = session.getJobHistoryId();
        long taskId = session.getTaskId();

        if (envelope.getContents() == null) {
            LOGGER.warn("插件 [{}] 收到空的 envelope, execId: {}, taskId: {}", id, execId, taskId);
            return;
        }

        Map<String, Object> resultData = envelope.getContents();
        Map<String, Object> metadata = (Map<String, Object>) resultData.get("fileObjectMetadata");
        List<Map<String, Object>> identificationResults = (List<Map<String, Object>>) resultData.get("identificationResults");
        String fileUri = String.valueOf(metadata.get("vfsUri"));
        try {
            // 如果没有识别结果，不需要发送了，因为之前已经将元数据发回去了。
            if (identificationResults != null && !identificationResults.isEmpty()){
                metadata.put("identificationResults", identificationResults);
                writer.writeContent(JSON.from(metadata).toString());
            }
        } catch (Exception e) {
            String errorMsg = String.format("处理或发送最终结果失败, execId: %d, taskId: %d, file: %s: %s", 
                execId, taskId, fileUri, e.getMessage());
            LOGGER.error("插件 [{}] {}", id, errorMsg, e);
            reportFileProcessingError(probeClientTaskContext, execId, taskId, fileUri, "kafka_send_initiation", errorMsg);
        }
    }

    /**
     * 报告文件处理错误。
     */
    private void reportFileProcessingError(ProbeClientTaskContext context, long execId, long taskId, String vfsUri,
                                           String stage, String errorMessage) {
        if (context != null) {
            try {
                context.reportErrorOccurredExecuting(StatusRecord.Position.VfsResultSending,null,errorMessage , null);
            } catch (Exception e) {
                LOGGER.error("插件 [{}] 报告文件处理错误失败, execId={}, taskId={}, file={}: {}",
                        id, execId, taskId, vfsUri, e.getMessage(), e);
            }
        }
    }

    @Override
    public void shutdown() {
        LOGGER.info("关闭结果 Kafka 发送插件: {}", id);
    }

    /**
     * 指定配置类型为 Map。
     * @return Map class
     */
    @Override
    public Class<? extends Map> configType() {
        return HashMap.class;
    }
}
