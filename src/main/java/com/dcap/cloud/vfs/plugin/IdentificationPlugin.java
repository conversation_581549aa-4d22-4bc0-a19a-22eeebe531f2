package com.dcap.cloud.vfs.plugin;

import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.Emitter;
import com.dcap.cloud.core.api.IntermediatePlugin;
import com.dcap.cloud.core.api.Session;
import com.dcap.cloud.vfs.model.ExtractionResult;
import com.dcap.cloud.vfs.model.TableData;
import com.dcap.cloud.vfs.report.VfsScanTaskReport;
import com.dcap.datalayer.DataClassifierUtil;
import com.dcap.utils.JSON;
import com.yd.dataclassifier.jni.*;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.dcap.probe.client.ProbeClientTaskUtil;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

import static com.yd.dcap.classifier.taskreport.StatusRecord.Position.VfsIdentification;


/**
 * 中间件插件，负责识别提取内容中的敏感数据。
 * 支持文本内容的非结构化识别和表格数据的结构化识别。
 */
public class IdentificationPlugin implements IntermediatePlugin<Map<String, Object>> {

    private static final Logger LOGGER = LoggerFactory.getLogger(IdentificationPlugin.class);

    private final String id = "vfs-file-content-identifier"; // 插件 ID

    private Map<String, Object> pluginConfig; // 存储插件配置

    private List<Map<String, Object>> dataTags; // 规则配置

    private Integer markThresholds = 60; // 默认阈值

    private Integer hitPct = 30; // 默认命中率阈值

    // 数据分类器相关
    private LibDataClassifierPointer classifierPtr; // 分类器指针

    private boolean classifierInitialized = false; // 分类器是否初始化

    private long scanJobHistoryId; // 扫描任务历史ID

    private ProbeClientTaskContext probeClientTaskContext;

    @Override
    public String id() {
        return id;
    }

    @Override
    public void init(Map<String, Object> pluginConfig) {
        LOGGER.info("初始化内容识别插件 [{}]...", id);
        this.pluginConfig = pluginConfig; // 保存配置
        try {
            // --- 加载配置 ---
            String markThresholdsText = Objects.toString(this.pluginConfig.get("markThresholds"), null);
            if (StringUtils.isNotBlank(markThresholdsText)) {
                try {
                    this.markThresholds = Integer.parseInt(markThresholdsText);
                } catch (NumberFormatException e) {
                    LOGGER.warn("无效的 markThresholds 配置值 '{}'，使用默认值 {}", markThresholdsText, this.markThresholds);
                }
            }

            String taskType = Objects.toString(pluginConfig.get("taskType"), "");
            long taskId = Long.parseLong(Objects.toString(pluginConfig.get("taskId"), ""));
            probeClientTaskContext = ProbeClientTaskUtil.getInstance().getProbeClientTaskContext(taskType, taskId);
            // --- 获取扫描任务ID ---
            this.scanJobHistoryId = probeClientTaskContext.getExecId();

            // --- 规则模型解析 ---
            LOGGER.info("开始解析规则模型...");
            this.dataTags = (List<Map<String, Object>>) this.pluginConfig.get("dataTags");
            if (dataTags == null || dataTags.isEmpty()) {
                LOGGER.warn("插件 [{}] 配置中未找到 'dataTags' 或为空，无识别规则加载。", id);
                return;
            }

            // 收集所有规则定义，用于创建模型配置
            List<Map<String, Object>> discoveryModels = new ArrayList<>();
            List<Map<String, Object>> classifyModels = new ArrayList<>();

            for (Map<String, Object> dataTagMap : dataTags) {
                try {
                    String dataTagText = (String) dataTagMap.get("dataTag");
                    List<Map<String, Object>> exprList = (List<Map<String, Object>>) dataTagMap.get("patterns");
                    if (exprList == null || exprList.isEmpty()) {
                        LOGGER.warn("dataTag '{}' 的 patterns 为空，跳过此规则。", dataTagText);
                        continue;
                    }

                    for (Map<String, Object> exprMap : exprList) {
                        String parentType = String.valueOf(exprMap.get("parentType"));
                        String expr = String.valueOf(exprMap.get("expr"));
                        // 尝试解码 Base64
                        String ruleDefinition;
                        try {
                            ruleDefinition = new String(Base64.getUrlDecoder().decode(expr));
                        } catch (Exception e) {
                            LOGGER.debug("无法 Base64 解码 dataTag '{}' 的 patterns，尝试按原始字符串解析。", dataTagText);
                            ruleDefinition = expr;
                        }

                        Map<String, Object> ruleModel = JSON.from(ruleDefinition).toObject(Map.class);

                        // 根据规则类型分类存储
                        if ("NonStructRuleModel".equals(parentType)) {
                            discoveryModels.add(ruleModel);
                        } else if ("StructRuleModel".equals(parentType)) {
                            classifyModels.add(ruleModel);
                        } else {
                            // 默认作为discovery规则
                            discoveryModels.add(ruleModel);
                        }
                    }
                    LOGGER.debug("成功解析规则模型: {}", dataTagText);
                } catch (Exception e) {
                    LOGGER.error("解析规则模型时出错 (dataTag: {}): {}", dataTagMap.get("dataTag"), e.getMessage(), e);
                    // 跳过这个错误的规则，继续解析下一个
                }
            }

            // 初始化数据分类器
            String discoveryModelExpr = discoveryModels.isEmpty() ? null :
                    JSON.from(UtilMisc.toMap("discovery_models", discoveryModels)).convertToYaml();

            String classifyModelExpr = classifyModels.isEmpty() ? null :
                    JSON.from(UtilMisc.toMap("classify_models", classifyModels)).convertToYaml();

            // 输出表达式内容到桌面文件
//            outputExpressionsToDesktop(discoveryModelExpr, classifyModelExpr);

            String dataDictionary = Objects.toString(this.pluginConfig.get("dataDictionary"), null);
            try {
                dataDictionary = new String(Base64.getUrlDecoder().decode(dataDictionary));
            } catch (Exception e) {
                LOGGER.debug("无法 Base64 解码 dataDictionary  尝试按原始字符串解析。");
            }

            initDataClassifier(dataDictionary, classifyModelExpr, discoveryModelExpr, probeClientTaskContext);
            LOGGER.info("规则模型解析完成，共加载发现规则 {} 个，分类规则 {} 个。", discoveryModels.size(), classifyModels.size());
            LOGGER.info("内容识别插件 [{}] 初始化完成", id);
        } catch (Exception e) {
            LOGGER.error("内容识别插件 [{}] 初始化失败", id, e);
            releaseClassifier(); // 释放分类器资源
            throw new RuntimeException("插件初始化失败", e);
        }
    }

    /**
     * 初始化数据分类器
     *
     * @param classifyModelExpr  结构化模型定义表达式
     * @param discoveryModelExpr 非结构化模型定义表达式
     */
    private void initDataClassifier(String dictExpr, String classifyModelExpr, String discoveryModelExpr, ProbeClientTaskContext probeClientTaskContext) {
        if (StringUtils.isBlank(classifyModelExpr) && StringUtils.isBlank(discoveryModelExpr)) {
            LOGGER.warn("分类和发现模型定义都为空，无法初始化数据分类器");
            return;
        }

//        URL resource = Thread.currentThread().getContextClassLoader().getResource("discovery_models.yaml");
//        try {
//            discoveryModelExpr = FileUtils.readFileToString(new File(resource.toURI()));
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        } catch (URISyntaxException e) {
//            throw new RuntimeException(e);
//        }

        try {
            // 获取数据分类器工具类实例
            DataClassifierUtil util = DataClassifierUtil.getInstance();
            // 构建分类器
            long tenantId = probeClientTaskContext.getTenantId(); // 默认租户ID

            if (StringUtils.isNotBlank(classifyModelExpr) && StringUtils.isNotBlank(discoveryModelExpr)) {
                // 同时支持结构化和非结构化识别
                classifierPtr = util.build(scanJobHistoryId, classifyModelExpr, discoveryModelExpr, dictExpr, tenantId);
            } else if (StringUtils.isNotBlank(discoveryModelExpr)) {
                // 仅支持非结构化识别
                classifierPtr = util.buildForDiscovery(scanJobHistoryId, discoveryModelExpr, tenantId);
            } else {
                // 仅支持结构化识别
                classifierPtr = util.build(scanJobHistoryId, classifyModelExpr, dictExpr, tenantId);
            }

            if (classifierPtr != null) {
                classifierInitialized = true;
                LOGGER.info("数据分类器初始化成功，任务ID: {}", scanJobHistoryId);
            } else {
                LOGGER.error("数据分类器初始化失败，任务ID: {}", scanJobHistoryId);
            }
        } catch (Exception e) {
            LOGGER.error("初始化数据分类器异常: {}", e.getMessage(), e);
        }
    }

    @Override
    public void accept(CloudEnvelope<Map<String, Object>> envelope, Emitter emitter) {
        Session session = envelope.getSession();
        ProbeClientTaskContext probeClientTaskContext = ProbeClientTaskUtil.getInstance()
                .getProbeClientTaskContext(session.getTaskType(), session.getTaskId());
        long execId = session.getJobHistoryId();
        long currentTaskId = session.getTaskId(); // 获取当前任务 ID

        // 检查输入 Envelope
        if (envelope.getContents() == null) {
            LOGGER.warn("插件 [{}] 收到空的或格式不正确的 envelope , execId: {}, taskId: {}", id, execId, currentTaskId);
            return;
        }
        Map<String, Object> contents = envelope.getContents();
        // 获取文件名和扩展名
        Map<String, Object> fileObjectMetadata = (Map<String, Object>) contents.get("fileObjectMetadata");
        String vfsUri = fileObjectMetadata.get("vfsUri").toString();
        List<Map<String, Object>> currentFileResults = new ArrayList<>();

        try {
            // 使用数据分类器进行内容识别
            if (!classifierInitialized || classifierPtr == null) {
                LOGGER.warn("插件 [{}] 数据分类器未初始化或存在错误, file: {}", id, vfsUri);
                return;
            }

            LOGGER.debug("插件 [{}] 使用数据分类器进行内容识别, file: {}", id, vfsUri);

            // 获取提取结果
            ExtractionResult extractionResult = (ExtractionResult) contents.get("extractionResult");
            if (extractionResult == null) {
                LOGGER.warn("未找到提取结果，无法进行识别, file: {}", vfsUri);
                return;
            }
            fileObjectMetadata.put("structType", extractionResult.getType());

            // 根据提取类型选择识别策略
            switch (extractionResult.getType()) {
                case TEXT:
                    currentFileResults.addAll(identifyTextContent(extractionResult, vfsUri));
                    break;
                case STRUCTURED:
                    currentFileResults.addAll(identifyStructuredContent(extractionResult, vfsUri));
                    break;
                case MIXED:
                    // 混合类型，同时处理文本和结构化数据
                    if (extractionResult.hasTextContent()) {
                        currentFileResults.addAll(identifyTextContent(extractionResult, vfsUri));
                    }
                    if (extractionResult.hasTableData()) {
                        currentFileResults.addAll(identifyStructuredContent(extractionResult, vfsUri));
                    }
                    break;
                default:
                    LOGGER.warn("未知的提取类型: {}, file: {}", extractionResult.getType(), vfsUri);
                    break;
            }

            LOGGER.info("插件 [{}] 内容识别完成, 找到 {} 个确认匹配结果。file: {}. ", id, currentFileResults.size(), vfsUri);
        } catch (Throwable e) {
            // 捕获规则迭代或构建输入的顶层异常
            String errorMessage = String.format("内容识别过程中发生意外错误, file: %s: %s", vfsUri, e.getMessage());
            LOGGER.error("插件 [{}] {}", id, errorMessage, e);
            reportFileProcessingError(probeClientTaskContext, execId, currentTaskId, vfsUri, "identification", errorMessage);
        }

        // 报告和发射结果
        // 将最终确认的识别结果列表添加到 contents Map 中
        contents.put("identificationResults", currentFileResults); // 使用 List<MatchedResult>

        // --- 发射包含 Map 的 Envelope (推荐) ---
        emitter.emit(CloudEnvelope.of(envelope, id, contents));
        LOGGER.debug("插件 [{}] 发射包含识别结果的 envelope, VFS URI: {}", id, vfsUri);

        probeClientTaskContext.recordField(VfsScanTaskReport.FILE_CONTENT_RECOGNITION_COUNT, 1);
    }

    /**
     * 识别文本内容中的敏感数据
     */
    private List<Map<String, Object>> identifyTextContent(ExtractionResult extractionResult, String vfsUri) {
        List<Map<String, Object>> results = new ArrayList<>();

        String extractedText = extractionResult.getTextContent();
//        if (extractedText.startsWith("复杂sql解析")) {
//            // 写道桌面
//            try {
//                FileUtils.writeStringToFile(new File("C:  \\Users\\xuezhiwei\\Desktop\\复杂sql解析.txt"), extractedText);
//            } catch (IOException e) {
//                throw new RuntimeException(e);
//            }
//        }
        if (StringUtils.isBlank(extractedText)) {
            LOGGER.debug("文本内容为空，跳过文本识别, file: {}", vfsUri);
            return results;
        }

        datacls_discovery_result result = null;
        datacls_string_array resultDataTags = null;
        try {

            // 执行非结构化文本发现
            result = DataClassifierUtil.getInstance().discoveryText(
                    classifierPtr,
                    extractedText
            );

            if (result == null) {
                LOGGER.warn("文本发现失败, file: {}", vfsUri);
                return results;
            }

            resultDataTags = result.getData_tags();
            byte[] bytes = extractedText.getBytes();
            // 处理发现结果
            int count = result.getData_tags().getCount();
            for (int i = 0; i < count; i++) {
                int startIndex = result.getByte_starts().getItem(i);
                int endIndex = result.getByte_ends().getItem(i);
                byte[] bs = new byte[endIndex - startIndex];
                System.arraycopy(bytes, startIndex, bs, 0, bs.length);
                String matchedText = new String(bs);
                String tag = result.getData_tags().getString(i);

                // 创建匹配结果
                LOGGER.debug("文本标签 '{}' 匹配成功, file: {}", tag, vfsUri);
                results.add(UtilMisc.toMap(
                        "type", "text",
                        "tag", tag,
                        "matchedText", matchedText,
                        "startIndex", startIndex,
                        "endIndex", endIndex
                ));
            }
        } catch (Exception e) {
            LOGGER.error("文本内容识别异常, file: {}, error: {}", vfsUri, e.getMessage(), e);
        } finally {
            try {
                if (result != null){
                    result.delete();
                }
                if (resultDataTags != null) {
                    resultDataTags.delete();
                }
            } catch (Throwable e){
                LOGGER.error("释放内存异常: {}", e.getMessage(), e);
            }
        }
        return results;
    }

    /**
     * 识别结构化内容中的敏感数据
     */
    private List<Map<String, Object>> identifyStructuredContent(ExtractionResult extractionResult, String vfsUri) {
        List<Map<String, Object>> results = new ArrayList<>();

        List<TableData> tableDataList = extractionResult.getTableDataList();
        if (tableDataList == null || tableDataList.isEmpty()) {
            LOGGER.debug("结构化数据为空，跳过结构化识别, file: {}", vfsUri);
            return results;
        }

        try {
            DataClassifierUtil util = DataClassifierUtil.getInstance();

            for (TableData tableData : tableDataList) {
                String sheetName = tableData.getSheetName();
                List<String> columnNames = tableData.getColumnNames();

                if (columnNames == null || columnNames.isEmpty()) {
                    LOGGER.debug("工作表 {} 没有列名，跳过, file: {}", sheetName, vfsUri);
                    continue;
                }

                // 对每一列进行敏感数据识别
                for (int columnIndex = 0; columnIndex < columnNames.size(); columnIndex++) {
                    String columnName = columnNames.get(columnIndex);
                    List<String> columnValues = tableData.getColumnValues(columnIndex);

                    if (columnValues == null || columnValues.isEmpty()) {
                        LOGGER.debug("工作表 {} 列 {} 没有数据，跳过, file: {}", sheetName, columnName, vfsUri);
                        continue;
                    }

                    // 过滤空值
                    String[] valueArray = columnValues.stream()
                            .filter(value -> value != null && !value.trim().isEmpty())
                            .toArray(String[]::new);

                    if (valueArray.length == 0) {
                        LOGGER.debug("工作表 {} 列 {} 过滤后无有效数据，跳过, file: {}", sheetName, columnName, vfsUri);
                        continue;
                    }


                    // 创建列数据对象
                    datacls_column_data columnData = null;
                    datacls_classify_result classifyResult = null;
                    datacls_string_array bizTags = null;
                    try {
                        columnData = util.createColumnData(
                                sheetName,
                                columnName,
                                "", // 列注释为空
                                DATACLS_COLUMN_TYPE.DATACLS_CT_STRING, // 统一按文本类型处理
                                valueArray
                        );


                        if (columnData == null) {
                            LOGGER.warn("创建列数据失败, sheet: {}, column: {}, file: {}", sheetName, columnName, vfsUri);
                            continue;
                        }

                        // 执行分类
                        Integer markThresholds = getMarkThresholds();
                        Integer hitPct = getHitPercentage();
                        int scoreThreshold = markThresholds != null ? markThresholds : 60;
                        int hitRatioThreshold = hitPct != null ? hitPct : 30;

                        classifyResult = util.classify(
                                classifierPtr,
                                columnData,
                                scoreThreshold,
                                hitRatioThreshold,
                                true
                        );

                        if (classifyResult == null) {
                            LOGGER.debug("列分类失败, sheet: {}, column: {}, file: {}", sheetName, columnName, vfsUri);
                            continue;
                        }

                        bizTags = classifyResult.getBiz_tags();
                        // 处理业务标签
                        if (bizTags.getCount() > 0) {
                            // 最多显示 5 个样本值
                            List<String> sampleValues = Arrays.asList(valueArray).subList(0, Math.min(5, valueArray.length));
                            for (int i = 0; i < bizTags.getCount(); i++) {
                                String tag = bizTags.getString(i);
                                LOGGER.debug("结构化标签 '{}' 匹配成功, sheet: {}, column: {}, file: {}", tag, sheetName, columnName, vfsUri);
                                results.add(UtilMisc.toMap(
                                        "type", "structured",
                                        "tag", tag,
                                        "tableName", sheetName,
                                        "columnName", sheetName+"_"+columnName+"_"+columnIndex,
                                        "columnIndex", columnIndex,
                                        "sampleCount", valueArray.length,
                                        "sampleValues", sampleValues
                                ));
                            }
                        }
                    } catch (Throwable e){
                        LOGGER.error("结构化内容识别异常, file: {}, error: {}", vfsUri, e.getMessage(), e);
                    } finally {
                        try {
                            if (columnData != null){
                                columnData.delete();
                            }
                            if (classifyResult != null){
                                classifyResult.delete();
                            }
                            if (bizTags != null){
                                bizTags.delete();
                            }
                        } catch (Throwable e){
                            // 内存释放异常
                            LOGGER.error("结构化内容识别内存释放异常, file: {}, error: {}", vfsUri, e.getMessage(), e);
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("结构化内容识别异常, file: {}, error: {}", vfsUri, e.getMessage(), e);
        }
        return results;
    }

    /**
     * 获取阈值配置
     */
    private Integer getMarkThresholds() {
        return this.markThresholds;
    }

    /**
     * 获取命中率阈值配置
     */
    private Integer getHitPercentage() {
        return this.hitPct;
    }

    /**
     * 报告文件处理错误。
     */
    private void reportFileProcessingError(ProbeClientTaskContext context, long execId, long taskId, String vfsUri, String stage, String errorMessage) {
        if (context != null) {
            try {
                context.reportErrorOccurredExecuting(VfsIdentification, vfsUri, errorMessage, null); // 示例
            } catch (Exception e) {
                LOGGER.error("插件 [{}] 报告文件处理错误失败, execId={}, taskId={}, file={}: {}",
                        id, execId, taskId, vfsUri, e.getMessage(), e);
            }
        } else {
            LOGGER.warn("插件 [{}] 无法获取 ProbeClientTaskContext 来报告错误, execId={}, taskId={}, file={}", id, execId, taskId, vfsUri);
        }
    }

    @Override
    public void shutdown() {
        LOGGER.info("关闭内容识别插件: {}", id);
        // 清理规则引擎资源
        releaseClassifier();
    }

    /**
     * 释放分类器资源
     */
    private void releaseClassifier() {
        if (classifierInitialized && classifierPtr != null) {
            try {
                DataClassifierUtil util = DataClassifierUtil.getInstance();
                util.releaseTaskClassifier(scanJobHistoryId, classifierPtr);
                classifierInitialized = false;
                classifierPtr = null;
                LOGGER.info("释放分类器资源成功，任务ID: {}", scanJobHistoryId);
            } catch (Exception e) {
                LOGGER.error("释放分类器资源异常: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 指定配置类型为 Map。
     *
     * @return Map class
     */
    @Override
    public Class<? extends Map> configType() {
        return HashMap.class;
    }
}
