package com.dcap.cloud.vfs;

import com.dcap.cloud.core.OrchestratorTrigger;
import com.dcap.cloud.vfs.report.VfsScanTaskReport;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.probe.TaskResultReporter;
import com.yd.dcap.probe.client.TaskType;
import com.yd.dcap.probe.entities.ProbeClientJobResult;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;

import java.util.Map;
import java.util.function.Consumer;

/**
 * VFS 扫描任务处理器，负责启动和管理 VFS 扫描 Orchestrator 流水线。
 */
@TaskType("VFS_FILE_SCAN") // 定义此处理器的任务类型
public class VfsScanProcessor extends OrchestratorTrigger {

    public VfsScanProcessor(ProbeClientTaskContext probeClientTaskContext) {
        super(probeClientTaskContext);
        TaskResultReporter taskResultReporter = probeClientTaskContext.getTaskResultReporter();
        probeClientTaskContext.registerTaskReport(new VfsScanTaskReport(taskResultReporter, probeClientTaskContext));
    }

    @Override
    protected Map<String, Object> doWork(long execId, long taskId, String taskType, long tenantId,
                                         Map<String, Object> taskConf, Consumer<ProbeClientJobResult> callback) {
        if (taskConf == null) {
            throw new IllegalArgumentException("taskParam is null");
        }
        taskConf.put("startedPosition", StatusRecord.Position.VfsScanStart);
        taskConf.put("interruptedPosition", StatusRecord.Position.VfsJobInterrupt);
        taskConf.put("completedPosition", StatusRecord.Position.VfsScanComplete);
        return super.doWork(execId, taskId, taskType, tenantId, taskConf, callback);
    }

    @Override
    protected void onInterrupt(long execId, long taskId, String taskType, long tenantId, Map<String, Object> taskParam) {
        if (taskParam == null) {
            throw new IllegalArgumentException("taskParam is null");
        }
        taskParam.put("startedPosition", StatusRecord.Position.VfsScanStart);
        taskParam.put("interruptedPosition", StatusRecord.Position.VfsJobInterrupt);
        taskParam.put("completedPosition", StatusRecord.Position.VfsScanComplete);
        super.onInterrupt(execId, taskId, taskType, tenantId, taskParam);
    }
}
