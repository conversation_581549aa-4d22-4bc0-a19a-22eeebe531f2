package com.dcap.cloud.vfs.service;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.Bucket;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.Emitter;
import com.dcap.cloud.core.api.Session;
import com.dcap.cloud.vfs.plugin.VfsOriginPlugin;
import com.github.vfss3.S3FileName;
import com.github.vfss3.S3FileSystemConfigBuilder;
import com.google.common.collect.ImmutableList;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.dcap.common.utils.sm4.SM4Utils;
import com.yd.dcap.probe.client.ProbeClientTaskUtil;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.vfs2.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.dcap.cloud.vfs.report.VfsScanTaskReport.FILE_METADATA_EXTRACTED_COUNT;
import static com.dcap.cloud.vfs.report.VfsScanTaskReport.FILE_TOTAL_COUNT;
import static java.util.regex.Pattern.compile;

/**
 * S3 文件服务发现实现。
 */
public class S3FileServiceDiscovery implements FileServiceDiscovery {

    private static final Logger LOGGER = LoggerFactory.getLogger(S3FileServiceDiscovery.class);

    private static final String FILE_SERVICE_TYPE = "s3";

    private static final Pattern ALIYUN_HOST_PATTERN = compile("((?<bucket>[a-z0-9\\-]+)\\.)?oss(-(?<region>[a-z0-9\\-]+))?\\.aliyuncs\\.com");

    private final String s3AccessKey;
    private final String s3SecretKey;
    private boolean s3UseHttps;
    private final FileSystemManager fsManager;
    private final String fileServiceType;
    private final Long fileServiceId;
    private final String fileServiceName;
    private String hostAndPort;
    private final AmazonS3 s3Client; // 添加 S3 客户端作为成员变量
    private List<String> scanPathList;
    private final boolean isAliyun;
    private final String region;
    private final boolean isInternalFileService;
    
    // SSE-C 配置参数
    private boolean ssecEnabled = false;
    private String ssecAlgorithm = "AES256";
    private String ssecEncryptionKey = "X1l1YW5EaWFuU2h1QW5LZUppWW91WGlhbkdvbmdTaV8=";
    private String ssecEncryptionKeyMD5 = "uTH6+ll9GQTMKQC+d7RQYw==";

    /**
     * 构造函数，从任务参数初始化 S3 配置。
     * @param taskParams 包含配置的任务参数 Map。
     * @throws NullPointerException 如果缺少必要的 S3 配置。
     * @throws FileSystemException 如果 VFS 管理器初始化失败。
     */
    public S3FileServiceDiscovery(Map<String, Object> taskParams) throws FileSystemException {
        List<String> tempScanPathList;
        LOGGER.info("初始化 S3 文件服务发现...");
        fileServiceType = (String) taskParams.get("fileServiceType");
        fileServiceName = (String) taskParams.get("fileServiceName");
        fileServiceId = Long.parseLong(Objects.toString(taskParams.get("fileServiceId"), "0"));
        isInternalFileService = Objects.equals(taskParams.get("isInternalFileService"), "Y");
        // 从配置中提取 VFS 相关参数
//        this.rootUri = Objects.requireNonNull((String) taskParams.get("rootUri"), "配置缺少 rootUri");
        this.s3AccessKey = SM4Utils.decCheckSm4ForEcb(Objects.requireNonNull((String) taskParams.get("username"), "配置缺少 s3AccessKey"));
        this.s3SecretKey = SM4Utils.decCheckSm4ForEcb(Objects.requireNonNull((String) taskParams.get("secret"), "配置缺少 s3SecretKey"));
        // 从 taskParams 获取 s3UseHttps，提供默认值 false
        this.s3UseHttps = Boolean.parseBoolean(Objects.toString(taskParams.get("useTls"), "false"));
        try {
            tempScanPathList = (List<String>)taskParams.get("scanPathList");
            if (tempScanPathList == null){
                tempScanPathList = new ArrayList<>();
            }
        } catch (Exception e){
            LOGGER.error("无法获取扫描路径列表");
            tempScanPathList = new ArrayList<>();
        }

        // 读取 s3Endpoint 和 s3PathStyleAccess
        // 添加成员变量
        this.scanPathList = tempScanPathList;
        // 如果是内置文件服务，设置扫描路径为租户下的路径
        if (isInternalFileService){
            this.scanPathList = new ArrayList<>();
            this.scanPathList.add("yuandian/files_"+taskParams.get("tenantId"));
            this.s3UseHttps = true;
            this.ssecEnabled = true;
        }
        String s3Endpoint = Objects.requireNonNull((String) taskParams.get("endpoint"), "配置缺少 s3Endpoint");
        Integer port = Integer.parseInt(Objects.toString(taskParams.get("port"), "0")); // 31281;//
        // 移除 endpoint 中的 scheme (http/https)
        String endpoint = s3Endpoint.replaceFirst("^https?://", "");
        if (port != null && port > 0 && !endpoint.contains(":")){
            hostAndPort = endpoint + ":" + port;
        } else {
            hostAndPort = endpoint;
        }
        Matcher hostNameMatcher = ALIYUN_HOST_PATTERN.matcher(hostAndPort);
        this.isAliyun = hostNameMatcher.matches();
        if (isAliyun){
            region = hostNameMatcher.group("region");
            String bucket = hostNameMatcher.group("bucket");
            if (StringUtils.isNotBlank(bucket)){
                tempScanPathList.add(bucket);
                // 从 host 中移除  bucket 部分，因为  bucket 已经被添加到 scanPathList 中，依靠后续逻辑拼接
                hostAndPort = hostAndPort.substring(bucket.length() + 1);
            } else {
                // 是 aliyun 对象存储扫描，但是 endpoint 不包含 bucket
                LOGGER.warn("检测到是 aliyun 对象存储扫描，但是 endpoint 不包含 bucket: {}", hostAndPort);
            }
        } else {
            region = null;
        }
        this.fsManager = VFS.getManager();

        // 初始化 S3 客户端
        LOGGER.info("初始化 S3 客户端: endpoint={}, useHttps={}", hostAndPort, s3UseHttps);
        this.s3Client = initializeS3Client();
        LOGGER.info("S3 客户端初始化完成");

        // 基础验证
//        if (!rootUri.startsWith("s3://")) {
//            throw new IllegalArgumentException("S3 服务发现仅支持 s3:// 协议的 VFS URI");
//        }
//        LOGGER.info("S3 文件服务发现初始化完成，目标 URI: {}", rootUri);
    }

    @Override
    public String service() {
        return FILE_SERVICE_TYPE;
    }

    @Override
    public void discover(Session session, Emitter emitter, FileSystemOptions baseOptions) {

        long execId = session.getJobHistoryId();
        long taskId = session.getTaskId();
        LOGGER.info("服务 [{}] 开始 VFS 发现任务, Task ID: {}, Exec ID: {}", FILE_SERVICE_TYPE, taskId, execId);

        ProbeClientTaskContext probeClientTaskContext = ProbeClientTaskUtil.getInstance()
                .getProbeClientTaskContext(session.getTaskType(), session.getTaskId());

        if (probeClientTaskContext == null) {
            LOGGER.error("服务 [{}] 无法获取 ProbeClientTaskContext, Task ID: {}, Exec ID: {}", FILE_SERVICE_TYPE, taskId, execId);
            // 无法报告状态，只能记录错误
        }

        if (probeClientTaskContext != null){
            // 报告任务开始（如果需要）
            probeClientTaskContext.reportExecuting(StatusRecord.Position.VfsScanStart, null, "开始 "+FILE_SERVICE_TYPE+" 扫描")
                    .sendToServer();
        }

        // 在循环外构建一次 FileSystemOptions，供本次任务的所有文件使用
        final FileSystemOptions taskFileSystemOptions = buildS3FileSystemOptions(baseOptions);

        try {
            // 标准化扫描路径
            List<String> normalizedScanPaths = normalizeScanPaths(scanPathList);
            // 标准化之后的路径应该去重
            normalizedScanPaths = new ArrayList<>(new HashSet<>(normalizedScanPaths));
            LOGGER.info("服务 [{}] 标准化后的扫描路径: {}", FILE_SERVICE_TYPE, normalizedScanPaths);

            // 获取所有存储桶
            List<String> buckets = listAllBuckets(probeClientTaskContext);

            // 如果无法获取存储桶列表或列表为空，尝试从扫描路径中提取存储桶
            if (buckets == null || buckets.isEmpty()) {
                LOGGER.warn("服务 [{}] 无法通过 API 获取存储桶列表，尝试从扫描路径中提取", FILE_SERVICE_TYPE);
                // 从扫描路径中提取存储桶名称
                buckets = extractBucketsFromScanPaths(normalizedScanPaths);
                if (buckets.isEmpty()) {
                    // 如果仍然无法获取存储桶，报告失败
                    String errorMsg = "无法获取存储桶列表，且扫描路径中未指定有效的存储桶";
                    LOGGER.error("服务 [{}] {}", FILE_SERVICE_TYPE, errorMsg);
                    if (probeClientTaskContext != null) {
                        probeClientTaskContext.reportFailed(StatusRecord.Position.VfsScanStart, null, errorMsg)
                                .sendToServer();
                    }
                    return;
                } else {
                    LOGGER.info("服务 [{}] 从扫描路径中提取到以下存储桶: {}", FILE_SERVICE_TYPE, buckets);
                    if (probeClientTaskContext != null) {
                        probeClientTaskContext.reportExecuting(StatusRecord.Position.VfsScanStart, null,
                                "无法通过 API 获取存储桶列表，将使用扫描路径中指定的存储桶")
                                .sendToServer();
                    }
                }
            } else {
                LOGGER.info("服务 [{}] 通过 API 找到以下存储桶: {}", FILE_SERVICE_TYPE, buckets);
            }

            // 为每个存储桶创建根路径并处理
            for (String bucket : buckets) {
                String rootPath = "/" + bucket;
                processBucketWithScanPaths(rootPath, normalizedScanPaths, taskFileSystemOptions, session, emitter, probeClientTaskContext);
            }

            if (probeClientTaskContext != null) {
                probeClientTaskContext.reportExecuting(StatusRecord.Position.VfsScanStart, null,
                                "S3 bucket 文件发现已经完成。")
                        .sendToServer();
            }
        } catch (Exception e) {
            String errorMsg = e.getMessage();
            LOGGER.error("服务 [{}] {}", FILE_SERVICE_TYPE, errorMsg, e);
            if (probeClientTaskContext != null) {
                probeClientTaskContext.reportFailed(StatusRecord.Position.VfsScanStart, null, errorMsg)
                        .sendToServer();
            }
        } finally {
            LOGGER.info("服务 [{}] VFS 发现任务结束, Task ID: {}, Exec ID: {}. ", FILE_SERVICE_TYPE, taskId, execId);
            if (probeClientTaskContext != null) {
                probeClientTaskContext.reportExecuting(StatusRecord.Position.VfsMetadataPhaseComplete,
                                null, "元数据提取完成。")
                        .sendToServer();
            }
        }
    }

    /**
     * 处理存储桶中的扫描路径
     *
     * @param bucket 存储桶根路径
     * @param normalizedScanPaths 标准化的扫描路径列表
     * @param fileSystemOptions 文件系统选项
     * @param session 会话
     * @param emitter 发射器
     * @param probeClientTaskContext 任务上下文
     */
    private void processBucketWithScanPaths(String bucket, List<String> normalizedScanPaths,
            FileSystemOptions fileSystemOptions, Session session, Emitter emitter,
            ProbeClientTaskContext probeClientTaskContext) {


        if (normalizedScanPaths == null || normalizedScanPaths.isEmpty()) {
            // 如果没有指定扫描路径，则扫描整个存储桶
            String fullUriString;
            if (isAliyun){
                // 阿里云不支持 Path-style URLs
                fullUriString = String.format("s3://%s.%s", bucket, hostAndPort);
            } else {
                fullUriString = String.format("s3://%s%s", hostAndPort, bucket);
            }

            // 移除可能的多余斜杠
            fullUriString = fullUriString.replaceAll("(?<!:)//", "/");
            LOGGER.info("服务 [{}] 扫描完整存储桶: {}", FILE_SERVICE_TYPE, fullUriString);
            processVfsDirectory(fullUriString, fileSystemOptions, session, emitter, probeClientTaskContext);
            return;
        }

        // 如果有指定扫描路径，则只扫描匹配的路径
        for (String scanPath : normalizedScanPaths) {
            // 检查 scanPath 是否为该存储桶的路径或其子路径
            if (isPathForRoot(scanPath, bucket)) {
                // 如果是，创建 URI 并处理
                String pathWithinBucket = scanPath.substring(bucket.length());
                if (pathWithinBucket.isEmpty()) {
                    pathWithinBucket = "/";
                }

                String fullUriString;
                if (isAliyun){
                    // 阿里云不支持 Path-style URLs
                    fullUriString = String.format("s3://%s.%s%s", bucket, hostAndPort, pathWithinBucket);
                } else {
                    fullUriString = String.format("s3://%s%s%s", hostAndPort, bucket, pathWithinBucket);
                }

                // 移除可能的多余斜杠
                fullUriString = fullUriString.replaceAll("(?<!:)//", "/");
                LOGGER.info("服务 [{}] 扫描路径: {}", FILE_SERVICE_TYPE, fullUriString);
                processVfsDirectory(fullUriString, fileSystemOptions, session, emitter, probeClientTaskContext);
            }
        }
    }

    /**
     * 标准化扫描路径列表
     *
     * @param paths 原始路径列表
     * @return 标准化后的路径列表
     */
    private List<String> normalizeScanPaths(List<String> paths) {
        if (paths == null || paths.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> normalized = new ArrayList<>();
        for (String path : paths) {
            if (path == null || path.trim().isEmpty()) {
                continue;
            }

            // 确保路径以 / 开头
            String normalizedPath = path.startsWith("/") ? path : "/" + path;

            // 确保路径以 / 结尾（用于目录匹配）
            if (!normalizedPath.endsWith("/")) {
                normalizedPath = normalizedPath + "/";
            }

            normalized.add(normalizedPath);
        }

        return normalized;
    }

    /**
     * 从扫描路径中提取存储桶名称
     * 路径格式应为：/bucket/path/ 或 /bucket/
     *
     * @param scanPaths 标准化后的扫描路径列表
     * @return 从扫描路径中提取的存储桶名称列表
     */
    private List<String> extractBucketsFromScanPaths(List<String> scanPaths) {
        if (scanPaths == null || scanPaths.isEmpty()) {
            return new ArrayList<>();
        }

        Set<String> bucketSet = new HashSet<>(); // 使用 Set 避免重复

        for (String path : scanPaths) {

            String pathWithoutLeadingSlash = path;

            // 路径以 / 开头
            if (path.startsWith("/")) {
                LOGGER.warn("扫描路径以 / 开头。移除后提取存储桶: {}", path);
                // 移除开头的 /
                pathWithoutLeadingSlash = path.substring(1);
            }

            // 如果路径为空或只有一个 /，则跳过
            if (pathWithoutLeadingSlash.isEmpty()) {
                LOGGER.warn("扫描路径为根路径，无法提取存储桶: {}", path);
                continue;
            }

            // 提取第一个路径组件作为存储桶名称
            String[] pathComponents = pathWithoutLeadingSlash.split("/");
            if (pathComponents.length > 0 && !pathComponents[0].isEmpty()) {
                bucketSet.add(pathComponents[0]);
                LOGGER.debug("从扫描路径 {} 中提取到存储桶: {}", path, pathComponents[0]);
            } else {
                LOGGER.warn("无法从扫描路径中提取存储桶: {}", path);
            }
        }

        return new ArrayList<>(bucketSet);
    }

    /**
     * 检查指定路径是否属于给定的根路径
     *
     * @param path 要检查的路径
     * @param rootPath 根路径
     * @return 如果路径属于根路径则返回true
     */
    private boolean isPathForRoot(String path, String rootPath) {
        if (rootPath.equals("/")) {
            // 根路径是 /，所有路径都匹配
            return true;
        }
        return path.equals(rootPath) || path.startsWith(rootPath + "/");
    }

    /**
     * 处理VFS目录中的文件
     *
     * @param directoryUri VFS目录 URI
     * @param fileSystemOptions VFS文件系统选项
     * @param session 会话
     * @param emitter 发射器
     * @param probeClientTaskContext 任务上下文
     */
    private void processVfsDirectory(String directoryUri, FileSystemOptions fileSystemOptions,
            Session session, Emitter emitter, ProbeClientTaskContext probeClientTaskContext) {
        try (FileObject root = fsManager.resolveFile(directoryUri, fileSystemOptions)) {
            if (!root.exists() || !root.isFolder()) {
                String errorMsg = String.format("VFS 根路径不存在或不是文件夹: %s", directoryUri);
                LOGGER.error("服务 [{}] {}", FILE_SERVICE_TYPE, errorMsg);
                probeClientTaskContext.reportFailed(StatusRecord.Position.VfsScanStart, null, errorMsg); // 使用合适的 Position
                return ;
            }

            FileObject[] files = root.findFiles(Selectors.SELECT_FILES);
            int fileCount = (files != null) ? files.length : 0;
            LOGGER.info("服务 [{}] 在 {} 下找到 {} 个文件", FILE_SERVICE_TYPE, directoryUri, fileCount);

            int processedCount = 0;
            int errorCount = 0;
            if (files == null) {
                return;
            }
            for (FileObject file : files) {
                try (FileObject currentFile = file) {
                    // 将预构建的 fileSystemOptions 传递给 processFile
                    processFile(currentFile, session, emitter, probeClientTaskContext, fileSystemOptions);
                    processedCount++;
                    if (processedCount % 100 == 0) {
                        LOGGER.info("服务 [{}] 已处理 {}/{} 文件。", FILE_SERVICE_TYPE, processedCount, fileCount);
                    }
                } catch (Exception e) {
                    errorCount++;
                    String fileUriStr = "未知文件";
                    try { fileUriStr = file.getName().getURI(); } catch (Exception ignored) {}
                    String fileErrorMsg = String.format("处理文件 %s 时出错: %s", fileUriStr, e.getMessage());
                    LOGGER.error("服务 [{}] {}", FILE_SERVICE_TYPE, fileErrorMsg, e);
                    probeClientTaskContext.reportErrorOccurredExecuting(StatusRecord.Position.VfsScanStart, fileUriStr, e.getMessage(), null);
                }
            }
            probeClientTaskContext.recordField(FILE_TOTAL_COUNT, fileCount);
        } catch (FileSystemException e) {
            String errorMsg = String.format("VFS 发现过程中发生文件系统异常 %s: %s", directoryUri, e.getMessage());
            LOGGER.error("服务 [{}] {}", FILE_SERVICE_TYPE, errorMsg, e);
            if (probeClientTaskContext != null){
                probeClientTaskContext.reportFailed(StatusRecord.Position.VfsScanStart, null, errorMsg).sendToServer();
            }
        } catch (Exception e) {
            String errorMsg = String.format("VFS 发现过程中发生未知异常 %s: %s", directoryUri, e.getMessage());
            LOGGER.error("服务 [{}] {}", FILE_SERVICE_TYPE, errorMsg, e);
            if (probeClientTaskContext != null) {
                probeClientTaskContext.reportFailed(StatusRecord.Position.VfsScanStart, null, errorMsg).sendToServer();
            }
        }
    }

    /**
     * 构建 S3 文件系统选项，合并基础选项。
     * @param baseOptions 基础文件系统选项
     * @return 配置好的 S3 FileSystemOptions
     */
    private FileSystemOptions buildS3FileSystemOptions(FileSystemOptions baseOptions) {
        // 复制基础选项以避免修改原始对象
        FileSystemOptions opts = (baseOptions != null) ? baseOptions : new FileSystemOptions();
        S3FileSystemConfigBuilder builder = S3FileSystemConfigBuilder.getInstance();
        BasicAWSCredentials awsCreds = new BasicAWSCredentials(s3AccessKey, s3SecretKey);
        AWSStaticCredentialsProvider credentialsProvider = new AWSStaticCredentialsProvider(awsCreds);
        builder.setCredentialsProvider(opts, credentialsProvider);
        builder.setUseHttps(opts, s3UseHttps);

        // 如果启用了 SSE-C，添加必要的头信息
        if (ssecEnabled) {
            // 创建 ClientConfiguration
            ClientConfiguration clientConfiguration = new ClientConfiguration();
            LOGGER.info("为 VFS 连接启用 SSE-C 加密: algorithm={}", ssecAlgorithm);
            clientConfiguration.withHeader("X-Amz-Server-Side-Encryption-Customer-Algorithm", ssecAlgorithm);
            clientConfiguration.withHeader("X-Amz-Server-Side-Encryption-Customer-Key", ssecEncryptionKey);
            clientConfiguration.withHeader("X-Amz-Server-Side-Encryption-Customer-Key-MD5", ssecEncryptionKeyMD5);

            builder.setClientConfiguration(opts, clientConfiguration);
            LOGGER.info("已添加 SSE-C HTTP 头到 ClientConfiguration");
        } else {
            LOGGER.info("SSE-C 未启用，使用标准配置");
        }


        return opts;
    }

    /**
     * 获取当前 S3 存储服务中的所有存储桶列表
     *
     * @return 存储桶名称列表，如果无法获取则返回空列表
     */
    public List<String> listAllBuckets(ProbeClientTaskContext probeClientTaskContext) {
        LOGGER.info("获取所有 S3 存储桶列表...");
        List<String> bucketNames = new ArrayList<>();
        try {
            // 使用成员变量中的 S3 客户端
            // 列出所有存储桶
            List<Bucket> buckets = s3Client.listBuckets();
            for (Bucket bucket : buckets) {
                bucketNames.add(bucket.getName());
                LOGGER.debug("发现存储桶: {}, 创建时间: {}", bucket.getName(), bucket.getCreationDate());
            }

            LOGGER.info("成功获取到 {} 个存储桶", bucketNames.size());
            return bucketNames;
        } catch (Exception e) {
            LOGGER.error("获取存储桶列表时发生错误: {}", e.getMessage(), e);
            // 有可能是因为访问令牌不允许进行 ListBuckets，这是常见的权限限制
            LOGGER.info("将尝试从扫描路径中提取存储桶信息");
            if (probeClientTaskContext != null){
                probeClientTaskContext.reportExecuting(StatusRecord.Position.VfsScanStart, null,
                                "无法通过 API 获取存储桶列表["+e.getMessage()+"]，将使用扫描路径中指定的存储桶")
                        .sendToServer();
            }
        }
        return bucketNames; // 返回空列表而不是 null，以保持一致性
    }

    /**
     * 处理单个文件，提取元数据并发送。
     * @param file 文件对象
     * @param session 会话
     * @param emitter 发射器
     * @param context 任务上下文用于报告
     * @throws FileSystemException 文件系统异常
     */
    // 添加 FileSystemOptions 参数
    private void processFile(FileObject file, Session session, Emitter emitter, ProbeClientTaskContext context, FileSystemOptions taskFileSystemOptions) throws FileSystemException {
        FileContent content = file.getContent();
        S3FileName s3FileName = (S3FileName) file.getName();

        Map<String, Object> attributes = content.getAttributes();
        FileContentInfo contentInfo = content.getContentInfo();
        String contentType = contentInfo.getContentType();
        String contentEncoding = contentInfo.getContentEncoding();

        String fileUri = file.getName().getURI();
        long fileSize = content.getSize();
        long lastModifiedTime = content.getLastModifiedTime();

        LOGGER.debug("服务 [{}] 处理文件: URI={}, 大小={}, 修改时间={}", FILE_SERVICE_TYPE, fileUri, fileSize, lastModifiedTime);

        // 例如：s3://127.0.0.1:9000/yuandian/test/logs/ch.log 应该解析出来 bucket 是 yuandian, filepath 是 /test/logs/ch.log, dirs 是 /test/logs/
        // 正确解析 s3 uri 获取 bucket, filepath, dirs
        try {
            // 解析 scheme 和 authority部分
            if (!fileUri.startsWith("s3://")) {
                LOGGER.warn("服务 [{}] 不是有效的 S3 URI 格式: {}", FILE_SERVICE_TYPE, fileUri);
                return;
            }

            // 移除 scheme
            String withoutScheme = fileUri.substring(5); // 移除"s3://"

            // 找到第一个斜杠，分离 host 和 path
            int firstSlash = withoutScheme.indexOf('/');
            if (firstSlash == -1) {
                LOGGER.warn("服务 [{}] URI没有路径部分: {}", FILE_SERVICE_TYPE, fileUri);
                return;
            }

            String path = withoutScheme.substring(firstSlash); // 包含开头的'/'

            if (path.length() == 1) {
                LOGGER.warn("服务 [{}] URI路径为空或无效: {}", FILE_SERVICE_TYPE, fileUri);
                return;
            }

            String[] pathParts = path.substring(1).split("/", 2);
            if (pathParts.length == 0) {
                LOGGER.warn("服务 [{}] 无法从 URI 解析 filepath: {}", FILE_SERVICE_TYPE, fileUri);
                return;
            }

            // 路径的第一部分是 bucket，剩余部分是文件路径
//            String bucket = pathParts[0];
            String filepath = pathParts.length > 1 ? "/" + pathParts[1] : "/";
            String key = pathParts.length > 1 ? pathParts[1] : pathParts[0];

            // 获取目录部分（不包含文件名）
            String dirs = filepath;
            int lastSlash = filepath.lastIndexOf('/');
            if (lastSlash >= 0 && lastSlash < filepath.length() - 1) {
                dirs = filepath.substring(0, lastSlash + 1);
            }

            // 使用 S3 SDK 获取对象的元数据
            Map<String, Object> s3Metadata = getS3ObjectMetadata(s3FileName.getBucket(), key);

            // 创建基本元数据 Map
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("vfsUri", fileUri);
            metadata.put("jobTenantId", context != null? context.getTenantId(): null);
            metadata.put("fileSize", fileSize);
            metadata.put("lastModifiedTime", lastModifiedTime);
            metadata.put("fileServiceType", fileServiceType);
            metadata.put("fileServiceId", fileServiceId);
            metadata.put("fileServiceName", fileServiceName);
            metadata.put("bucket", s3FileName.getBucket()); // 添加 bucket
            metadata.put("dirs", dirs); // 添加解析出的 dirs
            String fileBaseName = file.getName().getBaseName();
            metadata.put("objectFileName", fileBaseName);
            metadata.put("key", key); // 添加 S3 对象的 key

            String extension = file.getName().getExtension();
            if (StringUtils.isNotBlank(extension)){
                metadata.put("extension", extension);
            } else if (fileBaseName.lastIndexOf(".") != -1){
                metadata.put("extension", fileBaseName.substring(fileBaseName.lastIndexOf(".")));
            }

            // 添加任务关联 ID
            metadata.put("execId", session.getJobHistoryId());
            metadata.put("taskId", session.getTaskId());
            metadata.put("taskType", session.getTaskType());
            String fullPath = isAliyun? "/"+s3FileName.getBucket()+path:path;
            try {
                fullPath = URLDecoder.decode(fullPath, Charset.defaultCharset());
            } catch (Exception ignored) {
            }
            metadata.put("fullPath", fullPath);

            // 如果有 S3 元数据，合并到 metadata
            if (s3Metadata != null && !s3Metadata.isEmpty()) {
                mergeS3Metadata(metadata, s3Metadata);
            }


            metadata = Collections.unmodifiableMap(UtilMisc.toMap(
                    "fileObjectMetadata", metadata,
                    "vfsOptions", taskFileSystemOptions
            ));

            CloudEnvelope<Map<String, Object>> envelope = new CloudEnvelope<>(session, ImmutableList.of(VfsOriginPlugin.ID), null, metadata);
            emitter.emit(envelope);

            // 使用 recordField 记录单个文件元数据提取计数
            if (context != null){
                context.recordField(FILE_METADATA_EXTRACTED_COUNT, 1);
            }
        } catch (Exception e) {
            LOGGER.error("服务 [{}] 解析URI时出错: {}", FILE_SERVICE_TYPE, fileUri, e);
            throw new FileSystemException("无法解析文件URI: " + fileUri, e);
        }
    }

    /**
     * 将 S3 元数据合并到目标元数据中
     * 包括处理用户自定义元数据中的 Base64 编码的 JSON 数据
     *
     * @param targetMetadata 目标元数据 Map，将被修改
     * @param s3Metadata S3 元数据 Map，数据来源
     */
    private void mergeS3Metadata(Map<String, Object> targetMetadata, Map<String, Object> s3Metadata) {
        // 合并 S3 元数据，但不覆盖现有字段
        for (Map.Entry<String, Object> entry : s3Metadata.entrySet()) {
            if (!targetMetadata.containsKey(entry.getKey())) {
                targetMetadata.put(entry.getKey(), entry.getValue());
            }
        }

        // 处理用户自定义元数据
        if (s3Metadata.containsKey("userMetadata")) {
            Object userMetadataObj = s3Metadata.get("userMetadata");
            if (userMetadataObj instanceof Map) {
                Map<String, String> userMetadata = (Map<String, String>) userMetadataObj;
                // 遍历用户元数据
                for (Map.Entry<String, String> metaEntry : userMetadata.entrySet()) {
                    String metaKey = metaEntry.getKey();
                    String metaValue = metaEntry.getValue();

                    processUserMetadataEntry(targetMetadata, metaKey, metaValue);
                }
            }
        }
    }

    /**
     * 处理单个用户元数据项，包括 Base64 解码和 JSON 解析
     *
     * @param targetMetadata 目标元数据 Map，将被修改
     * @param metaKey 元数据键
     * @param metaValue 元数据值（可能是 Base64 编码的 JSON）
     */
    private void processUserMetadataEntry(Map<String, Object> targetMetadata, String metaKey, String metaValue) {
        // 如果是 Base64 编码的 JSON，则解码并解析
        try {
            if (metaValue != null && !metaValue.isEmpty()) {
                // 尝试 Base64 解码
                String decodedValue = new String(java.util.Base64.getDecoder().decode(metaValue), "UTF-8");

                // 尝试解析 JSON
                try {
                    Map<String, Object> jsonMap = com.dcap.utils.JSON.from(decodedValue).toObject(Map.class);
                    if (jsonMap != null && !jsonMap.isEmpty()) {
                        // 将解析的 JSON 合并到元数据中
                        LOGGER.debug("解析用户元数据 JSON 成功: key={}, 包含 {} 个字段", metaKey, jsonMap.size());
                        targetMetadata.putAll(jsonMap);
                        return; // 已处理完成，返回
                    }
                } catch (Exception e) {
                    // JSON 解析失败，使用原始解码值
                    LOGGER.warn("用户元数据不是有效的 JSON: {}", decodedValue);
                }
                // 如果不是 JSON 打印日志即可
                LOGGER.warn("用户元数据不是 json 结构: key={}, value={}", metaKey, decodedValue);
            }
        } catch (Exception e) {
            // Base64 解码失败，使用原始值
            LOGGER.warn("用户元数据不是有效的 Base64: {}", metaValue);
        }
    }

    /**
     * 获取 S3 对象的元数据
     *
     * @param bucket 存储桶名称
     * @param key 对象键
     * @return 包含对象元数据的 Map
     */
    private Map<String, Object> getS3ObjectMetadata(String bucket, String key) {
        try {
            LOGGER.debug("获取 S3 对象元数据: bucket={}, key={}", bucket, key);

            // 如果 key 为空或为根目录，返回空元数据
            if (key == null || key.isEmpty() || "/".equals(key)) {
                LOGGER.debug("跳过获取元数据: key 为空或根目录");
                return new HashMap<>();
            }

            // 获取对象的完整元数据
            ObjectMetadata objectMetadata = s3Client.getObjectMetadata(bucket, key);

            // 创建元数据映射
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("bucket", bucket);
            metadata.put("key", key);
            metadata.put("contentType", objectMetadata.getContentType());
            metadata.put("contentLength", objectMetadata.getContentLength());
            metadata.put("contentEncoding", objectMetadata.getContentEncoding());
            metadata.put("contentDisposition", objectMetadata.getContentDisposition());
            metadata.put("eTag", objectMetadata.getETag());
            metadata.put("lastModified", objectMetadata.getLastModified());

            // 获取 S3 对象摘要信息
            try {
                List<S3ObjectSummary> summaries = s3Client.listObjectsV2(bucket, key)
                        .getObjectSummaries();
                if (!summaries.isEmpty()) {
                    S3ObjectSummary summary = summaries.get(0);
                    metadata.put("size", summary.getSize());
                    metadata.put("storageClass", summary.getStorageClass());
                }
            } catch (Exception e) {
                LOGGER.debug("获取对象摘要信息失败: {}", e.getMessage());
            }

            // 添加用户自定义元数据
            Map<String, String> userMetadata = objectMetadata.getUserMetadata();
            if (userMetadata != null && !userMetadata.isEmpty()) {
                metadata.put("userMetadata", new HashMap<>(userMetadata));
            }

            return metadata;
        } catch (Exception e) {
            LOGGER.error("获取 S3 对象元数据失败: bucket={}, key={}, error={}", bucket, key, e.getMessage(), e);
            return new HashMap<>(); // 返回空映射，而不是 null
        }
    }

    /**
     * 初始化 S3 客户端，在构造函数中调用
     *
     * @return 配置好的 AmazonS3 客户端
     */
    private AmazonS3 initializeS3Client() {
        // 创建 AWS 凭证
        BasicAWSCredentials awsCredentials = new BasicAWSCredentials(s3AccessKey, s3SecretKey);

        // 配置 S3 客户端
        com.amazonaws.ClientConfiguration clientConfig = new com.amazonaws.ClientConfiguration();
        clientConfig.setProtocol(s3UseHttps ? com.amazonaws.Protocol.HTTPS : com.amazonaws.Protocol.HTTP);

        // 如果启用了 SSE-C，添加必要的头信息到 S3 客户端配置
        if (ssecEnabled) {
            LOGGER.info("为 S3 客户端启用 SSE-C 加密: algorithm={}", ssecAlgorithm);
            clientConfig.withHeader("X-Amz-Server-Side-Encryption-Customer-Algorithm", ssecAlgorithm);
            clientConfig.withHeader("X-Amz-Server-Side-Encryption-Customer-Key", ssecEncryptionKey);
            clientConfig.withHeader("X-Amz-Server-Side-Encryption-Customer-Key-MD5", ssecEncryptionKeyMD5);
            LOGGER.info("已添加 SSE-C HTTP 头到 S3 客户端配置");
        } else {
            LOGGER.info("S3 客户端 SSE-C 未启用，使用标准配置");
        }

        // 创建 S3 客户端
        return AmazonS3ClientBuilder.standard()
            .withCredentials(new AWSStaticCredentialsProvider(awsCredentials))
            .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(
                    hostAndPort, region == null? "cn-beijing":region)) // 区域可以任意设置，使用的是自定义端点
            .withClientConfiguration(clientConfig)
            .withPathStyleAccessEnabled(!isAliyun)
            .disableChunkedEncoding() // 某些 S3 兼容存储可能需要禁用分块编码
            .build();
    }
}