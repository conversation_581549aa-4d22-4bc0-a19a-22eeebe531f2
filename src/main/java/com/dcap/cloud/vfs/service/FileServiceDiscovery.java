package com.dcap.cloud.vfs.service;

import com.dcap.cloud.core.api.Emitter;
import com.dcap.cloud.core.api.Session;
import org.apache.commons.vfs2.FileSystemOptions; // 添加 import

public interface FileServiceDiscovery {

    String service();

    /**
     * 执行文件发现逻辑。
     *
     * @param session 当前会话信息
     * @param emitter 用于发送发现的文件元数据
     * @param options VFS 文件系统选项，包含连接和行为配置
     */
        void discover(Session session, Emitter emitter, FileSystemOptions options);

}
