package com.dcap.cloud.vfs.service;

import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.Emitter;
import com.dcap.cloud.core.api.Session;
import com.dcap.cloud.vfs.plugin.VfsOriginPlugin;
import com.google.common.collect.ImmutableList;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.dcap.common.utils.sm4.SM4Utils;
import com.yd.dcap.probe.client.ProbeClientTaskUtil;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.vfs2.*;
import org.apache.commons.vfs2.auth.StaticUserAuthenticator;
import org.apache.commons.vfs2.impl.DefaultFileSystemConfigBuilder;
import org.apache.commons.vfs2.impl.StandardFileSystemManager;
import org.apache.commons.vfs2.provider.mime.MimeFileProvider;
import org.apache.commons.vfs2.provider.smb.SmbFileProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.util.*;

import static com.dcap.cloud.vfs.report.VfsScanTaskReport.FILE_METADATA_EXTRACTED_COUNT;
import static com.dcap.cloud.vfs.report.VfsScanTaskReport.FILE_TOTAL_COUNT;

/**
 * CIFS 文件服务发现实现。
 * 支持Windows共享、Samba服务器等CIFS/SMB协议的文件系统。
 */
public class CifsFileServiceDiscovery implements FileServiceDiscovery {

    private static final Logger LOGGER = LoggerFactory.getLogger(CifsFileServiceDiscovery.class);

    private static final String FILE_SERVICE_TYPE = "smb";
    private static final int DEFAULT_SMB_PORT = 445;

    private final String endpoint;
    private final int port;
    private final String username;
    private final String password;
    private final String domain;
    private final String rootPath;
    private final StandardFileSystemManager fsManager;
    private final String fileServiceType;
    private final Long fileServiceId;
    private final String fileServiceName;
    private final List<String> scanPathList;
    private final boolean anonymousAccess;

    /**
     * 构造函数，从任务参数初始化 CIFS 配置。
     * @param taskParams 包含配置的任务参数 Map。
     * @throws NullPointerException 如果缺少必要的 CIFS 配置。
     * @throws FileSystemException 如果 VFS 管理器初始化失败。
     */
    public CifsFileServiceDiscovery(Map<String, Object> taskParams) throws FileSystemException {
        List<String> tempScanPathList;
        LOGGER.info("初始化 CIFS 文件服务发现...");

        fileServiceType = (String) taskParams.get("fileServiceType");
        fileServiceName = (String) taskParams.get("fileServiceName");
        fileServiceId = Long.parseLong(Objects.toString(taskParams.get("fileServiceId"), "0"));

        // 从配置中提取 CIFS 相关参数
        this.endpoint = Objects.requireNonNull((String) taskParams.get("endpoint"), "配置缺少 endpoint");
        this.port = Integer.parseInt(Objects.toString(taskParams.get("port"), String.valueOf(DEFAULT_SMB_PORT)));
//        this.shareName = Objects.requireNonNull((String) taskParams.get("shareName"), "配置缺少 shareName");
        this.rootPath = Objects.toString(taskParams.get("rootPath"), "/");

        // 认证参数（可选，支持匿名访问）
        String rawUsername = (String) taskParams.get("username");
        String rawPassword = (String) taskParams.get("secret");

        // 解密后判断是否为匿名访问
        String decryptedUsername = null;
        String decryptedPassword = null;

        if (StringUtils.isNotBlank(rawUsername)) {
            decryptedUsername = SM4Utils.decCheckSm4ForEcb(rawUsername);
        }
        if (StringUtils.isNotBlank(rawPassword)) {
            decryptedPassword = SM4Utils.decCheckSm4ForEcb(rawPassword);
        }

        // 判断是否为匿名访问
        boolean isAnonymousAccess = isAnonymousAccess(decryptedUsername, decryptedPassword);

        if (isAnonymousAccess) {
            this.username = null;
            this.password = null;
            this.anonymousAccess = true;
        } else {
            this.username = decryptedUsername;
            this.password = decryptedPassword;
            this.anonymousAccess = false;
        }

        this.domain = (String) taskParams.get("domain"); // 可选域名

        try {
            tempScanPathList = (List<String>) taskParams.get("scanPathList");
        } catch (Exception e) {
            LOGGER.error("无法获取扫描路径列表");
            tempScanPathList = new ArrayList<>();
        }

        this.scanPathList = tempScanPathList;
        this.fsManager = (StandardFileSystemManager) VFS.getManager();
        if (!fsManager.hasProvider("smb")) {
            fsManager.addProvider("smb", new SmbFileProvider());
        }
        if (!fsManager.hasProvider("mime")) {
            fsManager.addProvider("mime", new MimeFileProvider());
        }
        LOGGER.info("CIFS 文件服务发现初始化完成。服务类型: {}, 主机: {}, 端口: {}, 根路径: {}, 匿名访问: {}",
                fileServiceType, endpoint, port, rootPath, anonymousAccess);
    }

    /**
     * 判断是否为匿名访问
     *
     * @param username 解密后的用户名
     * @param password 解密后的密码
     * @return true 如果是匿名访问，false 否则
     */
    private boolean isAnonymousAccess(String username, String password) {
        // 情况1：用户名密码都为空
        if (StringUtils.isBlank(username) && StringUtils.isBlank(password)) {
            return true;
        }

        // 情况2：用户名密码都是 "anonymous"
        if ("anonymous".equalsIgnoreCase(username) && "anonymous".equalsIgnoreCase(password)) {
            return true;
        }

        return false;
    }

    @Override
    public String service() {
        return FILE_SERVICE_TYPE;
    }

    @Override
    public void discover(Session session, Emitter emitter, FileSystemOptions baseOptions) {
        long execId = session.getJobHistoryId();
        long taskId = session.getTaskId();
        LOGGER.info("服务 [{}] 开始 VFS 发现任务, Task ID: {}, Exec ID: {}", FILE_SERVICE_TYPE, taskId, execId);

        ProbeClientTaskContext probeClientTaskContext = ProbeClientTaskUtil.getInstance()
                .getProbeClientTaskContext(session.getTaskType(), session.getTaskId());

        if (probeClientTaskContext == null) {
            LOGGER.error("服务 [{}] 无法获取 ProbeClientTaskContext, Task ID: {}, Exec ID: {}", FILE_SERVICE_TYPE, taskId, execId);
            // 无法报告状态，只能记录错误
        }

        if (probeClientTaskContext != null) {
            // 报告任务开始
            probeClientTaskContext.reportExecuting(StatusRecord.Position.VfsScanStart, null, "开始 " + FILE_SERVICE_TYPE + " 扫描")
                    .sendToServer();
        }

        // 构建 CIFS 文件系统选项
        final FileSystemOptions taskFileSystemOptions = buildCifsFileSystemOptions(baseOptions);

        // 标准化扫描路径
        List<String> normalizedScanPaths = normalizeScanPaths(scanPathList);
        LOGGER.info("服务 [{}] 标准化后的扫描路径: {}", FILE_SERVICE_TYPE, normalizedScanPaths);

        // 构建完整的 VFS URI
        String fullUriString = buildCifsUri();

        try {
            LOGGER.info("服务 [{}] 使用构建的完整 URI 进行解析: {}", FILE_SERVICE_TYPE,
                    maskPassword(fullUriString));

            if (normalizedScanPaths != null && !normalizedScanPaths.isEmpty()) {
                // 如果指定了扫描路径，则只处理这些路径
                for (String scanPath : normalizedScanPaths) {
                    String fullPath = rootPath;
                    if (!rootPath.endsWith("/") && !scanPath.startsWith("/")) {
                        fullPath += "/";
                    }
                    fullPath += scanPath.startsWith("/") ? scanPath.substring(1) : scanPath;

                    String scanUri = buildCifsUri(fullPath);

                    LOGGER.info("服务 [{}] 扫描指定路径: {}", FILE_SERVICE_TYPE, maskPassword(scanUri));
                    processVfsDirectory(scanUri, taskFileSystemOptions, session, emitter, probeClientTaskContext);
                }
            } else {
                // 如果没有指定扫描路径，则处理整个根路径
                processVfsDirectory(fullUriString, taskFileSystemOptions, session, emitter, probeClientTaskContext);
            }

            if (probeClientTaskContext != null &&  probeClientTaskContext.getTaskStatus() != 3) {
                probeClientTaskContext.reportExecuting(StatusRecord.Position.VfsScanStart, null,
                                "CIFS 文件发现已经完成。")
                        .sendToServer();
            }
        } catch (Exception e) {
            String errorMsg = e.getMessage();
            LOGGER.error("服务 [{}] {}", FILE_SERVICE_TYPE, errorMsg, e);
            if (probeClientTaskContext != null) {
                probeClientTaskContext.reportFailed(StatusRecord.Position.VfsScanStart, null, errorMsg)
                        .sendToServer();
            }
        } finally {
            LOGGER.info("服务 [{}] VFS 发现任务结束, Task ID: {}, Exec ID: {}. ", FILE_SERVICE_TYPE, taskId, execId);
            if (probeClientTaskContext != null && probeClientTaskContext.getTaskStatus() != 3) {
                probeClientTaskContext.reportExecuting(StatusRecord.Position.VfsMetadataPhaseComplete,
                                null, "元数据提取完成。")
                        .sendToServer();
            }
        }
    }

    /**
     * 构建 CIFS URI
     */
    private String buildCifsUri() {
        return buildCifsUri(rootPath);
    }

    /**
     * 构建指定路径的 CIFS URI
     */
    private String buildCifsUri(String path) {
        StringBuilder uriBuilder = new StringBuilder("smb://");

        if (!anonymousAccess && StringUtils.isNotBlank(username)) {
            uriBuilder.append(username);
            if (StringUtils.isNotBlank(password)) {
                uriBuilder.append(":").append(password);
            }
            uriBuilder.append("@");
        }

        uriBuilder.append(endpoint);

        if (port != DEFAULT_SMB_PORT) {
            uriBuilder.append(":").append(port);
        }

        if (StringUtils.isNotBlank(path) && !"/".equals(path)) {
            if (!path.startsWith("/")) {
                uriBuilder.append("/");
            }
            uriBuilder.append(path);
        }

        return uriBuilder.toString();
    }

    /**
     * 掩码密码用于日志输出
     */
    private String maskPassword(String uri) {
        if (anonymousAccess || StringUtils.isBlank(password)) {
            return uri;
        }
        return uri.replace(":" + password + "@", ":***@");
    }

    /**
     * 标准化扫描路径列表
     */
    private List<String> normalizeScanPaths(List<String> paths) {
        if (paths == null || paths.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> normalized = new ArrayList<>();
        for (String path : paths) {
            if (path == null || path.trim().isEmpty()) {
                continue;
            }

            // 确保路径以 / 结尾（用于目录匹配）
            if (!path.endsWith("/")) {
                path = path + "/";
            }

            normalized.add(path);
        }

        return normalized;
    }

    /**
     * 处理VFS目录中的文件
     */
    private void processVfsDirectory(String directoryUri, FileSystemOptions fileSystemOptions,
                                     Session session, Emitter emitter, ProbeClientTaskContext probeClientTaskContext) {
        try (FileObject root = fsManager.resolveFile(directoryUri, fileSystemOptions)) {
            if (!root.exists() || !root.isFolder()) {
                String errorMsg = String.format("VFS 根路径不存在或不是文件夹: %s", maskPassword(directoryUri));
                LOGGER.error("服务 [{}] {}", FILE_SERVICE_TYPE, errorMsg);
                if (probeClientTaskContext != null) {
                    probeClientTaskContext.reportFailed(StatusRecord.Position.VfsScanStart, null, errorMsg);
                }
                return;
            }

            // 使用 Selectors.SELECT_FILES 递归查找所有文件
            FileObject[] files = root.findFiles(Selectors.SELECT_FILES);
            int fileCount = (files != null) ? files.length : 0;
            LOGGER.info("服务 [{}] 在 {} 下找到 {} 个文件", FILE_SERVICE_TYPE, maskPassword(directoryUri), fileCount);

            int processedCount = 0;
            int errorCount = 0;
            if (files == null) {
                return;
            }

            for (FileObject file : files) {
                try (FileObject currentFile = file) {
                    processFile(currentFile, session, emitter, probeClientTaskContext, fileSystemOptions);
                    processedCount++;
                    if (processedCount % 100 == 0) {
                        LOGGER.info("服务 [{}] 已处理 {}/{} 文件。", FILE_SERVICE_TYPE, processedCount, fileCount);
                    }
                } catch (Exception e) {
                    errorCount++;
                    String fileUriStr = "未知文件";
                    try {
                        fileUriStr = file.getName().getURI();
                    } catch (Exception ignored) {
                    }
                    String fileErrorMsg = String.format("处理文件 %s 时出错: %s", maskPassword(fileUriStr), e.getMessage());
                    LOGGER.error("服务 [{}] {}", FILE_SERVICE_TYPE, fileErrorMsg, e);
                    if (probeClientTaskContext != null) {
                        probeClientTaskContext.reportErrorOccurredExecuting(StatusRecord.Position.VfsScanStart, fileUriStr, e.getMessage(), null);
                    }
                }
            }

            if (probeClientTaskContext != null) {
                probeClientTaskContext.recordField(FILE_TOTAL_COUNT, fileCount);
            }
        } catch (FileSystemException e) {
            String errorMsg = String.format("VFS 发现过程中发生文件系统异常 %s: %s", maskPassword(directoryUri), e.getMessage());
            LOGGER.error("服务 [{}] {}", FILE_SERVICE_TYPE, errorMsg, e);
            if (probeClientTaskContext != null) {
                probeClientTaskContext.reportFailed(StatusRecord.Position.VfsScanStart, null, errorMsg).sendToServer();
            }
        } catch (Exception e) {
            String errorMsg = String.format("VFS 发现过程中发生未知异常 %s: %s", maskPassword(directoryUri), e.getMessage());
            LOGGER.error("服务 [{}] {}", FILE_SERVICE_TYPE, errorMsg, e);
            if (probeClientTaskContext != null) {
                probeClientTaskContext.reportFailed(StatusRecord.Position.VfsScanStart, null, errorMsg).sendToServer();
            }
        }
    }

    /**
     * 构建 CIFS 文件系统选项，合并基础选项。
     */
    private FileSystemOptions buildCifsFileSystemOptions(FileSystemOptions baseOptions) {
        // 复制基础选项以避免修改原始对象
        FileSystemOptions opts = (baseOptions != null) ? baseOptions : new FileSystemOptions();

        if (!anonymousAccess && StringUtils.isNotBlank(username)) {
            // 配置用户认证
            StaticUserAuthenticator auth = new StaticUserAuthenticator(domain, username, password);
            DefaultFileSystemConfigBuilder.getInstance().setUserAuthenticator(opts, auth);
            LOGGER.info("配置CIFS认证: 用户={}, 域={}, 密码长度={}", username, domain, password != null ? password.length() : 0);
        } else {
            LOGGER.warn("使用匿名访问CIFS");
        }

        return opts;
    }

    /**
     * 处理单个文件，提取元数据并发送。
     */
    private void processFile(FileObject file, Session session, Emitter emitter,
                             ProbeClientTaskContext context, FileSystemOptions taskFileSystemOptions) throws FileSystemException {
        FileContent content = file.getContent();
        Map<String, Object> attributes = content.getAttributes();
        FileContentInfo contentInfo = content.getContentInfo();
        String contentType = contentInfo.getContentType();
        String contentEncoding = contentInfo.getContentEncoding();

        String fileUri = file.getName().getURI();
        long fileSize = content.getSize();
        long lastModifiedTime = content.getLastModifiedTime();

        LOGGER.debug("服务 [{}] 处理文件: URI={}, 大小={}, 修改时间={}", FILE_SERVICE_TYPE, maskPassword(fileUri), fileSize, lastModifiedTime);

        try {
            // 解析 CIFS URI 获取路径信息
            String protocolPrefix = "smb://";
            if (!anonymousAccess && StringUtils.isNotBlank(username)) {
                protocolPrefix += username;
                if (StringUtils.isNotBlank(password)) {
                    protocolPrefix += ":" + password;
                }
                protocolPrefix += "@";
            }
            protocolPrefix += endpoint;
            if (port != DEFAULT_SMB_PORT) {
                protocolPrefix += ":" + port;
            }
//            protocolPrefix += "/" + shareName;

            // 获取相对路径
            String withoutScheme = fileUri.substring(fileUri.indexOf("://")+3);
            // 找到第一个斜杠，分离 host 和 path
            int firstSlash = withoutScheme.indexOf('/');
            if (firstSlash == -1) {
                LOGGER.warn("服务 [{}] URI没有路径部分: {}", FILE_SERVICE_TYPE, fileUri);
                return;
            }

            String relativePath = withoutScheme.substring(firstSlash); // 包含开头的'/'
            // 获取目录部分（不包含文件名）
            String dirs = relativePath;
            int lastSlash = relativePath.lastIndexOf('/');
            if (lastSlash >= 0 && lastSlash < relativePath.length() - 1) {
                dirs = relativePath.substring(0, lastSlash + 1);
            }

            // 创建基本元数据 Map
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("vfsUri", fileUri);
            metadata.put("jobTenantId", context != null ? context.getTenantId() : 0);
            metadata.put("fileSize", fileSize);
            metadata.put("lastModifiedTime", lastModifiedTime);
            metadata.put("fileServiceType", fileServiceType);
            metadata.put("fileServiceId", fileServiceId);
            metadata.put("fileServiceName", fileServiceName);
            metadata.put("dirs", dirs);

            String fileBaseName = file.getName().getBaseName();
            metadata.put("objectFileName", fileBaseName);
            metadata.put("key", relativePath); // 与其他服务保持一致，使用 key 而不是 path

            // 提取文件扩展名
            String extension = file.getName().getExtension();
            if (StringUtils.isNotBlank(extension)) {
                metadata.put("extension", extension);
            } else if (fileBaseName.lastIndexOf(".") != -1) {
                metadata.put("extension", fileBaseName.substring(fileBaseName.lastIndexOf(".")));
            }

            // 添加任务关联 ID
            metadata.put("execId", session.getJobHistoryId());
            metadata.put("taskId", session.getTaskId());
            metadata.put("taskType", session.getTaskType());
            String fullPath = null;
            try {
                fullPath = URLDecoder.decode(relativePath, Charset.defaultCharset());
            } catch (Exception e) {
                fullPath = relativePath;
            }
            metadata.put("fullPath", fullPath);

            // 包装元数据
            metadata = Collections.unmodifiableMap(UtilMisc.toMap(
                    "fileObjectMetadata", metadata,
                    "vfsOptions", taskFileSystemOptions
            ));

            // 创建并发送 CloudEnvelope
            CloudEnvelope<Map<String, Object>> envelope = new CloudEnvelope<>(session, ImmutableList.of(VfsOriginPlugin.ID), null, metadata);
            emitter.emit(envelope);

            // 记录单个文件元数据提取计数
            if (context != null) {
                context.recordField(FILE_METADATA_EXTRACTED_COUNT, 1);
            }
        } catch (Exception e) {
            LOGGER.error("服务 [{}] 处理文件元数据时出错: {}", FILE_SERVICE_TYPE, maskPassword(fileUri), e);
            throw new FileSystemException("无法处理文件元数据: " + maskPassword(fileUri), e);
        }
    }
}
