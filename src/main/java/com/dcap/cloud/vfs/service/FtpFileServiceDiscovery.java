package com.dcap.cloud.vfs.service;

import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.Emitter;
import com.dcap.cloud.core.api.Session;
import com.dcap.cloud.vfs.plugin.VfsOriginPlugin;
import com.google.common.collect.ImmutableList;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.dcap.common.utils.sm4.SM4Utils;
import com.yd.dcap.probe.client.ProbeClientTaskUtil;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.vfs2.*;
import org.apache.commons.vfs2.provider.ftp.FtpFileSystemConfigBuilder;
import org.apache.commons.vfs2.provider.ftps.FtpsDataChannelProtectionLevel;
import org.apache.commons.vfs2.provider.ftps.FtpsFileSystemConfigBuilder;
import org.apache.commons.vfs2.provider.ftps.FtpsMode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.time.Duration;
import java.util.*;

import static com.dcap.cloud.vfs.report.VfsScanTaskReport.FILE_METADATA_EXTRACTED_COUNT;
import static com.dcap.cloud.vfs.report.VfsScanTaskReport.FILE_TOTAL_COUNT;

/**
 * FTP/FTPS 文件服务发现实现。
 */
public class FtpFileServiceDiscovery implements FileServiceDiscovery {

    private static final Logger LOGGER = LoggerFactory.getLogger(FtpFileServiceDiscovery.class);

    private static final String FILE_SERVICE_TYPE = "ftp";
    private static final int DEFAULT_FTP_PORT = 21;
    private static final int DEFAULT_TIMEOUT = 30000; // 30 seconds

    private final String endpoint;
    private final int port;
    private final String username;
    private final String password;
    private final boolean useTls;
    private final String rootPath;
    private final FileSystemManager fsManager;
    private final String fileServiceType;
    private final Long fileServiceId;
    private final String fileServiceName;
    private final boolean passiveMode;
    private final int timeout;
    private final List<String> scanPathList; // 添加扫描路径列表

    /**
     * 构造函数，从任务参数初始化 FTP 配置。
     * @param taskParams 包含配置的任务参数 Map。
     * @throws NullPointerException 如果缺少必要的 FTP 配置。
     * @throws FileSystemException 如果 VFS 管理器初始化失败。
     */
    public FtpFileServiceDiscovery(Map<String, Object> taskParams) throws FileSystemException {
        List<String> tempScanPathList;
        LOGGER.info("初始化 FTP 文件服务发现...");
        fileServiceType = (String) taskParams.get("fileServiceType");
        fileServiceName = (String) taskParams.get("fileServiceName");
        fileServiceId = Long.parseLong(Objects.toString(taskParams.get("fileServiceId"), "0"));

        // 从配置中提取 FTP 相关参数
        this.endpoint = Objects.requireNonNull((String) taskParams.get("endpoint"), "配置缺少 endpoint");
        this.port = Integer.parseInt(Objects.toString(taskParams.get("port"), String.valueOf(DEFAULT_FTP_PORT)));
        this.username = SM4Utils.decCheckSm4ForEcb(Objects.requireNonNull((String) taskParams.get("username"), "配置缺少 username"));
        this.password = SM4Utils.decCheckSm4ForEcb(Objects.requireNonNull((String) taskParams.get("secret"), "配置缺少 password"));
        // 如果文件服务类型是 ftps，自动将 useTls 设置为 true
        if ("ftps".equalsIgnoreCase(fileServiceType)) {
            this.useTls = true;
        } else {
            // 否则使用配置参数中的 useTls 值（与 S3 保持一致）
            this.useTls = Boolean.parseBoolean(Objects.toString(taskParams.get("useTls"), "false"));
        }
        this.rootPath = Objects.toString(taskParams.get("rootPath"), "/");
        this.passiveMode = Boolean.parseBoolean(Objects.toString(taskParams.get("passiveMode"), "true"));
        this.timeout = Integer.parseInt(Objects.toString(taskParams.get("timeout"), String.valueOf(DEFAULT_TIMEOUT)));

        try {
            tempScanPathList = (List<String>)taskParams.get("scanPathList");
        } catch (Exception e){
            LOGGER.error("无法获取扫描路径列表");
            tempScanPathList = new ArrayList<>();
        }
        
        this.scanPathList = tempScanPathList;
        this.fsManager = VFS.getManager();

        LOGGER.info("FTP 文件服务发现初始化完成。服务类型: {}, 主机: {}, 端口: {}, 使用SSL: {}, 根路径: {}",
                fileServiceType, endpoint, port, useTls, rootPath);
    }

    @Override
    public String service() {
        return FILE_SERVICE_TYPE;
    }

    @Override
    public void discover(Session session, Emitter emitter, FileSystemOptions baseOptions) {
        long execId = session.getJobHistoryId();
        long taskId = session.getTaskId();
        LOGGER.info("服务 [{}] 开始 VFS 发现任务, Task ID: {}, Exec ID: {}", FILE_SERVICE_TYPE, taskId, execId);

        ProbeClientTaskContext probeClientTaskContext = ProbeClientTaskUtil.getInstance()
                .getProbeClientTaskContext(session.getTaskType(), session.getTaskId());

        if (probeClientTaskContext == null) {
            LOGGER.error("服务 [{}] 无法获取 ProbeClientTaskContext, Task ID: {}, Exec ID: {}", FILE_SERVICE_TYPE, taskId, execId);
            // 无法报告状态，只能记录错误
        }

        if (probeClientTaskContext != null){
            // 报告任务开始
            probeClientTaskContext.reportExecuting(StatusRecord.Position.VfsScanStart, null, "开始 " + FILE_SERVICE_TYPE + " 扫描")
                    .sendToServer();
        }


        // 构建 FTP 文件系统选项
        final FileSystemOptions taskFileSystemOptions = buildFtpFileSystemOptions(baseOptions);

        // 标准化扫描路径
        List<String> normalizedScanPaths = normalizeScanPaths(scanPathList);
        LOGGER.info("服务 [{}] 标准化后的扫描路径: {}", FILE_SERVICE_TYPE, normalizedScanPaths);

        // 构建完整的 VFS URI
        String protocol = useTls ? "ftps" : "ftp";
        String fullUriString = String.format("%s://%s:%s@%s:%d%s",
                protocol, username, password, endpoint, port, rootPath);

        try {
            LOGGER.info("服务 [{}] 使用构建的完整 URI 进行解析: {}", FILE_SERVICE_TYPE,
                    String.format("%s://%s:***@%s:%d%s", protocol, username, endpoint, port, rootPath));

            if (normalizedScanPaths != null && !normalizedScanPaths.isEmpty()) {
                // 如果指定了扫描路径，则只处理这些路径
                for (String scanPath : normalizedScanPaths) {
                    String fullPath = rootPath;
                    if (!rootPath.endsWith("/") && !scanPath.startsWith("/")) {
                        fullPath += "/";
                    }
                    fullPath += scanPath.startsWith("/") ? scanPath.substring(1) : scanPath;
                    
                    String scanUri = String.format("%s://%s:%s@%s:%d%s",
                            protocol, username, password, endpoint, port, fullPath);
                    
                    LOGGER.info("服务 [{}] 扫描指定路径: {}", FILE_SERVICE_TYPE, 
                            String.format("%s://%s:***@%s:%d%s", protocol, username, endpoint, port, fullPath));
                    processVfsDirectory(scanUri, taskFileSystemOptions, session, emitter, probeClientTaskContext);
                }
            } else {
                // 如果没有指定扫描路径，则处理整个根路径
                processVfsDirectory(fullUriString, taskFileSystemOptions, session, emitter, probeClientTaskContext);
            }
            
            if (probeClientTaskContext != null) {
                probeClientTaskContext.reportExecuting(StatusRecord.Position.VfsScanStart, null,
                                "FTP 文件发现已经完成。")
                        .sendToServer();
            }
        } catch (Exception e) {
            String errorMsg = e.getMessage();
            LOGGER.error("服务 [{}] {}", FILE_SERVICE_TYPE, errorMsg, e);
            if (probeClientTaskContext != null) {
                probeClientTaskContext.reportFailed(StatusRecord.Position.VfsScanStart, null, errorMsg)
                        .sendToServer();
            }
        } finally {
            LOGGER.info("服务 [{}] VFS 发现任务结束, Task ID: {}, Exec ID: {}. ", FILE_SERVICE_TYPE, taskId, execId);
            if (probeClientTaskContext != null && probeClientTaskContext.getTaskStatus() != 3) {
                probeClientTaskContext.reportExecuting(StatusRecord.Position.VfsMetadataPhaseComplete,
                                null, "元数据提取完成。")
                        .sendToServer();
            }
        }
    }

    /**
     * 标准化扫描路径列表
     * 
     * @param paths 原始路径列表
     * @return 标准化后的路径列表
     */
    private List<String> normalizeScanPaths(List<String> paths) {
        if (paths == null || paths.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<String> normalized = new ArrayList<>();
        for (String path : paths) {
            if (path == null || path.trim().isEmpty()) {
                continue;
            }
            
            // 确保路径以 / 结尾（用于目录匹配）
            if (!path.endsWith("/")) {
                path = path + "/";
            }
            
            normalized.add(path);
        }
        
        return normalized;
    }

    /**
     * 处理VFS目录中的文件
     *
     * @param directoryUri VFS目录 URI
     * @param fileSystemOptions VFS文件系统选项
     * @param session 会话
     * @param emitter 发射器
     * @param probeClientTaskContext 任务上下文
     */
    private void processVfsDirectory(String directoryUri, FileSystemOptions fileSystemOptions,
            Session session, Emitter emitter, ProbeClientTaskContext probeClientTaskContext) {
        try (FileObject root = fsManager.resolveFile(directoryUri, fileSystemOptions)) {
            if (!root.exists() || !root.isFolder()) {
                String errorMsg = String.format("VFS 根路径不存在或不是文件夹: %s", directoryUri);
                LOGGER.error("服务 [{}] {}", FILE_SERVICE_TYPE, errorMsg);
                if (probeClientTaskContext != null) {
                    probeClientTaskContext.reportFailed(StatusRecord.Position.VfsScanStart, null, errorMsg);
                }
                return;
            }

            // 使用 Selectors.SELECT_ALL_DESCENDANTS 递归查找所有文件
            FileObject[] files = root.findFiles(Selectors.SELECT_FILES);
            int fileCount = (files != null) ? files.length : 0;
            LOGGER.info("服务 [{}] 在 {} 下找到 {} 个文件", FILE_SERVICE_TYPE, directoryUri, fileCount);

            int processedCount = 0;
            int errorCount = 0;
            if (files == null) {
                return;
            }

            for (FileObject file : files) {
                try (FileObject currentFile = file) {
                    processFile(currentFile, session, emitter, probeClientTaskContext, fileSystemOptions);
                    processedCount++;
                    if (processedCount % 100 == 0) {
                        LOGGER.info("服务 [{}] 已处理 {}/{} 文件。", FILE_SERVICE_TYPE, processedCount, fileCount);
                    }
                } catch (Exception e) {
                    errorCount++;
                    String fileUriStr = "未知文件";
                    try { fileUriStr = file.getName().getURI(); } catch (Exception ignored) {}
                    String fileErrorMsg = String.format("处理文件 %s 时出错: %s", fileUriStr, e.getMessage());
                    LOGGER.error("服务 [{}] {}", FILE_SERVICE_TYPE, fileErrorMsg, e);
                    if (probeClientTaskContext != null) {
                        probeClientTaskContext.reportErrorOccurredExecuting(StatusRecord.Position.VfsScanStart, fileUriStr, e.getMessage(), null);
                    }
                }
            }

            if (probeClientTaskContext != null){
                probeClientTaskContext.recordField(FILE_TOTAL_COUNT, fileCount);
            }
        } catch (FileSystemException e) {
            String errorMsg = String.format("VFS 发现过程中发生文件系统异常 %s: %s", directoryUri, e.getMessage());
            LOGGER.error("服务 [{}] {}", FILE_SERVICE_TYPE, errorMsg, e);
            if (probeClientTaskContext != null) {
                probeClientTaskContext.reportFailed(StatusRecord.Position.VfsScanStart, null, errorMsg).sendToServer();
            }
        } catch (Exception e) {
            String errorMsg = String.format("VFS 发现过程中发生未知异常 %s: %s", directoryUri, e.getMessage());
            LOGGER.error("服务 [{}] {}", FILE_SERVICE_TYPE, errorMsg, e);
            if (probeClientTaskContext != null) {
                probeClientTaskContext.reportFailed(StatusRecord.Position.VfsScanStart, null, errorMsg).sendToServer();
            }
        }
    }

    /**
     * 构建 FTP 文件系统选项，合并基础选项。
     * @param baseOptions 基础文件系统选项
     * @return 配置好的 FTP FileSystemOptions
     */
    private FileSystemOptions buildFtpFileSystemOptions(FileSystemOptions baseOptions) {
        String ftpCharset = System.getenv().get("ftp_charset");
        if (StringUtils.isBlank(ftpCharset)) {
            ftpCharset = System.getenv("FTP_CHARSET");
        }
        if (StringUtils.isBlank(ftpCharset)){
            ftpCharset  = "UTF-8";
        }
        // 复制基础选项以避免修改原始对象
        FileSystemOptions opts = (baseOptions != null) ? baseOptions : new FileSystemOptions();
        if (useTls) {
            // FTPS 配置
            FtpsFileSystemConfigBuilder builder = FtpsFileSystemConfigBuilder.getInstance();
            builder.setPassiveMode(opts, passiveMode);
            builder.setUserDirIsRoot(opts, false);

            // 使用 Duration 设置超时，而不是直接使用 Integer
            builder.setDataTimeout(opts, Duration.ofMillis(timeout));
            builder.setSoTimeout(opts, Duration.ofMillis(timeout));
            builder.setConnectTimeout(opts, Duration.ofMillis(timeout));

            // 设置无条件信任任何地址，不进行 SSL 证书校验
            builder.setKeyManager(opts, null);
            // 使用 TrustManagerUtils.getAcceptAllTrustManager() 信任所有证书
            builder.setTrustManager(opts, org.apache.commons.net.util.TrustManagerUtils.getAcceptAllTrustManager());
            // 禁用远程验证（继承自 FtpFileSystemConfigBuilder）
            builder.setRemoteVerification(opts, false);

            // 设置 FTP 连接模式，先登录再启用 SSL/TLS
            builder.setFtpsMode(opts, FtpsMode.EXPLICIT);

            // 设置使用 SSL 保护数据传输
            builder.setDataChannelProtectionLevel(opts, FtpsDataChannelProtectionLevel.P);

            builder.setControlEncoding(opts, ftpCharset);
        } else {
            // FTP 配置
            FtpFileSystemConfigBuilder builder = FtpFileSystemConfigBuilder.getInstance();
            builder.setPassiveMode(opts, passiveMode);
            builder.setUserDirIsRoot(opts, false);

            // 使用 Duration 设置超时，而不是直接使用 Integer
            builder.setDataTimeout(opts, Duration.ofMillis(timeout));
            builder.setSoTimeout(opts, Duration.ofMillis(timeout));
            builder.setConnectTimeout(opts, Duration.ofMillis(timeout));
            builder.setControlEncoding(opts, ftpCharset);
        }

        return opts;
    }

    /**
     * 处理单个文件，提取元数据并发送。
     * @param file 文件对象
     * @param session 会话
     * @param emitter 发射器
     * @param context 任务上下文用于报告
     * @param taskFileSystemOptions 文件系统选项
     * @throws FileSystemException 文件系统异常
     */
    private void processFile(FileObject file, Session session, Emitter emitter,
                            ProbeClientTaskContext context, FileSystemOptions taskFileSystemOptions) throws FileSystemException {
        FileContent content = file.getContent();
        Map<String, Object> attributes = content.getAttributes();
        FileContentInfo contentInfo = content.getContentInfo();
        String contentType = contentInfo.getContentType();
        String contentEncoding = contentInfo.getContentEncoding();

        String fileUri = file.getName().getURI();
        long fileSize = content.getSize();
        long lastModifiedTime = content.getLastModifiedTime();

        LOGGER.debug("服务 [{}] 处理文件: URI={}, 大小={}, 修改时间={}", FILE_SERVICE_TYPE, fileUri, fileSize, lastModifiedTime);

        try {
            // 解析 FTP URI 获取路径信息
            String protocol = useTls ? "ftps" : "ftp";
            String protocolPrefix = protocol + "://" + username + ":" + password + "@" + endpoint + ":" + port;

            // 获取相对路径
            String withoutScheme = fileUri.substring(fileUri.indexOf("://")+3);
            // 找到第一个斜杠，分离 host 和 path
            int firstSlash = withoutScheme.indexOf('/');
            if (firstSlash == -1) {
                LOGGER.warn("服务 [{}] URI没有路径部分: {}", FILE_SERVICE_TYPE, fileUri);
                return;
            }
            String relativePath = withoutScheme.substring(firstSlash); // 包含开头的'/'
            // 获取目录部分（不包含文件名）
            String dirs = relativePath;
            int lastSlash = relativePath.lastIndexOf('/');
            if (lastSlash >= 0 && lastSlash < relativePath.length() - 1) {
                dirs = relativePath.substring(0, lastSlash + 1);
            }

            // 创建基本元数据 Map
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("vfsUri", fileUri);
            metadata.put("jobTenantId", context != null? context.getTenantId(): 0);
            metadata.put("fileSize", fileSize);
            metadata.put("lastModifiedTime", lastModifiedTime);
            metadata.put("fileServiceType", fileServiceType);
            metadata.put("fileServiceId", fileServiceId);
            metadata.put("fileServiceName", fileServiceName);
            metadata.put("dirs", dirs);

            String fileBaseName = file.getName().getBaseName();
            metadata.put("objectFileName", fileBaseName);
            metadata.put("key", relativePath); // 与 S3 保持一致，使用 key 而不是 path

            // 提取文件扩展名
            String extension = file.getName().getExtension();
            if (StringUtils.isNotBlank(extension)){
                metadata.put("extension", extension);
            } else if (fileBaseName.lastIndexOf(".") != -1){
                metadata.put("extension", fileBaseName.substring(fileBaseName.lastIndexOf(".")));
            }

            // 添加任务关联 ID
            metadata.put("execId", session.getJobHistoryId());
            metadata.put("taskId", session.getTaskId());
            metadata.put("taskType", session.getTaskType());
            String fullPath = null;
            try {
                fullPath = URLDecoder.decode(relativePath, Charset.defaultCharset());
            } catch (Exception e) {
                fullPath = relativePath;
            }
            metadata.put("fullPath", fullPath);

            // 包装元数据
            metadata = Collections.unmodifiableMap(UtilMisc.toMap(
                    "fileObjectMetadata", metadata,
                    "vfsOptions", taskFileSystemOptions
            ));

            // 创建并发送 CloudEnvelope
            CloudEnvelope<Map<String, Object>> envelope = new CloudEnvelope<>(session, ImmutableList.of(VfsOriginPlugin.ID), null, metadata);
            emitter.emit(envelope);

            // 记录单个文件元数据提取计数
            if (context != null) {
                context.recordField(FILE_METADATA_EXTRACTED_COUNT, 1);
            }
        } catch (Exception e) {
            LOGGER.error("服务 [{}] 处理文件元数据时出错: {}", FILE_SERVICE_TYPE, fileUri, e);
            throw new FileSystemException("无法处理文件元数据: " + fileUri, e);
        }
    }
}
