package com.dcap.cloud.vfs.service;

import com.dcap.cloud.core.api.CloudEnvelope;
import com.dcap.cloud.core.api.Emitter;
import com.dcap.cloud.core.api.Session;
import com.dcap.cloud.vfs.plugin.VfsOriginPlugin;
import com.google.common.collect.ImmutableList;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.dcap.common.utils.sm4.SM4Utils;
import com.yd.dcap.probe.client.ProbeClientTaskUtil;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.vfs2.*;
import org.apache.commons.vfs2.provider.sftp.IdentityInfo;
import org.apache.commons.vfs2.provider.sftp.SftpFileSystemConfigBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.*;

import static com.dcap.cloud.vfs.report.VfsScanTaskReport.FILE_METADATA_EXTRACTED_COUNT;
import static com.dcap.cloud.vfs.report.VfsScanTaskReport.FILE_TOTAL_COUNT;

/**
 * SFTP 文件服务发现实现。
 */
public class SftpFileServiceDiscovery implements FileServiceDiscovery {

    private static final Logger LOGGER = LoggerFactory.getLogger(SftpFileServiceDiscovery.class);

    private static final String FILE_SERVICE_TYPE = "sftp";
    private static final int DEFAULT_SFTP_PORT = 22;
    private static final int DEFAULT_TIMEOUT = 30000; // 30 seconds

    private final String endpoint;
    private final int port;
    private final String username;
    private final String password;
    private final String privateKeyPath;
    private final String privateKeyPassphrase;
    private final String rootPath;
    private final FileSystemManager fsManager;
    private final String fileServiceType;
    private final Long fileServiceId;
    private final String fileServiceName;
    private final int timeout;
    private final boolean strictHostKeyChecking;
    private final List<String> scanPathList; // 添加扫描路径列表

    /**
     * 构造函数，从任务参数初始化 SFTP 配置。
     * @param taskParams 包含配置的任务参数 Map。
     * @throws NullPointerException 如果缺少必要的 SFTP 配置。
     * @throws FileSystemException 如果 VFS 管理器初始化失败。
     */
    public SftpFileServiceDiscovery(Map<String, Object> taskParams) throws FileSystemException {
        List<String> tempScanPathList;
        LOGGER.info("初始化 SFTP 文件服务发现...");
        fileServiceType = (String) taskParams.get("fileServiceType");
        fileServiceName = (String) taskParams.get("fileServiceName");
        fileServiceId = Long.parseLong(Objects.toString(taskParams.get("fileServiceId"), "0"));

        // 从配置中提取 SFTP 相关参数
        this.endpoint = Objects.requireNonNull((String) taskParams.get("endpoint"), "配置缺少 endpoint");
        this.port = Integer.parseInt(Objects.toString(taskParams.get("port"), String.valueOf(DEFAULT_SFTP_PORT)));
        this.username = SM4Utils.decCheckSm4ForEcb(Objects.requireNonNull((String) taskParams.get("username"), "配置缺少 username"));

        // 支持密码或私钥认证
        this.password = SM4Utils.decCheckSm4ForEcb((String) taskParams.get("secret"));
        this.privateKeyPath = (String) taskParams.get("privateKeyPath");
        this.privateKeyPassphrase = (String) taskParams.get("privateKeyPassphrase");

        // 如果密码和私钥都没有提供，则抛出异常
        if (StringUtils.isBlank(password) && StringUtils.isBlank(privateKeyPath)) {
            throw new NullPointerException("配置缺少认证信息，需要提供 secret 或 privateKeyPath");
        }

        this.rootPath = Objects.toString(taskParams.get("rootPath"), "/");
        this.timeout = Integer.parseInt(Objects.toString(taskParams.get("timeout"), String.valueOf(DEFAULT_TIMEOUT)));
        this.strictHostKeyChecking = Boolean.parseBoolean(Objects.toString(taskParams.get("strictHostKeyChecking"), "false"));

        try {
            tempScanPathList = (List<String>)taskParams.get("scanPathList");
        } catch (Exception e){
            LOGGER.error("无法获取扫描路径列表");
            tempScanPathList = new ArrayList<>();
        }
        
        this.scanPathList = tempScanPathList;
        this.fsManager = VFS.getManager();

        System.setProperty("vfs.sftp.sshdir","/dcap-config/probe-client/probe-client");
        LOGGER.info("SFTP 文件服务发现初始化完成。服务类型: {}, 主机: {}, 端口: {}, 根路径: {}, 严格主机密钥检查: {}",
                fileServiceType, endpoint, port, rootPath, strictHostKeyChecking);
    }

    @Override
    public String service() {
        return FILE_SERVICE_TYPE;
    }

    @Override
    public void discover(Session session, Emitter emitter, FileSystemOptions baseOptions) {
        long execId = session.getJobHistoryId();
        long taskId = session.getTaskId();
        LOGGER.info("服务 [{}] 开始 VFS 发现任务, Task ID: {}, Exec ID: {}", FILE_SERVICE_TYPE, taskId, execId);

        ProbeClientTaskContext probeClientTaskContext = ProbeClientTaskUtil.getInstance()
                .getProbeClientTaskContext(session.getTaskType(), session.getTaskId());

        if (probeClientTaskContext == null) {
            LOGGER.error("服务 [{}] 无法获取 ProbeClientTaskContext, Task ID: {}, Exec ID: {}", FILE_SERVICE_TYPE, taskId, execId);
            // 无法报告状态，只能记录错误
        }

        if (probeClientTaskContext != null){
            // 报告任务开始
            probeClientTaskContext.reportExecuting(StatusRecord.Position.VfsScanStart, null, "开始 " + FILE_SERVICE_TYPE + " 扫描")
                    .sendToServer();
        }

        // 构建 SFTP 文件系统选项
        final FileSystemOptions taskFileSystemOptions = buildSftpFileSystemOptions(baseOptions);

        // 标准化扫描路径
        List<String> normalizedScanPaths = normalizeScanPaths(scanPathList);
        LOGGER.info("服务 [{}] 标准化后的扫描路径: {}", FILE_SERVICE_TYPE, normalizedScanPaths);

        // 构建完整的 VFS URI
        String fullUriString = String.format("sftp://%s@%s:%d%s",
                username, endpoint, port, rootPath);

        try {
            LOGGER.info("服务 [{}] 使用构建的完整 URI 进行解析: {}", FILE_SERVICE_TYPE,
                    String.format("sftp://%s@%s:%d%s", username, endpoint, port, rootPath));

            if (normalizedScanPaths != null && !normalizedScanPaths.isEmpty()) {
                // 如果指定了扫描路径，则只处理这些路径
                for (String scanPath : normalizedScanPaths) {
                    String fullPath = rootPath;
                    if (!rootPath.endsWith("/") && !scanPath.startsWith("/")) {
                        fullPath += "/";
                    }
                    fullPath += scanPath.startsWith("/") ? scanPath.substring(1) : scanPath;
                    
                    String scanUri = String.format("sftp://%s@%s:%d%s",
                            username, endpoint, port, fullPath);
                    
                    LOGGER.info("服务 [{}] 扫描指定路径: {}", FILE_SERVICE_TYPE, scanUri);
                    processVfsDirectory(scanUri, taskFileSystemOptions, session, emitter, probeClientTaskContext);
                }
            } else {
                // 如果没有指定扫描路径，则处理整个根路径
                processVfsDirectory(fullUriString, taskFileSystemOptions, session, emitter, probeClientTaskContext);
            }

            if (probeClientTaskContext != null) {
                probeClientTaskContext.reportExecuting(StatusRecord.Position.VfsScanStart, null,
                                "SFTP 文件发现已经完成。")
                        .sendToServer();
            }
        } catch (Exception e) {
            String errorMsg = e.getMessage();
            LOGGER.error("服务 [{}] {}", FILE_SERVICE_TYPE, errorMsg, e);
            if (probeClientTaskContext != null) {
                probeClientTaskContext.reportFailed(StatusRecord.Position.VfsScanStart, null, errorMsg)
                        .sendToServer();
            }
        } finally {
            LOGGER.info("sftp 服务 [{}] VFS 发现任务结束, Task ID: {}, Exec ID: {}. ", FILE_SERVICE_TYPE, taskId, execId);
            if (probeClientTaskContext != null && probeClientTaskContext.getTaskStatus() != 3) {
                probeClientTaskContext.reportExecuting(StatusRecord.Position.VfsMetadataPhaseComplete,
                                null, "元数据提取完成。")
                        .sendToServer();
            }
        }
    }

    /**
     * 标准化扫描路径列表
     * 
     * @param paths 原始路径列表
     * @return 标准化后的路径列表
     */
    private List<String> normalizeScanPaths(List<String> paths) {
        if (paths == null || paths.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<String> normalized = new ArrayList<>();
        for (String path : paths) {
            if (path == null || path.trim().isEmpty()) {
                continue;
            }
            
            // 确保路径以 / 结尾（用于目录匹配）
            if (!path.endsWith("/")) {
                path = path + "/";
            }
            
            normalized.add(path);
        }
        
        return normalized;
    }
    
    /**
     * 检查文件路径是否应该被扫描（基于扫描路径列表）
     * 
     * @param filePath 文件路径
     * @param scanPaths 扫描路径列表
     * @return 如果文件应该被扫描则返回true
     */
    private boolean shouldScanFilePath(String filePath, List<String> scanPaths) {
        // 如果没有扫描路径，则扫描所有文件
        if (scanPaths == null || scanPaths.isEmpty()) {
            return true;
        }
        
        // 添加结尾的 / 以便目录匹配
        String filePathForDirMatch = filePath;
        if (!filePathForDirMatch.endsWith("/")) {
            filePathForDirMatch = filePathForDirMatch + "/";
        }
        
        // 检查文件路径是否在任何扫描路径下
        for (String scanPath : scanPaths) {
            if (filePathForDirMatch.equals(scanPath) || filePathForDirMatch.startsWith(scanPath) || scanPath.startsWith(filePathForDirMatch)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 处理VFS目录中的文件
     *
     * @param directoryUri VFS目录 URI
     * @param fileSystemOptions VFS文件系统选项
     * @param session 会话
     * @param emitter 发射器
     * @param probeClientTaskContext 任务上下文
     */
    private void processVfsDirectory(String directoryUri, FileSystemOptions fileSystemOptions,
            Session session, Emitter emitter, ProbeClientTaskContext probeClientTaskContext) {
        try (FileObject root = fsManager.resolveFile(directoryUri, fileSystemOptions)) {
            if (!root.exists() || !root.isFolder()) {
                String errorMsg = String.format("VFS 根路径不存在或不是文件夹: %s", directoryUri);
                LOGGER.error("服务 [{}] {}", FILE_SERVICE_TYPE, errorMsg);
                if (probeClientTaskContext != null) {
                    probeClientTaskContext.reportFailed(StatusRecord.Position.VfsScanStart, null, errorMsg);
                }
                return;
            }

            // 使用 Selectors.SELECT_ALL_DESCENDANTS 递归查找所有文件
            FileObject[] files = root.findFiles(Selectors.SELECT_FILES);
            int fileCount = (files != null) ? files.length : 0;
            LOGGER.info("服务 [{}] 在 {} 下找到 {} 个文件", FILE_SERVICE_TYPE, directoryUri, fileCount);

            int processedCount = 0;
            int errorCount = 0;
            if (files == null) {
                return;
            }

            for (FileObject file : files) {
                try (FileObject currentFile = file) {
                    processFile(currentFile, session, emitter, probeClientTaskContext, fileSystemOptions);
                    processedCount++;
                    if (processedCount % 100 == 0) {
                        LOGGER.info("服务 [{}] 已处理 {}/{} 文件。", FILE_SERVICE_TYPE, processedCount, fileCount);
                    }
                } catch (Exception e) {
                    errorCount++;
                    String fileUriStr = "未知文件";
                    try { fileUriStr = file.getName().getURI(); } catch (Exception ignored) {}
                    String fileErrorMsg = String.format("处理文件 %s 时出错: %s", fileUriStr, e.getMessage());
                    LOGGER.error("服务 [{}] {}", FILE_SERVICE_TYPE, fileErrorMsg, e);
                    if (probeClientTaskContext != null) {
                        probeClientTaskContext.reportErrorOccurredExecuting(StatusRecord.Position.VfsScanStart, fileUriStr, e.getMessage(), null);
                    }
                }
            }

            if (probeClientTaskContext != null) {
                probeClientTaskContext.recordField(FILE_TOTAL_COUNT, fileCount);
            }
        } catch (FileSystemException e) {
            String errorMsg = String.format("VFS 发现过程中发生文件系统异常 %s: %s", directoryUri, e.getMessage());
            LOGGER.error("服务 [{}] {}", FILE_SERVICE_TYPE, errorMsg, e);
            if (probeClientTaskContext != null) {
                probeClientTaskContext.reportFailed(StatusRecord.Position.VfsScanStart, null, errorMsg).sendToServer();
            }
        } catch (Exception e) {
            String errorMsg = String.format("VFS 发现过程中发生未知异常 %s: %s", directoryUri, e.getMessage());
            LOGGER.error("服务 [{}] {}", FILE_SERVICE_TYPE, errorMsg, e);
            if (probeClientTaskContext != null) {
                probeClientTaskContext.reportFailed(StatusRecord.Position.VfsScanStart, null, errorMsg).sendToServer();
            }
        }
    }

    /**
     * 构建 SFTP 文件系统选项，合并基础选项。
     * @param baseOptions 基础文件系统选项
     * @return 配置好的 SFTP FileSystemOptions
     */
    private FileSystemOptions buildSftpFileSystemOptions(FileSystemOptions baseOptions) {
        // 复制基础选项以避免修改原始对象
        FileSystemOptions opts = (baseOptions != null) ? baseOptions : new FileSystemOptions();

        SftpFileSystemConfigBuilder builder = SftpFileSystemConfigBuilder.getInstance();

        // 设置基本连接参数
        builder.setUserDirIsRoot(opts, false);

        // 使用 Duration 设置超时，而不是直接使用 Integer
        builder.setSessionTimeout(opts, Duration.ofMillis(timeout));
        builder.setConnectTimeout(opts, Duration.ofMillis(timeout));

        try {
            builder.setStrictHostKeyChecking(opts, strictHostKeyChecking ? "yes" : "no");
        } catch (FileSystemException e) {
            throw new RuntimeException(e);
        }

        // 设置认证方式
        if (StringUtils.isNotBlank(password)) {
            // 使用密码认证
            LOGGER.info("服务 [{}] 使用密码认证", FILE_SERVICE_TYPE);
            builder.setPreferredAuthentications(opts, "password,keyboard-interactive");

            // 重要：强制禁用私钥认证，避免出现 invalid privatekey 错误
            builder.setIdentities(opts, new File[0]);

            // 设置 UserInfo 提供密码
            com.jcraft.jsch.UserInfo userInfo = new com.jcraft.jsch.UserInfo() {
                @Override
                public String getPassphrase() {
                    return null;
                }

                @Override
                public String getPassword() {
                    return password;
                }

                @Override
                public boolean promptPassword(String message) {
                    return true; // 返回 true 表示已提供密码
                }

                @Override
                public boolean promptPassphrase(String message) {
                    return false;
                }

                @Override
                public boolean promptYesNo(String message) {
                    return true; // 自动接受所有提示
                }

                @Override
                public void showMessage(String message) {
                    LOGGER.debug("服务 [{}] JSch 消息: {}", FILE_SERVICE_TYPE, message);
                }
            };
            builder.setUserInfo(opts, userInfo);
        } else if (StringUtils.isNotBlank(privateKeyPath)) {
            // 使用私钥认证
            File privateKey = new File(privateKeyPath);
            if (privateKey.exists() && privateKey.isFile()) {
                LOGGER.info("服务 [{}] 使用私钥认证，私钥文件: {}", FILE_SERVICE_TYPE, privateKeyPath);
                builder.setPreferredAuthentications(opts, "publickey,keyboard-interactive,password");

                try {
                    // 设置私钥文件
                    if (StringUtils.isNotBlank(privateKeyPassphrase)) {
                        LOGGER.info("服务 [{}] 私钥文件需要密码", FILE_SERVICE_TYPE);

                        // 使用 IdentityInfo 类来设置私钥和密码
                        IdentityInfo identityInfo = new IdentityInfo(
                                privateKey,
                                privateKeyPassphrase.getBytes(StandardCharsets.UTF_8)
                        );
                        builder.setIdentityInfo(opts, identityInfo);

                        // 设置 UserInfo 提供密码
                        com.jcraft.jsch.UserInfo userInfo = new com.jcraft.jsch.UserInfo() {
                            @Override
                            public String getPassphrase() {
                                return privateKeyPassphrase;
                            }

                            @Override
                            public String getPassword() {
                                return null;
                            }

                            @Override
                            public boolean promptPassword(String message) {
                                return false;
                            }

                            @Override
                            public boolean promptPassphrase(String message) {
                                return true; // 返回 true 表示已提供密码
                            }

                            @Override
                            public boolean promptYesNo(String message) {
                                return true; // 自动接受所有提示
                            }

                            @Override
                            public void showMessage(String message) {
                                LOGGER.debug("服务 [{}] JSch 消息: {}", FILE_SERVICE_TYPE, message);
                            }
                        };
                        builder.setUserInfo(opts, userInfo);
                    } else {
                        // 不需要密码的私钥
                        builder.setIdentities(opts, privateKey);
                    }
                } catch (Exception e) {
                    LOGGER.error("服务 [{}] 设置私钥时出错: {}", FILE_SERVICE_TYPE, e.getMessage(), e);
                    // 如果私钥设置失败，尝试使用密码认证作为备选
                    builder.setPreferredAuthentications(opts, "keyboard-interactive,password");
                }
            } else {
                LOGGER.warn("服务 [{}] 私钥文件不存在或不是文件: {}", FILE_SERVICE_TYPE, privateKeyPath);
                // 如果私钥文件不存在，尝试使用密码认证作为备选
                builder.setPreferredAuthentications(opts, "keyboard-interactive,password");
            }
        } else {
            LOGGER.warn("服务 [{}] 未提供密码或私钥，将尝试使用其他认证方式", FILE_SERVICE_TYPE);
            // 如果没有提供密码或私钥，尝试使用其他认证方式
            builder.setPreferredAuthentications(opts, "keyboard-interactive,password,publickey");
        }

        return opts;
    }

    /**
     * 处理单个文件，提取元数据并发送。
     * @param file 文件对象
     * @param session 会话
     * @param emitter 发射器
     * @param context 任务上下文用于报告
     * @param taskFileSystemOptions 文件系统选项
     * @throws FileSystemException 文件系统异常
     */
    private void processFile(FileObject file, Session session, Emitter emitter,
                            ProbeClientTaskContext context, FileSystemOptions taskFileSystemOptions) throws FileSystemException {
        FileContent content = file.getContent();
        Map<String, Object> attributes = content.getAttributes();
        FileContentInfo contentInfo = content.getContentInfo();
        String contentType = contentInfo.getContentType();
        String contentEncoding = contentInfo.getContentEncoding();

        String fileUri = file.getName().getURI();
        long fileSize = content.getSize();
        long lastModifiedTime = content.getLastModifiedTime();

        LOGGER.debug("服务 [{}] 处理文件: URI={}, 大小={}, 修改时间={}", FILE_SERVICE_TYPE, fileUri, fileSize, lastModifiedTime);

        try {
            // 解析 SFTP URI 获取路径信息
            String protocolPrefix = "sftp://" + username + "@" + endpoint + ":" + port;

            // 获取相对路径
            String relativePath = fileUri;
            if (fileUri.startsWith(protocolPrefix)) {
                relativePath = fileUri.substring(protocolPrefix.length());
            }

            // 检查文件是否在扫描路径内
//            List<String> normalizedScanPaths = normalizeScanPaths(scanPathList);
//            if (!shouldScanFilePath(relativePath, normalizedScanPaths)) {
//                LOGGER.debug("服务 [{}] 跳过不在扫描路径中的文件: {}", FILE_SERVICE_TYPE, fileUri);
//                return;
//            }

            // 获取目录部分（不包含文件名）
            String dirs = relativePath;
            int lastSlash = relativePath.lastIndexOf('/');
            if (lastSlash >= 0 && lastSlash < relativePath.length() - 1) {
                dirs = relativePath.substring(0, lastSlash + 1);
            }

            // 创建基本元数据 Map
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("vfsUri", fileUri);
            metadata.put("jobTenantId", context != null?context.getTenantId():0);
            metadata.put("fileSize", fileSize);
            metadata.put("lastModifiedTime", lastModifiedTime);
            metadata.put("fileServiceType", fileServiceType);
            metadata.put("fileServiceId", fileServiceId);
            metadata.put("fileServiceName", fileServiceName);
            metadata.put("dirs", dirs);

            String fileBaseName = file.getName().getBaseName();
            metadata.put("objectFileName", fileBaseName);
            metadata.put("key", relativePath); // 与 S3 保持一致，使用 key 而不是 path

            // 提取文件扩展名
            String extension = file.getName().getExtension();
            if (StringUtils.isNotBlank(extension)){
                metadata.put("extension", extension);
            } else if (fileBaseName.lastIndexOf(".") != -1){
                metadata.put("extension", fileBaseName.substring(fileBaseName.lastIndexOf(".")));
            }

            // 添加任务关联 ID
            metadata.put("execId", session.getJobHistoryId());
            metadata.put("taskId", session.getTaskId());
            metadata.put("taskType", session.getTaskType());
            String fullPath = null;
            try {
                fullPath = URLDecoder.decode(relativePath, Charset.defaultCharset());
            } catch (Exception e) {
                fullPath = relativePath;
            }
            metadata.put("fullPath", fullPath);

            // 包装元数据
            metadata = Collections.unmodifiableMap(UtilMisc.toMap(
                    "fileObjectMetadata", metadata,
                    "vfsOptions", taskFileSystemOptions
            ));

            // 创建并发送 CloudEnvelope
            CloudEnvelope<Map<String, Object>> envelope = new CloudEnvelope<>(session, ImmutableList.of(VfsOriginPlugin.ID), null, metadata);
            emitter.emit(envelope);

            if (context != null) {
                // 记录单个文件元数据提取计数
                context.recordField(FILE_METADATA_EXTRACTED_COUNT, 1);
            }
        } catch (Exception e) {
            LOGGER.error("服务 [{}] 处理文件元数据时出错: {}", FILE_SERVICE_TYPE, fileUri, e);
            throw new FileSystemException("无法处理文件元数据: " + fileUri, e);
        }
    }
}
