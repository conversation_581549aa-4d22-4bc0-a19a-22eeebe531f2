package com.dcap.cloud.vfs.report;

import com.dcap.classifier.Clock;
import com.dcap.utils.JSON;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.classifier.taskreport.TaskReport;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.dcap.probe.TaskResultReporter;
import com.yd.dcap.probe.entities.ProbeClientJobResult;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import org.apache.commons.lang3.StringUtils;

import java.time.Duration;
import java.time.Instant;
import java.util.*;

/**
 * VFS 扫描任务的自定义报告实现。
 * 负责将状态记录和字段记录转发给 TaskResultReporter (MqManager)。
 */
public class VfsScanTaskReport implements TaskReport {

    public static final String FILE_TOTAL_COUNT="fileTotalCount", FILE_METADATA_EXTRACTED_COUNT="fileMetadataExtractedCount",
            FILE_CONTENT_EXTRACTED_COUNT="fileContentExtractedCount", FILE_CONTENT_RECOGNITION_COUNT="fileContentRecognitionCount",
            FILE_METADATA_KAFKA_SENT="fileMetadataKafkaSent";

    private int fileTotalCount = 0;

    private int fileMetadataExtractedCount = 0;

    private int fileContentExtractedCount = 0;

    private int fileContentRecognitionCount = 0;

    private int fileMetadataKafkaSent = 0;

    final Instant start = Instant.now();

    private final Clock clock = new Clock();

    private final List<StatusRecord> statusRecordList = Collections.synchronizedList(new LinkedList<>());

    private final TaskResultReporter probeClient;

    private final ProbeClientTaskContext probeClientTaskContext;

    public VfsScanTaskReport(TaskResultReporter probeClient, ProbeClientTaskContext probeClientTaskContext) {
        this.probeClient = probeClient;
        this.probeClientTaskContext = probeClientTaskContext;
    }

    @Override
    public void record(String field, Object value) {
        if (Objects.equals(field, FILE_TOTAL_COUNT)){
            this.fileTotalCount += (Integer)value;
        } else if (Objects.equals(field, FILE_METADATA_EXTRACTED_COUNT)){
            this.fileMetadataExtractedCount += (Integer)value;
        } else if (Objects.equals(field, FILE_CONTENT_EXTRACTED_COUNT)) {
            this.fileContentExtractedCount += (Integer)value;
        } else if (Objects.equals(field, FILE_CONTENT_RECOGNITION_COUNT)){
            this.fileContentRecognitionCount += (Integer)value;
        } else if (Objects.equals(field, FILE_METADATA_KAFKA_SENT)){
            this.fileMetadataKafkaSent += (Integer)value;
        } else {
            throw new IllegalArgumentException("Unsupported field: " + field+", value: "+value);
        }
    }

    @Override
    public List<StatusRecord> addStatusRecord(StatusRecord statusRecord) {
        statusRecord.setElapsedTime(TaskReport.humanReadableFormat(Duration.between(start, Instant.now())));
        this.statusRecordList.add(statusRecord);
        return this.statusRecordList;
    }

    @Override
    public String toJson() {
        return JSON.from(toMap()).toString();
    }

    @Override
    public Map<String, Object> toMap() {
        int reportMsgTextLen = 0;
        int reportDataTextLen = 0;
        List<StatusRecord> newRecordList = new ArrayList<>();
        List<StatusRecord> statusRecordList = this.statusRecordList;
        for (StatusRecord statusRecord : statusRecordList) {
            String trace = statusRecord.getTrace();
            if (StringUtils.isNotBlank(trace)){
                statusRecord.setTrace(null);
            }
            if (statusRecord.getMessage() != null){
                reportMsgTextLen += statusRecord.getMessage().length();
            }
            if (statusRecord.getData() != null){
                reportDataTextLen += statusRecord.getData().length();
            }
            if (reportMsgTextLen > 50000 || reportDataTextLen > 50000){
                break;
            }
            newRecordList.add(statusRecord);
        }
        return UtilMisc.toMap(
                FILE_TOTAL_COUNT, fileTotalCount, FILE_METADATA_EXTRACTED_COUNT, fileMetadataExtractedCount,
                FILE_CONTENT_EXTRACTED_COUNT, fileContentExtractedCount, FILE_CONTENT_RECOGNITION_COUNT, fileContentRecognitionCount,
                FILE_METADATA_KAFKA_SENT, fileMetadataKafkaSent,
                "clock", clock, "statusRecordList", newRecordList
        );
    }

    @Override
    public void sendToServer() {
        ProbeClientJobResult probeClientJobResult = new ProbeClientJobResult(
                this.probeClientTaskContext.getExecId(), this.probeClientTaskContext.getTaskId(),
                this.probeClientTaskContext.getTaskType(), this.probeClientTaskContext.getTaskStatus(),
                this.probeClientTaskContext.getExecStatus(), toMap()
        );
        probeClient.updateProbeClientTaskStatus(probeClientJobResult);
    }
}
