package com.dcap.cloud.vfs.report;

import com.yd.dcap.classifier.taskreport.TaskStatusDict;

public class VfsScanTaskStatusDict implements TaskStatusDict {

    // 0 已经创建 1 正在执行 2 执行成功 3 执行失败，5 遇到错误，但仍旧在执行，9 任务已经中断
    public Integer executing(){
        return 1;
    }

    public Integer errorOccurredExecuting(){
        return 5;
    }

    // 0 是创建，等待开始。 1，5 都是执行状态。 2，3，9 都是结束状态。
    public Integer success(){
        return 2;
    }

    public Integer failed(){
        return 3;
    }

    public Integer interrupted(){
        return 9;
    }
}
