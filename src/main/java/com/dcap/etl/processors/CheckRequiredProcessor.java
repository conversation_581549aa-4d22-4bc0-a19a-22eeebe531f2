package com.dcap.etl.processors;

import com.dcap.etl.entities.ColumnMapping;
import com.dcap.etl.entities.DataMigrateConfig;
import com.dcap.etl.framework.AbstractProcessor;
import com.dcap.etl.framework.Context;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.stream.Collectors;

@Slf4j
public class CheckRequiredProcessor extends AbstractProcessor {

    @Override
    public Object internalExecute(Object paramData, Context context) throws Exception {
        DataMigrateConfig dataMigrateConfig = context.getDataMigrateConfig();
        Connection fromConnection = context.getFromConnection();
        log.info("检查来源数据表和列是否存在");
        // 这里只是查一下表和列，没有任何问题就可以。
        StringBuilder sql = new StringBuilder("SELECT ");
        sql.append(StringUtils.join(dataMigrateConfig.getTablePk().stream().map(ColumnMapping::getFromColumnName).collect(Collectors.toList()), ","))
                .append(", ")
                .append(StringUtils.join(dataMigrateConfig.getColumnMappingList().stream().map(ColumnMapping::getFromColumnName).collect(Collectors.toList()), ","))
                .append(" FROM ")
                .append(dataMigrateConfig.getFromTableName())
                .append(" WHERE 1=0");
        try(PreparedStatement preparedStatement = fromConnection.prepareStatement(sql.toString())){
            ResultSet resultSet = preparedStatement.executeQuery();
            int row = resultSet.getRow();
            if (row == 0){
                resultSet.close();
            }
        } catch (Exception e){
            throw new IllegalArgumentException(e);
        }

        Connection targetConnection = context.getTargetConnection();
        log.info("检查目标数据表和列是否存在");
        // 这里只是查一下表和列，没有任何问题就可以。
        sql = new StringBuilder("SELECT ");
        sql.append(StringUtils.join(dataMigrateConfig.getTablePk().stream().map(ColumnMapping::getTargetColumnName).collect(Collectors.toList()), ","))
                .append(", ")
                .append(StringUtils.join(dataMigrateConfig.getColumnMappingList().stream().map(ColumnMapping::getTargetColumnName).collect(Collectors.toList()), ","))
                .append(" FROM ")
                .append(dataMigrateConfig.getTargetTableName())
                .append(" WHERE 1=0");
        try(PreparedStatement preparedStatement = targetConnection.prepareStatement(sql.toString())){
            ResultSet resultSet = preparedStatement.executeQuery();
            int row = resultSet.getRow();
            if (row == 0){
                resultSet.close();
            }
        } catch (Exception e){
            throw new IllegalArgumentException(e);
        }

        log.info("迁移之前的校验完成");
        return "null";
    }

//    private boolean checkSameTable(DataMigrateConfig migrateConfig){
//        return migrateConfig.getFromDatabaseHost().equals(migrateConfig.getTargetDatabaseHost()) &&
//                migrateConfig.getFromDatabasePort() == migrateConfig.getTargetDatabasePort() &&
//                migrateConfig.getFromDatabaseType().equals(migrateConfig.getTargetDatabaseType()) &&
//                Objects.equals(migrateConfig.getFromSchemaName(),migrateConfig.getTargetSchemaName()) &&
//                Objects.equals(migrateConfig.getFromDatabaseName(), migrateConfig.getTargetDatabaseName()) &&
//                Objects.equals(migrateConfig.getFromTableName(),migrateConfig.getTargetTableName());
//    }
}
