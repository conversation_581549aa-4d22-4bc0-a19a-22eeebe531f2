package com.dcap.etl.processors;

import com.dcap.etl.entities.DataMigrateConfig;
import com.dcap.etl.framework.AbstractProcessor;
import com.dcap.etl.framework.Context;
import com.dcap.utils.UtilDB;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;

@Slf4j
public class PrepDatabaseSourceProcessor extends AbstractProcessor {

    @Override
    public Object internalExecute(Object paramData, Context context) throws Exception {
        DataMigrateConfig dataMigrateConfig = context.getDataMigrateConfig();
        String fromDatabaseType = dataMigrateConfig.getFromDatabaseType();
        Connection fromConnection = UtilDB.buildDatabaseConnection(fromDatabaseType, dataMigrateConfig.getFromDatabaseHost(),
                dataMigrateConfig.getFromDatabasePort(), dataMigrateConfig.getFromDatabaseUsername(), dataMigrateConfig.getFromDatabasePassword(),
                dataMigrateConfig.getFromDatabaseName());
        Connection targetConnection = UtilDB.buildDatabaseConnection(dataMigrateConfig.getTargetDatabaseType(), dataMigrateConfig.getTargetDatabaseHost(),
                dataMigrateConfig.getTargetDatabasePort(), dataMigrateConfig.getTargetDatabaseUsername(), dataMigrateConfig.getTargetDatabasePassword(),
                dataMigrateConfig.getTargetDatabaseName());
        context.setFromConnection(fromConnection);
        context.setTargetConnection(targetConnection);
        log.info("迁移数据所用的数据库连接准备就绪"); // 检查是否提供了 required 数据
        return "null";
    }


}
