package com.dcap.etl.processors;

import com.dcap.etl.entities.ColumnMapping;
import com.dcap.etl.entities.DataMigrateConfig;
import com.dcap.etl.framework.AbstractProcessor;
import com.dcap.etl.framework.Context;
import org.apache.commons.lang3.StringUtils;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.stream.Collectors;

public class CopyDataProcessor extends AbstractProcessor {

    @Override
    public Object internalExecute(Object paramData, Context context) throws Exception {
        DataMigrateConfig dataMigrateConfig = context.getDataMigrateConfig();
        Connection fromConnection = context.getFromConnection();
        StringBuilder sql = new StringBuilder("SELECT ");
        sql.append(StringUtils.join(dataMigrateConfig.getTablePk().stream().map(ColumnMapping::getFromColumnName).collect(Collectors.toList()), ","))
                .append(", ")
                .append(StringUtils.join(dataMigrateConfig.getColumnMappingList().stream().
                        map(ColumnMapping::getFromColumnName).collect(Collectors.toList()), ","))
                .append(" FROM ").append(dataMigrateConfig.getFromTableName());

        if(!StringUtils.isBlank(dataMigrateConfig.getMigratedColumn())){
            sql.append(" WHERE ").append(dataMigrateConfig.getMigratedColumn()).append("=0");
        }

        StringBuilder updateSql = new StringBuilder("UPDATE ");
        updateSql.append(dataMigrateConfig.getTargetTableName())
                .append(" SET ")
                .append(StringUtils.join(dataMigrateConfig.getColumnMappingList().stream().map(columnMapping -> columnMapping.getTargetColumnName()+" = ? ").collect(Collectors.toList()),","))
                .append(" WHERE ")
                .append(StringUtils.join(dataMigrateConfig.getTablePk().stream().map(pkColumn->pkColumn.getTargetColumnName()+"=?").collect(Collectors.toList())," AND "));

        Connection targetConnection = context.getTargetConnection();
        targetConnection.setAutoCommit(false);
        int updateNumber = 0;
        try(PreparedStatement preparedStatement = fromConnection.prepareStatement(sql.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
            ResultSet resultSet = preparedStatement.executeQuery();
            PreparedStatement targetPrepStatement = targetConnection.prepareStatement(updateSql.toString())){
            while (resultSet.next()){
                for (int i = 0; i < dataMigrateConfig.getColumnMappingList().size(); i++) {
                    ColumnMapping columnMapping = dataMigrateConfig.getColumnMappingList().get(i);
                    Object originalData = resultSet.getObject(columnMapping.getFromColumnName());
                    targetPrepStatement.setObject(i+1,originalData);
                }
                for (int i = 0; i < dataMigrateConfig.getTablePk().size(); i++) {
                    ColumnMapping pk = dataMigrateConfig.getTablePk().get(i);
                    Object originalPkData = resultSet.getObject(pk.getFromColumnName());
                    targetPrepStatement.setObject(dataMigrateConfig.getColumnMappingList().size()+1+i, originalPkData);
                }
                targetPrepStatement.addBatch();
                updateNumber++;
                if(updateNumber >= 5000){
                    targetPrepStatement.executeBatch();
                    targetConnection.commit();
                    updateNumber = 0;
                }
            }
            if(updateNumber > 0){
                targetPrepStatement.executeBatch();
                targetConnection.commit();
            }
        }
        return null;
    }




}
