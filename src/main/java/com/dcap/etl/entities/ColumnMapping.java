package com.dcap.etl.entities;

public class ColumnMapping {

    private String fromColumnName;
    private String fromColumnType;

    private String targetColumnName;
    private String targetColumnType;

    public ColumnMapping(String fromColumnName, String fromColumnType, String targetColumnName, String targetColumnType) {
        this.fromColumnName = fromColumnName;
        this.fromColumnType = fromColumnType;
        this.targetColumnName = targetColumnName;
        this.targetColumnType = targetColumnType;
    }

    public String getFromColumnName() {
        return fromColumnName;
    }

    public void setFromColumnName(String fromColumnName) {
        this.fromColumnName = fromColumnName;
    }

    public String getFromColumnType() {
        return fromColumnType;
    }

    public void setFromColumnType(String fromColumnType) {
        this.fromColumnType = fromColumnType;
    }

    public String getTargetColumnName() {
        return targetColumnName;
    }

    public void setTargetColumnName(String targetColumnName) {
        this.targetColumnName = targetColumnName;
    }

    public String getTargetColumnType() {
        return targetColumnType;
    }

    public void setTargetColumnType(String targetColumnType) {
        this.targetColumnType = targetColumnType;
    }
}
