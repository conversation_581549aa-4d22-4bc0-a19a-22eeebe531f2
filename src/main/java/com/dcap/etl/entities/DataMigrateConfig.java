package com.dcap.etl.entities;

import java.util.List;

public class DataMigrateConfig {
    private String taskId;
    private String fromDatabaseType;
    private String fromDatabaseHost;
    private int fromDatabasePort;

    private String fromDatabaseUsername;

    private String fromDatabasePassword;
    private String fromSchemaName;
    private String fromDatabaseName;
    private String fromTableName;

    private String targetDatabaseType;
    private String targetDatabaseHost;
    private int targetDatabasePort;

    private String targetDatabaseUsername;

    private String targetDatabasePassword;
    private String targetSchemaName;
    private String targetDatabaseName;
    private String targetTableName;

    private List<ColumnMapping> tablePk;

    private List<ColumnMapping> columnMappingList;

    // 记录迁移状态的列 值为 1 说明已经完成迁移
    private String migratedColumn;


    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getFromDatabaseType() {
        return fromDatabaseType;
    }

    public void setFromDatabaseType(String fromDatabaseType) {
        this.fromDatabaseType = fromDatabaseType;
    }

    public String getFromDatabaseHost() {
        return fromDatabaseHost;
    }

    public void setFromDatabaseHost(String fromDatabaseHost) {
        this.fromDatabaseHost = fromDatabaseHost;
    }

    public int getFromDatabasePort() {
        return fromDatabasePort;
    }

    public void setFromDatabasePort(int fromDatabasePort) {
        this.fromDatabasePort = fromDatabasePort;
    }

    public String getFromSchemaName() {
        return fromSchemaName;
    }

    public void setFromSchemaName(String fromSchemaName) {
        this.fromSchemaName = fromSchemaName;
    }

    public String getFromDatabaseName() {
        return fromDatabaseName;
    }

    public void setFromDatabaseName(String fromDatabaseName) {
        this.fromDatabaseName = fromDatabaseName;
    }

    public String getFromTableName() {
        return fromTableName;
    }

    public void setFromTableName(String fromTableName) {
        this.fromTableName = fromTableName;
    }


    public String getTargetDatabaseType() {
        return targetDatabaseType;
    }

    public void setTargetDatabaseType(String targetDatabaseType) {
        this.targetDatabaseType = targetDatabaseType;
    }

    public String getTargetDatabaseHost() {
        return targetDatabaseHost;
    }

    public void setTargetDatabaseHost(String targetDatabaseHost) {
        this.targetDatabaseHost = targetDatabaseHost;
    }

    public int getTargetDatabasePort() {
        return targetDatabasePort;
    }

    public void setTargetDatabasePort(int targetDatabasePort) {
        this.targetDatabasePort = targetDatabasePort;
    }

    public String getTargetSchemaName() {
        return targetSchemaName;
    }

    public void setTargetSchemaName(String targetSchemaName) {
        this.targetSchemaName = targetSchemaName;
    }

    public String getTargetDatabaseName() {
        return targetDatabaseName;
    }

    public void setTargetDatabaseName(String targetDatabaseName) {
        this.targetDatabaseName = targetDatabaseName;
    }

    public String getTargetTableName() {
        return targetTableName;
    }

    public void setTargetTableName(String targetTableName) {
        this.targetTableName = targetTableName;
    }

    public String getFromDatabaseUsername() {
        return fromDatabaseUsername;
    }

    public void setFromDatabaseUsername(String fromDatabaseUsername) {
        this.fromDatabaseUsername = fromDatabaseUsername;
    }

    public String getFromDatabasePassword() {
        return fromDatabasePassword;
    }

    public void setFromDatabasePassword(String fromDatabasePassword) {
        this.fromDatabasePassword = fromDatabasePassword;
    }

    public String getTargetDatabaseUsername() {
        return targetDatabaseUsername;
    }

    public void setTargetDatabaseUsername(String targetDatabaseUsername) {
        this.targetDatabaseUsername = targetDatabaseUsername;
    }

    public String getTargetDatabasePassword() {
        return targetDatabasePassword;
    }

    public void setTargetDatabasePassword(String targetDatabasePassword) {
        this.targetDatabasePassword = targetDatabasePassword;
    }

    public List<ColumnMapping> getColumnMappingList() {
        return columnMappingList;
    }

    public void setColumnMappingList(List<ColumnMapping> columnMappingList) {
        this.columnMappingList = columnMappingList;
    }

    public String getMigratedColumn() {
        return migratedColumn;
    }

    public void setMigratedColumn(String migratedColumn) {
        this.migratedColumn = migratedColumn;
    }

    public List<ColumnMapping> getTablePk() {
        return tablePk;
    }

    public void setTablePk(List<ColumnMapping> tablePk) {
        this.tablePk = tablePk;
    }
}
