package com.dcap.etl.framework;


import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AbstractProcessor<T> implements Processor {
    @Override
    public Object execute(Object paramData, Context context) {
        try{
            Object result = internalExecute((T) paramData, context);
            if(result == null){
                context.getPipeline().setFinish(true);
                return null;
            }
            return result;
        } catch (Exception e){
            e.printStackTrace();
            context.getPipeline().setFinish(true);
            log.error(e.getMessage());
            return null;
        }
    }

    public abstract Object internalExecute(T paramData, Context context) throws Exception;
}
