package com.dcap.etl.framework;


import com.dcap.etl.entities.DataMigrateConfig;

import java.sql.Connection;

public class Context {

    private Pipeline pipeline;

    private DataMigrateConfig dataMigrateConfig;

    private Connection fromConnection;

    private Connection targetConnection;

    Context(DataMigrateConfig dataMigrateConfig, Pipeline pipeline) {
        this.dataMigrateConfig = dataMigrateConfig;
        this.pipeline = pipeline;
    }

    public DataMigrateConfig getDataMigrateConfig() {
        return dataMigrateConfig;
    }

    public Pipeline getPipeline() {
        return pipeline;
    }

    public Connection getFromConnection() {
        return fromConnection;
    }

    public void setFromConnection(Connection fromConnection) {
        this.fromConnection = fromConnection;
    }

    public Connection getTargetConnection() {
        return targetConnection;
    }

    public void setTargetConnection(Connection targetConnection) {
        this.targetConnection = targetConnection;
    }
}
