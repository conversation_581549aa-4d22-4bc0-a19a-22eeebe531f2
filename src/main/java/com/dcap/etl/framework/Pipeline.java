package com.dcap.etl.framework;


import com.dcap.etl.entities.DataMigrateConfig;

import java.util.concurrent.ConcurrentLinkedDeque;

public class Pipeline {

    final ConcurrentLinkedDeque<Processor> processors = new ConcurrentLinkedDeque<Processor>();

    private Context context;

    private Boolean isFinish = false;

    public Object execute(Object input) throws Exception {
        Object executeResult = null;
        Processor processor;
        while ((processor = processors.poll()) != null){
            if(isFinish){
                break;
            }
            if(executeResult == null){
                executeResult = processor.execute(input, context);
            } else{
                executeResult = processor.execute(executeResult, context);
            }
        }
        return executeResult;
    }

    public Pipeline addFirst(Processor processor){
        processors.addFirst(processor);
        return this;
    }

    public Pipeline addLast(Processor processor){
        processors.addLast(processor);
        return this;
    }

    /**
     * @Description: 是在 processor 中添加到后面，就是添加到第一个。
     * @param processor
     */
    public Pipeline addAfter(Processor processor){
        processors.addFirst(processor);
        return this;
    }

    public Pipeline clearAndAddFirst(Processor processor){
        processors.clear();
        processors.addFirst(processor);
        return this;
    }

    private void setContext(Context context){
        this.context = context;
    }

    public void setFinish(Boolean finish) {
        isFinish = finish;
    }

    public static Pipeline create(DataMigrateConfig dataMigrateConfig){
        Pipeline pipeline = new Pipeline();
        Context customContext = new Context(dataMigrateConfig, pipeline);
        pipeline.setContext(customContext);
        return pipeline;
    }

}
