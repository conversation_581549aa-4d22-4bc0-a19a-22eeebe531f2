package com.dcap.etl.framework;

import java.util.Date;
import java.util.Map;

public abstract class Event {

    private Long tenantId;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 事件提供者的账号 ID
     * 如果是 MNS queue 的事件，这里就是创建 queue 的 账号 ID
     * 如果是 MNS topic 的事件，这里就是创建 topic 的 账号 ID
     * 如果是 OSSTrigger 的事件，这里就是创建 bucket 的账号 ID
     */
    private String eventProviderAccountId;

    /**
     * 提供者名称
     * 如果是 MNS queue ，这里就是 queue 的名字
     * 如果是 MNS topic ，这里就是 topic 的名字
     * 如果是 OSSTrigger，这里就是 bucket 的名字
     */
    private String eventProviderName;

    /**
     * 事件来源
     * 当来自 MNS 队列时 mns:Queue:SendMessage
     * 当来自 MNS topic 时 mns:Topic:SendMessage
     * 当来自 OSS Trigger 时 acs:oss
     */
    private String eventSource;

    /**
     * 事件主题，事件来源的详细描述
     * 例如来自 MNS queue时  acs:mns:cn-beijing-internal:****************:queues/testq
     * 来自 OSSTrigger 时  acs:oss:cn-beijing:****************:dcap-test-oss-1
     * 来自 MNS topic 时   就是具体的 topic 名称
     */
    private String eventSubject;

    /**
     * 事件时间
     */
    private Date eventTime;

    /**
     * 事件版本
     */
    private String eventVersion;

    /**
     * 事件地区
     */
    private String region;

    // 事件请求参数
    private Map<String, Object> requestParameters;

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public Map<String, Object> getRequestParameters() {
        return requestParameters;
    }

    public void setRequestParameters(Map<String, Object> requestParameters) {
        this.requestParameters = requestParameters;
    }

    public String getEventSubject() {
        return eventSubject;
    }

    public void setEventSubject(String eventSubject) {
        this.eventSubject = eventSubject;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public String getEventSource() {
        return eventSource;
    }

    public void setEventSource(String eventSource) {
        this.eventSource = eventSource;
    }

    public Date getEventTime() {
        return eventTime;
    }

    public void setEventTime(Date eventTime) {
        this.eventTime = eventTime;
    }

    public String getEventVersion() {
        return eventVersion;
    }

    public void setEventVersion(String eventVersion) {
        this.eventVersion = eventVersion;
    }

    public String getEventProviderAccountId() {
        return eventProviderAccountId;
    }

    public void setEventProviderAccountId(String eventProviderAccountId) {
        this.eventProviderAccountId = eventProviderAccountId;
    }

    public String getEventProviderName() {
        return eventProviderName;
    }

    public void setEventProviderName(String eventProviderName) {
        this.eventProviderName = eventProviderName;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }
}
