package com.dcap.etl;


import com.dcap.etl.entities.ColumnMapping;
import com.dcap.etl.entities.DataMigrateConfig;
import com.dcap.etl.framework.Pipeline;
import com.dcap.etl.processors.CheckRequiredProcessor;
import com.dcap.etl.processors.CopyDataProcessor;
import com.dcap.etl.processors.PrepDatabaseSourceProcessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.time.Instant;
import java.util.Collections;

public class Etl {
    private static final Logger LOGGER = LoggerFactory.getLogger(Etl.class);
    private static final ObjectMapper MAPPER = new ObjectMapper(new YAMLFactory());
    private static final String DEFAULT_CONFIG_FILE = "config.yaml";

    public static String humanReadableFormat(Duration duration) {
        return duration.toString().substring(2).replaceAll("(\\d[HMS])(?!$)", "$1 ").toLowerCase();
    }

    public static void doWork(String bodyData) throws Exception {
//        DataMigrateConfig dataMigrateConfig = JSON.from(bodyData).toObject(DataMigrateConfig.class);
        DataMigrateConfig dataMigrateConfig = new DataMigrateConfig();
        dataMigrateConfig.setTaskId("1");
        String host = "mysql-db.dcap-dac-alpha-01.yuandiansec.net";
        int port = 10001;
        String username = "root";
        String password = "yuandian#1";

        dataMigrateConfig.setFromDatabaseHost(host);
        dataMigrateConfig.setFromDatabasePort(port);
        dataMigrateConfig.setFromDatabaseUsername(username);
        dataMigrateConfig.setFromDatabasePassword(password);
        dataMigrateConfig.setFromDatabaseType("mysql");
        dataMigrateConfig.setFromDatabaseName("dcap_conf");
        dataMigrateConfig.setFromTableName("test");



        dataMigrateConfig.setTargetDatabaseHost(host);
        dataMigrateConfig.setTargetDatabasePort(port);
        dataMigrateConfig.setTargetDatabaseUsername(username);
        dataMigrateConfig.setTargetDatabasePassword(password);
        dataMigrateConfig.setTargetDatabaseType("mysql");
        dataMigrateConfig.setTargetDatabaseName("dcap_conf");
        dataMigrateConfig.setTargetTableName("test");

        dataMigrateConfig.setTablePk(Collections.singletonList(new ColumnMapping("column1",null,"column1",null)));
        dataMigrateConfig.setColumnMappingList(Collections.singletonList(new ColumnMapping("column2",null,"column3",null)));

        final Instant start = Instant.now();

        Pipeline.create(dataMigrateConfig)
                .addLast(new PrepDatabaseSourceProcessor())
                .addLast(new CheckRequiredProcessor())
                .addLast(new CopyDataProcessor())
                .execute(null);
    }

}
