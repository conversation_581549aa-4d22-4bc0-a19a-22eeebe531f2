package com.dcap.sampling;


import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.core.CountRequest;
import co.elastic.clients.elasticsearch.core.CountResponse;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.core.search.HitsMetadata;
import com.dcap.utils.JSON;
import com.dcap.utils.UtilDB;
import com.google.common.primitives.Primitives;
import com.mongodb.MongoClient;
import com.mongodb.client.MongoDatabase;
import com.yd.dcap.common.utils.StringUtils;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.dcap.common.utils.UtilRandom;
import com.yd.dcap.platform.model.DataSourceType;
import com.yd.dcap.platform.model.ProbeClientTask;
import com.yd.dcap.probe.client.AbstractProbeClientProcessor;
import com.yd.dcap.probe.client.TaskType;
import com.yd.dcap.probe.entities.ProbeClientJobResult;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import org.bson.Document;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;
import software.amazon.awssdk.services.dynamodb.model.ScanRequest;
import software.amazon.awssdk.services.dynamodb.model.ScanResponse;

import java.io.IOException;
import java.sql.*;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@TaskType(ProbeClientTask.TASK_TYPE_SAMPLING)
public class DataSampler extends AbstractProbeClientProcessor {

    public DataSampler(ProbeClientTaskContext probeClientTask) {
        super(probeClientTask);
    }


        public static void main(String[] args) throws IOException {
        // {"host": "**************", "port": "32004", "limit": 20, "dbType": "clickhouse", "status": 0, "password": "1qaz2wsx",
            // "tenantId": 1, "username": "root", "tableName": "tables", "columnName": "primary_key", "schemaName": "system",
            // "databaseName": "yuandian",
            // "datasourceId": 64100, "dbInstanceId": 63526, "useRandomSampling": true, "excludeEmptyValues": false}
            Map<String, Object> body = UtilMisc.toMap(
                    "tenantId",1L,"taskId","123","taskType","SAMPLING",
                    "taskMark","a6806fb61fbfaea904876fb59d35a793df61c6c6",
                    "probeUuid","00000000-0000-0000-0000-000000000001",
                    "taskParam",UtilMisc.toMap(
                            "limit", 20, "status", 0,
                            "dbType", "kingbase",
                            "host", "localhost",
                            "port", "54321",
                            "username", "xuezhiwei",
                            "password","12345678",
                            "databaseName","",
                            "schemaName", "yd_common_test",
                            "tableName","table_str_type",
                            "columnName","email",
                            "useRandomSampling", true,
                            "excludeEmptyValues", true
                    )
            );

            ProbeClientTask probeClientTask = JSON.from(body).toObject(ProbeClientTask.class);
            probeClientTask.setTenantId(1L);
            ProbeClientTaskContext object = new ProbeClientTaskContext(probeClientTask);
            new DataSampler(object).doWork((result) -> System.out.println(JSON.from(result)));
    }
    @Override
    public Map<String, Object> doWork(long execId, long taskId, String taskType, long tenantId,
                                        Map<String, Object> taskParam, Consumer<ProbeClientJobResult> callback) throws Exception {
        ScanSamplingConfig scanSamplingConfig = JSON.from(taskParam).toObject(ScanSamplingConfig.class);
        List<Object> samplingResult = null;
        if (UtilDB.useJDBC(scanSamplingConfig.getDbType())) {
            try (Connection connection = UtilDB.buildDatabaseConnection(scanSamplingConfig.getDbType(), scanSamplingConfig.getHost(), scanSamplingConfig.getPort(), scanSamplingConfig.getUsername(), scanSamplingConfig.getPassword(), scanSamplingConfig.getDatabaseName())) {
                samplingResult = this.samplingRDBMS(scanSamplingConfig.getDbType(), connection, scanSamplingConfig);
            }
        } else if (UtilDB.useMongo(scanSamplingConfig.getDbType())) {
            try (MongoClient mongoClient = UtilDB.buildMongoClient(scanSamplingConfig.getHost(), scanSamplingConfig.getPort(),
                    scanSamplingConfig.getUsername(), scanSamplingConfig.getPassword(),
                    scanSamplingConfig.getDatabaseName()+"?maxPoolSize=1")) {
                samplingResult = this.samplingMongo(mongoClient, scanSamplingConfig);
            }
        } else if (UtilDB.useElasticsearch(scanSamplingConfig.getDbType())){
            ElasticsearchClient elasticsearchClient = UtilDB.buildElasticsearchClient(scanSamplingConfig.getHost(),
                    scanSamplingConfig.getPort(), scanSamplingConfig.getUsername(), scanSamplingConfig.getPassword());
            samplingResult= this.samplingElasticsearch(elasticsearchClient, scanSamplingConfig);
        } else if (UtilDB.useDynamodb(scanSamplingConfig.getDbType())){
            try(DynamoDbClient dynamoDbClient = UtilDB.buildDynamoDbClient(scanSamplingConfig.getUsername(), scanSamplingConfig.getPassword(), scanSamplingConfig.getDatabaseName())){
                samplingResult = this.samplingDynamo(dynamoDbClient, scanSamplingConfig);
            }
        }
        if (samplingResult != null && !samplingResult.isEmpty()) {
            samplingResult = samplingResult.stream().map(data -> UtilMisc.toMap(scanSamplingConfig.getColumnName(), data)).collect(Collectors.toList());
        }
        return UtilMisc.toMap("data", samplingResult);
    }

    private List<Object> samplingRDBMS(String dbType, Connection connection, ScanSamplingConfig scanSamplingConfig) throws Exception {
        if (Objects.equals(dbType, "doris")){
            try (Statement statement = connection.createStatement()){
                statement.executeUpdate("switch " + scanSamplingConfig.getDatabaseName());
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        }
        boolean excludeEmptyValues = scanSamplingConfig.isExcludeEmptyValues();

        String schemaName = scanSamplingConfig.getSchemaName();
        String tableName = scanSamplingConfig.getTableName();
        String columnName = scanSamplingConfig.getColumnName();
        String querySql;
        String basedSql = "SELECT " + columnName + " FROM " + (StringUtils.isBlank(schemaName) ? "" : schemaName + ".")
                + tableName + (excludeEmptyValues?" WHERE "+columnName+" IS NOT NULL AND "+columnName+" <> '' ":"");
        List<Object> samplingResult = new ArrayList<>();
        if (dbType.equals(DataSourceType.SourceType.MAXCOMPUTE.getValue())){
            querySql= "set odps.sql.allow.fullscan=true;" + basedSql;
        } else if (dbType.equals(DataSourceType.SourceType.HANA.getValue())){
            querySql = "SELECT " + columnName + " FROM " +
                    (StringUtils.isBlank(scanSamplingConfig.getDatabaseName()) ? "" : scanSamplingConfig.getDatabaseName() + ".")
                    + scanSamplingConfig.getTableName()+(excludeEmptyValues?" WHERE "+columnName+" IS NOT NULL AND "+columnName+" != '' ":"");;
        } else if (dbType.equals(DataSourceType.SourceType.DREMIO.getValue())){
            querySql = "SELECT \"" + columnName + "\" FROM " + (StringUtils.isBlank(scanSamplingConfig.getDatabaseName()) ?
                    "" : "\""+scanSamplingConfig.getDatabaseName() + "\".") + "\""+scanSamplingConfig.getTableName()+"\""
                    +(excludeEmptyValues?" WHERE \""+columnName+"\" IS NOT NULL AND \""+columnName+"\" != '' ":"");
        } else if (dbType.equals(DataSourceType.SourceType.ORACLE.getValue()) && Objects.equals(schemaName.toLowerCase(),"public")) {
            // 如果是 oracle 数据库，同时 schemaName 是 public，那么就不需要加 schemaName
            // 因为 oracle 数据库的 public schema 目前看是公共同义词
            querySql = "SELECT " + columnName + " FROM " + tableName + (excludeEmptyValues?" WHERE "+columnName+" IS NOT NULL AND "+columnName+" != '' ":"");
        } else if (dbType.equals(DataSourceType.SourceType.ORACLE.getValue())) {
            querySql = "SELECT " + columnName + " FROM " + (StringUtils.isBlank(schemaName) ? "" : schemaName + ".")
                    + tableName + (excludeEmptyValues?" WHERE "+columnName+" IS NOT NULL":"");
        } else if (dbType.equals(DataSourceType.SourceType.TRANSWARP_INCEPTOR.getValue())) {
            querySql = "SELECT " + columnName + " FROM " + (StringUtils.isBlank(schemaName) ? "" : schemaName + ".")
                    + tableName + (excludeEmptyValues?" WHERE "+columnName+" IS NOT NULL":"") +" limit "+scanSamplingConfig.getLimit();
        } else {
            querySql = basedSql;
        }
        logger.info("the sampling sql is: [{}]", querySql);
        try (PreparedStatement preparedStatement = dbType.equals("impala") || dbType.equals("hana")
                || dbType.equals("dremio") || dbType.equals("db2")
                ?
                connection.prepareStatement(querySql):
                connection.prepareStatement(querySql, ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY)) {
            int startNo = 1;
            if (scanSamplingConfig.isUseRandomSampling() && !dbType.equals(DataSourceType.SourceType.TRANSWARP_INCEPTOR.getValue())) {
                int rowCount = countRDBMS(dbType, connection, scanSamplingConfig) - 1;
                if (rowCount < 0) {
                    return null;
                } else if (rowCount == 0 || rowCount == 1){
                    rowCount = 2;
                }
                startNo = UtilRandom.getSecureRandom().nextInt(Math.min(rowCount, 1000) - 1) + 1;
            }
            preparedStatement.setMaxRows(startNo + scanSamplingConfig.getLimit() - 1);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (!dbType.equals(DataSourceType.SourceType.HIVE.getValue())
                        && !dbType.equals(DataSourceType.SourceType.IMPALA.getValue())
                        && !dbType.equals(DataSourceType.SourceType.HANA.getValue())
                        && !dbType.equals(DataSourceType.SourceType.DREMIO.getValue())
                        && !dbType.equals(DataSourceType.SourceType.DB2.getValue())
                        && !dbType.equals(DataSourceType.SourceType.TRANSWARP_INCEPTOR.getValue())
                        && !dbType.equals(DataSourceType.SourceType.CLICKHOUSE.getValue())
                ) {
                    resultSet.first();
                    resultSet.relative(startNo - 2);
                } else if (startNo != 1){
                    int end = startNo - 2;
                    int i = 1;
                    while (resultSet.next()) {
                        if (i > end) {
                            break;
                        }
                        i++;
                    }
                }
                while (resultSet.next()) {
                    Object object = resultSet.getObject(1);
                    Class<?> valueClass = object.getClass();
                    if (!valueClass.isPrimitive()
                            && !Primitives.isWrapperType(valueClass)
                            && !valueClass.equals(String.class)) {
                        object = resultSet.getString(1);
                    }
                    samplingResult.add(object);
                }
            }
        }
        return samplingResult;
    }

    private int countRDBMS(String dbType, Connection connection, ScanSamplingConfig scanSamplingConfig) throws Exception {
        String schemaName = scanSamplingConfig.getSchemaName();
        String tableName = scanSamplingConfig.getTableName();
        String columnName = scanSamplingConfig.getColumnName();
        boolean excludeEmptyValues = scanSamplingConfig.isExcludeEmptyValues();
        String querySql;
        String basedSql = "SELECT COUNT(" + columnName + ") FROM " +
                (StringUtils.isBlank(schemaName) ? "" : schemaName + ".") +
                tableName+(excludeEmptyValues?" WHERE "+columnName+" IS NOT NULL AND "+columnName+" <> '' ":"");
        int count = 0;
        if (dbType.equals(DataSourceType.SourceType.MAXCOMPUTE.getValue())) {
            querySql= "set odps.sql.allow.fullscan=true;"+basedSql;
        } else if (dbType.equals(DataSourceType.SourceType.HANA.getValue())) {
            querySql = "SELECT COUNT(" + columnName + ") FROM " +
                    (StringUtils.isBlank(scanSamplingConfig.getDatabaseName()) ? "" : scanSamplingConfig.getDatabaseName() + ".")
                    + scanSamplingConfig.getTableName()+(excludeEmptyValues?" WHERE "+columnName+" IS NOT NULL AND "+columnName+" != '' ":"");
        } else if (dbType.equals(DataSourceType.SourceType.DREMIO.getValue())){
            querySql = "SELECT COUNT(\"" + columnName + "\") FROM " +
                    (StringUtils.isBlank(scanSamplingConfig.getDatabaseName()) ? "" : "\""+scanSamplingConfig.getDatabaseName() + "\".")
                    + "\""+scanSamplingConfig.getTableName()+"\"" + (excludeEmptyValues?" WHERE \""+columnName+"\" IS NOT NULL AND \""+columnName+"\" != '' ":"");;
        } else if (dbType.equals(DataSourceType.SourceType.ORACLE.getValue()) && Objects.equals(schemaName.toLowerCase(),"public")) {
            querySql = "SELECT COUNT(" + columnName + ") FROM " + tableName+(excludeEmptyValues?" WHERE "+columnName+" IS NOT NULL ":"");
        } else if (dbType.equals(DataSourceType.SourceType.ORACLE.getValue())) {
            querySql = "SELECT COUNT(" + columnName + ") FROM " +
                    (StringUtils.isBlank(schemaName) ? "" : schemaName + ".") + tableName+(excludeEmptyValues?" WHERE "+columnName+" IS NOT NULL ":"");
        } else if (dbType.equals(DataSourceType.SourceType.TRANSWARP_INCEPTOR.getValue())) {
            querySql = "SELECT COUNT(" + columnName + ") FROM " +
                    (StringUtils.isBlank(schemaName) ? "" : schemaName + ".") + tableName+(excludeEmptyValues?" WHERE "+columnName+" IS NOT NULL ":"");
        } else {
            querySql = basedSql;
        }
        logger.info("the count sql is：[{}]", querySql);
        try (PreparedStatement preparedStatement = connection.prepareStatement(querySql);
             ResultSet resultSet = preparedStatement.executeQuery()) {
            if (resultSet.next()) {
                count = resultSet.getInt(1);
            }
        }
        return count;
    }

    private List<Object> samplingMongo(MongoClient mongoClient, ScanSamplingConfig scanSamplingConfig) throws Exception {
        int rowCount = countMongo(mongoClient, scanSamplingConfig);
        if (rowCount <= 0) {
            return null;
        }
        int startNo = 1;
        if (scanSamplingConfig.isUseRandomSampling()) {
            startNo = UtilRandom.getSecureRandom().nextInt(Math.min(rowCount, 1000) - 1) + 1;
        }
        List<Object> samplingResult = new ArrayList<>();
        Iterable<Document> documents = mongoClient.getDatabase(scanSamplingConfig.getDatabaseName()).getCollection(scanSamplingConfig.getTableName()).find().skip(rowCount <= scanSamplingConfig.getLimit() ? 0 : startNo).limit(scanSamplingConfig.getLimit());
        for (Document document : documents) {
            samplingResult.add(document.get(scanSamplingConfig.getColumnName()));
        }
        return samplingResult;
    }

    private int countMongo(MongoClient mongoClient, ScanSamplingConfig scanSamplingConfig) throws Exception {
        MongoDatabase database = mongoClient.getDatabase(scanSamplingConfig.getDatabaseName());
        return (int) database.getCollection(scanSamplingConfig.getTableName()).countDocuments();
    }

    private List<Object> samplingDynamo(DynamoDbClient dynamoDbClient, ScanSamplingConfig scanSamplingConfig) {
        List<Object> samplingResult = new ArrayList<>();
        ScanRequest scanRequest = ScanRequest.builder()
                .tableName(scanSamplingConfig.getTableName())
                .attributesToGet(scanSamplingConfig.getColumnName())
                .limit(50)
                .build();
        ScanResponse response = dynamoDbClient.scan(scanRequest);
        if (!response.hasItems()){
            return samplingResult;
        }
        List<Map<String, AttributeValue>> items = response.items();
        if (items == null || items.isEmpty()){
            return samplingResult;
        }
        for (Map<String, AttributeValue> item : items) {
            if (item.isEmpty()) {
                continue;
            }
            AttributeValue value = item.entrySet().stream().findFirst().get().getValue();
            Optional<Object> valueForField = value.getValueForField(value.type().name(), Object.class);
            String v = Objects.toString(valueForField.orElse(null),null);
            samplingResult.add(v);
        }
        return samplingResult;
    }

    private List<Object> samplingElasticsearch(ElasticsearchClient elasticsearchClient, ScanSamplingConfig scanSamplingConfig) throws Exception {
        int rowCount = countElasticsearch(elasticsearchClient, scanSamplingConfig);
        if (rowCount <= 0) {
            return null;
        }
        int startNo = 1;
        if (scanSamplingConfig.isUseRandomSampling()) {
            startNo = UtilRandom.getSecureRandom().nextInt(Math.min(rowCount, 1000) - 1) + 1;
        }
        List<Object> samplingResult = new ArrayList<>();
        SearchRequest searchRequest = new SearchRequest.Builder()
                .index(scanSamplingConfig.getTableName())
                .size(50)
                .from(startNo)
                .build();
        SearchResponse<Map> search = elasticsearchClient.search(searchRequest, Map.class);
        if (search == null){
            return samplingResult;
        }
        HitsMetadata<Map> hits = search.hits();
        if (hits == null || hits.total() == null || hits.total().value() <=0){
            return samplingResult;
        }
        List<Hit<Map>> hs = hits.hits();
        if (hs == null || hs.isEmpty()){
            return samplingResult;
        }
        for (Hit<Map> hit : hs) {
            Map<String,Object> document = hit.source();
            if (document != null) {
                samplingResult.add(document.get(scanSamplingConfig.getColumnName()));
            }
        }
        return samplingResult;
    }

    private int countElasticsearch(ElasticsearchClient elasticsearchClient, ScanSamplingConfig scanSamplingConfig) throws IOException {
        CountRequest countRequest = new CountRequest.Builder()
                .index(scanSamplingConfig.getTableName())
                .build();
        CountResponse count = elasticsearchClient.count(countRequest);
        return (int) count.count();
    }
}
