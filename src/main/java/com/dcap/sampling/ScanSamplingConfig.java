package com.dcap.sampling;

import com.yd.dcap.platform.model.BaseBeanModel;

public class ScanSamplingConfig extends BaseBeanModel {

    private long datasourceId;

    private long dbInstanceId;


    private String dbType;
    private String host;
    private Integer port;
    private String username;

    private String password;
    private String schemaName;
    private String databaseName;
    private String tableName;
    private String columnName;

    private int limit = 20;

    private boolean excludeEmptyValues = false;
    private boolean useRandomSampling = false;




    public String getDbType() {
        return dbType;
    }

    public void setDbType(String dbType) {
        this.dbType = dbType;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSchemaName() {
        return schemaName;
    }

    public void setSchemaName(String schemaName) {
        this.schemaName = schemaName;
    }

    public String getDatabaseName() {
        return databaseName;
    }

    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getColumnName() {
        return columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public boolean isUseRandomSampling() {
        return useRandomSampling;
    }

    public void setUseRandomSampling(boolean useRandomSampling) {
        this.useRandomSampling = useRandomSampling;
    }


    public long getDatasourceId() {
        return datasourceId;
    }

    public void setDatasourceId(long datasourceId) {
        this.datasourceId = datasourceId;
    }

    public long getDbInstanceId() {
        return dbInstanceId;
    }

    public void setDbInstanceId(long dbInstanceId) {
        this.dbInstanceId = dbInstanceId;
    }

    public boolean isExcludeEmptyValues() {
        return excludeEmptyValues;
    }

    public void setExcludeEmptyValues(boolean excludeEmptyValues) {
        this.excludeEmptyValues = excludeEmptyValues;
    }
}
