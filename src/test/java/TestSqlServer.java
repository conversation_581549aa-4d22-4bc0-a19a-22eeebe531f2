import org.junit.jupiter.api.Test;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

public class TestSqlServer {
    @Test
    public void testUpdateInRead() throws Exception {
        Connection con1 = DriverManager.getConnection("***************************************************************************************;" +
                        "Encrypt=false;trustServerCertificate=true",
                "sa", "p0o9I*U&");

        con1.setTransactionIsolation(Connection.TRANSACTION_READ_UNCOMMITTED);

        Connection con2 = DriverManager.getConnection("***************************************************************************************;" +
                        "Encrypt=false;trustServerCertificate=true",
                "sa", "p0o9I*U&");
        Statement stmt1 = con1.createStatement();
        ResultSet rs = stmt1.executeQuery("select [id],[c2] from [xzw_test].[dbo].[newtable] " +
                "where c2 is not null and cast(c2 as varchar(max)) not like '%|~|' ");
//        ResultSet rs = stmt1.executeQuery("select top 500 [id],[c2] from [xzw_test].[dbo].[newtable] where c2 is not null and cast(c2 as varchar(max)) not like '%|~|' ");
//        ResultSet rs = stmt1.executeQuery("select [id],[c2] from [xzw_test].[dbo].[newtable] where c2 is not null");
        while (rs.next()) {
            int id = rs.getInt(1);
            String c2 = rs.getString(2);
            System.out.println("查询完毕->" + id + ":" + c2);
            try (Statement stm2 = con2.createStatement()) {
                System.out.println("开始读中更新:"+id);
                int cnt = stm2.executeUpdate("update [xzw_test].[dbo].[newtable] " +
                        "set [c2] = 'vL6 vgoI8vPLNhRUArMvtWuD2RwzYUhufVQaGBmLKkbd74ghpwFzD/ yXtoMvaOXIokDMVCzhVaG5egdWYuHES2lqa2Eifb2Xv/IfjdwGLjNgki9BSm7ooFAnCzoQ1g=|~|' " +
                        "where [id] = " + id + " AND CAST([c2] AS varchar(max)) = '" + c2 + "'");
                System.out.println("更新完毕:" + id + ":" + cnt);
            }
        }
        rs.close();
        con1.close();
        con2.close();
    }
}
