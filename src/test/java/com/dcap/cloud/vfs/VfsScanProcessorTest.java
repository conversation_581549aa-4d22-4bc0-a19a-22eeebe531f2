package com.dcap.cloud.vfs;

import com.dcap.utils.JSON;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.dcap.probe.config.DcapConstants;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.StringSerializer;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ExecutionException;

/**
 * VFS扫描任务处理器测试类
 * 用于发送Kafka消息触发VfsScanProcessor任务
 */
public class VfsScanProcessorTest {

    // Kafka配置
    private static final String BOOTSTRAP_SERVERS = "172.27.253.29:32666"; // 根据实际环境修改
    private static final String TASK_TOPIC = "dev-task-topic"; // 与应用配置一致
    private static final String PROBE_UUID = "00000000-0000-0000-0000-000000000001"; // 测试使用的探针 UUID

    public static void main(String[] args) throws Exception {
        System.out.println("======== VFS 扫描任务处理器测试 ========");
        
        // 确保主题存在
        ensureTopicExists(TASK_TOPIC, 1, (short)1);
        
        // 发送VFS扫描任务消息
        sendVfsScanTaskMessage();
        
        System.out.println("测试消息已发送，请检查应用日志以确认任务是否被正确处理");
    }
    
    /**
     * 确保Kafka主题存在，如果不存在则创建
     */
    private static void ensureTopicExists(String topicName, int partitions, short replicationFactor) throws ExecutionException, InterruptedException {
        Properties props = new Properties();
        props.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);
        
        try (AdminClient adminClient = AdminClient.create(props)) {
            // 检查主题是否存在
            boolean topicExists = adminClient.listTopics().names().get().contains(topicName);
            
            if (!topicExists) {
                System.out.println("创建Kafka主题: " + topicName);
                NewTopic newTopic = new NewTopic(topicName, partitions, replicationFactor);
                adminClient.createTopics(Collections.singleton(newTopic)).all().get();
                System.out.println("主题创建成功");
            } else {
                System.out.println("主题已存在: " + topicName);
            }
        }
    }
    
    /**
     * 发送VFS扫描任务消息到Kafka
     */
    private static void sendVfsScanTaskMessage() {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        
        try (KafkaProducer<String, String> producer = new KafkaProducer<>(props)) {
            // 创建任务配置
            Map<String, Object> taskConfig = createVfsScanTaskConfig();
            
            // 创建任务消息
            String taskBody = JSON.from(taskConfig).toString();
            
            // 创建完整的消息结构
            Map<String, Object> taskMsg = UtilMisc.toMap(
                    "jobId", taskConfig.get("id"),
                    "msgType", DcapConstants.MSG_TYPE_PROBE_CLIENT_TASK_START,
                    "body", taskBody
            );
            
            String taskMsgJson = JSON.from(taskMsg).toString();
            
            // 发送消息
            ProducerRecord<String, String> record = new ProducerRecord<>(
                    TASK_TOPIC,
                    PROBE_UUID, // 消息key为probeUUID
                    taskMsgJson
            );
            
            System.out.println("发送任务消息: " + taskMsgJson);
            producer.send(record).get();
            System.out.println("消息发送成功");
        } catch (Exception e) {
            System.err.println("发送消息时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 创建VFS扫描任务配置
     * 使用Map直接构建JSON结构，符合ProbeClientTaskContext构造函数预期的格式
     */
    private static Map<String, Object> createVfsScanTaskConfig() {
        Map<String, Object> taskConfig = new HashMap<>();
        
        // 基本任务信息
        long currentTime = System.currentTimeMillis();
        taskConfig.put("id", currentTime); // execId
        taskConfig.put("taskId", currentTime); // 任务ID
        taskConfig.put("tenantId", 1L); // 租户ID
        taskConfig.put("probeUuid", PROBE_UUID); // 探针UUID
        taskConfig.put("name", "VFS扫描测试任务-" + currentTime);
        
        // 重要！必须使用"VFS_FILE_SCAN"，与ProbeClientTaskContext构造函数中的判断逻辑一致
        taskConfig.put("taskType", "VFS_FILE_SCAN");
        
        // 执行状态 - 初始化为待执行状态
        taskConfig.put("execStatus", 0);
        
        // 创建任务配置参数
        Map<String, Object> taskConf = new HashMap<>();
        
        // VFS 扫描特定参数
        taskConf.put("providerId", "vfs");
        taskConf.put("serviceId", "minio");
        // --- 修改为您的 MinIO 配置 ---
        taskConf.put("vfsRootUri", "s3://yuandian/"); // 扫描 yuandian bucket 根目录
        taskConf.put("s3Endpoint", "http://127.0.0.1:9000"); // 您的 MinIO 地址
        taskConf.put("s3AccessKey", "wI0hnAhN7Rjsinyk14Oi"); // 您的 Access Key
        taskConf.put("s3SecretKey", "pjV1PJl2CXV8YvjnxjxffqvMTFakziff4hs6QnC1"); // 您的 Secret Key
        taskConf.put("s3PathStyleAccess", true); // MinIO 通常需要
        taskConf.put("s3UseHttps", false); // 使用 http

        
        // 配置文件路径
        taskConf.put("configPath", "orchestrator/vfs/vfs_file_scan/vfs_scan_config.yaml");
        
        // 数据源信息
        Map<String, Object> dataSource = new HashMap<>();
        dataSource.put("sourceType", "VFS");
        taskConf.put("datasource", dataSource);
        
        // 将任务配置添加到主消息中
        taskConfig.put("taskConf", taskConf);
        
        return taskConfig;
    }
} 