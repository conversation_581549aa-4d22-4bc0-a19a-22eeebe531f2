package com.dcap.cloud.vfs.util;

import com.dcap.cloud.vfs.model.TableData;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CsvParser测试类
 */
public class CsvParserTest {

    private static final Logger LOGGER = LoggerFactory.getLogger(CsvParserTest.class);

    @Test
    public void testParseCsvWithCommaDelimiter() throws IOException {
        String csvContent = "姓名,年龄,城市\n" +
                           "张三,25,北京\n" +
                           "李四,30,上海\n" +
                           "王五,28,广州\n";

        ByteArrayInputStream inputStream = new ByteArrayInputStream(
                csvContent.getBytes(StandardCharsets.UTF_8));

        List<TableData> result = CsvParser.parseCsv(inputStream, "test.csv");

        assertNotNull(result);
        assertEquals(1, result.size());

        TableData tableData = result.get(0);
        assertEquals("test.csv", tableData.getSheetName());
        assertEquals(3, tableData.getColumnCount());
        assertEquals(3, tableData.getRowCount());

        List<String> columnNames = tableData.getColumnNames();
        assertEquals("姓名", columnNames.get(0));
        assertEquals("年龄", columnNames.get(1));
        assertEquals("城市", columnNames.get(2));

        List<List<String>> rows = tableData.getRows();
        assertEquals("张三", rows.get(0).get(0));
        assertEquals("25", rows.get(0).get(1));
        assertEquals("北京", rows.get(0).get(2));

        LOGGER.info("CSV解析测试成功 - 逗号分隔符");
    }

    @Test
    public void testParseCsvWithSemicolonDelimiter() throws IOException {
        String csvContent = "Name;Age;City\n" +
                           "John;25;New York\n" +
                           "Jane;30;Los Angeles\n";

        ByteArrayInputStream inputStream = new ByteArrayInputStream(
                csvContent.getBytes(StandardCharsets.UTF_8));

        List<TableData> result = CsvParser.parseCsv(inputStream, "test_semicolon.csv");

        assertNotNull(result);
        assertEquals(1, result.size());

        TableData tableData = result.get(0);
        assertEquals(3, tableData.getColumnCount());
        assertEquals(2, tableData.getRowCount());

        List<String> columnNames = tableData.getColumnNames();
        assertEquals("Name", columnNames.get(0));
        assertEquals("Age", columnNames.get(1));
        assertEquals("City", columnNames.get(2));

        LOGGER.info("CSV解析测试成功 - 分号分隔符");
    }

    @Test
    public void testParseCsvWithTabDelimiter() throws IOException {
        String csvContent = "产品\t价格\t数量\n" +
                           "苹果\t5.5\t100\n" +
                           "香蕉\t3.2\t200\n";

        ByteArrayInputStream inputStream = new ByteArrayInputStream(
                csvContent.getBytes(StandardCharsets.UTF_8));

        List<TableData> result = CsvParser.parseCsv(inputStream, "test_tab.csv");

        assertNotNull(result);
        assertEquals(1, result.size());

        TableData tableData = result.get(0);
        assertEquals(3, tableData.getColumnCount());
        assertEquals(2, tableData.getRowCount());

        List<String> columnNames = tableData.getColumnNames();
        assertEquals("产品", columnNames.get(0));
        assertEquals("价格", columnNames.get(1));
        assertEquals("数量", columnNames.get(2));

        LOGGER.info("CSV解析测试成功 - Tab分隔符");
    }

    @Test
    public void testParseCsvWithEmptyColumns() throws IOException {
        String csvContent = "Name,Age,City,Country\n" +
                           "John,25,,USA\n" +
                           "Jane,,London,UK\n" +
                           ",,Paris,France\n";

        ByteArrayInputStream inputStream = new ByteArrayInputStream(
                csvContent.getBytes(StandardCharsets.UTF_8));

        List<TableData> result = CsvParser.parseCsv(inputStream, "test_empty.csv");

        assertNotNull(result);
        assertEquals(1, result.size());

        TableData tableData = result.get(0);
        assertEquals(4, tableData.getColumnCount());
        assertEquals(3, tableData.getRowCount());

        List<List<String>> rows = tableData.getRows();
        assertEquals("John", rows.get(0).get(0));
        assertEquals("25", rows.get(0).get(1));
        assertEquals("", rows.get(0).get(2)); // 空值
        assertEquals("USA", rows.get(0).get(3));

        LOGGER.info("CSV解析测试成功 - 空列处理");
    }

    @Test
    public void testParseCsvWithQuotedValues() throws IOException {
        String csvContent = "Name,Description,Price\n" +
                           "\"Apple iPhone\",\"Latest smartphone, 128GB\",999.99\n" +
                           "\"Samsung Galaxy\",\"Android phone, 64GB\",799.99\n";

        ByteArrayInputStream inputStream = new ByteArrayInputStream(
                csvContent.getBytes(StandardCharsets.UTF_8));

        List<TableData> result = CsvParser.parseCsv(inputStream, "test_quoted.csv");

        assertNotNull(result);
        assertEquals(1, result.size());

        TableData tableData = result.get(0);
        assertEquals(3, tableData.getColumnCount());
        assertEquals(2, tableData.getRowCount());

        List<List<String>> rows = tableData.getRows();
        assertEquals("Apple iPhone", rows.get(0).get(0));
        assertEquals("Latest smartphone, 128GB", rows.get(0).get(1));
        assertEquals("999.99", rows.get(0).get(2));

        LOGGER.info("CSV解析测试成功 - 引号值处理");
    }

    @Test
    public void testParseCsvWithMissingColumnNames() throws IOException {
        String csvContent = ",Age,\n" +
                           "John,25,USA\n" +
                           "Jane,30,UK\n";

        ByteArrayInputStream inputStream = new ByteArrayInputStream(
                csvContent.getBytes(StandardCharsets.UTF_8));

        List<TableData> result = CsvParser.parseCsv(inputStream, "test_missing_headers.csv");

        assertNotNull(result);
        assertEquals(1, result.size());

        TableData tableData = result.get(0);
        assertEquals(3, tableData.getColumnCount());

        List<String> columnNames = tableData.getColumnNames();
        assertEquals("第1列", columnNames.get(0)); // 自动生成的列名
        assertEquals("Age", columnNames.get(1));
        assertEquals("第3列", columnNames.get(2)); // 自动生成的列名

        LOGGER.info("CSV解析测试成功 - 缺失列名自动生成");
    }

    @Test
    public void testParseCsvEmptyFile() throws IOException {
        String csvContent = "";

        ByteArrayInputStream inputStream = new ByteArrayInputStream(
                csvContent.getBytes(StandardCharsets.UTF_8));

        List<TableData> result = CsvParser.parseCsv(inputStream, "empty.csv");

        assertNotNull(result);
        assertTrue(result.isEmpty());

        LOGGER.info("CSV解析测试成功 - 空文件处理");
    }

    @Test
    public void testParseCsvOnlyHeaders() throws IOException {
        String csvContent = "Name,Age,City";

        ByteArrayInputStream inputStream = new ByteArrayInputStream(
                csvContent.getBytes(StandardCharsets.UTF_8));

        List<TableData> result = CsvParser.parseCsv(inputStream, "headers_only.csv");

        assertNotNull(result);
        assertEquals(1, result.size());

        TableData tableData = result.get(0);
        assertEquals(3, tableData.getColumnCount());
        assertEquals(0, tableData.getRowCount()); // 没有数据行

        List<String> columnNames = tableData.getColumnNames();
        assertEquals("Name", columnNames.get(0));
        assertEquals("Age", columnNames.get(1));
        assertEquals("City", columnNames.get(2));

        LOGGER.info("CSV解析测试成功 - 仅有表头");
    }
}
