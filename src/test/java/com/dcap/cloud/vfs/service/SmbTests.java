package com.dcap.cloud.vfs.service;

import com.hierynomus.msdtyp.AccessMask;
import com.hierynomus.msfscc.FileAttributes;
import com.hierynomus.mssmb2.SMB2CreateDisposition;
import com.hierynomus.mssmb2.SMB2CreateOptions;
import com.hierynomus.mssmb2.SMB2ShareAccess;
import com.hierynomus.smbj.SMBClient;
import com.hierynomus.smbj.SmbConfig;
import com.hierynomus.smbj.auth.AuthenticationContext;
import com.hierynomus.smbj.connection.Connection;
import com.hierynomus.smbj.session.Session;
import com.hierynomus.smbj.share.DiskShare;

import java.io.InputStream;
import java.util.EnumSet;

public class SmbTests {
    public static void main(String[] args) throws Exception {
//        AuthenticationContext ac = AuthenticationContext.anonymous(); // 匿名访问
        SmbConfig config = SmbConfig.builder()
                .withEncryptData(true)
                .build();
        SMBClient client = new SMBClient();
        AuthenticationContext ac = new AuthenticationContext("user1", "yuandian#1".toCharArray(), "WORKGROUP");
        try (Connection connection = client.connect("172.16.2.105")) {

            Session session = connection.authenticate(ac);

            try (DiskShare share = (DiskShare) session.connectShare("shared_data")) {
                if (share.fileExists("1.txt")) {
                    try (InputStream is = share.openFile(
                            "1.txt",
                            EnumSet.of(AccessMask.GENERIC_READ),
                            EnumSet.of(FileAttributes.FILE_ATTRIBUTE_NORMAL),
                            EnumSet.of(SMB2ShareAccess.FILE_SHARE_READ),
                            SMB2CreateDisposition.FILE_OPEN,
                            EnumSet.noneOf(SMB2CreateOptions.class)
                    ).getInputStream()) {
                        System.out.println("读取成功：" + is.read());
                    }
                }
            }
        }
    }
}
