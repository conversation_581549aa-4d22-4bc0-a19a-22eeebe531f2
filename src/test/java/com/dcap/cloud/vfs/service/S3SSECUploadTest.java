package com.dcap.cloud.vfs.service;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.Base64;

/**
 * S3 SSE-C 加密上传测试类
 * 
 * 测试使用客户提供的密钥（SSE-C）上传文件到 S3/MinIO
 */
public class S3SSECUploadTest {

    private static final Logger LOGGER = LoggerFactory.getLogger(S3SSECUploadTest.class);

    // 测试配置
    private static final String ENDPOINT = "127.0.0.1:9000"; // MinIO 本地测试端点
    private static final String ACCESS_KEY = "minioadmin";           // MinIO 默认访问密钥
    private static final String SECRET_KEY = "minioadmin";      // MinIO 默认秘密密钥
    private static final String BUCKET_NAME = "test-bucket";    // 测试存储桶
    private static final String OBJECT_KEY = "test-file-with-ssec.txt"; // 测试对象键
    private static final boolean USE_HTTPS = true;            // 本地测试使用 HTTP
    private static final String REGION = "us-east-1";          // 区域

    // SSE-C 配置
    private static final String SSE_ALGORITHM = "AES256";
    // 32字节的密钥（256位），Base64编码
    private static final String SSE_CUSTOMER_KEY = "_YuanDianShuAnKeJiYouXianGongSi_"; // 32字节密钥
    private static final String SSE_CUSTOMER_KEY_BASE64 = Base64.getEncoder().encodeToString(SSE_CUSTOMER_KEY.getBytes());

    public static void main(String[] args) {
        disableSSLVerification();
        S3SSECUploadTest test = new S3SSECUploadTest();
        try {
            test.testSSECUpload();
        } catch (Exception e) {
            LOGGER.error("测试失败", e);
        }
    }

    /**
     * 测试 SSE-C 加密上传
     */
    public void testSSECUpload() {
        LOGGER.info("开始 S3 SSE-C 加密上传测试...");
        LOGGER.info("端点: {}", ENDPOINT);
        LOGGER.info("存储桶: {}", BUCKET_NAME);
        LOGGER.info("对象键: {}", OBJECT_KEY);
        LOGGER.info("使用 HTTPS: {}", USE_HTTPS);

        try {
            // 1. 创建 S3 客户端
            AmazonS3 s3Client = createS3Client();
            LOGGER.info("S3 客户端创建成功");

            // 2. 确保存储桶存在
            ensureBucketExists(s3Client);

            // 3. 准备上传的文件内容
            String fileContent = "这是一个使用 SSE-C 加密的测试文件。\n" +
                    "文件创建时间: " + java.time.LocalDateTime.now() + "\n" +
                    "加密算法: " + SSE_ALGORITHM + "\n" +
                    "这些内容将使用客户提供的密钥进行服务端加密。";
            
            byte[] fileBytes = fileContent.getBytes(StandardCharsets.UTF_8);
            LOGGER.info("准备上传文件，大小: {} 字节", fileBytes.length);

            // 4. 计算密钥的 MD5 哈希
            String keyMD5 = calculateMD5(SSE_CUSTOMER_KEY.getBytes());
            LOGGER.info("密钥 MD5: {}", keyMD5);

            // 5. 创建带有 SSE-C 的上传请求
            PutObjectRequest putRequest = createSSECPutRequest(fileBytes, keyMD5);

            // 6. 执行上传
            LOGGER.info("开始上传文件...");
            PutObjectResult result = s3Client.putObject(putRequest);
            LOGGER.info("文件上传成功！");
            LOGGER.info("ETag: {}", result.getETag());
            LOGGER.info("版本ID: {}", result.getVersionId());

            // 7. 验证上传结果
            verifyUpload(s3Client);

        } catch (Exception e) {
            LOGGER.error("SSE-C 上传测试失败", e);
            throw new RuntimeException("测试失败", e);
        }

        LOGGER.info("S3 SSE-C 加密上传测试完成！");
    }

    /**
     * 创建配置了 SSE-C 的 S3 客户端
     */
    private AmazonS3 createS3Client() {
        LOGGER.info("创建 S3 客户端，启用 SSE-C...");

        // 创建 AWS 凭证
        BasicAWSCredentials awsCredentials = new BasicAWSCredentials(ACCESS_KEY, SECRET_KEY);

        // 配置客户端
        ClientConfiguration clientConfig = new ClientConfiguration();
        clientConfig.setProtocol(USE_HTTPS ? Protocol.HTTPS : Protocol.HTTP);

//        clientConfig.setTlsKeyManagersProvider(NoneTlsKeyManagersProvider.getInstance());
        // 添加 SSE-C 头信息
        clientConfig.withHeader("X-Amz-Server-Side-Encryption-Customer-Algorithm", SSE_ALGORITHM);
        clientConfig.withHeader("X-Amz-Server-Side-Encryption-Customer-Key", SSE_CUSTOMER_KEY_BASE64);
        
        String keyMD5 = calculateMD5(SSE_CUSTOMER_KEY.getBytes());
        clientConfig.withHeader("X-Amz-Server-Side-Encryption-Customer-Key-MD5", keyMD5);


        LOGGER.info("已添加 SSE-C HTTP 头到客户端配置");
        LOGGER.info("SSE 算法: {}", SSE_ALGORITHM);
        LOGGER.info("SSE 密钥 (Base64): {}", SSE_CUSTOMER_KEY_BASE64);
        LOGGER.info("SSE 密钥 MD5: {}", keyMD5);

        // 创建 S3 客户端
        return AmazonS3ClientBuilder.standard()
                .withCredentials(new AWSStaticCredentialsProvider(awsCredentials))
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(ENDPOINT, REGION))
                .withClientConfiguration(clientConfig)
                .withPathStyleAccessEnabled(true) // MinIO 需要启用路径样式访问
                .disableChunkedEncoding()
                .build();
    }

    /**
     * 禁用所有 SSL 证书验证
     * 建议在应用程序启动时调用一次
     */
    public static void disableSSLVerification() {
        try {
            // 创建信任所有证书的 TrustManager
            TrustManager[] trustAllCerts = new TrustManager[] {
                    new X509TrustManager() {
                        public X509Certificate[] getAcceptedIssuers() {
                            return null;
                        }
                        public void checkClientTrusted(X509Certificate[] certs, String authType) {
                            // 不执行任何验证
                        }
                        public void checkServerTrusted(X509Certificate[] certs, String authType) {
                            // 不执行任何验证
                        }
                    }
            };

            // 安装信任所有证书的 TrustManager
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            SSLContext.setDefault(sc);
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            // 创建跳过主机名验证的 HostnameVerifier
            HostnameVerifier allHostsValid = new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };

            // 安装跳过主机名验证的 HostnameVerifier
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);

            // 设置系统属性
            System.setProperty("com.amazonaws.sdk.disableCertChecking", "true");
            System.setProperty("trust_all_cert", "true");
            System.setProperty("com.amazonaws.sdk.disableEc2Metadata", "true");

            // 禁用 SNI（服务器名称指示）以避免某些 SSL 问题
            System.setProperty("jsse.enableSNIExtension", "false");

            // 设置更宽松的 SSL 协议
            System.setProperty("https.protocols", "TLSv1,TLSv1.1,TLSv1.2,TLSv1.3");

            System.out.println("SSL certificate verification disabled globally.");

        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            throw new RuntimeException("Failed to disable SSL verification", e);
        }
    }

    /**
     * 确保存储桶存在
     */
    private void ensureBucketExists(AmazonS3 s3Client) {
        try {
            if (!s3Client.doesBucketExistV2(BUCKET_NAME)) {
                LOGGER.info("存储桶 {} 不存在，正在创建...", BUCKET_NAME);
                s3Client.createBucket(BUCKET_NAME);
                LOGGER.info("存储桶 {} 创建成功", BUCKET_NAME);
            } else {
                LOGGER.info("存储桶 {} 已存在", BUCKET_NAME);
            }
        } catch (Exception e) {
            LOGGER.error("检查/创建存储桶失败", e);
            throw new RuntimeException("存储桶操作失败", e);
        }
    }

    /**
     * 创建带有 SSE-C 配置的上传请求
     */
    private PutObjectRequest createSSECPutRequest(byte[] fileBytes, String keyMD5) {
        // 创建输入流
        ByteArrayInputStream inputStream = new ByteArrayInputStream(fileBytes);

        // 创建对象元数据
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(fileBytes.length);
        metadata.setContentType("text/plain; charset=utf-8");

        // 添加自定义元数据
        metadata.addUserMetadata("test-purpose", "sse-c-encryption-test");
        metadata.addUserMetadata("upload-time", String.valueOf(System.currentTimeMillis()));

        LOGGER.info("创建上传请求，包含 SSE-C 配置");

        // 创建上传请求
        PutObjectRequest putRequest = new PutObjectRequest(BUCKET_NAME, OBJECT_KEY, inputStream, metadata);
        
        // 创建 SSE-C 密钥对象
        SSECustomerKey sseCustomerKey = new SSECustomerKey(SSE_CUSTOMER_KEY_BASE64);
        
        // 设置 SSE-C 配置到请求中
        putRequest.setSSECustomerKey(sseCustomerKey);

        return putRequest;
    }

    /**
     * 验证上传结果
     */
    private void verifyUpload(AmazonS3 s3Client) {
        try {
            LOGGER.info("验证上传的对象...");
            
            // 检查对象是否存在
            boolean exists = s3Client.doesObjectExist(BUCKET_NAME, OBJECT_KEY);
            if (exists) {
                LOGGER.info("✓ 对象 {} 在存储桶 {} 中存在", OBJECT_KEY, BUCKET_NAME);
                
                // 获取对象元数据（需要提供相同的 SSE-C 密钥）
                try {
                    ObjectMetadata objectMetadata = s3Client.getObjectMetadata(BUCKET_NAME, OBJECT_KEY);
                    LOGGER.info("✓ 成功获取对象元数据");
                    LOGGER.info("  - 内容长度: {}", objectMetadata.getContentLength());
                    LOGGER.info("  - 内容类型: {}", objectMetadata.getContentType());
                    LOGGER.info("  - ETag: {}", objectMetadata.getETag());
                    LOGGER.info("  - 最后修改时间: {}", objectMetadata.getLastModified());
                    
                    // 显示用户元数据
                    if (objectMetadata.getUserMetadata() != null && !objectMetadata.getUserMetadata().isEmpty()) {
                        LOGGER.info("  - 用户元数据:");
                        objectMetadata.getUserMetadata().forEach((key, value) -> 
                            LOGGER.info("    * {}: {}", key, value));
                    }
                    S3Object object = s3Client.getObject(BUCKET_NAME, OBJECT_KEY);
                    System.out.println("================="+new String(object.getObjectContent().readAllBytes()));
                } catch (Exception e) {
                    LOGGER.warn("获取对象元数据失败（可能是 SSE-C 密钥不匹配）: {}", e.getMessage());
                }
            } else {
                LOGGER.error("✗ 对象不存在，上传可能失败");
            }
        } catch (Exception e) {
            LOGGER.error("验证上传结果失败", e);
        }
    }

    /**
     * 计算字节数组的 MD5 哈希值
     */
    private String calculateMD5(byte[] data) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(data);
            return Base64.getEncoder().encodeToString(hash);
        } catch (Exception e) {
            throw new RuntimeException("计算 MD5 失败", e);
        }
    }
}
