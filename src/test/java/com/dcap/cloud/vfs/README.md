# VfsScanProcessor 测试指南

这个测试类用于测试基于VFS的文件扫描功能，通过向Kafka发送任务消息来触发`VfsScanProcessor`处理器的执行。

## 前提条件

1. 确保有可用的Kafka服务器，默认连接地址为`localhost:9092`。如需修改，请更新`BOOTSTRAP_SERVERS`常量。
2. 确保应用配置中的任务主题与测试类中的`TASK_TOPIC`常量一致，默认为`dev-task-topic`。
3. 确保被测试应用正常运行，并配置为监听相同的Kafka主题。
4. 确保`PROBE_UUID`与应用配置匹配，不匹配的UUID会导致消息被忽略。
5. 如需测试实际的S3/MinIO连接，请确保MinIO服务器可用，或修改测试类中的S3配置信息。

## 使用方法

### 方法1：直接在IDE中运行

1. 在IDE中找到`VfsScanProcessorTest`类
2. 右键点击并选择"Run 'VfsScanProcessorTest.main()'"
3. 观察控制台输出和应用日志

### 方法2：命令行运行

```bash
cd /path/to/project
mvn test-compile exec:java -Dexec.classpathScope=test -Dexec.mainClass="com.dcap.cloud.vfs.VfsScanProcessorTest"
```

## 测试消息结构

测试类构建的任务消息结构如下：

```json
{
  "jobId": [任务ID],
  "msgType": 1,
  "body": {
    "id": [执行ID],
    "taskId": [任务ID],
    "tenantId": 1,
    "probeUuid": "test-probe-uuid",
    "name": "VFS扫描测试任务-[时间戳]",
    "taskType": "VFS_FILE_SCAN",
    "execStatus": 0,
    "taskConf": {
      "vfsRootUri": "s3://test-bucket/test-path",
      "s3Endpoint": "http://localhost:9000",
      "s3AccessKey": "minioadmin",
      "s3SecretKey": "minioadmin",
      "s3PathStyleAccess": true,
      "s3UseHttps": false,
      "configPath": "orchestrator/vfs/minio/vfs_scan_config.yaml",
      "datasource": {
        "sourceType": "VFS"
      }
    }
  }
}
```

## 消息结构说明

消息结构遵循`ProbeClientTaskContext`构造函数的期望格式：

### 顶层消息
- `jobId`: 任务ID，用于消息跟踪
- `msgType`: 消息类型，使用`DcapConstants.MSG_TYPE_SCAN_TASK`(值为1)
- `body`: 实际任务内容的JSON字符串

### 任务内容(`body`)
- `id`: 执行ID，用于唯一标识任务执行实例
- `taskId`: 任务ID，通常与执行ID相同
- `tenantId`: 租户ID
- `probeUuid`: 探针UUID，必须与应用配置匹配
- `taskType`: 任务类型，必须为"VFS_FILE_SCAN"
- `execStatus`: 执行状态，初始为0
- `taskConf`: 任务配置，包含VFS特定参数

### 任务配置(`taskConf`)
- `vfsRootUri`: VFS根URI，指定S3存储桶和路径
- `s3Endpoint`: S3/MinIO服务器地址
- `s3AccessKey`/`s3SecretKey`: 访问凭证
- `s3PathStyleAccess`/`s3UseHttps`: S3连接选项
- `configPath`: Orchestrator配置文件路径
- `datasource`: 数据源信息

## 任务处理流程

当任务消息被发送到Kafka后，应用中的以下组件将被依次触发：

1. `MqManager` 通过`@KafkaListener`接收消息
2. `JobHandler.onNext()` 处理消息，解析任务配置
3. `ProbeClientTaskContext` 构造任务上下文
4. `ProbeClientTaskUtil.createTaskProcessor()` 根据任务类型创建`VfsScanProcessor`实例
5. `VfsScanProcessor.doWork()` 执行任务，启动Orchestrator处理流水线

## 常见问题排查

1. **消息未被处理**
   - 检查Kafka服务器是否正常运行
   - 确认主题名称是否与应用配置一致
   - 确认probeUUID是否正确，应用根据UUID过滤消息

2. **任务初始化失败**
   - 确认任务类型是否为"VFS_FILE_SCAN"
   - 检查任务参数结构是否正确，特别是`taskConf`字段
   - 查看应用日志中的错误信息

3. **Orchestrator配置问题**
   - 确认配置文件路径是否正确
   - 检查配置文件中的插件定义是否与代码匹配

4. **S3连接失败**
   - 确认MinIO服务器是否可用
   - 检查S3连接参数是否正确
   - 确认应用是否有权限访问指定的存储桶 