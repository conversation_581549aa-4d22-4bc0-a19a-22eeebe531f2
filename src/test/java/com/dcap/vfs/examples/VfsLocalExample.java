package com.dcap.vfs.examples;

import org.apache.commons.vfs2.*;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Apache Commons VFS 本地文件系统操作示例
 */
public class VfsLocalExample {

    private static FileSystemManager fsManager;

    @TempDir
    static Path tempDir; // JUnit 5 提供的临时目录

    @BeforeAll
    static void setUp() throws FileSystemException {
        fsManager = VFS.getManager();
        System.out.println("Temporary directory for tests: " + tempDir.toAbsolutePath());
    }

    @AfterAll
    static void tearDown() {
        // 9. 资源管理：不关闭通过 VFS.getManager() 获取的共享实例
        // if (fsManager != null) {
        //     fsManager.close(); // Don't close the shared manager
        //     System.out.println("FileSystemManager closed.");
        // }
        System.out.println("Skipping FileSystemManager close for shared instance.");
    }

    @Test
    @DisplayName("检查文件/目录存在与类型")
    void testExistsAndType() throws IOException {
        // 准备一个临时文件和一个临时目录
        Path testFilePath = tempDir.resolve("testFile.txt");
        Path testDirPath = tempDir.resolve("testDir");
        assertTrue(testFilePath.toFile().createNewFile(), "Failed to create test file");
        assertTrue(testDirPath.toFile().mkdir(), "Failed to create test directory");

        String testFileUri = testFilePath.toUri().toString();
        String testDirUri = testDirPath.toUri().toString();
        String nonExistentUri = tempDir.resolve("nonexistent.tmp").toUri().toString();

        // 使用 try-with-resources 确保 FileObject 被关闭
        // 统一使用 resolveFile(uri)
        try (FileObject fileObj = fsManager.resolveFile(testFileUri);
             FileObject dirObj = fsManager.resolveFile(testDirUri);
             FileObject nonExistentObj = fsManager.resolveFile(nonExistentUri)) { // Use resolveFile

            // 6. 检查存在与类型
            assertTrue(fileObj.exists(), "File should exist: " + testFileUri); // Log URI
            assertEquals(FileType.FILE, fileObj.getType(), "Should be a file");

            assertTrue(dirObj.exists(), "Directory should exist: " + testDirUri); // Log URI
            assertEquals(FileType.FOLDER, dirObj.getType(), "Should be a folder");

            assertFalse(nonExistentObj.exists(), "File should not exist: " + nonExistentUri); // Log URI
            // 对于不存在的文件，类型通常是 IMAGINARY
            assertEquals(FileType.IMAGINARY, nonExistentObj.getType(), "Type should be imaginary for non-existent file");

        } catch (FileSystemException e) {
            fail("FileSystemException occurred: " + e.getMessage(), e);
        }
    }

    @Test
    @DisplayName("创建和写入文件")
    void testCreateAndWriteFile() throws FileSystemException {
        Path newFilePath = tempDir.resolve("newFile.txt");
        String contentToWrite = "Hello VFS World!";
        String newFileUri = newFilePath.toUri().toString();

        // 使用 try-with-resources
        // 使用 resolveFile(uri)
        try (FileObject newFileObj = fsManager.resolveFile(newFileUri)) { // Use resolveFile

            // 确保文件不存在
            assertFalse(newFileObj.exists(), "File should not exist initially: " + newFileUri);

            // 5. 创建和写入文件
            // 如果父目录不存在，VFS 通常不会自动创建，这里我们使用临时目录，父目录肯定存在
            // createFile() 会创建文件
            newFileObj.createFile();
            assertTrue(newFileObj.exists(), "File should exist after creation");
            assertEquals(FileType.FILE, newFileObj.getType(), "Should be a file after creation");

            // 获取输出流并写入内容
            try (OutputStream os = newFileObj.getContent().getOutputStream()) {
                os.write(contentToWrite.getBytes(StandardCharsets.UTF_8));
            } // try-with-resources 会自动关闭流

            // 验证写入的内容 (通过读取来验证)
            try (InputStream is = newFileObj.getContent().getInputStream()) {
                byte[] buffer = new byte[1024];
                int bytesRead = is.read(buffer);
                String contentRead = new String(buffer, 0, bytesRead, StandardCharsets.UTF_8);
                assertEquals(contentToWrite, contentRead, "Content read should match content written");
            }

        } catch (IOException e) { // IOException 包括 FileSystemException
            fail("IOException occurred during file creation/writing: " + e.getMessage(), e);
        }
    }

    @Test
    @DisplayName("读取文件内容")
    void testReadFileContent() throws IOException {
        // 准备一个包含内容的文件
        Path sourceFilePath = tempDir.resolve("sourceFile.txt");
        String expectedContent = "This is the content to be read.";
        java.nio.file.Files.writeString(sourceFilePath, expectedContent, StandardCharsets.UTF_8);
        String sourceFileUri = sourceFilePath.toUri().toString();

        // 使用 resolveFile(uri)
        try (FileObject sourceFileObj = fsManager.resolveFile(sourceFileUri)) { // Use resolveFile
            assertTrue(sourceFileObj.exists(), "Source file must exist: " + sourceFileUri);

            // 4. 读取文件内容
            StringBuilder contentBuilder = new StringBuilder();
            try (InputStream is = sourceFileObj.getContent().getInputStream()) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = is.read(buffer)) != -1) {
                    contentBuilder.append(new String(buffer, 0, bytesRead, StandardCharsets.UTF_8));
                }
            } // try-with-resources 自动关闭流

            assertEquals(expectedContent, contentBuilder.toString(), "Read content should match expected content");

        } catch (FileSystemException e) {
            fail("FileSystemException occurred during file reading: " + e.getMessage(), e);
        }
    }

    @Test
    @DisplayName("列出目录内容")
    void testListDirectory() throws IOException {
        // 准备目录结构
        Path parentDirPath = tempDir.resolve("parentDir");
        Path childFilePath = parentDirPath.resolve("childFile.txt");
        Path childDirPath = parentDirPath.resolve("childDir");
        assertTrue(parentDirPath.toFile().mkdirs(), "Failed to create parent directory");
        assertTrue(childFilePath.toFile().createNewFile(), "Failed to create child file");
        assertTrue(childDirPath.toFile().mkdir(), "Failed to create child directory");
        String parentDirUri = parentDirPath.toUri().toString();

        // 使用 resolveFile(uri)
        try (FileObject parentDirObj = fsManager.resolveFile(parentDirUri)) { // Use resolveFile
            assertTrue(parentDirObj.exists(), "Parent directory must exist: " + parentDirUri);
            assertEquals(FileType.FOLDER, parentDirObj.getType(), "Should be a folder");

            // 3. 列出目录内容
            FileObject[] children = parentDirObj.getChildren();
            assertNotNull(children, "Children array should not be null");
            assertEquals(2, children.length, "Should have 2 children (file and directory)");

            boolean foundChildFile = false;
            boolean foundChildDir = false;
            for (FileObject child : children) {
                System.out.println("Found child: " + child.getName().getBaseName() + " Type: " + child.getType());
                if (child.getName().getBaseName().equals("childFile.txt") && child.getType() == FileType.FILE) {
                    foundChildFile = true;
                } else if (child.getName().getBaseName().equals("childDir") && child.getType() == FileType.FOLDER) {
                    foundChildDir = true;
                }
                // 9. 资源管理：关闭子 FileObject
                child.close();
            }

            assertTrue(foundChildFile, "Child file 'childFile.txt' should be found");
            assertTrue(foundChildDir, "Child directory 'childDir' should be found");

        } catch (FileSystemException e) {
            fail("FileSystemException occurred during directory listing: " + e.getMessage(), e);
        }
    }

    @Test
    @DisplayName("提取文件元数据")
    void testGetMetadata() throws IOException {
        Path metaFilePath = tempDir.resolve("metadataFile.txt");
        String content = "Some content for size.";
        java.nio.file.Files.writeString(metaFilePath, content, StandardCharsets.UTF_8);
        long lastModifiedBefore = System.currentTimeMillis(); // 获取一个大致时间

        // 等待一小段时间确保最后修改时间不同
        try { Thread.sleep(50); } catch (InterruptedException ignored) {}

        // 修改文件以更新时间戳
        java.nio.file.Files.writeString(metaFilePath, content + " updated", StandardCharsets.UTF_8);
        long lastModifiedAfter = System.currentTimeMillis();
        String metaFileUri = metaFilePath.toUri().toString();

        // 使用 resolveFile(uri)
        try (FileObject metaFileObj = fsManager.resolveFile(metaFileUri)) { // Use resolveFile
            assertTrue(metaFileObj.exists(), "File must exist: " + metaFileUri);

            // 7. 提取元数据
            FileContent fileContent = metaFileObj.getContent();
            for (Map.Entry<String, Object> entry : fileContent.getAttributes().entrySet()) {
                System.out.println(entry.getKey()+": "+ entry.getValue());
            }
            assertNotNull(fileContent, "FileContent should not be null");

            long size = fileContent.getSize();
            long lastModifiedTime = fileContent.getLastModifiedTime();

            System.out.println("File Size: " + size);
            System.out.println("Last Modified Timestamp: " + lastModifiedTime);
            System.out.println("Current Time Approx (Before): " + lastModifiedBefore);
            System.out.println("Current Time Approx (After): " + lastModifiedAfter);


            assertTrue(size > 0, "Size should be greater than 0");
            // 检查最后修改时间是否在预期范围内 (考虑到文件系统精度可能不同)
            assertTrue(lastModifiedTime > lastModifiedBefore - 5000 && lastModifiedTime < lastModifiedAfter + 5000,
                       "Last modified time seems out of expected range.");
            // 可以添加更多元数据检查，如 getContentType() 等，但本地文件系统可能不提供

        } catch (FileSystemException e) {
            fail("FileSystemException occurred while getting metadata: " + e.getMessage(), e);
        }
    }

    @Test
    @DisplayName("基本错误处理 - 访问不存在的文件")
    void testErrorHandlingNonExistent() {
        Path nonExistentPath = tempDir.resolve("definitelyDoesNotExist.xyz");
        String nonExistentUri = nonExistentPath.toUri().toString();

        // 8. 基本错误处理
        // 使用 resolveFile(uri)
        try (FileObject nonExistentObj = fsManager.resolveFile(nonExistentUri)) { // Use resolveFile
            assertFalse(nonExistentObj.exists());

            // 尝试读取不存在文件的内容会抛出异常
            assertThrows(FileSystemException.class, () -> {
                try (InputStream is = nonExistentObj.getContent().getInputStream()) {
                    // 这行不会执行
                    is.read();
                }
            }, "Accessing content of a non-existent file should throw FileSystemException");

        } catch (FileSystemException e) {
            // resolveFile 本身不应因文件不存在而抛异常，除非URI格式错误
             fail("resolveFile should not throw exception for a non-existent file URI: " + e.getMessage(), e);
        }
    }
}
