package com.dcap.vfs.examples;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.github.vfss3.S3FileSystemConfigBuilder;
import org.apache.commons.vfs2.*;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assumptions.assumeTrue;

/**
 * Apache Commons VFS S3 文件系统操作示例
 *
 * 重要提示:
 * 1. 运行此测试前，请确保 pom.xml 中已包含 vfs-s3 和 aws-java-sdk-s3 依赖。
 * 2. 将下面的 S3_ENDPOINT, S3_ACCESS_KEY, S3_SECRET_KEY, S3_BUCKET_NAME
 *    替换为您的实际 S3/MinIO 配置。
 * 3. 考虑使用更安全的方式管理密钥，例如环境变量或配置文件，而不是硬编码。
 * 4. 测试可能需要在目标 S3 Bucket 中预先创建一些文件和目录结构。
 * 5. 可以通过设置系统属性 `vfs.s3.test.enabled=true` 来启用此测试类。
 */
//@EnabledIfSystemProperty(named = "vfs.s3.test.enabled", matches = "true")
//@Disabled("S3 tests require actual credentials and setup - enable manually via system property 'vfs.s3.test.enabled=true'")
public class VfsS3Example {

    // --- 配置占位符 ---
    // TODO: 替换为实际的 S3/MinIO Endpoint (例如 "http://localhost:9000" for MinIO)
    private static final String S3_ENDPOINT = "http://127.0.0.1/9000";

    private static final String S3_ACCESS_KEY = "wI0hnAhN7Rjsinyk14Oi";
    private static final String S3_SECRET_KEY = "pjV1PJl2CXV8YvjnxjxffqvMTFakziff4hs6QnC1";
    private static final String S3_BUCKET_NAME = "yuandian";
    // --- 配置占位符结束 ---

    // 示例路径 (根据您的 Bucket 结构调整)
    private static final String S3_TEST_FOLDER_PATH = "/"; // 必须以 / 结尾表示目录
    private static final String S3_TEST_FILE_PATH = S3_TEST_FOLDER_PATH + "集成指南.pdf";
    private static final String S3_NON_EXISTENT_PATH = "/non-existent-file.tmp";

    private static FileSystemManager fsManager;
    private static FileSystemOptions fsOptions;

    @BeforeAll
    static void setUp() throws FileSystemException {
        // 检查配置是否已设置 (简单检查，非空即可)
        assumeTrue(S3_ENDPOINT != null && !S3_ENDPOINT.equals("your_s3_or_minio_endpoint"), "S3 Endpoint not configured.");
        assumeTrue(S3_ACCESS_KEY != null && !S3_ACCESS_KEY.equals("your_access_key"), "S3 Access Key not configured.");
        assumeTrue(S3_SECRET_KEY != null && !S3_SECRET_KEY.equals("your_secret_key"), "S3 Secret Key not configured.");
        assumeTrue(S3_BUCKET_NAME != null && !S3_BUCKET_NAME.equals("your_bucket_name"), "S3 Bucket Name not configured.");

        fsManager = VFS.getManager();
        fsOptions = new FileSystemOptions();
        System.setProperty("aws.java.v1.disableDeprecationAnnouncement", "true");
        BasicAWSCredentials awsCreds = new BasicAWSCredentials(S3_ACCESS_KEY, S3_SECRET_KEY);
        AWSStaticCredentialsProvider credentialsProvider = new AWSStaticCredentialsProvider(awsCreds);

        S3FileSystemConfigBuilder builder = S3FileSystemConfigBuilder.getInstance();
        builder.setCredentialsProvider(fsOptions, credentialsProvider);
        builder.setUseHttps(fsOptions, false);

        System.out.println("FileSystemManager obtained via VFS.getManager().");
    }

    @AfterAll
    static void tearDown() {
        // 不关闭共享的 FileSystemManager
        System.out.println("Skipping FileSystemManager close for shared instance.");
    }

    private String buildS3Uri(String path) {
        // 构建 S3 URI: s3://bucket-name/path
        return "s3://127.0.0.1:9000/" + S3_BUCKET_NAME + path;
    }

    @Test
    @DisplayName("[S3] 检查文件/目录存在与类型")
    void testS3ExistsAndType() {
        String folderUri = buildS3Uri(S3_TEST_FOLDER_PATH);
        String fileUri = buildS3Uri(S3_TEST_FILE_PATH);
        String nonExistentUri = buildS3Uri(S3_NON_EXISTENT_PATH);

        System.out.println("Testing existence for Folder URI: " + folderUri);
        System.out.println("Testing existence for File URI: " + fileUri);
        System.out.println("Testing existence for Non-Existent URI: " + nonExistentUri);


        try (FileObject folderObj = fsManager.resolveFile(folderUri, fsOptions);
             FileObject fileObj = fsManager.resolveFile(fileUri, fsOptions);
             FileObject nonExistentObj = fsManager.resolveFile(nonExistentUri, fsOptions)) {

            // 假设 S3_TEST_FOLDER_PATH 和 S3_TEST_FILE_PATH 在 Bucket 中已存在
            assertTrue(folderObj.exists(), "S3 Folder should exist: " + folderUri);
            assertEquals(FileType.FOLDER, folderObj.getType(), "Should be a folder");

            assertTrue(fileObj.exists(), "S3 File should exist: " + fileUri);
            assertEquals(FileType.FILE, fileObj.getType(), "Should be a file");

            assertFalse(nonExistentObj.exists(), "S3 Object should not exist: " + nonExistentUri);
            assertEquals(FileType.IMAGINARY, nonExistentObj.getType(), "Type should be imaginary for non-existent S3 object");

        } catch (FileSystemException e) {
            fail("FileSystemException occurred during S3 existence check: " + e.getMessage(), e);
        }
    }

    @Test
    @DisplayName("[S3] 读取文件内容")
    void testS3ReadFileContent() {
        String fileUri = buildS3Uri(S3_TEST_FILE_PATH);
        System.out.println("Attempting to read S3 file: " + fileUri);
        // 假设 S3_TEST_FILE_PATH 文件存在且包含 UTF-8 文本 "Hello S3 VFS!"

        try (FileObject fileObj = fsManager.resolveFile(fileUri, fsOptions)) {
            assertTrue(fileObj.exists(), "S3 file must exist for reading: " + fileUri);
            assertEquals(FileType.FILE, fileObj.getType(), "Must be a file for reading");

            StringBuilder contentBuilder = new StringBuilder();
            try (InputStream is = fileObj.getContent().getInputStream()) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = is.read(buffer)) != -1) {
                    contentBuilder.append(new String(buffer, 0, bytesRead, StandardCharsets.UTF_8));
                }
            } // 自动关闭流

            System.out.println("Read content: " + contentBuilder.toString());
            // 在这里添加断言来验证内容，如果知道预期内容的话
            // assertEquals("Hello S3 VFS!", contentBuilder.toString(), "Read content should match expected");
            assertNotNull(contentBuilder.toString(), "Content should not be null"); // 基本检查

        } catch (IOException e) { // FileSystemException is an IOException
            fail("IOException occurred during S3 file reading: " + e.getMessage(), e);
        }
    }

    @Test
    @DisplayName("[S3] 列出目录内容")
    void testS3ListDirectory() {
        String folderUri = buildS3Uri(S3_TEST_FOLDER_PATH);
        System.out.println("Attempting to list S3 folder: " + folderUri);
        // 假设 S3_TEST_FOLDER_PATH 存在且包含 S3_TEST_FILE_PATH

        try (FileObject folderObj = fsManager.resolveFile(folderUri, fsOptions)) {
            assertTrue(folderObj.exists(), "S3 folder must exist for listing: " + folderUri);
            assertEquals(FileType.FOLDER, folderObj.getType(), "Must be a folder for listing");

            FileObject[] children = folderObj.getChildren();
            assertNotNull(children, "Children array should not be null");
            System.out.println("Found " + children.length + " children in " + folderUri);

            boolean foundTestFile = false;
            for (FileObject child : children) {
                System.out.println("Found child: " + child.getName().getBaseName() + " Type: " + child.getType());
                if (child.getName().getPath().equals(S3_TEST_FILE_PATH)) {
                    foundTestFile = true;
                }
                child.close(); // 关闭子对象
            }

            assertTrue(foundTestFile, "Expected test file '" + S3_TEST_FILE_PATH + "' not found in " + folderUri);
            // 可以添加更多断言，例如检查子目录等

        } catch (FileSystemException e) {
            fail("FileSystemException occurred during S3 directory listing: " + e.getMessage(), e);
        }
    }

    @Test
    @DisplayName("[S3] 提取文件元数据")
    void testS3GetMetadata() {
        String fileUri = buildS3Uri(S3_TEST_FILE_PATH);
        System.out.println("Attempting to get metadata for S3 file: " + fileUri);

        try (FileObject fileObj = fsManager.resolveFile(fileUri, fsOptions)) {
            assertTrue(fileObj.exists(), "S3 file must exist to get metadata: " + fileUri);

            FileContent fileContent = fileObj.getContent();
            assertNotNull(fileContent, "FileContent should not be null");

            long size = fileContent.getSize();
            long lastModifiedTime = fileContent.getLastModifiedTime();

            System.out.println("S3 File Size: " + size);
            System.out.println("S3 Last Modified Timestamp: " + lastModifiedTime);

            assertTrue(size >= 0, "Size should be non-negative"); // S3 文件大小可能为 0
            assertTrue(lastModifiedTime > 0, "Last modified time should be positive");

            // S3 可能提供 ETag 等特定元数据，但 VFS API 可能不直接暴露
             Map<String, Object> attributes = fileContent.getAttributes();
             System.out.println("Attributes: " + attributes);

        } catch (FileSystemException e) {
            fail("FileSystemException occurred while getting S3 metadata: " + e.getMessage(), e);
        }
    }

    // 注意：写入/创建 S3 对象的示例通常更复杂，可能涉及权限、覆盖逻辑等，
    // 并且会对实际存储产生副作用，因此在此基础示例中省略。
    // 如果需要，可以后续添加 testS3CreateAndWriteFile 方法。

}
