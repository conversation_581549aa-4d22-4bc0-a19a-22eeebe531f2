package com.dcap.classifier.analyzer.getters;

import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.RuleContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.contains;
import static org.mockito.Mockito.*;

/**
 * HiveViewDefinitionGetter 单元测试
 */
public class HiveViewDefinitionGetterTest {

    private HiveViewDefinitionGetter getter;
    
    @Mock
    private RuleContext mockContext;
    
    @Mock
    private ContextTable mockView;
    
    @Mock
    private Connection mockConnection;
    
    @Mock
    private Statement mockStatement;
    
    @Mock
    private ResultSet mockResultSet;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        getter = new HiveViewDefinitionGetter();
    }

    @Test
    void testShowCreateTableSuccess() throws Exception {
        // 设置模拟数据
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        when(mockStatement.executeQuery(contains("SHOW CREATE TABLE"))).thenReturn(mockResultSet);
        
        // 模拟SHOW CREATE TABLE的多行返回
        when(mockResultSet.next()).thenReturn(true, true, false);
        when(mockResultSet.getString("createtab_stmt"))
            .thenReturn("CREATE VIEW test_view AS")
            .thenReturn("SELECT id, name FROM users WHERE active = 1");
        
        when(mockView.getCatalog()).thenReturn("test_db");
        when(mockView.getTableName()).thenReturn("test_view");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertNotNull(result);
        assertTrue(result.contains("SELECT id, name FROM users"));
        verify(mockStatement).executeQuery(contains("SHOW CREATE TABLE test_db.test_view"));
    }

    @Test
    void testShowCreateTableWithComplexView() throws Exception {
        // 测试复杂视图的CREATE语句解析
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        when(mockStatement.executeQuery(contains("SHOW CREATE TABLE"))).thenReturn(mockResultSet);
        
        when(mockResultSet.next()).thenReturn(true, true, true, false);
        when(mockResultSet.getString("createtab_stmt"))
            .thenReturn("CREATE VIEW complex_view")
            .thenReturn("AS")
            .thenReturn("SELECT u.id, u.name, p.title FROM users u JOIN profiles p ON u.id = p.user_id");
        
        when(mockView.getCatalog()).thenReturn("analytics");
        when(mockView.getTableName()).thenReturn("complex_view");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertNotNull(result);
        assertTrue(result.contains("SELECT u.id, u.name, p.title"));
        assertTrue(result.contains("FROM users u JOIN profiles p"));
    }

    @Test
    void testFallbackToInformationSchema() throws Exception {
        // 设置SHOW CREATE TABLE失败，使用information_schema作为后备
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        
        // SHOW CREATE TABLE失败
        when(mockStatement.executeQuery(contains("SHOW CREATE TABLE")))
            .thenThrow(new RuntimeException("Command not supported"));
        
        // information_schema成功
        when(mockStatement.executeQuery(contains("information_schema.views"))).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getString("view_definition")).thenReturn("SELECT * FROM base_table");
        
        when(mockView.getCatalog()).thenReturn("test_db");
        when(mockView.getTableName()).thenReturn("test_view");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertEquals("SELECT * FROM base_table", result);
        verify(mockStatement).executeQuery(contains("information_schema.views"));
    }

    @Test
    void testFallbackToMetastoreQuery() throws Exception {
        // 前两种方法都失败，使用metastore查询
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        
        // SHOW CREATE TABLE失败
        when(mockStatement.executeQuery(contains("SHOW CREATE TABLE")))
            .thenThrow(new RuntimeException("Command not supported"));
        
        // information_schema失败
        when(mockStatement.executeQuery(contains("information_schema.views")))
            .thenThrow(new RuntimeException("Table not found"));
        
        // metastore查询成功
        when(mockStatement.executeQuery(contains("tbls t JOIN table_params tp"))).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getString("param_value")).thenReturn("SELECT col1, col2 FROM source_table");
        
        when(mockView.getCatalog()).thenReturn("warehouse");
        when(mockView.getTableName()).thenReturn("materialized_view");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertEquals("SELECT col1, col2 FROM source_table", result);
        verify(mockStatement).executeQuery(contains("tbls t JOIN table_params tp"));
    }

    @Test
    void testAllMethodsFail() throws Exception {
        // 所有方法都失败，返回null
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        
        // 所有查询都失败
        when(mockStatement.executeQuery(anyString()))
            .thenThrow(new RuntimeException("Database error"));
        
        when(mockView.getCatalog()).thenReturn("test_db");
        when(mockView.getTableName()).thenReturn("test_view");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertNull(result);
    }

    @Test
    void testWithSchemaInsteadOfCatalog() throws Exception {
        // 测试使用schema而不是catalog的情况
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        when(mockStatement.executeQuery(contains("SHOW CREATE TABLE"))).thenReturn(mockResultSet);
        
        when(mockResultSet.next()).thenReturn(true, false);
        when(mockResultSet.getString("createtab_stmt"))
            .thenReturn("CREATE VIEW schema_view AS SELECT * FROM table1");
        
        when(mockView.getCatalog()).thenReturn(null);
        when(mockView.getSchema()).thenReturn("test_schema");
        when(mockView.getTableName()).thenReturn("schema_view");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertNotNull(result);
        assertTrue(result.contains("SELECT * FROM table1"));
        verify(mockStatement).executeQuery(contains("SHOW CREATE TABLE test_schema.schema_view"));
    }

    @Test
    void testWithoutDatabaseName() throws Exception {
        // 测试没有数据库名称的情况（使用default）
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        when(mockStatement.executeQuery(contains("SHOW CREATE TABLE"))).thenReturn(mockResultSet);
        
        when(mockResultSet.next()).thenReturn(true, false);
        when(mockResultSet.getString("createtab_stmt"))
            .thenReturn("CREATE VIEW simple_view AS SELECT count(*) FROM transactions");
        
        when(mockView.getCatalog()).thenReturn(null);
        when(mockView.getSchema()).thenReturn(null);
        when(mockView.getTableName()).thenReturn("simple_view");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertNotNull(result);
        assertTrue(result.contains("SELECT count(*) FROM transactions"));
        verify(mockStatement).executeQuery(contains("SHOW CREATE TABLE simple_view"));
    }

    @Test
    void testExtractViewDefinitionFromCreateStatement() throws Exception {
        // 测试各种CREATE VIEW语句的解析
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        when(mockStatement.executeQuery(contains("SHOW CREATE TABLE"))).thenReturn(mockResultSet);
        
        // 测试包含复杂AS语句的情况
        when(mockResultSet.next()).thenReturn(true, false);
        when(mockResultSet.getString("createtab_stmt"))
            .thenReturn("CREATE VIEW test_view (id, name, status) AS SELECT u.id, u.full_name, u.account_status FROM users u WHERE u.active = true");
        
        when(mockView.getCatalog()).thenReturn("test");
        when(mockView.getTableName()).thenReturn("test_view");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertNotNull(result);
        assertTrue(result.startsWith("SELECT u.id, u.full_name"));
        assertFalse(result.contains("CREATE VIEW"));
        assertFalse(result.startsWith("AS "));
    }

    @Test
    void testDifferentResultSetColumnNames() throws Exception {
        // 测试不同Hive版本可能使用不同的列名
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        when(mockStatement.executeQuery(contains("SHOW CREATE TABLE"))).thenReturn(mockResultSet);
        
        when(mockResultSet.next()).thenReturn(true, false);
        when(mockResultSet.getString("createtab_stmt")).thenReturn(null); // 标准列名失败
        when(mockResultSet.getString(1)).thenReturn("CREATE VIEW alt_view AS SELECT * FROM alt_table"); // 使用索引
        
        when(mockView.getCatalog()).thenReturn("alternative");
        when(mockView.getTableName()).thenReturn("alt_view");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertNotNull(result);
        assertTrue(result.contains("SELECT * FROM alt_table"));
    }

    @Test
    void testConnectionException() throws Exception {
        // 测试连接异常处理
        when(mockContext.getConnection()).thenThrow(new RuntimeException("Connection failed"));
        when(mockView.getTableName()).thenReturn("test_view");
        when(mockView.getCatalog()).thenReturn("test_db");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertNull(result);
    }

    @Test
    void testEmptyViewDefinition() throws Exception {
        // 测试空的视图定义
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        when(mockStatement.executeQuery(contains("SHOW CREATE TABLE"))).thenReturn(mockResultSet);
        
        when(mockResultSet.next()).thenReturn(false); // 没有结果
        
        // information_schema也返回空
        when(mockStatement.executeQuery(contains("information_schema.views"))).thenReturn(mockResultSet);
        
        when(mockView.getCatalog()).thenReturn("empty_db");
        when(mockView.getTableName()).thenReturn("empty_view");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertNull(result);
    }
}
