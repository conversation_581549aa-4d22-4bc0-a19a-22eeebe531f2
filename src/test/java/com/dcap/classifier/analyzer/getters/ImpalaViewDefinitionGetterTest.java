package com.dcap.classifier.analyzer.getters;

import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.RuleContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * ImpalaViewDefinitionGetter单元测试
 */
class ImpalaViewDefinitionGetterTest {

    @Mock
    private RuleContext ruleContext;
    
    @Mock
    private Connection connection;
    
    @Mock
    private Statement statement;
    
    @Mock
    private ResultSet resultSet;
    
    @Mock
    private ContextTable contextTable;
    
    private ImpalaViewDefinitionGetter getter;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        getter = new ImpalaViewDefinitionGetter();
    }
    
    @Test
    void testGetViewDefinitionWithShowCreateView() throws Exception {
        // 准备测试数据
        when(contextTable.getCatalog()).thenReturn("test_db");
        when(contextTable.getTableName()).thenReturn("test_view");
        when(ruleContext.getConnection()).thenReturn(connection);
        when(connection.createStatement()).thenReturn(statement);
        when(statement.executeQuery(anyString())).thenReturn(resultSet);
        
        // 模拟SHOW CREATE VIEW的返回结果
        when(resultSet.next()).thenReturn(true, true, false);
        when(resultSet.getString(1))
            .thenReturn("CREATE VIEW test_db.test_view AS")
            .thenReturn("SELECT id, name FROM test_table WHERE status = 'active'");
        
        // 执行测试
        String result = getter.getViewDefinition(ruleContext, contextTable);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("SELECT id, name FROM test_table"));
        
        // 验证调用
        verify(statement).executeQuery("SHOW CREATE VIEW test_db.test_view");
    }
    
    @Test
    void testGetViewDefinitionWithDescribeFormatted() throws Exception {
        // 准备测试数据
        when(contextTable.getCatalog()).thenReturn("test_db");
        when(contextTable.getTableName()).thenReturn("test_view");
        when(ruleContext.getConnection()).thenReturn(connection);
        when(connection.createStatement()).thenReturn(statement);
        
        // 第一次调用SHOW CREATE VIEW失败
        when(statement.executeQuery("SHOW CREATE VIEW test_db.test_view"))
            .thenThrow(new RuntimeException("Command not supported"));
            
        // 第二次调用DESCRIBE FORMATTED成功
        when(statement.executeQuery("DESCRIBE FORMATTED test_db.test_view"))
            .thenReturn(resultSet);
        
        when(resultSet.next()).thenReturn(true, true, false);
        when(resultSet.getString(1))
            .thenReturn("view_text")
            .thenReturn("");
        when(resultSet.getString(2))
            .thenReturn("SELECT * FROM users WHERE active = 1")
            .thenReturn("");
        
        // 执行测试
        String result = getter.getViewDefinition(ruleContext, contextTable);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("SELECT * FROM users"));
        
        // 验证调用
        verify(statement).executeQuery("SHOW CREATE VIEW test_db.test_view");
        verify(statement).executeQuery("DESCRIBE FORMATTED test_db.test_view");
    }
    
    @Test
    void testGetViewDefinitionWithNullCatalog() throws Exception {
        // 准备测试数据 - catalog为null，使用schema
        when(contextTable.getCatalog()).thenReturn(null);
        when(contextTable.getSchema()).thenReturn("test_schema");
        when(contextTable.getTableName()).thenReturn("test_view");
        when(ruleContext.getConnection()).thenReturn(connection);
        when(connection.createStatement()).thenReturn(statement);
        when(statement.executeQuery(anyString())).thenReturn(resultSet);
        
        when(resultSet.next()).thenReturn(true, false);
        when(resultSet.getString(1))
            .thenReturn("CREATE VIEW test_schema.test_view AS SELECT 1");
        
        // 执行测试
        String result = getter.getViewDefinition(ruleContext, contextTable);
        
        // 验证结果
        assertNotNull(result);
        
        // 验证调用
        verify(statement).executeQuery("SHOW CREATE VIEW test_schema.test_view");
    }
    
    @Test
    void testGetViewDefinitionWithNoSchemaOrCatalog() throws Exception {
        // 准备测试数据 - catalog和schema都为null
        when(contextTable.getCatalog()).thenReturn(null);
        when(contextTable.getSchema()).thenReturn(null);
        when(contextTable.getTableName()).thenReturn("test_view");
        when(ruleContext.getConnection()).thenReturn(connection);
        when(connection.createStatement()).thenReturn(statement);
        when(statement.executeQuery(anyString())).thenReturn(resultSet);
        
        when(resultSet.next()).thenReturn(true, false);
        when(resultSet.getString(1))
            .thenReturn("CREATE VIEW test_view AS SELECT 1");
        
        // 执行测试
        String result = getter.getViewDefinition(ruleContext, contextTable);
        
        // 验证结果
        assertNotNull(result);
        
        // 验证调用
        verify(statement).executeQuery("SHOW CREATE VIEW test_view");
    }
    
    @Test
    void testGetViewDefinitionAllStrategiesFail() throws Exception {
        // 准备测试数据
        when(contextTable.getCatalog()).thenReturn("test_db");
        when(contextTable.getTableName()).thenReturn("test_view");
        when(ruleContext.getConnection()).thenReturn(connection);
        when(connection.createStatement()).thenReturn(statement);
        
        // 所有策略都失败
        when(statement.executeQuery(anyString())).thenThrow(new RuntimeException("Connection failed"));
        
        // 执行测试
        String result = getter.getViewDefinition(ruleContext, contextTable);
        
        // 验证结果
        assertNull(result);
    }
    
    @Test
    void testGetViewDefinitionEmptyResult() throws Exception {
        // 准备测试数据
        when(contextTable.getCatalog()).thenReturn("test_db");
        when(contextTable.getTableName()).thenReturn("test_view");
        when(ruleContext.getConnection()).thenReturn(connection);
        when(connection.createStatement()).thenReturn(statement);
        when(statement.executeQuery(anyString())).thenReturn(resultSet);
        
        // 返回空结果
        when(resultSet.next()).thenReturn(false);
        
        // 执行测试
        String result = getter.getViewDefinition(ruleContext, contextTable);
        
        // 验证结果
        assertNull(result);
    }
    
    @Test
    void testExtractViewDefinitionFromCreateStatement() throws Exception {
        // 通过反射测试私有方法的逻辑 - 这里我们通过集成测试来验证
        when(contextTable.getCatalog()).thenReturn("test_db");
        when(contextTable.getTableName()).thenReturn("test_view");
        when(ruleContext.getConnection()).thenReturn(connection);
        when(connection.createStatement()).thenReturn(statement);
        when(statement.executeQuery(anyString())).thenReturn(resultSet);
        
        // 模拟完整的CREATE VIEW语句
        when(resultSet.next()).thenReturn(true, false);
        when(resultSet.getString(1))
            .thenReturn("CREATE VIEW test_db.test_view (id, name) AS SELECT user_id, user_name FROM users WHERE status = 'active'");
        
        // 执行测试
        String result = getter.getViewDefinition(ruleContext, contextTable);
        
        // 验证结果 - 应该提取出AS后面的SELECT语句
        assertNotNull(result);
        assertTrue(result.contains("SELECT user_id, user_name FROM users"));
        assertFalse(result.contains("CREATE VIEW"));
    }

    @Test
    void testConnectionException() throws Exception {
        // 准备测试数据
        when(contextTable.getCatalog()).thenReturn("test_db");
        when(contextTable.getTableName()).thenReturn("test_view");
        when(ruleContext.getConnection()).thenReturn(connection);
        
        // 模拟连接异常
        when(connection.createStatement()).thenThrow(new RuntimeException("Connection timeout"));
        
        // 执行测试
        String result = getter.getViewDefinition(ruleContext, contextTable);
        
        // 验证结果
        assertNull(result);
    }
}
