package com.dcap.classifier.analyzer.getters;

import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.RuleContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.contains;
import static org.mockito.Mockito.*;

/**
 * Db2ViewDefinitionGetter 单元测试
 */
public class Db2ViewDefinitionGetterTest {

    private Db2ViewDefinitionGetter getter;
    
    @Mock
    private RuleContext mockContext;
    
    @Mock
    private ContextTable mockView;
    
    @Mock
    private Connection mockConnection;
    
    @Mock
    private Statement mockStatement;
    
    @Mock
    private ResultSet mockResultSet;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        getter = new Db2ViewDefinitionGetter();
    }

    @Test
    void testSyscatViewsSuccess() throws Exception {
        // 设置模拟数据
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        when(mockStatement.executeQuery(contains("SYSCAT.VIEWS"))).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getString("TEXT")).thenReturn(
            "SELECT EMPNO, FIRSTNME, LASTNAME FROM EMPLOYEE WHERE WORKDEPT = 'A00'"
        );
        
        when(mockView.getSchema()).thenReturn("TESTDB");
        when(mockView.getTableName()).thenReturn("EMP_VIEW");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertNotNull(result);
        assertTrue(result.contains("SELECT EMPNO, FIRSTNME, LASTNAME"));
        assertTrue(result.contains("FROM EMPLOYEE"));
        verify(mockStatement).executeQuery(contains("SYSCAT.VIEWS"));
        verify(mockStatement).executeQuery(contains("VIEWSCHEMA = 'TESTDB'"));
        verify(mockStatement).executeQuery(contains("VIEWNAME = 'EMP_VIEW'"));
    }

    @Test
    void testSyscatViewsWithComplexQuery() throws Exception {
        // 测试复杂视图查询
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        when(mockStatement.executeQuery(contains("SYSCAT.VIEWS"))).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getString("TEXT")).thenReturn(
            "SELECT E.EMPNO, E.FIRSTNME, E.LASTNAME, D.DEPTNAME, E.SALARY " +
            "FROM EMPLOYEE E INNER JOIN DEPARTMENT D ON E.WORKDEPT = D.DEPTNO " +
            "WHERE E.SALARY > 50000"
        );
        
        when(mockView.getSchema()).thenReturn("HR");
        when(mockView.getTableName()).thenReturn("HIGH_SALARY_EMP");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertNotNull(result);
        assertTrue(result.contains("INNER JOIN DEPARTMENT"));
        assertTrue(result.contains("WHERE E.SALARY > 50000"));
    }

    @Test
    void testFallbackToInformationSchema() throws Exception {
        // SYSCAT.VIEWS失败，使用INFORMATION_SCHEMA作为后备
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        
        // SYSCAT.VIEWS失败
        when(mockStatement.executeQuery(contains("SYSCAT.VIEWS")))
            .thenThrow(new RuntimeException("Table not accessible"));
        
        // INFORMATION_SCHEMA成功
        when(mockStatement.executeQuery(contains("INFORMATION_SCHEMA.VIEWS"))).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getString("VIEW_DEFINITION")).thenReturn("SELECT * FROM CUSTOMER");
        
        when(mockView.getSchema()).thenReturn("SALES");
        when(mockView.getTableName()).thenReturn("CUSTOMER_VIEW");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertEquals("SELECT * FROM CUSTOMER", result);
        verify(mockStatement).executeQuery(contains("INFORMATION_SCHEMA.VIEWS"));
    }

    @Test
    void testFallbackToSysibmSysviews() throws Exception {
        // 前两种方法都失败，使用SYSIBM.SYSVIEWS
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        
        // SYSCAT.VIEWS失败
        when(mockStatement.executeQuery(contains("SYSCAT.VIEWS")))
            .thenThrow(new RuntimeException("Access denied"));
        
        // INFORMATION_SCHEMA失败
        when(mockStatement.executeQuery(contains("INFORMATION_SCHEMA.VIEWS")))
            .thenThrow(new RuntimeException("Not supported"));
        
        // SYSIBM.SYSVIEWS成功
        when(mockStatement.executeQuery(contains("SYSIBM.SYSVIEWS"))).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getString("TEXT")).thenReturn("SELECT ORDER_ID, CUSTOMER_ID FROM ORDERS");
        
        when(mockView.getSchema()).thenReturn("LEGACY");
        when(mockView.getTableName()).thenReturn("ORDER_VIEW");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertEquals("SELECT ORDER_ID, CUSTOMER_ID FROM ORDERS", result);
        verify(mockStatement).executeQuery(contains("SYSIBM.SYSVIEWS"));
        verify(mockStatement).executeQuery(contains("CREATOR = 'LEGACY'"));
    }

    @Test
    void testWithNullSchema() throws Exception {
        // 测试schema为null的情况，应该获取当前schema
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        
        // 模拟获取当前schema
        ResultSet currentSchemaRs = mock(ResultSet.class);
        when(currentSchemaRs.next()).thenReturn(true);
        when(currentSchemaRs.getString(1)).thenReturn("DB2ADMIN");
        when(mockStatement.executeQuery("VALUES(CURRENT SCHEMA)")).thenReturn(currentSchemaRs);
        
        // 模拟视图查询
        when(mockStatement.executeQuery(contains("SYSCAT.VIEWS"))).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getString("TEXT")).thenReturn("SELECT * FROM PRODUCTS");
        
        when(mockView.getSchema()).thenReturn(null);
        when(mockView.getTableName()).thenReturn("PRODUCT_VIEW");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertNotNull(result);
        assertEquals("SELECT * FROM PRODUCTS", result);
        verify(mockStatement).executeQuery("VALUES(CURRENT SCHEMA)");
        verify(mockStatement).executeQuery(contains("VIEWSCHEMA = 'DB2ADMIN'"));
    }

    @Test
    void testWithEmptySchema() throws Exception {
        // 测试schema为空字符串的情况
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        
        // 模拟获取当前schema失败，使用默认值
        when(mockStatement.executeQuery("VALUES(CURRENT SCHEMA)"))
            .thenThrow(new RuntimeException("Query failed"));
        
        // 模拟视图查询
        when(mockStatement.executeQuery(contains("SYSCAT.VIEWS"))).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getString("TEXT")).thenReturn("SELECT COUNT(*) FROM TRANSACTIONS");
        
        when(mockView.getSchema()).thenReturn("   "); // 空白字符串
        when(mockView.getTableName()).thenReturn("TRANS_SUMMARY");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertNotNull(result);
        assertEquals("SELECT COUNT(*) FROM TRANSACTIONS", result);
        // 应该使用默认的DB2INST1 schema
        verify(mockStatement).executeQuery(contains("VIEWSCHEMA = 'DB2INST1'"));
    }

    @Test
    void testAllMethodsFail() throws Exception {
        // 所有查询方法都失败
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        
        // 所有查询都失败
        when(mockStatement.executeQuery(anyString()))
            .thenThrow(new RuntimeException("Database connection error"));
        
        when(mockView.getSchema()).thenReturn("TEST");
        when(mockView.getTableName()).thenReturn("TEST_VIEW");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertNull(result);
    }

    @Test
    void testCaseInsensitiveNames() throws Exception {
        // 测试大小写不敏感的名称处理
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        when(mockStatement.executeQuery(contains("SYSCAT.VIEWS"))).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getString("TEXT")).thenReturn("SELECT * FROM mixed_case_table");
        
        when(mockView.getSchema()).thenReturn("MixedCaseSchema");
        when(mockView.getTableName()).thenReturn("mixedCaseView");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertNotNull(result);
        // 验证查询中使用了大写的名称
        verify(mockStatement).executeQuery(contains("VIEWSCHEMA = 'MIXEDCASESCHEMA'"));
        verify(mockStatement).executeQuery(contains("VIEWNAME = 'MIXEDCASEVIEW'"));
    }

    @Test
    void testEmptyViewDefinition() throws Exception {
        // 测试空的视图定义
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        when(mockStatement.executeQuery(contains("SYSCAT.VIEWS"))).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getString("TEXT")).thenReturn("   "); // 空白字符串
        
        when(mockView.getSchema()).thenReturn("EMPTY");
        when(mockView.getTableName()).thenReturn("EMPTY_VIEW");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertNull(result); // 应该fallback到下一个策略
        // 验证尝试了后续的查询方法
        verify(mockStatement).executeQuery(contains("INFORMATION_SCHEMA.VIEWS"));
    }

    @Test
    void testViewNotFound() throws Exception {
        // 测试视图不存在的情况
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        
        // 所有查询都返回空结果
        when(mockStatement.executeQuery(anyString())).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(false);
        
        when(mockView.getSchema()).thenReturn("NONEXISTENT");
        when(mockView.getTableName()).thenReturn("MISSING_VIEW");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertNull(result);
    }

    @Test
    void testConnectionException() throws Exception {
        // 测试连接异常
        when(mockContext.getConnection()).thenThrow(new RuntimeException("Connection failed"));
        when(mockView.getSchema()).thenReturn("TEST");
        when(mockView.getTableName()).thenReturn("TEST_VIEW");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertNull(result);
    }

    @Test
    void testComplexViewDefinitionParsing() throws Exception {
        // 测试复杂的视图定义解析
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        when(mockStatement.executeQuery(contains("SYSCAT.VIEWS"))).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getString("TEXT")).thenReturn(
            "  SELECT \n" +
            "    E.EMPNO,\n" +
            "    E.FIRSTNME || ' ' || E.LASTNAME AS FULLNAME,\n" +
            "    CASE \n" +
            "      WHEN E.SALARY > 100000 THEN 'HIGH'\n" +
            "      WHEN E.SALARY > 50000 THEN 'MEDIUM'\n" +
            "      ELSE 'LOW'\n" +
            "    END AS SALARY_GRADE\n" +
            "  FROM EMPLOYEE E\n" +
            "  WHERE E.JOB <> 'INTERN'  "
        );
        
        when(mockView.getSchema()).thenReturn("HR");
        when(mockView.getTableName()).thenReturn("EMP_GRADES");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertNotNull(result);
        assertTrue(result.contains("CASE"));
        assertTrue(result.contains("SALARY_GRADE"));
        assertTrue(result.contains("WHERE E.JOB"));
        // 验证多余空白被清理
        assertFalse(result.startsWith(" "));
        assertFalse(result.endsWith(" "));
    }

    @Test
    void testCurrentSchemaQuerySuccess() throws Exception {
        // 测试成功获取当前schema
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        
        // 模拟成功的当前schema查询
        ResultSet schemaRs = mock(ResultSet.class);
        when(schemaRs.next()).thenReturn(true);
        when(schemaRs.getString(1)).thenReturn("  CURRENT_USER  "); // 包含空白字符
        when(mockStatement.executeQuery("VALUES(CURRENT SCHEMA)")).thenReturn(schemaRs);
        
        // 模拟视图查询
        when(mockStatement.executeQuery(contains("SYSCAT.VIEWS"))).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getString("TEXT")).thenReturn("SELECT * FROM USER_DATA");
        
        when(mockView.getSchema()).thenReturn(null);
        when(mockView.getTableName()).thenReturn("MY_VIEW");

        String result = getter.getViewDefinition(mockContext, mockView);
        
        assertNotNull(result);
        // 验证使用了清理后的schema名称
        verify(mockStatement).executeQuery(contains("VIEWSCHEMA = 'CURRENT_USER'"));
    }
}
