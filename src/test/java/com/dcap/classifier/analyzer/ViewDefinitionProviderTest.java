package com.dcap.classifier.analyzer;

import com.dcap.classifier.context.ContextTable;
import com.dcap.classifier.context.RuleContext;
import com.dcap.datalayer.DataSourceType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * ViewDefinitionProvider 单元测试
 */
public class ViewDefinitionProviderTest {

    @Mock
    private RuleContext mockContext;
    
    @Mock
    private ContextTable mockView;
    
    @Mock
    private Connection mockConnection;
    
    @Mock
    private Statement mockStatement;
    
    @Mock
    private ResultSet mockResultSet;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testIsSupportedForRegisteredDatabases() {
        assertTrue(ViewDefinitionProvider.isSupported(DataSourceType.MYSQL));
        assertTrue(ViewDefinitionProvider.isSupported(DataSourceType.ORACLE));
        assertTrue(ViewDefinitionProvider.isSupported(DataSourceType.POSTGRESQL));
        assertTrue(ViewDefinitionProvider.isSupported(DataSourceType.MSSQL));
        assertTrue(ViewDefinitionProvider.isSupported(DataSourceType.HIVE));
        assertTrue(ViewDefinitionProvider.isSupported(DataSourceType.DB2));
    }

    @Test
    void testIsNotSupportedForUnregisteredDatabases() {
        assertFalse(ViewDefinitionProvider.isSupported(DataSourceType.MONGODB));
        assertFalse(ViewDefinitionProvider.isSupported(DataSourceType.REDIS));
        assertFalse(ViewDefinitionProvider.isSupported(DataSourceType.CLICKHOUSE));
        assertFalse(ViewDefinitionProvider.isSupported(DataSourceType.ELASTIC_SEARCH));
    }

    @Test
    void testGetViewDefinitionForUnsupportedDatabase() {
        when(mockView.getTableName()).thenReturn("test_view");
        String result = ViewDefinitionProvider.getViewDefinition(DataSourceType.MONGODB, mockContext, mockView);
        assertNull(result);
    }

    @Test
    void testPostgreSQLViewDefinitionSuccess() throws Exception {
        // 设置模拟数据
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        when(mockStatement.executeQuery(anyString())).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getString("view_definition")).thenReturn("SELECT * FROM test_table");
        
        when(mockView.getSchema()).thenReturn("public");
        when(mockView.getTableName()).thenReturn("test_view");

        String result = ViewDefinitionProvider.getViewDefinition(DataSourceType.POSTGRESQL, mockContext, mockView);
        
        assertEquals("SELECT * FROM test_table", result);
        verify(mockStatement).executeQuery(contains("information_schema.views"));
    }

    @Test
    void testPostgreSQLViewDefinitionFallbackToPgViews() throws Exception {
        // 设置模拟数据 - information_schema 返回空结果
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        
        // 第一次查询返回空结果
        ResultSet emptyResultSet = mock(ResultSet.class);
        when(emptyResultSet.next()).thenReturn(false);
        
        // 第二次查询返回有效结果
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getString("definition")).thenReturn("SELECT col1, col2 FROM base_table");
        
        when(mockStatement.executeQuery(contains("information_schema.views"))).thenReturn(emptyResultSet);
        when(mockStatement.executeQuery(contains("pg_views"))).thenReturn(mockResultSet);
        
        when(mockView.getSchema()).thenReturn("public");
        when(mockView.getTableName()).thenReturn("test_view");

        String result = ViewDefinitionProvider.getViewDefinition(DataSourceType.POSTGRESQL, mockContext, mockView);
        
        assertEquals("SELECT col1, col2 FROM base_table", result);
        verify(mockStatement).executeQuery(contains("information_schema.views"));
        verify(mockStatement).executeQuery(contains("pg_views"));
    }

    @Test
    void testSqlServerViewDefinitionSuccess() throws Exception {
        // 设置模拟数据
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        when(mockStatement.executeQuery(anyString())).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getString("definition")).thenReturn(
            "CREATE VIEW test_view AS SELECT id, name FROM users WHERE active = 1"
        );
        
        when(mockView.getSchema()).thenReturn("dbo");
        when(mockView.getTableName()).thenReturn("test_view");

        String result = ViewDefinitionProvider.getViewDefinition(DataSourceType.MSSQL, mockContext, mockView);
        
        assertEquals("SELECT id, name FROM users WHERE active = 1", result);
        verify(mockStatement).executeQuery(contains("sys.views"));
    }

    @Test
    void testSqlServerViewDefinitionWithComplexAs() throws Exception {
        // 测试复杂的 CREATE VIEW ... AS 语句
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        when(mockStatement.executeQuery(anyString())).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getString("definition")).thenReturn(
            "CREATE VIEW [dbo].[complex_view] AS\n" +
            "SELECT u.id, u.name, p.title\n" +
            "FROM users u\n" +
            "JOIN profiles p ON u.id = p.user_id\n" +
            "WHERE u.status = 'ACTIVE'"
        );
        
        when(mockView.getSchema()).thenReturn("dbo");
        when(mockView.getTableName()).thenReturn("complex_view");

        String result = ViewDefinitionProvider.getViewDefinition(DataSourceType.MSSQL, mockContext, mockView);
        
        assertTrue(result.startsWith("SELECT"));
        assertTrue(result.contains("FROM users u"));
        assertTrue(result.contains("JOIN profiles p"));
    }

    @Test
    void testSqlServerViewDefinitionFallbackToInformationSchema() throws Exception {
        // 设置模拟数据 - sys.views 查询失败，使用 INFORMATION_SCHEMA 作为后备
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        
        // 第一次查询返回空结果
        ResultSet emptyResultSet = mock(ResultSet.class);
        when(emptyResultSet.next()).thenReturn(false);
        
        // 第二次查询返回有效结果
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getString("VIEW_DEFINITION")).thenReturn("SELECT * FROM base_table");
        
        when(mockStatement.executeQuery(contains("sys.views"))).thenReturn(emptyResultSet);
        when(mockStatement.executeQuery(contains("INFORMATION_SCHEMA.VIEWS"))).thenReturn(mockResultSet);
        
        when(mockView.getSchema()).thenReturn("dbo");
        when(mockView.getTableName()).thenReturn("test_view");

        String result = ViewDefinitionProvider.getViewDefinition(DataSourceType.MSSQL, mockContext, mockView);
        
        assertEquals("SELECT * FROM base_table", result);
    }

    @Test
    void testExceptionHandling() throws Exception {
        // 测试异常处理
        when(mockContext.getConnection()).thenThrow(new RuntimeException("Connection failed"));
        when(mockView.getTableName()).thenReturn("test_view");
        when(mockView.getSchema()).thenReturn("public");

        String result = ViewDefinitionProvider.getViewDefinition(DataSourceType.POSTGRESQL, mockContext, mockView);
        
        assertNull(result);
    }

    @Test
    void testPostgreSQLWithNullSchema() throws Exception {
        // 测试 schema 为 null 的情况，应该默认使用 "public"
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        when(mockStatement.executeQuery(anyString())).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getString("view_definition")).thenReturn("SELECT * FROM test_table");
        
        when(mockView.getSchema()).thenReturn(null); // null schema
        when(mockView.getTableName()).thenReturn("test_view");

        String result = ViewDefinitionProvider.getViewDefinition(DataSourceType.POSTGRESQL, mockContext, mockView);
        
        assertEquals("SELECT * FROM test_table", result);
        // 验证查询中使用了 "public" schema
        verify(mockStatement).executeQuery(contains("table_schema = 'public'"));
    }

    @Test
    void testSqlServerWithNullSchema() throws Exception {
        // 测试 schema 为 null 的情况，应该默认使用 "dbo"
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        when(mockStatement.executeQuery(anyString())).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getString("definition")).thenReturn("CREATE VIEW test AS SELECT * FROM table1");
        
        when(mockView.getSchema()).thenReturn(null); // null schema
        when(mockView.getTableName()).thenReturn("test_view");

        String result = ViewDefinitionProvider.getViewDefinition(DataSourceType.MSSQL, mockContext, mockView);
        
        assertEquals("SELECT * FROM table1", result);
        // 验证查询中使用了 "dbo" schema
        verify(mockStatement).executeQuery(contains("s.name = 'dbo'"));
    }

    @Test
    void testHiveViewDefinitionSuccess() throws Exception {
        // 测试Hive视图定义获取
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        when(mockStatement.executeQuery(contains("SHOW CREATE TABLE"))).thenReturn(mockResultSet);
        
        when(mockResultSet.next()).thenReturn(true, true, false);
        when(mockResultSet.getString("createtab_stmt"))
            .thenReturn("CREATE VIEW hive_view AS")
            .thenReturn("SELECT customer_id, sum(order_amount) FROM orders GROUP BY customer_id");
        
        when(mockView.getCatalog()).thenReturn("analytics");
        when(mockView.getTableName()).thenReturn("hive_view");

        String result = ViewDefinitionProvider.getViewDefinition(DataSourceType.HIVE, mockContext, mockView);
        
        assertNotNull(result);
        assertTrue(result.contains("SELECT customer_id, sum(order_amount)"));
        assertTrue(result.contains("FROM orders GROUP BY customer_id"));
        verify(mockStatement).executeQuery(contains("SHOW CREATE TABLE analytics.hive_view"));
    }

    @Test
    void testHiveViewDefinitionFallback() throws Exception {
        // 测试Hive的后备查询机制
        when(mockContext.getConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        
        // SHOW CREATE TABLE失败
        when(mockStatement.executeQuery(contains("SHOW CREATE TABLE")))
            .thenThrow(new RuntimeException("Command not supported in this Hive version"));
        
        // information_schema成功
        when(mockStatement.executeQuery(contains("information_schema.views"))).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getString("view_definition")).thenReturn("SELECT * FROM legacy_table");
        
        when(mockView.getCatalog()).thenReturn("legacy_db");
        when(mockView.getTableName()).thenReturn("legacy_view");

        String result = ViewDefinitionProvider.getViewDefinition(DataSourceType.HIVE, mockContext, mockView);
        
        assertEquals("SELECT * FROM legacy_table", result);
        verify(mockStatement).executeQuery(contains("information_schema.views"));
    }
}
