package com.yd.dcap.probe;

import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.Serdes;
import org.apache.kafka.common.serialization.StringSerializer;
import org.apache.kafka.streams.KafkaStreams;
import org.apache.kafka.streams.StreamsBuilder;
import org.apache.kafka.streams.StreamsConfig;
import org.apache.kafka.streams.kstream.KStream;
import org.apache.kafka.streams.kstream.TimeWindows;
import org.apache.kafka.streams.kstream.Windowed;

import java.time.Duration;
import java.time.Instant;
import java.util.Properties;
import java.util.concurrent.CountDownLatch;

public class KafkaStreamWriteDemo {

    // Kafka主题
    private static final String INPUT_TOPIC = "log-input-topic";

    // 写入日志到Kafka
    public static void writeLogToKafka(String message) {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "172.27.240.42:32092");
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());

        KafkaProducer<String, String> producer = new KafkaProducer<>(props);
        ProducerRecord<String, String> record = new ProducerRecord<>(INPUT_TOPIC, "logKey", message);

        producer.send(record, (metadata, exception) -> {
            if (exception == null) {
                System.out.println("日志消息已发送: " + message+", date:" + Instant.now());
                System.out.println("发送到分区: " + metadata.partition() + ", 偏移量: " + metadata.offset());
            } else {
                System.err.println("发送日志消息时出错: " + exception.getMessage());
            }
        });

        producer.flush();
        producer.close();
    }

    // 启动Kafka Streams进行窗口聚合
    public static void startKafkaStreamsProcessing() {
        Properties props = new Properties();
        props.put(StreamsConfig.APPLICATION_ID_CONFIG, "log-aggregation-app");
        props.put(StreamsConfig.BOOTSTRAP_SERVERS_CONFIG, "172.27.240.42:32092");
        props.put(StreamsConfig.DEFAULT_KEY_SERDE_CLASS_CONFIG, Serdes.String().getClass().getName());
        props.put(StreamsConfig.DEFAULT_VALUE_SERDE_CLASS_CONFIG, Serdes.String().getClass().getName());

        StreamsBuilder builder = new StreamsBuilder();

        // 创建输入流
        KStream<String, String> logStream = builder.stream(INPUT_TOPIC);

        // 设置30秒窗口并聚合
        logStream
                .groupByKey()
                .windowedBy(
                        TimeWindows.of(Duration.ofSeconds(6))
                        .advanceBy(Duration.ofSeconds(6))  // 每次前进30秒
                        .grace(Duration.ofSeconds(5))
                )      // 允许5秒的宽限期
                .count()
                .toStream()
                .foreach((Windowed<String> key, Long count) -> {
                    // 窗口结束后触发事件，打印消息
                    System.out.println("====================================");
                    System.out.println("窗口聚合触发事件！");
                    System.out.println("时间窗口: " + key.window().startTime() + " 到 " + key.window().endTime());
                    System.out.println("键: " + key.key());
                    System.out.println("30秒内收到的日志消息数: " + count);
                    System.out.println("====================================");
                });

        // 构建和启动Kafka Streams
        KafkaStreams streams = new KafkaStreams(builder.build(), props);

        // 优雅关闭
        CountDownLatch latch = new CountDownLatch(1);
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            streams.close();
            latch.countDown();
        }));

        try {
            streams.start();
            latch.await();
        } catch (InterruptedException e) {
            System.err.println("Streams应用程序中断: " + e.getMessage());
            System.exit(1);
        }
    }

    // 主函数演示
    public static void main(String[] args) throws InterruptedException {
        // 写入一些日志消息
        System.out.println("开始发送日志消息...");
        for (int i = 1; i <= 100; i++) {
            writeLogToKafka("这是测试日志消息 #" + i);
            Thread.sleep(1000); // 每 1 秒发送一条消息
        }
    }
}
