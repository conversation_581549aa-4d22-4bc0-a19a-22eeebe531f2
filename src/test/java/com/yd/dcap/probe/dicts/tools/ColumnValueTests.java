package com.yd.dcap.probe.dicts.tools;


import com.google.common.collect.Lists;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.rules.engine.ScoringModel;
import com.yd.rules.engine.models.ScoringModelInput;
import com.yd.rules.engine.models.ScoringModelOutput;
import com.yd.rules.engine.parser.CustomScoreParser;
import com.yd.rules.engine.parser.ScoringModelParser;
import com.yd.rules.engine.parser.ScoringRuleParser;
import com.yd.rules.engine.result.MatchedResult;
import org.apache.commons.io.IOUtils;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class ColumnValueTests {
    private ScoringModel scoringModel;
    private final String dataTag = "data_tag:basic:12345678";
    private final String dataTagType = "BASIC";
    private final int dataTagOrderBy = 50;

    public void setUp() throws IOException {

    }


    public void testColumnValue() throws IOException {
        String modelFileName = "疾病名称.yaml";
        String modelText = IOUtils.toString(
                Objects.requireNonNull(this.getClass().getResourceAsStream("/dicts/gen/" + modelFileName)),
                Charset.defaultCharset()
        );
        ScoringModelParser scoringModelParser = new ScoringModelParser(new CustomScoreParser(new ScoringRuleParser()));
        scoringModel = scoringModelParser.parseScoringModel(dataTag, dataTagType, modelText, dataTagOrderBy);

        Map<String, Object> modelInputData = UtilMisc.toMap(
                "dataStore", UtilMisc.toMap(
                        "name", "数据库名称"
                ),
                "table", UtilMisc.toMap(
                        "name", "person",
                        "type", "表类型",
                        "comment", "表注释",
                        "catalog", "db catalog",
                        "schema", "db schema"
                ),
                "column", UtilMisc.toMap(
                        "name", "address",
                        "type", "text",
                        "comment", "办公地址",
                        "size", 100,
                        "values", Lists.newArrayList(
                                "  新生儿病理性黄疸 "
                        ),
                        // 该列 当前已经自动标记的。这包含了分数
                        "currentMarked", new HashMap<>(),
                        // 该列 已经手动打标的
                        "manualTags", new HashSet<>(),
                        // 该列 已经排除的标签
                        "deletedAutoTags", new HashSet<>()
                ),
                "dataTag", dataTag
        );

        ScoringModelOutput interpret = scoringModel.interpret(new ScoringModelInput(modelInputData));
        MatchedResult matchedResult = interpret.getMatchedResult(0);
        int score = matchedResult.getScore();
        String evaluation = matchedResult.getEvaluation();

        assertEquals(dataTag, matchedResult.getDataTag());
        // 应该是 100 分
        assertEquals(100, score);
        assertEquals(MatchedResult.CONFIRMED, evaluation);
    }


    public void testColumnComment() throws IOException {
        String modelFileName = "0.9%氯化钠ml.yaml";
        String modelText = IOUtils.toString(
                Objects.requireNonNull(this.getClass().getResourceAsStream("/dicts/gen/" + modelFileName)),
                Charset.defaultCharset()
        );
        ScoringModelParser scoringModelParser = new ScoringModelParser(new CustomScoreParser(new ScoringRuleParser()));
        scoringModel = scoringModelParser.parseScoringModel(dataTag, dataTagType, modelText, dataTagOrderBy);
        Map<String, Object> modelInputData = UtilMisc.toMap(
                "dataStore", UtilMisc.toMap(
                        "name", "数据库名称"
                ),
                "table", UtilMisc.toMap(
                        "name", "person",
                        "type", "表类型",
                        "comment", "表注释",
                        "catalog", "db catalog",
                        "schema", "db schema"
                ),
                "column", UtilMisc.toMap(
                        "name", "address",
                        "type", "text",
                        "comment", " 0.9%氯化钠ml ",
                        "size", 100,
                        "values", Lists.newArrayList(
                                "  IDS "
                        ),
                        // 该列 当前已经自动标记的。这包含了分数
                        "currentMarked", new HashMap<>(),
                        // 该列 已经手动打标的
                        "manualTags", new HashSet<>(),
                        // 该列 已经排除的标签
                        "deletedAutoTags", new HashSet<>()
                ),
                "dataTag", dataTag
        );

        ScoringModelOutput interpret = scoringModel.interpret(new ScoringModelInput(modelInputData));
        MatchedResult matchedResult = interpret.getMatchedResult(0);
        int score = matchedResult.getScore();
        String evaluation = matchedResult.getEvaluation();

        assertEquals(dataTag, matchedResult.getDataTag());
        // 应该是 100 分
        assertEquals(100, score);
        assertEquals(MatchedResult.CONFIRMED, evaluation);
    }

}
