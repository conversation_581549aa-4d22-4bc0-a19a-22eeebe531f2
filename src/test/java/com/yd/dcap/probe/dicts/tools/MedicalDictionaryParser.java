package com.yd.dcap.probe.dicts.tools;

import cdjd.org.apache.curator.shaded.com.google.common.collect.Sets;
import com.dcap.utils.JSON;
import com.google.common.collect.Lists;
import com.opencsv.bean.CsvToBean;
import com.opencsv.bean.CsvToBeanBuilder;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.dcap.probe.dicts.entity.MedicalDictionary;
import com.yd.dcap.probe.dicts.entity.MedicalType;
import org.apache.commons.lang3.StringUtils;

import java.io.FileWriter;
import java.io.PrintWriter;
import java.io.Reader;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

public class MedicalDictionaryParser {

    public List<MedicalDictionary> parseMedicalDictFile(String filePath) throws Exception {
        try (Reader reader = Files.newBufferedReader(Paths.get(filePath))) {
            CsvToBean<MedicalDictionary> csvToBean = new CsvToBeanBuilder<MedicalDictionary>(reader)
                    .withType(MedicalDictionary.class)
                    .withIgnoreLeadingWhiteSpace(true)
                    .build();
            return csvToBean.parse();
        }
    }


    public void buildMedicalDictList() throws Exception {
        URI uri = ClassLoader.getSystemResource("dicts/medical_dict.csv").toURI();
        String mainPath = Paths.get(uri).toString();
        List<MedicalDictionary> medicalDictionaries = parseMedicalDictFile(
                mainPath
        );
        Map<String, Set<String>> idAndValue = new HashMap<>();
        for (MedicalDictionary medicalDictionary : medicalDictionaries) {
            String dictionaryValue = medicalDictionary.getDictionaryValue();
            String[] split = dictionaryValue.split("\\|");
            Set<String> dictValues = Sets.newHashSet(split);
            idAndValue.put(medicalDictionary.getDictId(), dictValues);
        }
    }

    public List<MedicalType> parseMedicalTypeFile(String filePath) throws Exception {
        try (Reader reader = Files.newBufferedReader(Paths.get(filePath))) {
            CsvToBean<MedicalType> csvToBean = new CsvToBeanBuilder<MedicalType>(reader)
                    .withType(MedicalType.class)
                    .withIgnoreLeadingWhiteSpace(true)
                    .withIgnoreQuotations(false)
                    .build();
            return csvToBean.parse();
        }
    }

    public void buildMedicalTypeList() throws Exception {
        URI uri = ClassLoader.getSystemResource("dicts/medical_dict.csv").toURI();
        String mainPath = Paths.get(uri).toString();
        List<MedicalDictionary> medicalDictionaries = parseMedicalDictFile(
                mainPath
        );
        Map<String, Set<String>> idAndValue = new HashMap<>();
        for (MedicalDictionary medicalDictionary : medicalDictionaries) {
            String dictionaryValue = medicalDictionary.getDictionaryValue();
            String[] split = dictionaryValue.split("\\|");
            Set<String> dictValues = Sets.newHashSet(split);
            idAndValue.put(medicalDictionary.getDictId(), dictValues);
        }

        uri = ClassLoader.getSystemResource("dicts/medical_type.csv").toURI();
        mainPath = Paths.get(uri).toString();
        List<MedicalType> medicalTypeList = parseMedicalTypeFile(
                mainPath
        );

        String dir = "C:\\Users\\<USER>\\IdeaProjects\\dcap_classifier_engine\\src\\test\\resources\\dicts\\gen\\";
        FileWriter fileWriter = new FileWriter(dir+"output.csv", true);
        PrintWriter printWriter = new PrintWriter(fileWriter);
        Map<String,String> typenameAndEncModel = new HashMap<>();

        for (int i = 0; i < medicalTypeList.size(); i++) {
            MedicalType medicalType = medicalTypeList.get(i);
            String dictType = medicalType.getDictType();
            Map<String,Object> yamlObj = null;
            if (StringUtils.isNotBlank(medicalType.getDict())) {
                String [] ids = medicalType.getDict().split("\\|");
                Set<String> values = new HashSet<>();
                for (String id : ids) {
                    Set<String> strings = idAndValue.get(id);
                    if (strings != null && !strings.isEmpty()){
                        values.addAll(strings);
                    }
                }
                if (values.isEmpty()){
                    continue;
                }
                yamlObj = UtilMisc.toMap(
                        "model", "1.0",
                        "desc", dictType,
                        "scores",Lists.newArrayList(
                                UtilMisc.toMap(
                                        "desc", "字典包含列数据",
                                        "name", "ColumnValue",
                                        "output", true,
                                        "rule", UtilMisc.toMap(
                                                ">=", Arrays.asList(
                                                        UtilMisc.toMap(
                                                                "search_in_values",
                                                                Arrays.asList(
                                                                        values,
                                                                        UtilMisc.toMap("var", "column.values")
                                                                )
                                                        ),
                                                        1
                                                )
                                        )
                                ),
                                UtilMisc.toMap(
                                        "desc", "计算得分",
                                        "name", "OverallScore",
                                        "output", true,
                                        "rule", UtilMisc.toMap(
                                                "condition", Arrays.asList(
                                                        UtilMisc.toMap("var", "$.ColumnValue"),
                                                        100,0
                                                )
                                        )
                                )
                        )
                );
            } else {
                // comment 包含
                String ruleValue = dictType.trim().replaceAll("\\(", "\\\\(")
                        .replaceAll("\\)", "\\\\)")
                        .replaceAll("\\[", "\\\\[")
                        .replaceAll("\\]", "\\\\]")
                        .replaceAll("\\{", "\\\\{")
                        .replaceAll("\\}", "\\\\}");
                yamlObj = UtilMisc.toMap(
                        "model", "1.0",
                        "desc", dictType,
                        "scores",Lists.newArrayList(
                                UtilMisc.toMap(
                                        "desc", "列注释包含 "+dictType,
                                        "name", "ColumnComment",
                                        "output", true,
                                        "rule", UtilMisc.toMap(
                                                "regexp_find",
                                                Arrays.asList(
                                                        "\\s*"+convertToUnicode(ruleValue)+"\\s*",
                                                        UtilMisc.toMap("var", "column.comment")
                                                )
                                        )
                                ),
                                UtilMisc.toMap(
                                        "desc", "计算得分",
                                        "name", "OverallScore",
                                        "output", true,
                                        "rule", UtilMisc.toMap(
                                                "condition", Arrays.asList(
                                                        UtilMisc.toMap("var", "$.ColumnComment"),
                                                        100,0
                                                )
                                        )
                                )
                        )
                );
            }
            if (yamlObj == null){
                continue;
            }
            String fileName = dictType.replaceAll("[\"/]", "") +".yaml";
            String encText = JSON.from(yamlObj).toString();
            printWriter.println(dictType + "," + encText); // 写入 dictType 和 encText，用逗号分隔
//            FileUtils.writeStringToFile(new File(dir+fileName), encText, Charset.defaultCharset());
        }
        printWriter.close();
    }

    public static String convertToUnicode(String input) {
        StringBuilder unicode = new StringBuilder();
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            if (c >= '\u4e00' && c <= '\u9fa5') { // 判断是否是中文字符
                unicode.append("\\u").append(Integer.toHexString(c));
            } else {
                unicode.append(c);
            }
        }
        return unicode.toString();
    }
}
