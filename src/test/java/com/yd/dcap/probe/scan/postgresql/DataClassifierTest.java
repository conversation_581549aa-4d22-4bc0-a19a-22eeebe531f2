package com.yd.dcap.probe.scan.postgresql;

import com.yd.dataclassifier.jni.*;
import com.yd.dcap.common.utils.sm4.SM4Utils;
import com.yd.lib.utils.JniLoader;
import org.junit.jupiter.api.Test;
import org.yaml.snakeyaml.Yaml;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;


public class DataClassifierTest {
    static private LibDataClassifierPointer classifierPtr;
    static private final String currentDir;

    static {
        // get current work director
        currentDir = System.getProperty("user.dir");
        initClassifier();
    }

    private static void initClassifier() {
        try {
            // 加载 JNI 库
            JniLoader.load(currentDir + "/data");
            classifierPtr = new LibDataClassifierPointer();

            // 1. 初始化配置
            lib_data_classifier_config config = new lib_data_classifier_config();
            config.setTenant_id(1000);
            config.setConfig_dir(currentDir + "/config");
            config.setLog_dir(currentDir + "/log");
            config.setRemote_service("");
            config.setUser("");
            config.setPassword("");

            // 2. 创建分类器实例
            DATACLS_RET ret = LibDataClassifier.new_lib_data_classifier(config, classifierPtr.cast());
            if (ret != DATACLS_RET.DATACLS_SUCCESS) {
                throw new RuntimeException("创建分类器失败");
            }

            // 3. 启动分类器
            ret = LibDataClassifier.start_lib_data_classifier(classifierPtr.value());
            if (ret != DATACLS_RET.DATACLS_SUCCESS) {
                throw new RuntimeException("启动分类器失败");
            }

            // 4. 设置日志级别
            LibDataClassifier.datacls_set_log_level(DATACLS_LOG_LEVEL.DATACLS_LOG_DEBUG);

            // 7. 清理资源
            // LibDataClassifier.delete_lib_data_classifier(classifierPtr.value());
            
        } catch (Exception e) {
            System.err.println("加载JNI库失败: " + e.getMessage());
            e.printStackTrace();
        }
    }    
    
    /**
     * 对单个样本文件进行分类测试
     * 
     * @param sampleFile 样本文件
     * @throws IOException 文件读取异常
     */
    private void classifySampleFile(File sampleFile) throws IOException {
        String fileName = sampleFile.getName();
        String className = fileName.substring(0, fileName.lastIndexOf("."));
        String expectedTag = "T_" + className;
        
        // System.out.println("\n测试文件: " + fileName);
        // System.out.println("期望标签: " + expectedTag);
        
        // 读取样本文件内容
        List<String> lines = Files.readAllLines(sampleFile.toPath());
        // 移除空行
        lines = lines.stream().filter(line -> !line.trim().isEmpty()).collect(Collectors.toList());
        
        if (lines.isEmpty()) {
            System.out.println("警告: 文件 " + fileName + " 内容为空");
            return;
        }
        
        // 创建列数据对象
        datacls_column_data columnData = new datacls_column_data();
        columnData.setTable_name("test_table");
        columnData.setColumn_name(className);
        columnData.setColumn_comment(className);
        columnData.setColumn_type(DATACLS_COLUMN_TYPE.DATACLS_CT_STRING);
        
        // 设置样本数据
        datacls_string_array sampleData = new datacls_string_array();
        sampleData.createArray(lines.size());
        for (int i = 0; i < lines.size(); i++) {
            sampleData.setString(i, lines.get(i));
        }
        columnData.setColumn_value(sampleData);
        
        // 调用通用分类方法，传入期望标签用于验证
        classifyAndPrintResult(columnData, expectedTag);
    }
    
    /**
     * 通用的分类方法，对列数据进行分类并输出结果
     * 
     * @param columnData 要分类的列数据
     * @param expectedTag 期望匹配的业务标签，如果为null则不进行标签验证
     */
    private void classifyAndPrintResult(datacls_column_data columnData, String expectedTag) {
        // 创建分类配置
        datacls_classify_config classifyConfig = new datacls_classify_config();
        classifyConfig.setScore_threshold(30);
        classifyConfig.setHit_ratio_threshold(10);
        classifyConfig.setQuick_regex_match(false);
        // classifyConfig.setColumn_name_case_insensitive(true);
        
        // 创建结果对象
        datacls_classify_result result = new datacls_classify_result();
        
        // 执行分类
        DATACLS_RET ret = LibDataClassifier.datacls_classify(classifierPtr.value(), columnData, classifyConfig, result);
        
        if (ret != DATACLS_RET.DATACLS_SUCCESS) {
            if (expectedTag != null) {
                fail("分类失败，返回错误码: " + ret);
            } else {
                System.out.println("分类失败，返回错误码: " + ret);
            }
            return;
        }
        
        // System.out.println("分类结果: " + result.getStatus());
        
        // 输出业务标签
        boolean found = false;
        if (result.getBiz_tags().getCount() > 0) {
            System.out.print("业务标签: ");
            for (int i = 0; i < result.getBiz_tags().getCount(); i++) {
                String tag = result.getBiz_tags().getString(i);
                System.out.print(tag + " ");
                if (expectedTag != null && tag.equals(expectedTag)) {
                    found = true;
                }
            }
            System.out.println();
        }

        // 输出技术标签
        if (result.getTech_tags().getCount() > 0) {
            System.out.print("技术标签: ");
            for (int i = 0; i < result.getTech_tags().getCount(); i++) {
                System.out.print(result.getTech_tags().getString(i) + " ");
            }
            System.out.println();
        }

        // 如果指定了期望标签，则验证结果
        if (expectedTag != null) {
            assertTrue(found, "未匹配到预期标签 " + expectedTag);
            System.out.println("✓ 测试通过: 成功匹配到预期标签 " + expectedTag);
        }
    }

    @Test
    public void testClassifySampleFiles() throws IOException {
    // 样本文件所在目录
    Path samplesDir = Paths.get(currentDir, "data", "samples");
    File samplesFolder = samplesDir.toFile();
    
    if (!samplesFolder.exists() || !samplesFolder.isDirectory()) {
        fail("样本目录不存在: " + samplesDir);
        return;
    }
    
    // 获取所有样本文件
    File[] sampleFiles = samplesFolder.listFiles((dir, name) -> name.endsWith(".txt"));
    
    if (sampleFiles == null || sampleFiles.length == 0) {
        fail("样本目录中没有找到任何样本文件");
        return;
    }
    
    // System.out.println("\n开始测试样本文件的分类...");
    // System.out.println("共找到 " + sampleFiles.length + " 个样本文件");
    // 遍历每个样本文件进行测试
    for (File sampleFile : sampleFiles) {
            classifySampleFile(sampleFile);
        }
    }

    @Test
    public void testSM4() throws IOException {
        String conf_model_path = currentDir + "/config/classify_models.yaml";
        String conf_dict_path = currentDir + "/config/classify_dicts.yaml";
        String csvOutputPath = currentDir + "/output/通用特征V3.csv";

        // 确保输出目录存在
        new File(currentDir + "/output").mkdirs();

        // 1. 读取conf_model_path路径的yaml文件，以yaml对象方式加载
        File yamlFile = new File(conf_model_path);
        InputStream inputStream = new FileInputStream(yamlFile);
        
        // 使用SnakeYAML加载YAML文件
        Yaml yaml = new Yaml();
        Map<String, Object> yamlData = yaml.load(inputStream);
        
        // 2. 打印加载后的yaml对象
        System.out.println("\n加载的YAML对象：");
        
        // 3. 准备CSV文件，使用UTF-8编码
        try (OutputStreamWriter csvWriter = new OutputStreamWriter(
                new FileOutputStream(csvOutputPath), StandardCharsets.UTF_8)) {
            // 写入UTF-8 BOM
            csvWriter.write('\ufeff');
            
            // 写入CSV头
            csvWriter.write("数据特征*,数据标签,排序,安全级别,敏感级别,规则类型,适用范围,得分,规则定义,描述\n");
            
            // 处理每个模型
            List<Map<String, Object>> models = (List<Map<String, Object>>) yamlData.get("classify_models");
            for (Map<String, Object> model : models) {
                String modelStr = yaml.dump(model);
                String encryptedStr = SM4Utils.encCheckSm4ForEcb(modelStr);
                String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                
                // 构建CSV行
                String csvLine = String.format("%s,%s,0,C3,S3,StructRuleModel,\"DDP,ADP,SDI\",100,\"%s\",\"Updated on %s\"\n",
                    model.get("name"),
                    model.get("data_tag"),
                    encryptedStr.replace("\"", "\"\""), // 转义CSV中的双引号
                    timestamp
                );
                
                // 写入CSV行
                csvWriter.write(csvLine);
                
                // 打印处理进度
                System.out.println("处理模型: " + model.get("name"));
            }
        }
        
        System.out.println("\nCSV文件已生成: " + csvOutputPath);
    }
} 



