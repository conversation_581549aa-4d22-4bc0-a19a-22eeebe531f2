package com.yd.dcap.probe.scan.hive;

import com.dcap.utils.JSON;
import com.yd.dcap.classifier.ClassifierService;
import com.yd.dcap.classifier.taskreport.TaskReport;
import com.yd.dcap.probe.client.ProbeClientTaskUtil;
import com.yd.dcap.probe.client.TaskConfig;
import com.yd.dcap.probe.entities.ProbeClientTaskContext;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Objects;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

public class HiveScanTests {
    @InjectMocks
    private ClassifierService classifierService;

    @Mock
    private ProbeClientTaskUtil probeClientTaskUtil;

    @Mock
    private ProbeClientTaskContext probeClientTaskContext;


    @Mock
    TaskReport mockTaskReport;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testScanHive2() throws IOException {
        String fileName = "hive2.json";
        String text = IOUtils.toString(
                Objects.requireNonNull(this.getClass().getResourceAsStream("/taskconfig/hive/" + fileName)),
                Charset.defaultCharset()
        );
        TaskConfig taskConfig = JSON.from(text).toObject(TaskConfig.class);
        try (MockedStatic<ProbeClientTaskUtil> mock = Mockito.mockStatic(ProbeClientTaskUtil.class)) {
            mock.when(ProbeClientTaskUtil::getInstance).thenReturn(probeClientTaskUtil);
            when(probeClientTaskUtil.getProbeClientTaskContext(anyString(), anyLong())).thenReturn(probeClientTaskContext);
            when(probeClientTaskContext.recordField(anyString(), any())).thenReturn(mockTaskReport);
            when(probeClientTaskContext.reportExecuting(any(), anyString(), anyString())).thenReturn(mockTaskReport);
            when(probeClientTaskContext.reportSuccess(any(), anyString(), anyString())).thenReturn(mockTaskReport);

            classifierService.scan("SCAN", taskConfig);
        }
    }

}
