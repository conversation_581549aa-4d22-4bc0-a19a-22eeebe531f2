package com.yd.dcap.probe;

import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.ast.expr.SQLIdentifierExpr;
import com.alibaba.druid.sql.ast.expr.SQLPropertyExpr;
import com.alibaba.druid.sql.ast.statement.SQLExprTableSource;
import com.alibaba.druid.sql.ast.statement.SQLSelectItem;
import com.alibaba.druid.sql.ast.statement.SQLSelectStatement;
import com.alibaba.druid.sql.dialect.oracle.ast.stmt.OracleSelectQueryBlock;
import com.alibaba.druid.sql.dialect.oracle.parser.OracleStatementParser;
import com.alibaba.druid.sql.dialect.postgresql.visitor.PGSchemaStatVisitor;
import com.alibaba.druid.sql.parser.SQLStatementParser;
import com.alibaba.druid.sql.visitor.SchemaStatVisitor;
import com.alibaba.druid.stat.TableStat;
import com.alibaba.druid.util.JdbcConstants;
import com.dcap.datalayer.ConnectionException;
import com.dcap.utils.UtilDB;
import org.jetbrains.annotations.NotNull;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SqlParserTests {


    public void getViewSql() throws ConnectionException, SQLException {
        Connection connection = UtilDB.buildDatabaseConnection(
                "oracle",
                "app-alpha.yuandiansec.net",
                30103,
                "scott",
                "first@YD",
                "helowin"
        );
        System.out.println(connection.getCatalog());

        DatabaseMetaData metaData = connection.getMetaData();
        ResultSet resultSet = metaData.getTables(null, "SCOTT", null, new String[]{"VIEW"});
        String viewSql = null;
        while (resultSet.next()) {
            String viewName = resultSet.getString("TABLE_NAME");
            if ("TESTFOUR".equals(viewName)) {
                PreparedStatement ps = connection.prepareStatement(
                        "SELECT text FROM user_views WHERE view_name = ?"
                );
                ps.setString(1, viewName.toUpperCase());
                ResultSet rs = ps.executeQuery();
                if (rs.next()) {
                    viewSql = rs.getString("text");
                    break;
                }
                rs.close();
                ps.close();
                break;
            }
        }

        resultSet.close();
        connection.close();
        System.out.println(viewSql);
//        Map<String, List<String>> tableFieldsMap = getTableFieldsMap(viewSql);
//
//        // 打印结果
//        for (Map.Entry<String, List<String>> entry : tableFieldsMap.entrySet()) {
//            System.out.println("Table: " + entry.getKey());
//            System.out.println("Fields: " + entry.getValue());
//        }

        // 解析 SQL 语句
        List<SQLStatement> stmtList = SQLUtils.parseStatements(viewSql, JdbcConstants.ORACLE);

        // 获取 SQLSelectStatement 对象
        SQLSelectStatement selectStatement = (SQLSelectStatement) stmtList.get(0);

        // 获取 SQLSelectQueryBlock 对象
        OracleSelectQueryBlock queryBlock = (OracleSelectQueryBlock) selectStatement.getSelect().getQuery();

        // 获取 selectList
        List<SQLSelectItem> selectList = queryBlock.getSelectList();

        // 创建 map 用于保存结果
        Map<String, List<String>> resultMap = new HashMap<>();

        // 遍历 selectList
        for (SQLSelectItem item : selectList) {
            // 获取列名
            String columnName = item.getAlias();
            if (columnName == null) {
                columnName = item.getExpr().toString();
            }

            // 获取表达式
            if (item.getExpr() instanceof SQLPropertyExpr) {
                SQLPropertyExpr expr = (SQLPropertyExpr) item.getExpr();

                // 获取表名和字段名
                String tableName = expr.getOwnernName();
                String fieldName = expr.getName();

                // 获取 schema 名称
                String schemaName = null;
                if (queryBlock.getFrom() instanceof SQLExprTableSource) {
                    SQLExprTableSource tableSource = (SQLExprTableSource) queryBlock.getFrom();
                    schemaName = tableSource.getSchemaObject().getSchema().getSimpleName();
                }

                // 组合成 "schema.table.column" 的格式
                String tableField = schemaName + "." + tableName + "." + fieldName;

                // 添加到 map 中
                if (!resultMap.containsKey(columnName)) {
                    resultMap.put(columnName, new ArrayList<>());
                }
                resultMap.get(columnName).add(tableField);
            } else if (item.getExpr() instanceof SQLIdentifierExpr) {
                SQLIdentifierExpr expr = (SQLIdentifierExpr) item.getExpr();

                // 获取字段名
                String fieldName = expr.getName();
                // 获取表名
                String tableName = null;
                if (queryBlock.getFrom() instanceof SQLExprTableSource) {
                    SQLExprTableSource tableSource = (SQLExprTableSource) queryBlock.getFrom();
                    tableName = tableSource.getName().getSimpleName();
                }

                String schemaName = null;
                if (queryBlock.getFrom() instanceof SQLExprTableSource) {
                    SQLExprTableSource tableSource = (SQLExprTableSource) queryBlock.getFrom();
                    if (tableSource.getExpr() instanceof SQLPropertyExpr) {
                        schemaName = ((SQLPropertyExpr)tableSource.getExpr()).getOwnernName();
                    }
                }

                // 组合成 "表名.字段名" 的格式
                // 组合成 "schema.table.column" 的格式
                String tableField = schemaName + "." + tableName + "." + fieldName;

                // 添加到 map 中
                if (!resultMap.containsKey(columnName)) {
                    resultMap.put(columnName, new ArrayList<>());
                }
                resultMap.get(columnName).add(tableField);
            }

        }

        // 打印结果
        for (Map.Entry<String, List<String>> entry : resultMap.entrySet()) {
            System.out.println("View Column: " + entry.getKey());
            System.out.println("Table.Fields: " + entry.getValue());
        }


    }

    @NotNull
    private static Map<String, List<String>> getTableFieldsMap(String viewSql) {
        SQLStatementParser pgsqlStatementParser = new OracleStatementParser(viewSql);
        SQLStatement statement = pgsqlStatementParser.parseStatement();

        SchemaStatVisitor visitor = new PGSchemaStatVisitor();
        statement.accept(visitor);

        Map<String, List<String>> tableFieldsMap = new HashMap<>();
        Map<TableStat.Name, TableStat> tables = visitor.getTables();
        for (TableStat.Name tableName : tables.keySet()) {
            List<String> fields = new ArrayList<>();
            for (TableStat.Column column : visitor.getColumns()) {
                if (column.getTable().equals(tableName.getName())) {
                    fields.add(column.getName());
                }
            }
            tableFieldsMap.put(tableName.getName(), fields);
        }
        return tableFieldsMap;
    }
}
