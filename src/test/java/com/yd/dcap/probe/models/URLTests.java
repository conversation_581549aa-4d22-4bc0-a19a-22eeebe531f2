package com.yd.dcap.probe.models;


import com.google.common.collect.Lists;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.rules.engine.result.MatchedResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

public class URLTests extends AbstractForTests {

    @BeforeEach
    public void beforeEach() throws IOException {
        this.setUp("url.yaml");
    }
    @Test
    public void testColumnComments() {
        MatchedResult matchedResult = interpret(UtilMisc.toMap("column.comment", " url "));
        assertEquals(100, matchedResult.getScore());
        assertEquals(MatchedResult.CONFIRMED, matchedResult.getEvaluation());

        matchedResult = interpret(UtilMisc.toMap("column.comment", "_ url "));
        assertEquals(0, matchedResult.getScore());
        assertNull(matchedResult.getEvaluation());

    }

    @Test
    public void findColumnName() {
        MatchedResult matchedResult = interpret(UtilMisc.toMap("column.name", "URL"));
        assertEquals(100, matchedResult.getScore());
        assertEquals(MatchedResult.CONFIRMED, matchedResult.getEvaluation());
    }

    @Test
    public void findColumnValue() {
        MatchedResult matchedResult = interpret(UtilMisc.toMap("column.values", Lists.newArrayList(
                "http://www.googe.com",
                "https://baidu.com",
                "https://yuandiansec.net/api"
        )));
        assertEquals(100, matchedResult.getScore());
        assertEquals(MatchedResult.CONFIRMED, matchedResult.getEvaluation());
    }
}
