package com.yd.dcap.probe.models;


import com.google.common.collect.Lists;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.rules.engine.models.ScoringModelInput;
import com.yd.rules.engine.models.ScoringModelOutput;
import com.yd.rules.engine.result.MatchedResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class BankCardNumberTests extends AbstractForTests {

    @BeforeEach
    public void beforeEach() throws IOException {
        this.setUp("bank_card_number.yaml");
    }

    @Test
    public void testFindColumnComment() {
        Map<String, Object> modelInputData = UtilMisc.toMap(
                "column", UtilMisc.toMap(
                        "comment", " 银行卡号  "
                )
        );

        ScoringModelOutput interpret = scoringModel.interpret(new ScoringModelInput(modelInputData));
        int expectedScore = 100;
        MatchedResult matchedResult = interpret.getMatchedResult(expectedScore);
        int score = matchedResult.getScore();
        String evaluation = matchedResult.getEvaluation();

        assertEquals(expectedScore, score);
        assertEquals(MatchedResult.CONFIRMED, evaluation);
    }

    @Test
    public void testFindColumnName() {
        Map<String, Object> modelInputData = UtilMisc.toMap(
                "column", UtilMisc.toMap(
                        "name", "bank_account"
                )
        );

        ScoringModelOutput interpret = scoringModel.interpret(new ScoringModelInput(modelInputData));
        int expectedScore = 100;
        MatchedResult matchedResult = interpret.getMatchedResult(expectedScore);
        int score = matchedResult.getScore();
        String evaluation = matchedResult.getEvaluation();

        assertEquals(expectedScore, score);
        assertEquals(MatchedResult.CONFIRMED, evaluation);
    }

    @Test
    public void testColumnValueValidator() {
        Map<String, Object> modelInputData = UtilMisc.toMap(
                "column", UtilMisc.toMap(
                        "values", Lists.newArrayList(
                                "320102046000000870"
                        )
                )
        );

        ScoringModelOutput interpret = scoringModel.interpret(new ScoringModelInput(modelInputData));
        int expectedScore = 100;
        MatchedResult matchedResult = interpret.getMatchedResult(expectedScore);
        int score = matchedResult.getScore();
        String evaluation = matchedResult.getEvaluation();

        assertEquals(expectedScore, score);
        assertEquals(MatchedResult.CONFIRMED, evaluation);
    }

}
