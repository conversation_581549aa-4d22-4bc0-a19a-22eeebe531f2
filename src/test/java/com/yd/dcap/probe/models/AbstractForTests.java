package com.yd.dcap.probe.models;

import com.dcap.utils.JSON;
import com.yd.rules.engine.ScoringModel;
import com.yd.rules.engine.models.ScoringModelInput;
import com.yd.rules.engine.models.ScoringModelOutput;
import com.yd.rules.engine.parser.CustomScoreParser;
import com.yd.rules.engine.parser.ScoringModelParser;
import com.yd.rules.engine.parser.ScoringRuleParser;
import com.yd.rules.engine.result.MatchedResult;
import org.apache.commons.io.IOUtils;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class AbstractForTests {
    protected ScoringModel scoringModel;
    protected final String dataTag = "data_tag:basic:12345678";
    protected final String dataTagType = "BASIC";
    protected final int dataTagOrderBy = 50;

    protected void setUp(String modelFileName) throws IOException {
        String modelText = IOUtils.toString(
                Objects.requireNonNull(this.getClass().getResourceAsStream("/structured/" + modelFileName)),
                Charset.defaultCharset()
        );
        HashMap object = JSON.from(modelText).toObject(HashMap.class);
        System.out.println(JSON.from(object).toString());
        // [{"type":"catalog","expr":"(?i)address"}]
//        System.out.println(", {\"type\":\"rule\",\"expr\":\""+SM4Utils.encCheckSm4ForEcb(modelText)+"\"}");
        ScoringModelParser scoringModelParser = new ScoringModelParser(new CustomScoreParser(new ScoringRuleParser()));
        scoringModel = scoringModelParser.parseScoringModel(dataTag, dataTagType, modelText, dataTagOrderBy);
    }
    protected MatchedResult interpret(Map<String,Object> locationAndData){
        Map<String, Object> inputData = new HashMap<>();
        Map<String, Object> finalData = inputData;
        for (Map.Entry<String, Object> locationAndDataEntry : locationAndData.entrySet()) {
            inputData = finalData;
            String dataLocation = locationAndDataEntry.getKey();
            Object data = locationAndDataEntry.getValue();
            String [] paths = dataLocation.split("\\.");

            for (int i = 0; i < paths.length; i++) {
                String key = paths[i];
                if (i == paths.length - 1){
                    inputData.put(key, data);
                } else {
                    inputData.computeIfAbsent(key, k -> new HashMap<>());
                    inputData = (Map<String, Object>) inputData.get(key);
                }
            }
        }

        ScoringModelOutput interpret = scoringModel.interpret(new ScoringModelInput(finalData));
        return interpret.getMatchedResult(0);
    }
}
