package com.yd.dcap.probe.models;


import com.yd.dcap.common.utils.UtilMisc;
import com.yd.rules.engine.result.MatchedResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class DateTests extends AbstractForTests {

    @BeforeEach
    public void beforeEach() throws IOException {
        this.setUp("date.yaml");
    }

    @Test
    public void testColumnComments() {
        MatchedResult matchedResult = interpret(UtilMisc.toMap("column.comment", "日期时间"));
        assertEquals(100, matchedResult.getScore());
        assertEquals(MatchedResult.CONFIRMED, matchedResult.getEvaluation());
    }

    @Test
    public void testColumnName() {
        MatchedResult matchedResult = interpret(UtilMisc.toMap("column.name", "date"));
        assertEquals(100, matchedResult.getScore());
        assertEquals(MatchedResult.CONFIRMED, matchedResult.getEvaluation());
    }
}
