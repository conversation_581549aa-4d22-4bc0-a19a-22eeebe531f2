package com.yd.dcap.probe.models;


import com.google.common.collect.Lists;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.rules.engine.result.MatchedResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

public class Ipv6Tests extends AbstractForTests {

    @BeforeEach
    public void beforeEach() throws IOException {
        this.setUp("ipv6.yaml");
    }
    @Test
    public void testColumnComments() {
        MatchedResult matchedResult = interpret(UtilMisc.toMap("column.comment", "_ipv6 "));
        assertEquals(100, matchedResult.getScore());
        assertEquals(MatchedResult.CONFIRMED, matchedResult.getEvaluation());

        matchedResult = interpret(UtilMisc.toMap("column.comment", "ipv6_"));
        assertEquals(0, matchedResult.getScore());
        assertNull(matchedResult.getEvaluation());

    }

    @Test
    public void findColumnName() {
        MatchedResult matchedResult = interpret(UtilMisc.toMap("column.name", "_IPV6_"));
        assertEquals(100, matchedResult.getScore());
        assertEquals(MatchedResult.CONFIRMED, matchedResult.getEvaluation());
    }

    @Test
    public void findColumnValue() {
        MatchedResult matchedResult = interpret(UtilMisc.toMap("column.values", Lists.newArrayList(
                "2001:0db8:85a3:0000:0000:8a2e:0370:7334",
                "FE80:CD00:0000:0CDE:1257:0000:211E:729C",
                "FE80:CD00:0:CDE:1257:0:211E:729C"
        )));
        assertEquals(100, matchedResult.getScore());
        assertEquals(MatchedResult.CONFIRMED, matchedResult.getEvaluation());
    }
}
