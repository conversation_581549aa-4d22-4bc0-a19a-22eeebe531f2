package com.yd.dcap.probe.models;

import com.google.common.collect.Lists;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.rules.engine.models.ScoringModelInput;
import com.yd.rules.engine.models.ScoringModelOutput;
import com.yd.rules.engine.result.MatchedResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

public class PersonNameTests extends AbstractForTests {

    @BeforeEach
    public void beforeEach() throws IOException {
        this.setUp("person_name.yaml");
    }

    @Test
    public void testFindColumnNameList() {
        Map<String,Integer> currentColumnMarked = new HashMap<>();
        Set<String> columnManualTagsSet = new HashSet<>();
        Set<String> columnDeletedAutoTagsSet = new HashSet<>();
        Map<String, Object> modelInputData = UtilMisc.toMap(
                "dataStore", UtilMisc.toMap(
                        "name", "数据库名称"
                ),
                "table", UtilMisc.toMap(
                        "name", "person",
                        "type", "表类型",
                        "comment", "表注释测试多主键更新",
                        "catalog", "db catalog",
                        "schema", "db schema"
                ),
                "column", UtilMisc.toMap(
                        "name", "name_Of",
                        "type", "text",
                        "comment", "姓名",
                        "size", 10,
                        "values", Lists.newArrayList(""),
                        // 该列 当前已经自动标记的。这包含了分数
                        "currentMarked", currentColumnMarked,
                        // 该列 已经手动打标的
                        "manualTags", columnManualTagsSet,
                        // 该列 已经排除的标签
                        "deletedAutoTags", columnDeletedAutoTagsSet
                ),
                "dataTag", dataTag
        );

        ScoringModelOutput interpret = scoringModel.interpret(new ScoringModelInput(modelInputData));
        int expectedScore = 100;
        MatchedResult matchedResult = interpret.getMatchedResult(expectedScore);
        int score = matchedResult.getScore();
        String evaluation = matchedResult.getEvaluation();

        assertEquals(expectedScore, score);
        assertEquals(MatchedResult.CONFIRMED, evaluation);
    }

    @Test
    public void testFindColumnComment() {
        Map<String,Integer> currentColumnMarked = new HashMap<>();
        Set<String> columnManualTagsSet = new HashSet<>();
        Set<String> columnDeletedAutoTagsSet = new HashSet<>();
        Map<String, Object> modelInputData = UtilMisc.toMap(
                "dataStore", UtilMisc.toMap(
                        "name", "数据库名称"
                ),
                "table", UtilMisc.toMap(
                        "name", "person",
                        "type", "表类型",
                        "comment", "表注释测试多主键更新",
                        "catalog", "db catalog",
                        "schema", "db schema"
                ),
                "column", UtilMisc.toMap(
                        "name", "sur_name",
                        "type", "text",
                        "comment", " 业as 姓名 wsdw ",
                        "size", 10,
                        "values", Lists.newArrayList(""),
                        // 该列 当前已经自动标记的。这包含了分数
                        "currentMarked", currentColumnMarked,
                        // 该列 已经手动打标的
                        "manualTags", columnManualTagsSet,
                        // 该列 已经排除的标签
                        "deletedAutoTags", columnDeletedAutoTagsSet
                ),
                "dataTag", dataTag
        );

        ScoringModelOutput interpret = scoringModel.interpret(new ScoringModelInput(modelInputData));
        int expectedScore = 100;
        MatchedResult matchedResult = interpret.getMatchedResult(expectedScore);
        int score = matchedResult.getScore();
        String evaluation = matchedResult.getEvaluation();

        assertEquals(expectedScore, score);
        assertEquals(MatchedResult.CONFIRMED, evaluation);
    }

    @Test
    public void testReverseFindColumnComment() {
        Map<String,Integer> currentColumnMarked = new HashMap<>();
        Set<String> columnManualTagsSet = new HashSet<>();
        Set<String> columnDeletedAutoTagsSet = new HashSet<>();
        Map<String, Object> modelInputData = UtilMisc.toMap(
                "dataStore", UtilMisc.toMap(
                        "name", "数据库名称"
                ),
                "table", UtilMisc.toMap(
                        "name", "person",
                        "type", "表类型",
                        "comment", "表注释测试多主键更新",
                        "catalog", "db catalog",
                        "schema", "db schema"
                ),
                "column", UtilMisc.toMap(
                        "name", "sur_name",
                        "size", 10,
                        "type", "text",
                        "comment", "公司名称",
                        "values", Lists.newArrayList(""),
                        // 该列 当前已经自动标记的。这包含了分数
                        "currentMarked", currentColumnMarked,
                        // 该列 已经手动打标的
                        "manualTags", columnManualTagsSet,
                        // 该列 已经排除的标签
                        "deletedAutoTags", columnDeletedAutoTagsSet
                ),
                "dataTag", dataTag
        );

        ScoringModelOutput interpret = scoringModel.interpret(new ScoringModelInput(modelInputData));

        MatchedResult matchedResult = interpret.getMatchedResult(100);
        int score = matchedResult.getScore();
        String evaluation = matchedResult.getEvaluation();

        assertEquals(0, score);
        assertNull(evaluation);
    }


    @Test
    public void testNlpAnalysis() {
        Map<String,Integer> currentColumnMarked = new HashMap<>();
        Set<String> columnManualTagsSet = new HashSet<>();
        Set<String> columnDeletedAutoTagsSet = new HashSet<>();
        Map<String, Object> modelInputData = UtilMisc.toMap(
                "dataStore", UtilMisc.toMap(
                        "name", "数据库名称"
                ),
                "table", UtilMisc.toMap(
                        "name", "person",
                        "type", "表类型",
                        "comment", "表注释测试多主键更新",
                        "catalog", "db catalog",
                        "schema", "db schema"
                ),
                "column", UtilMisc.toMap(
                        "name", "xxx",
                        "size", 10,
                        "type", "text",
                        "comment", "xxx",
                        "values", Lists.newArrayList("雪智伟"),
                        // 该列 当前已经自动标记的。这包含了分数
                        "currentMarked", currentColumnMarked,
                        // 该列 已经手动打标的
                        "manualTags", columnManualTagsSet,
                        // 该列 已经排除的标签
                        "deletedAutoTags", columnDeletedAutoTagsSet
                ),
                "dataTag", dataTag
        );

        ScoringModelOutput interpret = scoringModel.interpret(new ScoringModelInput(modelInputData));
        MatchedResult matchedResult = interpret.getMatchedResult(0);
        int score = matchedResult.getScore();
        String evaluation = matchedResult.getEvaluation();

        assertEquals(dataTag, matchedResult.getDataTag());
        // 应该是 100 分
        assertEquals(100, score);
        assertEquals(MatchedResult.CONFIRMED, evaluation);
    }

    @Test
    public void testTableAndColumnCombination() {
        Map<String, Object> modelInputData = UtilMisc.toMap(
                "table", UtilMisc.toMap(
                        "name", "senderinfo"
                ),
                "column", UtilMisc.toMap(
                        "name", "name"
                )
        );

        ScoringModelOutput interpret = scoringModel.interpret(new ScoringModelInput(modelInputData));
        MatchedResult matchedResult = interpret.getMatchedResult(0);
        int score = matchedResult.getScore();
        String evaluation = matchedResult.getEvaluation();

        // 应该是 100 分
        assertEquals(100, score);
        assertEquals(MatchedResult.CONFIRMED, evaluation);
    }
}
