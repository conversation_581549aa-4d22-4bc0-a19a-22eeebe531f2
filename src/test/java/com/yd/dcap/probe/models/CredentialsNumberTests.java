package com.yd.dcap.probe.models;


import com.google.common.collect.Lists;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.rules.engine.models.ScoringModelInput;
import com.yd.rules.engine.models.ScoringModelOutput;
import com.yd.rules.engine.result.MatchedResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class CredentialsNumberTests extends AbstractForTests {

    @BeforeEach
    public void beforeEach() throws IOException {
        this.setUp("credentials_number.yaml");
    }

    @Test
    public void testChineseIdCard() {
        Map<String,Integer> currentColumnMarked = new HashMap<>();
        Set<String> columnManualTagsSet = new HashSet<>();
        Set<String> columnDeletedAutoTagsSet = new HashSet<>();
        Map<String, Object> modelInputData = UtilMisc.toMap(
                "dataStore", UtilMisc.toMap(
                        "name", "数据库名称"
                ),
                "table", UtilMisc.toMap(
                        "name", "person",
                        "type", "表类型",
                        "comment", "表注释测试多主键更新",
                        "catalog", "db catalog",
                        "schema", "db schema"
                ),
                "column", UtilMisc.toMap(
                        "name", "id_card",
                        "type", "text",
                        "comment", "证件号码",
                        "size", 100,
                        "values", Lists.newArrayList("0513-80107106  "),
                        // 该列 当前已经自动标记的。这包含了分数
                        "currentMarked", currentColumnMarked,
                        // 该列 已经手动打标的
                        "manualTags", columnManualTagsSet,
                        // 该列 已经排除的标签
                        "deletedAutoTags", columnDeletedAutoTagsSet
                ),
                "dataTag", dataTag
        );

        ScoringModelOutput interpret = scoringModel.interpret(new ScoringModelInput(modelInputData));
        MatchedResult matchedResult = interpret.getMatchedResult(0);
        int score = matchedResult.getScore();
        String evaluation = matchedResult.getEvaluation();

        assertEquals(dataTag, matchedResult.getDataTag());
        // 应该是 100 分
        assertEquals(100, score);
        assertEquals(MatchedResult.CONFIRMED, evaluation);
    }
}
