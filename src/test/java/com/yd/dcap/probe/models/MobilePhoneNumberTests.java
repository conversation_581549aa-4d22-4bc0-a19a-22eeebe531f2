package com.yd.dcap.probe.models;


import com.google.common.collect.Lists;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.rules.engine.models.ScoringModelInput;
import com.yd.rules.engine.models.ScoringModelOutput;
import com.yd.rules.engine.result.MatchedResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

public class MobilePhoneNumberTests extends AbstractForTests {

    @BeforeEach
    public void beforeEach() throws IOException {
        this.setUp("mobile_phone_number.yaml");
    }

    @Test
    public void testMobilePhoneColumnComment() {
        Map<String, Object> modelInputData = UtilMisc.toMap(
                "column", UtilMisc.toMap(
                        "comment", "手机号码"
                )
        );

        ScoringModelOutput interpret = scoringModel.interpret(new ScoringModelInput(modelInputData));
        int expectedScore = 100;
        MatchedResult matchedResult = interpret.getMatchedResult(expectedScore);
        int score = matchedResult.getScore();
        String evaluation = matchedResult.getEvaluation();

        assertEquals(expectedScore, score);
        assertEquals(MatchedResult.CONFIRMED, evaluation);
    }

    @Test
    public void testColumnNameEndWithId() {
        Map<String, Object> modelInputData = UtilMisc.toMap(
                "column", UtilMisc.toMap(
                        "name", "user_id"
                )
        );

        ScoringModelOutput interpret = scoringModel.interpret(new ScoringModelInput(modelInputData));
        int expectedScore = 0;
        MatchedResult matchedResult = interpret.getMatchedResult(expectedScore);
        int score = matchedResult.getScore();
        String evaluation = matchedResult.getEvaluation();

        assertEquals(expectedScore, score);
        assertNull(evaluation);
    }

    @Test
    public void testColumnValueRegexpFind() {
        Map<String, Object> modelInputData = UtilMisc.toMap(
                "column", UtilMisc.toMap(
                        "values", Lists.newArrayList(" +86 - 18611800051 ")
                )
        );

        ScoringModelOutput interpret = scoringModel.interpret(new ScoringModelInput(modelInputData));
        int expectedScore = 100;
        MatchedResult matchedResult = interpret.getMatchedResult(expectedScore);
        int score = matchedResult.getScore();
        String evaluation = matchedResult.getEvaluation();

        assertEquals(expectedScore, score);
        assertEquals(MatchedResult.CONFIRMED, evaluation);
    }

}
