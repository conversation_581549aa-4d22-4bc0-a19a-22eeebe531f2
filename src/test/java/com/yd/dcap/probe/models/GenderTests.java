package com.yd.dcap.probe.models;


import com.google.common.collect.Lists;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.rules.engine.result.MatchedResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class GenderTests extends AbstractForTests {

    @BeforeEach
    public void beforeEach() throws IOException {
        this.setUp("gender.yaml");
    }

    @Test
    public void testColumnComments() {
        MatchedResult matchedResult = interpret(UtilMisc.toMap("column.comment", "性别   "));
        assertEquals(100, matchedResult.getScore());
        assertEquals(MatchedResult.CONFIRMED, matchedResult.getEvaluation());
    }

    @Test
    public void testDataDict() {
        MatchedResult matchedResult = interpret(UtilMisc.toMap("column.values",
                Lists.newArrayList("男生","男","女","F","f","M","m")));
        assertEquals(100, matchedResult.getScore());
        assertEquals(MatchedResult.CONFIRMED, matchedResult.getEvaluation());
    }

    @Test
    public void testFindColumnName() {
        MatchedResult matchedResult = interpret(UtilMisc.toMap("column.name", "gender"));
        assertEquals(100, matchedResult.getScore());
        assertEquals(MatchedResult.CONFIRMED, matchedResult.getEvaluation());

        matchedResult = interpret(UtilMisc.toMap("column.name", "sexual"));
        assertEquals(100, matchedResult.getScore());
        assertEquals(MatchedResult.CONFIRMED, matchedResult.getEvaluation());

        matchedResult = interpret(UtilMisc.toMap("column.name", "sex"));
        assertEquals(100, matchedResult.getScore());
        assertEquals(MatchedResult.CONFIRMED, matchedResult.getEvaluation());
    }
}
