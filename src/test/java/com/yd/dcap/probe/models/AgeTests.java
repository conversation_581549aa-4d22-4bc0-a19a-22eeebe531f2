package com.yd.dcap.probe.models;


import com.google.common.collect.Lists;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.rules.engine.result.MatchedResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class AgeTests extends AbstractForTests {

    @BeforeEach
    public void beforeEach() throws IOException {
        this.setUp("age.yaml");
    }

    @Test
    public void testColumnComments() {
        MatchedResult matchedResult = interpret(UtilMisc.toMap("column.comment", "年龄 "));
        assertEquals(100, matchedResult.getScore());
        assertEquals(MatchedResult.CONFIRMED, matchedResult.getEvaluation());
    }

    @Test
    public void testColumnNameAndValue() {
        // todo: 列名精确匹配，且值 0-100 之间，90% 命中率
        MatchedResult matchedResult = interpret(UtilMisc.toMap(
                "column.name", "age",
                "column.values", Lists.newArrayList(100,"33","36")
                ));
        assertEquals(100, matchedResult.getScore());
        assertEquals(MatchedResult.CONFIRMED, matchedResult.getEvaluation());
    }
}
