package com.yd.dcap.probe.models;


import com.google.common.collect.Lists;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.rules.engine.result.MatchedResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class CountryTests extends AbstractForTests {

    @BeforeEach
    public void beforeEach() throws IOException {
        this.setUp("country.yaml");
    }
    @Test
    public void testColumnComments() {
        MatchedResult matchedResult = interpret(UtilMisc.toMap("column.comment", "国家 "));
        assertEquals(100, matchedResult.getScore());
        assertEquals(MatchedResult.CONFIRMED, matchedResult.getEvaluation());
    }

    @Test
    public void testDataDict() {
        MatchedResult matchedResult = interpret(UtilMisc.toMap("column.values", Lists.newArrayList(
                "摩尔多瓦",
                        "纳米比亚",
                        "圣多美和普林西比民主共和国",
                        "波黑",
                        "冰岛共和国",
                        "中国台湾",
                        "德国"
        )));
        assertEquals(100, matchedResult.getScore());
        assertEquals(MatchedResult.CONFIRMED, matchedResult.getEvaluation());
    }

    @Test
    public void testFindColumnName() {
        MatchedResult matchedResult = interpret(UtilMisc.toMap("column.name", "country"));
        assertEquals(100, matchedResult.getScore());
        assertEquals(MatchedResult.CONFIRMED, matchedResult.getEvaluation());

        matchedResult = interpret(UtilMisc.toMap("column.name", "NATION"));
        assertEquals(100, matchedResult.getScore());
        assertEquals(MatchedResult.CONFIRMED, matchedResult.getEvaluation());

        matchedResult = interpret(UtilMisc.toMap("column.name", "_country_of_"));
        assertEquals(100, matchedResult.getScore());
        assertEquals(MatchedResult.CONFIRMED, matchedResult.getEvaluation());

        matchedResult = interpret(UtilMisc.toMap("column.name", "_nationOf_"));
        assertEquals(100, matchedResult.getScore());
        assertEquals(MatchedResult.CONFIRMED, matchedResult.getEvaluation());


    }
}
