package com.yd.dcap.probe;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collection;
import java.util.Enumeration;
import java.util.jar.*;

public class JarsMaster {
    public static void main(String[] args) throws IOException {
        Collection<File> files = FileUtils.listFiles(
                new File("D:\\work"),
                new String[]{"jar"}, false
        );
//        String collect = files.stream().map(File::getName).collect(Collectors.joining("\",\""));
//        System.out.println(collect);
        for (File file : files) {
            if (file.getName().contains("5.3.39")){
                setJarVersion(file.getPath(), "5.3.41");
            }
        }
    }

    /**
     *
     * @param jarPath JAR 包路径
     * @param newVersion 新的版本号
     */
    public static void setJarVersion(String jarPath, String newVersion){
        String outputPath = jarPath+"_new";
        try {
            // 读取原始 JAR 包
            try (JarFile jarFile = new JarFile(jarPath)) {
                Manifest manifest = jarFile.getManifest();
                Attributes attributes = manifest.getMainAttributes();

                // 获取当前版本号
                String currentVersion = attributes.getValue(Attributes.Name.IMPLEMENTATION_VERSION);
                if(StringUtils.isBlank(currentVersion)){
                    currentVersion = attributes.getValue(Attributes.Name.MANIFEST_VERSION);
                }
                System.out.println("Current version: " + currentVersion);

                // 修改版本号
                attributes.put(Attributes.Name.IMPLEMENTATION_VERSION, newVersion);
                attributes.put(Attributes.Name.MANIFEST_VERSION, newVersion);
                attributes.put(Attributes.Name.SIGNATURE_VERSION, newVersion);
                attributes.put(Attributes.Name.SPECIFICATION_VERSION, newVersion);
                attributes.put(new Attributes.Name("Archiver-Version"), newVersion);
                attributes.put(new Attributes.Name("Bundle-Version"), newVersion);
                attributes.put(new Attributes.Name("Import-Package"), "");
                attributes.put(new Attributes.Name("Export-Package"), "");


                // 创建新的 JAR 包
                try (JarOutputStream jarOutputStream = new JarOutputStream(new FileOutputStream(outputPath), manifest)) {
                    Enumeration<JarEntry> entries = jarFile.entries();
                    byte[] buffer = new byte[4096];
                    int bytesRead;

                    while (entries.hasMoreElements()) {
                        JarEntry entry = entries.nextElement();
                        // 跳过原始 MANIFEST.MF 条目
                        if (entry.getName().startsWith("META-INF/MANIFEST.MF")) {
                            continue;
                        }

                        // 同样跳过 maven 目录
                        if (entry.getName().contains("META-INF/maven")) {
                            continue;
                        }
                        jarOutputStream.putNextEntry(new JarEntry(entry.getName()));

                        if (!entry.isDirectory()) {
                            try (InputStream inputStream = jarFile.getInputStream(entry)) {
                                while ((bytesRead = inputStream.read(buffer)) != -1) {
                                    jarOutputStream.write(buffer, 0, bytesRead);
                                }
                            }
                        }
                        jarOutputStream.closeEntry();
                    }
                    jarOutputStream.finish();
                    System.out.println("Modified JAR written to: " + outputPath);
                }
            }
            File file = new File(jarPath);
            FileUtils.delete(file);
            int lastIndex = file.getName().lastIndexOf("-");
            String newFileName = file.getName().substring(0,lastIndex).concat(".jar");
            FileUtils.moveFile(new File(outputPath), new File(file.getParent().concat(File.separator).concat(newFileName)));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
