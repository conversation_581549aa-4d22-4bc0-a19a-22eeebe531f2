package com.yd.dcap.probe;

import org.slf4j.LoggerFactory;
import org.zeroturnaround.exec.ProcessExecutor;
import org.zeroturnaround.exec.stream.slf4j.Slf4jStream;

import java.io.IOException;
import java.util.concurrent.TimeoutException;

public class ScannerCaller {
    public static void main(String[] args) throws IOException, InterruptedException, TimeoutException {
        new ProcessExecutor()
                .command("java", "-version")
                .redirectOutput(Slf4jStream.of(LoggerFactory.getLogger(ScannerCaller.class.getName() + ".MyProcess")).asInfo())
                .execute();
    }

}
