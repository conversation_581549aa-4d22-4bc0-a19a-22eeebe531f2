package com.yd.dcap.probe;

import com.dcap.datalayer.ConnectionException;
import oracle.jdbc.driver.OracleConnection;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Properties;

public class OracleTests {
    public static void main(String[] args) throws ConnectionException {
        Connection connect = OracleTests.connect();
    }
    public static Connection connect() throws ConnectionException {
        DriverManager.setLoginTimeout(30);
//        String drvName = "oracle.jdbc.driver.OracleDriver";
//        Driver dbDriver;
//        try {
//            dbDriver = (Driver) Class.forName(drvName).newInstance();
//            DriverManager.registerDriver(dbDriver);
//        } catch (Exception e) {
//            throw new ConnectionException("Can not initialize the driver class: " + drvName, e);
//        }

        Properties props = new Properties();
        // notesReporting
        String url = "**************************************";
        props.setProperty("user", "system");
        props.setProperty("password", "oracle");

        Connection connection;
        try {
            connection = DriverManager.getConnection(url,props);// dbDriver.connect(url, props);
            OracleConnection oraCon = (OracleConnection)connection;
            oraCon.setRemarksReporting(true);
        } catch (SQLException e) {
            throw new ConnectionException("Can not connect to database: " + url, e);
        }
        return connection;
    }
}
