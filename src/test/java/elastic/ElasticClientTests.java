package elastic;

import com.yd.dcap.classifier.ElasticsearchDatasource;
import com.yd.dcap.probe.client.TaskConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertNotNull;

public class ElasticClientTests {

    private ElasticsearchDatasource elasticsearchDatasource;

    @BeforeEach
    public void setup() {
        TaskConfig.DataSource dsConfig = new TaskConfig.DataSource();
        dsConfig.setHost("127.0.0.1");
        dsConfig.setPort("10080");
        dsConfig.setAuthCfg(new TaskConfig.AuthCfg());
        dsConfig.getAuthCfg().setUsername("xuezhiwei");
        dsConfig.getAuthCfg().setPassword("12345678");
        elasticsearchDatasource = (ElasticsearchDatasource) ElasticsearchDatasource.buildDatasource(1L, dsConfig).get(0);
    }


    @Test
    public void testGetCatalogs() {
        List<String> catalogs = elasticsearchDatasource.getCatalogs();

        System.out.println(catalogs);
        // 验证结果，例如检查返回的列表是否为空，或者是否包含预期的索引名称
        assertNotNull(catalogs);
        // assertTrue(catalogs.contains("expectedIndexName"));
    }


    public void testGetTables() {
        List<String> tables = elasticsearchDatasource.getTables("可以随便写");

        System.out.println(tables);
        // 验证结果，例如检查返回的列表是否为空，或者是否包含预期的索引名称
        assertNotNull(tables);
        // assertTrue(catalogs.contains("expectedIndexName"));
    }

    public void testGetRows() {
        List<String> tables = elasticsearchDatasource.getTables("可以随便写");
        for (String table : tables) {
            List<Map<String, Object>> rows = elasticsearchDatasource.getRows(table, 10);
            for (Map<String, Object> row : rows) {
                System.out.println(row);
            }
        }
    }


}
