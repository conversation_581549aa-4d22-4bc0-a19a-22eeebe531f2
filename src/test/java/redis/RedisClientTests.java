package redis;

import com.yd.dcap.classifier.RedisDatasource;
import com.yd.dcap.probe.client.TaskConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertNotNull;

public class RedisClientTests {

    private RedisDatasource redisDatasource;

    @BeforeEach
    public void setup() {
        TaskConfig.DataSource dsConfig = new TaskConfig.DataSource();
        dsConfig.setHost("**************");
        dsConfig.setPort("30813");
        dsConfig.setAuthCfg(new TaskConfig.AuthCfg());
        dsConfig.getAuthCfg().setUsername("");
        dsConfig.getAuthCfg().setPassword("1qaz@WSX3edc");
        redisDatasource = (RedisDatasource) RedisDatasource.buildDatasource(1L, dsConfig).get(0);
    }


    @Test
    public void testGetCatalogs() {
        List<String> catalogs = redisDatasource.getCatalogs();

        System.out.println(catalogs);
        // 验证结果，例如检查返回的列表是否为空，或者是否包含预期的索引名称
        assertNotNull(catalogs);
    }


    @Test
    public void testGetTables() {
        List<String> catalogs = redisDatasource.getCatalogs();
        for (String db : catalogs) {
            int index = Integer.parseInt(db);
            List<String> tables = redisDatasource.getTables(index);
            System.out.println(tables);
        }
    }

    @Test
    public void testGetRows() {
        List<String> catalogs = redisDatasource.getCatalogs();
        for (String db : catalogs) {
            int index = Integer.parseInt(db);
            List<String> tables = redisDatasource.getTables(index);
            for (String table : tables) {
                List<Map<String, Object>> rows = redisDatasource.getRows(index, table, 10);
                for (Map<String, Object> row : rows) {
                    System.out.println(row);
                }
            }
        }
    }


}
