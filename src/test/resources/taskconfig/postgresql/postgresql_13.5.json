{"taskParam": {"sampleCount": 1000, "dataType": "TEXT,NUMBER", "tableType": "TABLE,VIEW,SYNONYM", "emptyPercentage": 1, "excludeColumn": "", "excludeEmptyValues": true, "excludeSchema": "", "excludeTable": "", "hitPercentage": 1, "maximumLength": 150, "minimumLength": 1, "tableRowCount": 100, "tableRowCountEnabled": false, "tableRowCountUnit": 1, "scanRange": {"selectedDatabase": [], "selectedSchema": [], "selectedSynonym": [], "selectedView": [], "selectedTable": [], "excludedDatabase": [], "excludedSchema": [], "excludedSynonym": [], "excludedView": [], "excludedTable": []}, "sampleSqlTimeout": 15, "dcapProbeId": 1, "dbInstAddressId": 78297, "dbInstAccountId": 78181, "viewDefinitionSampling": false}, "datasource": {"dbNameType": null, "sourceType": "pgsql", "port": "5432", "name": "pgsql_13.5_dcap_admin_De0Fn", "host": "**********", "authCfg": {"password": "Jeeboot&9527", "username": "jee<PERSON>t"}, "id": 78313, "extraCfg": "jee<PERSON>t"}, "tenantId": 61, "name": "De0Fn_1717495564399", "policies": [{"id": 31409, "name": "电话号码(中国内地)", "builtin": "1", "category": 20296, "dataTag": "cls.pii::phonech", "dataTagTypeForDspm": "BASIC", "dataTagOrderBy": 50, "level": 40, "levelName": "S4", "patterns": [{"type": "data", "expr": "(^(0\\d{2}-\\d{8}(-\\d{1,4})?)|(0\\d{3}-\\d{7,8}(-\\d{1,4})?))|(^((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(18[0,5-9]))\\d{8})[ ]*$"}]}], "scanJobHistoryId": 80251, "taskId": 77105}