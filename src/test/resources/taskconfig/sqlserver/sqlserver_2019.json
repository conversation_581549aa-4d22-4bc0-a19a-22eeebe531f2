{"taskId": 2105, "currentVersion": 0, "scanJobHistoryId": 59436, "tenantId": 1, "name": "default-3995", "taskParam": {"excludeEmptyValues": true, "emptyPercentage": 1, "useRandomSampling": false, "sampleCount": 100, "sampleSqlTimeout": 15, "tableRowCountEnabled": true, "tableRowCount": 100, "tableRowCountUnit": 1, "excludeSchema": "", "excludeTable": "", "excludeColumn": "", "tableNameLike": null, "tableType": "TABLE", "columnNameLike": null, "searchValueLike": null, "minimumLength": 1, "maximumLength": 100, "hitPercentage": 60, "dataType": "TEXT,NUMBER", "scanRange": {"selectedSchema": [], "selectedDatabase": [], "selectedTable": [], "selectedView": [], "selectedSynonym": [], "excludedDatabase": [], "excludedSchema": [], "excludedTable": [], "excludedView": [], "excludedSynonym": []}, "markThresholds": 80, "tableRowCountLimit": 100}, "datasource": {"id": "3995", "name": "sqlserver2019", "sourceType": "mssql", "host": "app-alpha.yuandiansec.net", "port": "30107", "authCfg": {"username": "sa", "password": "p0o9I*U&"}, "extraCfg": ""}, "policies": []}