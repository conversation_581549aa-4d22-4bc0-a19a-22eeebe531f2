{"taskParam": {"dcapProbeId": 1, "dbInstAddressId": 25882, "dbInstAccountId": 81462, "viewDefinitionSampling": false, "sampleCount": 100, "sampleSqlTimeout": 15, "minimumLength": 0, "maximumLength": 256, "hitPercentage": 60, "tableType": "TABLE", "dataType": "TEXT,NUMBER", "excludeSchema": "", "excludeTable": "", "excludeColumn": "", "tableRowCountEnabled": false, "tableRowCount": 100, "tableRowCountUnit": 1, "excludeEmptyValues": true, "emptyPercentage": 1, "scanRange": {"excludedDatabase": [], "excludedSchema": [], "excludedSynonym": [], "excludedTable": [], "excludedView": [], "selectedDatabase": [], "selectedSchema": [], "selectedSynonym": [], "selectedTable": [], "selectedView": []}}, "datasource": {"dbNameType": 2, "sourceType": "impala", "port": "21050", "name": "test-impala", "host": "***********", "authCfg": {"password": "test", "username": "test"}, "id": 25884}, "tenantId": 1, "name": "default-db-25884", "policies": [{"id": 31409, "name": "电话号码(中国内地)", "builtin": "1", "category": 20296, "dataTag": "cls.pii::phonech", "dataTagTypeForDspm": "BASIC", "dataTagOrderBy": 50, "level": 40, "levelName": "S4", "patterns": [{"type": "data", "expr": "(^(0\\d{2}-\\d{8}(-\\d{1,4})?)|(0\\d{3}-\\d{7,8}(-\\d{1,4})?))|(^((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(18[0,5-9]))\\d{8})[ ]*$"}]}], "scanJobHistoryId": 83815, "taskId": 80754}