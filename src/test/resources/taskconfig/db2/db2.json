{"taskParam": {"viewDefinitionSampling": false, "sampleCount": 100, "sampleSqlTimeout": 15, "minimumLength": 1, "maximumLength": 100, "hitPercentage": 60, "tableType": "TABLE", "dataType": "TEXT,NUMBER", "excludeSchema": "", "excludeTable": "", "excludeColumn": "", "tableRowCountEnabled": false, "tableRowCount": 100, "tableRowCountUnit": 1, "excludeEmptyValues": false, "emptyPercentage": 90, "scanRange": {"excludedDatabase": [], "excludedSchema": [], "excludedSynonym": [], "excludedTable": [], "excludedView": [], "selectedDatabase": ["sample.DB2INST1"], "selectedSchema": [], "selectedSynonym": [], "selectedTable": [], "selectedView": []}}, "datasource": {"sourceType": "db2", "port": "30125", "dbInstanceName": "db2v10_liusf", "name": "db2v10_liusf", "host": "app-alpha.yuandiansec.net", "dbInstanceId": 12573, "authCfg": {"password": "first@YD", "username": "db2inst1"}, "id": 19414, "extraCfg": "SAMPLE"}, "tenantId": 1, "name": "default-19414", "policies": [{"level": 30, "dataTag": "cls.piilinkable::affiliation", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)affiliation"}], "name": "单位", "description": null, "levelName": "S3", "id": 31383, "category": 20296}, {"level": 30, "dataTag": "cls.piilinkable::age", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)^age$"}], "name": "年龄", "description": null, "levelName": "S3", "id": 31384, "category": 20296}, {"level": 30, "dataTag": "cls.piilinkable::gender", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)gender"}], "name": "性别", "description": null, "levelName": "S3", "id": 31385, "category": 20296}, {"level": 20, "dataTag": "cls.pii::date", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)^date$"}], "name": "日期", "description": null, "levelName": "S2", "id": 31386, "category": 20296}, {"level": 30, "dataTag": "cls.piilinkable::birth", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)birth"}], "name": "生日", "description": null, "levelName": "S3", "id": 31387, "category": 20296}, {"level": 30, "dataTag": "cls.pii::name", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)first.?name|last.?name|initial"}], "name": "姓名", "description": null, "levelName": "S3", "id": 31388, "category": 20296}, {"level": 30, "dataTag": "cls.pii::photo", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)photo"}], "name": "照片", "description": null, "levelName": "S3", "id": 31389, "category": 20296}, {"level": 30, "dataTag": "cls.piilinkable::religion", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)religion"}], "name": "宗教", "description": null, "levelName": "S3", "id": 31390, "category": 20296}, {"level": 40, "dataTag": "cls.piilinkable::location", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)location|latitude|longitude"}], "name": "位置", "description": null, "levelName": "S4", "id": 31391, "category": 20296}, {"level": 30, "dataTag": "cls.piilinkable::address", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)address"}], "name": "地址", "description": null, "levelName": "S3", "id": 31392, "category": 20296}, {"level": 40, "dataTag": "cls.piilinkable::street", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)street"}], "name": "街道", "description": null, "levelName": "S4", "id": 31393, "category": 20296}, {"level": 20, "dataTag": "cls.piilinkable::city", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)city"}], "name": "城市", "description": null, "levelName": "S2", "id": 31394, "category": 20296}, {"level": 20, "dataTag": "cls.piilinkable::country", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)country"}], "name": "国家", "description": null, "levelName": "S2", "id": 31395, "category": 20296}, {"level": 20, "dataTag": "cls.piilinkable::zipcode", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)postal"}], "name": "邮编", "description": null, "levelName": "S2", "id": 31396, "category": 20296}, {"level": 30, "dataTag": "cls.piilinkable::dateofbirth", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)date.?of.?birth"}], "name": "出生日期", "description": null, "levelName": "S3", "id": 31397, "category": 20296}, {"level": 30, "dataTag": "cls.phi::bloodpressure", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)blood.?pressure"}], "name": "血压", "description": null, "levelName": "S3", "id": 31398, "category": 20296}, {"level": 30, "dataTag": "cls.phi::bloodtype", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)blood.?type"}], "name": "血型", "description": null, "levelName": "S3", "id": 31399, "category": 20296}, {"level": 30, "dataTag": "cls.phi::heartrate", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)heart.?rate"}], "name": "心率", "description": null, "levelName": "S3", "id": 31400, "category": 20296}, {"level": 30, "dataTag": "cls.phi::patientid", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)patient.?id"}], "name": "患者ID", "description": null, "levelName": "S3", "id": 31401, "category": 20296}, {"level": 40, "dataTag": "cls.piilinkable::nationality", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)nationality"}], "name": "国籍", "description": null, "levelName": "S4", "id": 31402, "category": 20296}, {"level": 30, "dataTag": "cls.piilinkable::occupation", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)occupation"}], "name": "职业", "description": null, "levelName": "S3", "id": 31403, "category": 20296}, {"level": 30, "dataTag": "cls.piilinkable::password", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)password"}], "name": "密码", "description": null, "levelName": "S3", "id": 31404, "category": 20296}, {"level": 30, "dataTag": "cls.piilinkable::userid", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)user.?id"}], "name": "用户ID", "description": null, "levelName": "S3", "id": 31405, "category": 20296}, {"level": 30, "dataTag": "cls.piilinkable::username", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)user.?name"}], "name": "用户名", "description": null, "levelName": "S3", "id": 31406, "category": 20296}, {"level": 30, "dataTag": "cls.piilinkable::weburl", "builtin": 1, "patterns": [{"type": "catalog", "expr": "(?i)web.?url"}], "name": "Web URL", "description": null, "levelName": "S3", "id": 31407, "category": 20296}, {"level": 30, "dataTag": "cls.pci::swiftcode", "builtin": 1, "patterns": [{"type": "data", "expr": "^[A-Za-z]{4}(?:GB|US|DE|RU|CA|JP|CN)[0-9a-zA-Z]{2,5}[ ]*$"}], "name": "SwiftCode", "description": null, "levelName": "S3", "id": 31408, "category": 20296}, {"level": 40, "dataTag": "cls.pii::phonech", "builtin": 1, "patterns": [{"type": "data", "expr": "(^(0\\d{2}-\\d{8}(-\\d{1,4})?)|(0\\d{3}-\\d{7,8}(-\\d{1,4})?))|(^((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(18[0,5-9]))\\d{8})[ ]*$"}], "name": "电话号码(中国内地)", "description": null, "levelName": "S4", "id": 31409, "category": 20296}, {"level": 40, "dataTag": "cls.pii::cardnum", "builtin": 1, "patterns": [{"type": "data", "expr": "^([京津晋冀蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼渝川贵云藏陕甘青宁新][ABCDEFGHJKLMNPQRSTUVWXY][1-9DF][1-9ABCDEFGHJKLMNPQRSTUVWXYZ]\\d{3}[1-9DF]|[京津晋冀蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼渝川贵云藏陕甘青宁新][ABCDEFGHJKLMNPQRSTUVWXY][\\dABCDEFGHJKLNMxPQRSTUVWXYZ]{5})[ ]*$"}], "name": "车牌号(中国内地)", "description": null, "levelName": "S4", "id": 31410, "category": 20296}, {"level": 40, "dataTag": "cls.pii::email", "builtin": 1, "patterns": [{"type": "data", "expr": "^[a-zA-Z0-9._%-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}[ ]*$"}], "name": "邮箱", "description": null, "levelName": "S4", "id": 31411, "category": 20296}, {"level": 40, "dataTag": "cls.pii::idcard", "builtin": 1, "patterns": [{"type": "data", "expr": "^(([1][1-5])|([2][1-3])|([3][1-7])|([4][1-6])|([5][0-4])|([6][1-5])|([7][1])|([8][1-2]))\\d{4}(([1][9]\\d{2})|([2]\\d{3}))(([0][1-9])|([1][0-2]))(([0][1-9])|([1-2][0-9])|([3][0-1]))\\d{3}[0-9xX][ ]*$"}], "name": "身份证(中国内地)", "description": null, "levelName": "S4", "id": 31412, "category": 20296}, {"level": 40, "dataTag": "cls.pii::creditcard", "builtin": 1, "patterns": [{"type": "data", "expr": "^[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[ ]*$"}], "name": "信用卡", "description": null, "levelName": "S4", "id": 31413, "category": 20296}, {"level": 40, "dataTag": "cls.pii::mastercard", "builtin": 1, "patterns": [{"type": "data", "expr": "^5[1-5][0-9]{2}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[ ]*$"}], "name": "万事达卡", "description": null, "levelName": "S4", "id": 31414, "category": 20296}, {"level": 40, "dataTag": "cls.pii::visacard", "builtin": 1, "patterns": [{"type": "data", "expr": "^4[0-9]{3}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[ ]*$"}], "name": "VISA卡", "description": null, "levelName": "S4", "id": 31415, "category": 20296}, {"level": 40, "dataTag": "cls.pii::unionpay", "builtin": 1, "patterns": [{"type": "data", "expr": "^62[0-5][0-9]{13,16}[ ]*$"}], "name": "银联卡", "description": null, "levelName": "S4", "id": 31416, "category": 20296}, {"level": 40, "dataTag": "cls.pii::iban", "builtin": 1, "patterns": [{"type": "data", "expr": "^[a-zA-Z]{2}[0-9]{2}[a-zA-Z0-9]{4}[0-9]{7}([a-zA-Z0-9]?){0,16}[ ]*$"}], "name": "国际银行账号(IBAN)", "description": null, "levelName": "S4", "id": 31417, "category": 20296}, {"level": 30, "dataTag": "cls.operation::url", "builtin": 1, "patterns": [{"type": "data", "expr": "(?i)^(ht|f)tp(s?)\\:\\/\\/[0-9a-zA-Z]([-.\\w]*[0-9a-zA-Z])*(:(0-9)*)*(\\/?)([a-zA-Z0-9\\-\\.\\?\\,\\'\\/\\\\\\+&%\\$#_]*)?[ ]*$"}], "name": "URL链接", "description": null, "levelName": "S3", "id": 31418, "category": 20299}, {"level": 30, "dataTag": "cls.operation::ipv6", "builtin": 1, "patterns": [{"type": "data", "expr": "^(([a-fA-F0-9]{1,4}|):){1,7}([a-fA-F0-9]{1,4}|:)[ ]*$"}], "name": "IPv6地址", "description": null, "levelName": "S3", "id": 31419, "category": 20299}, {"level": 20, "dataTag": "cls.operation::mac", "builtin": 1, "patterns": [{"type": "data", "expr": "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})|([0-9a-fA-F]{4}\\\\.[0-9a-fA-F]{4}\\\\.[0-9a-fA-F]{4})[ ]*$"}], "name": "MAC地址", "description": "dddddccc", "levelName": "S2", "id": 31420, "category": 20297}, {"level": 30, "dataTag": "cls.operation::ipv4", "builtin": 1, "patterns": [{"type": "data", "expr": "^(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])[ ]*$"}], "name": "IPv4地址", "description": null, "levelName": "S3", "id": 31421, "category": 20299}, {"level": 10, "dataTag": "1::ddddccc", "builtin": 0, "patterns": [{"type": "data", "expr": "(?i)city"}], "name": "ddddccc", "description": "dddddccc", "levelName": "S1", "id": 31423, "category": 20296}, {"level": 10, "dataTag": "1::he_ji_jine", "builtin": 0, "patterns": [{"type": "catalog", "expr": "total_price"}], "name": "合计金额", "description": "", "levelName": "S1", "id": 31433, "category": 20297}, {"level": 40, "dataTag": "1::shou_ji_hao__DW", "builtin": 0, "patterns": [{"type": "catalog", "expr": "login_name_enc"}], "name": "手机号_enc_DW", "description": "", "levelName": "S4", "id": 31437, "category": 20296}, {"level": 40, "dataTag": "1::shou_ji_hao__mask_DW", "builtin": 0, "patterns": [{"type": "catalog", "expr": "login_name_mask"}], "name": "手机号_mask_DW", "description": "", "levelName": "S4", "id": 31438, "category": 20429}, {"level": 40, "dataTag": "1::zi_ding_yi_column", "builtin": 0, "patterns": [{"type": "catalog", "expr": "^column_\\d+$"}], "name": "自定义column", "description": "", "levelName": "S4", "id": 31443, "category": 20428}, {"level": 40, "dataTag": "1::you_xiang__new", "builtin": 0, "patterns": [{"type": "rule", "expr": "{   \"model\" : \"1.0\",   \"desc\" : \"邮箱\",   \"scores\" : [ {     \"desc\" : \"匹配邮箱； 内容正则匹配 && 内容校验\",     \"name\" : \"HitRatio\",     \"rule\" : {       \"array_hit_ratio\" : [ {         \"and\" : [ {           \"regexp_find\" : [ \"^[a-zA-Z0-9._%-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,4}[ ]*$\", {             \"var\" : \"ArrayElementValue\"           }]         }, {           \"validator\" : [ \"cls.pii::email\", {             \"var\" : \"ArrayElementValue\"           } ]         } ]       }, {         \"var\" : \"column.values\"       } ]     },     \"output\" : true   }, {     \"desc\" : \"命中率必须大于等于 40\",     \"name\" : \"OverallScore\",     \"rule\" : {       \">=\" : [ {         \"var\" : \"$.HitRatio\"       }, 40 ]     },     \"output\" : true   } ] }"}], "name": "邮箱_new", "description": "", "levelName": "S4", "id": 31681, "category": 20296}, {"level": 40, "dataTag": "1::mask_column", "builtin": 0, "patterns": [{"type": "catalog", "expr": "^mask_column$"}], "name": "mask_column", "description": "", "levelName": "S4", "id": 31931, "category": 20296}, {"level": 40, "dataTag": "1::mask_column_regex_replace", "builtin": 0, "patterns": [{"type": "catalog", "expr": "c_regex_replace"}], "name": "c_regex_replace", "description": "", "levelName": "S4", "id": 31943, "category": 20451}, {"level": 40, "dataTag": "1::mask_column_keep_front_n_rear_m", "builtin": 0, "patterns": [{"type": "catalog", "expr": "c_keep_front_n_rear_m"}], "name": "c_keep_front_n_rear_m", "description": "", "levelName": "S4", "id": 31944, "category": 20451}, {"level": 40, "dataTag": "1::mask_column_keep_from_n_to_m", "builtin": 0, "patterns": [{"type": "catalog", "expr": "c_keep_from_n_to_m"}], "name": "c_keep_from_n_to_m", "description": "", "levelName": "S4", "id": 31945, "category": 20451}, {"level": 40, "dataTag": "1::mask_column_shelter_front_n_rear_m", "builtin": 0, "patterns": [{"type": "catalog", "expr": "c_shelter_front_n_rear_m"}], "name": "c_shelter_front_n_rear_m", "description": "", "levelName": "S4", "id": 31946, "category": 20451}, {"level": 40, "dataTag": "1::mask_column_shelter_from_n_to_m", "builtin": 0, "patterns": [{"type": "catalog", "expr": "c_shelter_from_n_to_m"}], "name": "c_shelter_from_n_to_m", "description": "", "levelName": "S4", "id": 31947, "category": 20451}, {"level": 40, "dataTag": "1::mask_column_shelter_all", "builtin": 0, "patterns": [{"type": "catalog", "expr": "c_shelter_all"}], "name": "c_shelter_all", "description": "", "levelName": "S4", "id": 31948, "category": 20451}, {"level": 40, "dataTag": "1::mask_column_shelter_spec_char_bef", "builtin": 0, "patterns": [{"type": "catalog", "expr": "c_shelter_spec_char_bef"}], "name": "c_shelter_spec_char_bef", "description": "", "levelName": "S4", "id": 31949, "category": 20451}, {"level": 40, "dataTag": "1::mask_column_figure_shifting", "builtin": 0, "patterns": [{"type": "catalog", "expr": "c_figure_shifting"}], "name": "c_figure_shifting", "description": "", "levelName": "S4", "id": 31950, "category": 20451}, {"level": 40, "dataTag": "1::mask_column_do_nothing", "builtin": 0, "patterns": [{"type": "catalog", "expr": "c_do_nothing"}], "name": "c_do_nothing", "description": "", "levelName": "S4", "id": 31951, "category": 20451}, {"level": 40, "dataTag": "1::dan_bao_ren_shou_jihao", "builtin": 0, "patterns": [{"type": "rule", "expr": "{     \"model\": \"1.0\",     \"desc\": \"担保人手机号\",     \"scores\": [         {             \"desc\": \"手机号（中国内地)； 内容正则匹配 && 内容长度限制 && 内容校验\",             \"name\": \"PhoneHitRatio\",             \"output\": true,             \"rule\": {                 \"array_hit_ratio\": [                     {                         \"and\": [                             {                                 \"regexp_find\": [                                     \"信贷\",                                     {                                         \"var\": \"table.comment\"                                     }                                 ]                             },                             {                                 \"regexp_find\": [                                     \"担保人\",                                     {                                         \"var\": \"column.comment\"                                     }                                 ]                             },                             {                                 \"regexp_find\": [                                     \"(^(0\\\\d{2}-\\\\d{8}(-\\\\d{1,4})?)|(0\\\\d{3}-\\\\d{7,8}(-\\\\d{1,4})?))|(^((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(18[0,5-9]))\\\\d{8})[ ]*$\",                                     {                                         \"var\": \"ArrayElementValue\"                                     }                                 ]                             }                         ]                     },                     {                         \"var\": \"column.values\"                     }                 ]             }         },         {             \"desc\": \"命中率必须大于等于 30\",             \"name\": \"CalculateHitRatio\",             \"output\": true,             \"rule\": {                 \">=\": [                     {                         \"var\": \"$.PhoneHitRatio\"                     },                     30                 ]             }         },         {             \"desc\": \"计算得分\",             \"name\": \"OverallScore\",             \"output\": true,             \"rule\": {                 \"condition\": [                     {                         \"var\": \"$.CalculateHitRatio\"                     },                     100,                     0                 ]             }         }     ] }"}], "name": "担保人手机号", "description": "", "levelName": "S4", "id": 32767, "category": 20297}, {"level": 10, "dataTag": "1::zuo_ji_ce_shi_2_LY", "builtin": 0, "patterns": [{"type": "rule", "expr": "{     \"model\": \"1.0\",     \"desc\": \"座机号码（中国内地)（结构化）\",     \"scores\": [         {             \"desc\": \"匹配座机号码（中国内地)； 列名匹配 && 内容正则匹配 && 内容长度限制\",             \"name\": \"LandlineHitRatio\",             \"output\": true,             \"rule\": {                 \"array_hit_ratio\": [                     {                         \"and\": [                             {                                 \"<=\": [                                     {                                         \"length\": {                                             \"var\": \"ArrayElementValue\"                                         }                                     },                                     20                                 ]                             },                             {                                 \"regexp_find\": [                                     \"(?i)(fax|phone|tel)\",                                     {                                         \"var\": \"column.name\"                                     }                                 ]                             },                             {                                 \"regexp_find\": [                                     \"((^0\\\\d{2}[-]?\\\\d{8}([-]?\\\\d{1,4})?)|(^0\\\\d{3}[-]?\\\\d{7,8}([-]?\\\\d{1,4})?))[ ]*$\",                                     {                                         \"var\": \"ArrayElementValue\"                                     }                                 ]                             }                         ]                     },                     {                         \"var\": \"column.values\"                     }                 ]             }         },         {             \"desc\": \"命中率必须大于等于 30\",             \"name\": \"CalculateHitRatio\",             \"output\": true,             \"rule\": {                 \">=\": [                     {                         \"var\": \"$.LandlineHitRatio\"                     },                     30                 ]             }         },         {             \"desc\": \"计算得分\",             \"name\": \"OverallScore\",             \"output\": true,             \"rule\": {                 \"condition\": [                     {                         \"var\": \"$.CalculateHitRatio\"                     },                     80,                     0                 ]             }         }     ] }"}], "name": "座机号码", "description": "", "levelName": "S1", "id": 32892, "category": 20428}, {"level": 40, "dataTag": "1::di_zhi__mo_xing_dingyi", "builtin": 0, "patterns": [{"type": "rule", "expr": "ENC[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]"}], "name": "地址_模型定义", "description": "", "levelName": "S4", "id": 34009, "category": 20296}, {"level": 40, "dataTag": "1::c_shelter_related_field", "builtin": 0, "patterns": [{"type": "catalog", "expr": "c_shelter_related_field"}], "name": "c_shelter_related_field", "description": "", "levelName": "S4", "id": 34250, "category": 20451}, {"level": 30, "dataTag": "1::ye_wu_bian_hao", "builtin": 0, "patterns": [], "name": "业务编号", "description": "", "levelName": "S3", "id": 34667, "category": 20297}, {"level": 30, "dataTag": "1::ye_wu_daima", "builtin": 0, "patterns": [], "name": "业务代码", "description": "", "levelName": "S3", "id": 34668, "category": 20297}, {"level": 10, "dataTag": "1::liu_han_ceshi", "builtin": 0, "patterns": [], "name": "刘寒测试", "description": "", "levelName": "S1", "id": 35368, "category": 20491}, {"level": 10, "dataTag": "1::liu_han_ce_shi_11", "builtin": 0, "patterns": [], "name": "刘寒测试11", "description": "", "levelName": "S1", "id": 35369, "category": 20490}, {"level": 10, "dataTag": "1::liu_han_ce_shi_111", "builtin": 0, "patterns": [], "name": "刘寒测试111", "description": "", "levelName": "S1", "id": 35370, "category": 20491}, {"level": 10, "dataTag": "1::sadfasdf", "builtin": 0, "patterns": [], "name": "sadfasdf", "description": "", "levelName": "S1", "id": 35371, "category": 20500}, {"level": 10, "dataTag": "1::234qwer", "builtin": 0, "patterns": [], "name": "234qwer", "description": "", "levelName": "S1", "id": 35372, "category": 20500}, {"level": 10, "dataTag": "1::8888", "builtin": 0, "patterns": [], "name": "8888", "description": "", "levelName": "S1", "id": 35373, "category": 20498}], "scanJobHistoryId": 64733, "taskId": 18026, "sensDataMarkings": [{"manualTags": "[\"cls.pii::name\"]", "deletedAutoTags": "[]", "qualifiedName": "sample.db2inst1.staff.name", "datasourceId": 19414, "dbInstanceId": 12573, "tenantId": 1}]}