{"taskParam": {"dcapProbeId": 1, "dbInstAddressId": 83869, "dbInstAccountId": 28223, "viewDefinitionSampling": false, "sampleCount": 100, "sampleSqlTimeout": 15, "minimumLength": 0, "maximumLength": 256, "hitPercentage": 60, "tableType": "TABLE", "dataType": "TEXT,NUMBER", "excludeSchema": "", "excludeTable": "", "excludeColumn": "", "tableRowCountEnabled": false, "tableRowCount": 100, "tableRowCountUnit": 1, "excludeEmptyValues": false, "emptyPercentage": 90, "scanRange": {"excludedDatabase": [], "excludedSchema": [], "excludedSynonym": [], "excludedTable": [], "excludedView": [], "selectedDatabase": [], "selectedSchema": [], "selectedSynonym": [], "selectedTable": [], "selectedView": []}}, "datasource": {"dbNameType": 2, "sourceType": "redshift", "port": "5439", "name": "redshift", "host": "yuandian-workgroup.************.cn-north-1.redshift-serverless.amazonaws.com.cn", "authCfg": {"password": "BDPEFgjjbl485.!", "username": "admin"}, "id": 28225, "extraCfg": ""}, "tenantId": 1, "name": "default-db-28225", "policies": [], "scanJobHistoryId": 86125, "taskId": 82902}