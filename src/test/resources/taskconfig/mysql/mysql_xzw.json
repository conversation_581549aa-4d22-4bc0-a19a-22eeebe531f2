{"taskParam": {"dcapProbeId": 1, "dbInstAddressId": 96174, "dbInstAccountId": 95197, "viewDefinitionSampling": false, "sampleCount": 100, "sampleSqlTimeout": 15, "minimumLength": 0, "maximumLength": 256, "hitPercentage": 60, "tableType": "TABLE", "dataType": "TEXT,NUMBER", "excludeSchema": "", "excludeTable": "", "excludeColumn": "", "tableRowCountEnabled": false, "tableRowCount": 100, "tableRowCountUnit": 1, "excludeEmptyValues": false, "emptyPercentage": 90, "scanRange": {"excludedDatabase": [], "excludedSchema": [], "excludedSynonym": [], "excludedTable": [], "excludedView": [], "selectedDatabase": ["enc_test"], "selectedSchema": [], "selectedSynonym": [], "selectedTable": [], "selectedView": []}}, "datasource": {"dbNameType": 1, "sourceType": "mysql", "port": "3306", "encryptionSwitch": 1, "name": "mysql_xuezhiwei_xzw_雪智伟", "host": "mysql.had.uesec.com", "authCfg": {"password": "Jeeboot&9527", "username": "root"}, "id": 95287, "extraCfg": ""}, "tenantId": 1, "name": "default-db-95287", "policies": [{"level": null, "dataTag": "1.liuhanceshifenleifen.chepaihaodalu", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"车牌号(大陆)（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 车牌、车牌号 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"\\u8F66\\u724C[\\u53F7]?\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]正则模糊匹配license(允许包含_)plate、plate(允许包含_)number\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"((?i)license[_]?plate)|((?i)plate[_]?number)\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 内置车牌号匹配规则 cls.pii::cardnum (命中率80%)\"\r\n    name: \"FindValidator\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - validator:\r\n                  - \"cls.pii::cardnum\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindValidator\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "ch<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "0607", "levelName": null, "id": "48cfd14b-a04b-4181-813d-68b4beaea30b", "category": null}, {"level": null, "dataTag": "1.zhangyanlongceshi.teseta", "builtin": false, "patterns": [{"expr": "test", "type": "catalog"}], "name": "teseta", "description": "多对多", "levelName": null, "id": "0dac5b29-6b16-4eef-8f90-3e1ddf6311a0", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.shengri", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"生日（结构化）\"\r\nscores:\r\n- desc: \"列注释 以 生日 、 出生日期 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"([\\u51FA]?\\u751F\\u65E5[\\u671F]?\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则精确匹配 birth、模糊匹配birthday、date(允许包含_)of(允许包含_) birth\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(^(?i)birth$)|((?i)birthday)|((?i)date[_]?of[_]?birth)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "e6a779d1-1efa-4422-8e23-f20ef4cd8f01", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.dizhi", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"地址（结构化）\"\r\nscores:\r\n- desc: \"列名匹配 address 或者 addr\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(?i)address|(?i)addr\"\r\n      - var: \"column.name\"\r\n- desc: \"列定义长度 大于 20\"\r\n  name: \"ColumnDefLength\"\r\n  output: true\r\n  rule:\r\n    and:\r\n    - var: \"$.FindColumnName\"\r\n    - '>=':\r\n      - var: \"column.size\"\r\n      - 20\r\n- desc: \"字段注释 包含 住址 or 家庭地址 or 办公地址 or 联系地址\"\r\n  name: \"FieldCommentsContains\"\r\n  output: true\r\n  rule:\r\n    and:\r\n    - var: \"$.ColumnDefLength\"\r\n    - regexp_find:\r\n        - \"\\u4f4f\\u5740|\\u5bb6\\u5ead\\u5730\\u5740|\\u529e\\u516c\\u5730\\u5740|\\u8054\\u7cfb\\u5730\\u5740\"\r\n        - var: \"column.comment\"\r\n- desc: \"NLP 识别地址。以上条件都满足或者是 NLP 识别率达到 30%\"\r\n  name: \"NlpAnalysis\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FieldCommentsContains\"\r\n      - and:\r\n        - var: \"$.ColumnDefLength\"\r\n        - '>=':\r\n            - array_hit_ratio:\r\n                - validator:\r\n                    - \"cls.piilinkable::address\"\r\n                    - var: \"ArrayElementValue\"\r\n                - var: \"column.values\"\r\n            - 30\r\n- desc: \"NLP 如果不满足，继续 字段内容 匹配规则列表\"\r\n  name: \"RegexpListFind\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.NlpAnalysis\"\r\n      - and:\r\n          - var: \"$.ColumnDefLength\"\r\n          - '>=':\r\n              - array_hit_ratio:\r\n                  - or: # 包含 小区|单元|号楼|大厦|家园|新区|村委会|公安局|派出所|街道办|公寓|厂区\r\n                      - regexp_find:\r\n                          - \"\\u5c0f\\u533a|\\u5355\\u5143|\\u53f7\\u697c|\\u5927\\u53a6|\\u5bb6\\u56ed|\\u65b0\\u533a|\\u6751\\u59d4\\u4f1a|\\u516c\\u5b89\\u5c40|\\u6d3e\\u51fa\\u6240|\\u8857\\u9053\\u529e|\\u516c\\u5bd3|\\u5382\\u533a\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 以 3位数字+室or房 结尾\r\n                          - \"\\\\d{1,3}\\\\s*[\\u5BA4\\u623F]$\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 市 and 区 and （1位数字+号），市区两字不可紧相邻\r\n                          - \"\\u5E02(\\\\s*[^\\\\s\\u5E02\\u533A\\\\d])+\\\\s*\\u533A(\\\\s*[^\\\\s\\u5E02\\u533A\\u53F7])+\\\\d*\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 村 and 号，之间有至少一位值\r\n                          - \"\\u6751(\\\\s*[^\\\\s\\u6751\\u53F7])+\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 市 and 村，之间有至少一位值\r\n                          - \"\\u5E02(\\\\s*[^\\\\s\\u5E02\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 区 and 村，之间有至少一位值\r\n                          - \"\\u533A(\\\\s*[^\\\\s\\u533A\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 镇 and 村，之间有至少一位值\r\n                          - \"\\u9547(\\\\s*[^\\\\s\\u9547\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 县 and 村，之间有至少一位值\r\n                          - \"\\u53BF(\\\\s*[^\\\\s\\u53BF\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 巷 and 号，之间有至少一位值\r\n                          - \"\\u5DF7(\\\\s*[^\\\\s\\u5DF7\\u53F7])+\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 包含 任意位数字+栋+任意位数字\r\n                          - \"\\\\d+\\\\s*\\u680B\\\\s*\\\\d+\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 社区 and 号，之间有至少一位值\r\n                          - \"\\u793E\\u533A(\\\\s*[^\\\\s\\u793E\\u533A\\u53F7])+\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 以 局 结尾\r\n                          - \"\\u5C40\\\\s*$\"\r\n                          - var: \"ArrayElementValue\"\r\n                  - var: \"column.values\"\r\n              - 30\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.RegexpListFind\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "dizhi", "description": null, "levelName": null, "id": "aead267a-f02f-44e2-b4fc-805c77190b89", "category": null}, {"level": null, "dataTag": "1.qianyi<PERSON>lei.c_shelter_from_n_to_", "builtin": false, "patterns": [{"expr": "c_shelter_from_n_to_m", "type": "catalog"}], "name": "c_shelter_from_n_to_", "description": "", "levelName": null, "id": "00fe87b0-bdf2-4cd2-8c62-a13076a71531", "category": null}, {"level": null, "dataTag": "1.qianyi<PERSON>lei.c_chinese_name", "builtin": false, "patterns": [{"expr": "c_chinese_name", "type": "catalog"}], "name": "c_chinese_name", "description": "", "levelName": null, "id": "5bae9074-0346-45de-b4b0-c6a3fda090da", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.c_bank_card", "builtin": false, "patterns": [{"expr": "c_bank_card", "type": "catalog"}], "name": "c_bank_card", "description": "", "levelName": null, "id": "fa4a7f49-2c54-459b-a206-d961bfe980b1", "category": null}, {"level": null, "dataTag": "1.qianyi<PERSON><PERSON>i.<PERSON><PERSON><PERSON><PERSON>ma", "builtin": false, "patterns": [{"expr": "{     \"model\": \"1.0\",     \"desc\": \"座机号码（中国内地)（结构化）\",     \"scores\": [         {             \"desc\": \"匹配座机号码（中国内地)； 列名匹配 && 内容正则匹配 && 内容长度限制\",             \"name\": \"LandlineHitRatio\",             \"output\": true,             \"rule\": {                 \"array_hit_ratio\": [                     {                         \"and\": [                             {                                 \"<=\": [                                     {                                         \"length\": {                                             \"var\": \"ArrayElementValue\"                                         }                                     },                                     20                                 ]                             },                             {                                 \"regexp_find\": [                                     \"(?i)(fax|phone|tel)\",                                     {                                         \"var\": \"column.name\"                                     }                                 ]                             },                             {                                 \"regexp_find\": [                                     \"((^0\\\\d{2}[-]?\\\\d{8}([-]?\\\\d{1,4})?)|(^0\\\\d{3}[-]?\\\\d{7,8}([-]?\\\\d{1,4})?))[ ]*$\",                                     {                                         \"var\": \"ArrayElementValue\"                                     }                                 ]                             }                         ]                     },                     {                         \"var\": \"column.values\"                     }                 ]             }         },         {             \"desc\": \"命中率必须大于等于 30\",             \"name\": \"CalculateHitRatio\",             \"output\": true,             \"rule\": {                 \">=\": [                     {                         \"var\": \"$.LandlineHitRatio\"                     },                     30                 ]             }         },         {             \"desc\": \"计算得分\",             \"name\": \"OverallScore\",             \"output\": true,             \"rule\": {                 \"condition\": [                     {                         \"var\": \"$.CalculateHitRatio\"                     },                     80,                     0                 ]             }         }     ] }", "type": "rule"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "6297ed1c-7b02-4591-bd2a-24adbee03ad0", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.youbiandal<PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"邮编（结构化）\"\r\nscores:\r\n- desc: \"列注释以 邮编 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u90AE\\u7F16\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则精确匹配postal、postcode、postalcode、zipcode 模糊匹配 (允许包含_)of\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(^(?i)postal$)|(^(?i)postcode$)|(^(?i)postalcode$)|(^(?i)zipcode$)|((?i)postal[_]?of)|((?i)postcode[_]?of)|((?i)postalcode[_]?of)|((?i)zipcode[_]?of)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "1278f81f-8e12-4a2b-a15c-1a424219c159", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.SwiftCode", "builtin": true, "patterns": [{"expr": "^[A-Za-z]{4}(?:GB|US|DE|RU|CA|JP|CN)[0-9a-zA-Z]{2,5}[ ]*$", "type": "data"}], "name": "SwiftCode", "description": null, "levelName": null, "id": "b8abfce4-3669-44e0-9d7b-99caa775a047", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.WebURL", "builtin": true, "patterns": [{"expr": "(?i)web.?url", "type": "catalog"}], "name": "WebURL", "description": null, "levelName": null, "id": "a1f13582-a625-416d-855d-8b1959cfbe03", "category": null}, {"level": null, "dataTag": "1.q<PERSON><PERSON><PERSON><PERSON><PERSON>.salary", "builtin": false, "patterns": [{"expr": "salary", "type": "catalog"}], "name": "salary", "description": "", "levelName": null, "id": "8cf66d8b-ea34-4d43-8ac7-66418e0eb68f", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.c_regex_replace", "builtin": false, "patterns": [{"expr": "c_regex_replace", "type": "catalog"}], "name": "c_regex_replace", "description": "", "levelName": null, "id": "70e9bf68-5f01-42e9-b284-f8aaa462ae88", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.MAC", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"MAC（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 mac 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"(?i)mac\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]精确匹配mac\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"^(?i)mac$\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 满足正则匹配规则\"\r\n    name: \"FindColumnValue\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - regexp_find:\r\n                  - \"^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})|([0-9a-fA-F]{4}\\\\\\\\.[0-9a-fA-F]{4}\\\\\\\\.[0-9a-fA-F]{4})[ ]*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnValue\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "MAC", "description": null, "levelName": null, "id": "31bad24e-c234-4514-951b-c89b84a7d856", "category": null}, {"level": null, "dataTag": "1.qianyi<PERSON><PERSON>i.xingbie", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"性别（结构化）\"\r\nscores:\r\n- desc: \"列注释匹配以 性别 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u6027\\u522B\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"数据字典(命中率90%)\"\r\n  name: \"FindDataDict\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - \">=\":\r\n          - array_hit_ratio:\r\n              - \">\":\r\n                - search_in_dictionary:\r\n                    - \"gender\"\r\n                    - var: \"ArrayElementValue\"\r\n                - 0\r\n              - var: \"column.values\"\r\n          - 90\r\n- desc: \"[字段名]正则精确匹配gender、sexual、sex\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindDataDict\"\r\n      - regexp_find:\r\n          - \"(^(?i)gender$)|(^(?i)sexual$)|(^(?i)sex$)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "8eeee038-4481-4ce6-a1ff-417c514c9eb5", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.jiedao", "builtin": true, "patterns": [{"expr": "(?i)street", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "ba65674d-d485-496a-bf21-958f9e12574a", "category": null}, {"level": null, "dataTag": "1.qianyi<PERSON>lei.c_shelter_spec_char_", "builtin": false, "patterns": [{"expr": "c_shelter_spec_char_bef", "type": "catalog"}], "name": "c_shelter_spec_char_", "description": "", "levelName": null, "id": "abf54e07-f119-4cea-879f-65e5448a2049", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.zidingyicolumn", "builtin": false, "patterns": [{"expr": "^column_\\d+$", "type": "catalog"}], "name": "zidingyicolumn", "description": "", "levelName": null, "id": "bbd3e1d6-6e28-4985-b8fd-e0dc18e9f0b3", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.c_id_card", "builtin": false, "patterns": [{"expr": "c_id_card", "type": "catalog"}], "name": "c_id_card", "description": "", "levelName": null, "id": "bb870642-fdef-4821-84cd-2600c03f0356", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.mima", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"密码（结构化）\"\r\nscores:\r\n  - desc: \"列注释匹配以 密码 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"\\u5BC6\\u7801\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]正则精确匹配password、passcode、passphrase\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"(^(?i)password$)|(^(?i)passcode$)|(^(?i)passphrase$)\"\r\n            - var: \"column.name\"\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnName\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "mima", "description": null, "levelName": null, "id": "b06bc6a6-bb45-4873-bea1-c6d7f8f3f20c", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.xueya", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"血压（结构化）\"\r\nscores:\r\n- desc: \"列注释匹配 以 血压 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u8840\\u538B\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则模糊匹配blood(允许包含_)pressure 且 内容校验在 20-220 之间的整数(命中率90%)\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - and:\r\n        - regexp_find:\r\n          - \"(?i)blood[_]?pressure\"\r\n          - var: \"column.name\"\r\n        - \">=\":\r\n            - array_hit_ratio:\r\n                - and:\r\n                    - \">=\":\r\n                        - var: \"ArrayElementValue\"\r\n                        - 20\r\n                    - \"<=\":\r\n                        - var: \"ArrayElementValue\"\r\n                        - 220\r\n                - var: \"column.values\"\r\n            - 90\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "x<PERSON>ya", "description": null, "levelName": null, "id": "cbf0f2d2-d3cd-440b-8f23-cb7f7bd9ffb6", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.IPv4", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"IPV4（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 ip、ipv4 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"((?i)ipv4\\\\s*$)|((?i)ip\\\\s*$)\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]精确匹配ip、ipv4\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"(^(?i)ip$)|(^(?i)ipv4$)\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 满足正则匹配规则\"\r\n    name: \"FindColumnValue\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - regexp_find:\r\n                  - \"^(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\\\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])[ ]*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnValue\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "IPv4", "description": "", "levelName": null, "id": "cdff981b-c328-4f55-8821-ed9915f133fa", "category": null}, {"level": null, "dataTag": "1.qianyi<PERSON>lei.c_shelter_front_n_re", "builtin": false, "patterns": [{"expr": "c_shelter_front_n_rear_m", "type": "catalog"}], "name": "c_shelter_front_n_re", "description": "", "levelName": null, "id": "aef4babb-1575-403f-8cc1-f5a96603f673", "category": null}, {"level": null, "dataTag": "1.q<PERSON><PERSON><PERSON><PERSON><PERSON>.<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "---\nmodel: \"1.0\"\ndesc: \"\"\nname: \"测试时间\"\nscores:\n- desc: \"测试注释包含时间\"\n  name: \"ScoreName_11\"\n  output: true\n  rule:\n    condition:\n    - regexp_find:\n      - \"时间\"\n      - var: \"column.comment\"\n    - 11\n    - 0\n- desc: \"计算得分\"\n  name: \"OverallScore\"\n  output: true\n  rule:\n    condition:\n    - ==:\n      - var: \"$.ScoreName_11\"\n      - 11\n    - 11\n    - 0\n", "type": "rule"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "a8a53b20-d900-4e96-82e4-56c8ecd11a07", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.ddddccc", "builtin": false, "patterns": [{"expr": "(?i)city", "type": "data"}], "name": "ddddccc", "description": "dddddccc", "levelName": null, "id": "9bf4650d-b5d5-45dd-bccb-ffa35403365e", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.yinlianka", "builtin": true, "patterns": [{"expr": "^62[0-5][0-9]{13,16}[ ]*$", "type": "data"}], "name": "yin<PERSON><PERSON>", "description": null, "levelName": null, "id": "d7834b67-4ba2-4f2d-bdc6-93b94146dae4", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.shen<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"身份证（中国内地）（结构化）\"\r\nscores:\r\n- desc: \"内容长度限制必须大于等于 18\"\r\n  name: \"IdCardLength\"\r\n  output: true\r\n  rule:\r\n    \">=\":\r\n      - var: \"column.size\"\r\n      - 18\r\n- desc: \"注释以身份证号结尾\"\r\n  name: \"ColumnComment\"\r\n  output: true\r\n  rule:\r\n    and:\r\n      - var: \"$.IdCardLength\"\r\n      - regexp_find:\r\n          - \"\\u8EAB\\u4EFD\\u8BC1\\u53F7$\"\r\n          - var: \"column.comment\"\r\n- desc: \"如果注释匹配，直接返回，否则计算命中率。\"\r\n  name: \"RegexpFindAndValidator\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.ColumnComment\"\r\n      - 100\r\n      - array_hit_ratio:\r\n          - and:\r\n            - '>=':\r\n                - length:\r\n                  - var: \"ArrayElementValue\"\r\n                - 18\r\n            - regexp_find:\r\n                - \"(([1][1-5])|([2][1-3])|([3][1-7])|([4][1-6])|([5][0-4])|([6][1-5])|([7][1])|([8][1-2]))\\\\d{4}(([1][9]\\\\d{2})|([2]\\\\d{3}))(([0][1-9])|([1][0-2]))(([0][1-9])|([1-2][0-9])|([3][0-1]))\\\\d{3}[0-9xX]\\\\s*\"\r\n                - var: \"ArrayElementValue\"\r\n            - validator:\r\n                - \"cls.pii::idcard\"\r\n                - var: \"ArrayElementValue\"\r\n          - var: \"column.values\"\r\n- desc: \"命中率必须大于等于 30\"\r\n  name: \"CalculateHitRatio\"\r\n  output: true\r\n  rule:\r\n    '>=':\r\n      - var: \"$.RegexpFindAndValidator\"\r\n      - 30\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    'condition':\r\n    - var: \"$.CalculateHitRatio\"\r\n    - 100\r\n    - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "12ba8234-c432-4648-b847-154cbf6a4259", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.c_mobile", "builtin": false, "patterns": [{"expr": "c_mobile", "type": "catalog"}], "name": "c_mobile", "description": "", "levelName": null, "id": "c39b8ed2-8999-4142-bb81-d3deac5d141a", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.VISAka", "builtin": true, "patterns": [{"expr": "^4[0-9]{3}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[ ]*$", "type": "data"}], "name": "VISAka", "description": null, "levelName": null, "id": "a74b91af-c63e-47d5-80fa-9d7bf6baf94a", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.zongjiao", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"宗教（结构化）\"\r\nscores:\r\n- desc: \"列注释以 宗教、信仰 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u5B97\\u6559\\\\s*$)|(\\u4FE1\\u4EF0\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"数据字典(命中率90%)\"\r\n  name: \"FindDataDict\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - \">=\":\r\n          - array_hit_ratio:\r\n              - \">\":\r\n                - search_in_dictionary:\r\n                    - \"religions\"\r\n                    - var: \"ArrayElementValue\"\r\n                - 0\r\n              - var: \"column.values\"\r\n          - 90\r\n- desc: \"[字段名]正则精确匹配 faith 或 精确匹配religion 或 模糊匹配religion(允许包含_)of \"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindDataDict\"\r\n      - regexp_find:\r\n          - \"(^(?i)faith$)|(^(?i)religion$)|((?i)religion[_]?of)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON>jiao", "description": null, "levelName": null, "id": "642a0939-7e26-43bc-b2b8-f9bc7b75259b", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.guojiyinxingzhanghao", "builtin": true, "patterns": [{"expr": "^[a-zA-Z]{2}[0-9]{2}[a-zA-Z0-9]{4}[0-9]{7}([a-zA-Z0-9]?){0,16}[ ]*$", "type": "data"}], "name": "guojiyinxingzhanghao", "description": null, "levelName": null, "id": "46aa1eb7-2424-4bdd-9952-7fec65f3fd74", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.c_shelter_related_fi", "builtin": false, "patterns": [{"expr": "c_shelter_related_field", "type": "catalog"}], "name": "c_shelter_related_fi", "description": "", "levelName": null, "id": "05ec6c00-d4d1-4070-995a-e333e6544bbd", "category": null}, {"level": null, "dataTag": "1.qianyi<PERSON><PERSON>i.c_do_nothing", "builtin": false, "patterns": [{"expr": "c_do_nothing", "type": "catalog"}], "name": "c_do_nothing", "description": "", "levelName": null, "id": "e8ac679d-c97b-4145-bbc2-24b755d7fe0c", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.danwei", "builtin": true, "patterns": [{"expr": "(?i)affiliation", "type": "catalog"}], "name": "danwei", "description": null, "levelName": null, "id": "5aa3380f-5791-4732-9c3f-bc1f5d1f5118", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.xinlv", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"心率（结构化）\"\r\nscores:\r\n  - desc: \"列注释匹配 以 心率 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"\\u5FC3\\u7387\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]正则模糊匹配heart (允许包含_)rate 且 内容校验是在40-180之间的整数(命中率90%)\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - and:\r\n            - regexp_find:\r\n                - \"(?i)heart[_]?rate\"\r\n                - var: \"column.name\"\r\n            - \">=\":\r\n                - array_hit_ratio:\r\n                    - and:\r\n                        - \">=\":\r\n                            - var: \"ArrayElementValue\"\r\n                            - 40\r\n                        - \"<=\":\r\n                            - var: \"ArrayElementValue\"\r\n                            - 180\r\n                    - var: \"column.values\"\r\n                - 90\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnName\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "xinlv", "description": null, "levelName": null, "id": "1484c971-3d37-4996-b8b4-e6b981677b6e", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.y<PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"银行卡（结构化）\"\r\nscores:\r\n- desc: \"列注释以 银行卡号 或 银行账号 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u94F6\\u884C[\\u5361\\u8D26][\\u53F7]?\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"字段名匹配\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"^(?i)bank[_-]?accno$|^(?i)bank[_-]?account$|^(?i)bank[_-]?accno$|^(?i)bank[_-]?card[_-]?no$|^(?i)bank[_-]?card[_-]?number$\"\r\n      - var: \"column.name\"\r\n- desc: \"正则+校验\"\r\n  name: \"ColumnValuesRegexpAndValidator\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.FindColumnComment\"\r\n      - 100\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - array_hit_ratio:\r\n          - and:\r\n              - regexp_find:\r\n                  - \"^\\\\s*\\\\d{10,20}\\\\s*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - regexp_find:\r\n                  - \"^\\\\s*(?!(\\\\d)\\\\1{5}).*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - validator:\r\n                  - \"cls.pii::unionpay\"\r\n                  - var: \"ArrayElementValue\"\r\n          - var: \"column.values\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - '>=':\r\n          - var: \"$.ColumnValuesRegexpAndValidator\"\r\n          - 80\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "yin<PERSON><PERSON><PERSON>", "description": "Update on 2024.04.19", "levelName": null, "id": "f7c3dbad-c8f9-4971-b925-dbe734636f89", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.nianling", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"年龄（结构化）\"\r\nscores:\r\n- desc: \"列注释匹配 以 年龄 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u5E74\\u9F84\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"列名精确匹配 age，且值 0-100 之间，90% 命中率\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - and:\r\n        - regexp_find:\r\n          - \"^(?i)age$\"\r\n          - var: \"column.name\"\r\n        - \">=\":\r\n            - array_hit_ratio:\r\n                - and:\r\n                    - \">=\":\r\n                        - var: \"ArrayElementValue\"\r\n                        - 0\r\n                    - \"<=\":\r\n                        - var: \"ArrayElementValue\"\r\n                        - 100\r\n                - var: \"column.values\"\r\n            - 90\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "117ed049-e491-4d86-86e0-0f2a22da668e", "category": null}, {"level": null, "dataTag": "1.qianyi<PERSON><PERSON>i.c_keep_from_n_to_m", "builtin": false, "patterns": [{"expr": "c_keep_from_n_to_m", "type": "catalog"}], "name": "c_keep_from_n_to_m", "description": "", "levelName": null, "id": "bf6aa1d1-2bd5-4d5f-968e-d08ac448f971", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.chushengriqi", "builtin": true, "patterns": [{"expr": "(?i)date.?of.?birth", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "09c412cd-fc5c-4d67-bada-ebc53f1eb966", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.chepaihaodalu", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"车牌号(大陆)（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 车牌、车牌号 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"\\u8F66\\u724C[\\u53F7]?\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]正则模糊匹配license(允许包含_)plate、plate(允许包含_)number\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"((?i)license[_]?plate)|((?i)plate[_]?number)\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 内置车牌号匹配规则 cls.pii::cardnum (命中率80%)\"\r\n    name: \"FindValidator\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - validator:\r\n                  - \"cls.pii::cardnum\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindValidator\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "ch<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "1da035a5-25c8-487d-b976-ad16150595b2", "category": null}, {"level": null, "dataTag": "1.q<PERSON><PERSON><PERSON><PERSON><PERSON>.xingming", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"姓名（结构化）\"\r\nscores:\r\n- desc: \"字段名规则列表\"\r\n  name: \"FindColumnNameList\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - regexp_find:\r\n          - \"^(?i)(insured|insured[_-]?name|receiver|receiver[_-]?name|sender|sender[_-]?name|recipient|recipient[-_]?name|full[_-]?name|first[_-]?name|last[_-]?name|real[_-]?name|staff[_-]?name|candidate|cardholder|cardholder[_-]?name|member[_-]?name|payer[_-]?name|payee[_-]?name|purchaser[_-]?name|trustee[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(bank[_-]?account[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(customer[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(holder[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(contact[s]?[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(patient[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(spouse[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(kin[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(student[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"^(?i)(name[_-]?of|name[_-]?on)\"\r\n          - var: \"column.name\"\r\n- desc: \"列名匹配\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"^(?!.*(org|medicine))(.*(name|er|yee))$|^(insured|initial)$\"\r\n      - var: \"column.name\"\r\n- desc: \"字段注释\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    and:\r\n      - var: \"$.FindColumnName\"\r\n      - regexp_find: # （字符串中包含 姓名 或者 账户名称）同时（不能包含 企 或者 公 或者 人 或者 修改 或者 编辑 或者 变更 或者 创建 或者 操作)\r\n          - \"^(?=.*(\\u59D3\\u540D|\\u8D26\\u6237\\u540D\\u79F0))(?!.*(\\u4F01|\\u516C|\\u4EBA|\\u4FEE\\u6539|\\u7F16\\u8F91|\\u53D8\\u66F4|\\u521B\\u5EFA|\\u64CD\\u4F5C)).*$\"\r\n          - var: \"column.comment\"\r\n- desc: \"反向字段注释\"\r\n  name: \"ReverseFindColumnComment\"\r\n  output: true\r\n  rule:\r\n    and:\r\n      - var: \"$.FindColumnName\"\r\n      - regexp_find: # 品名 或 品名称 或 司名称 或 业名称 或 厂名称 或 商名称 或 社名称 或 机构名称 或 种名称 结尾\r\n          - \"(\\u54C1\\u540D|\\u54C1\\u540D\\u79F0|\\u53F8\\u540D\\u79F0|\\u4E1A\\u540D\\u79F0|\\u5382\\u540D\\u79F0|\\u5546\\u540D\\u79F0|\\u793E\\u540D\\u79F0|\\u673A\\u6784\\u540D\\u79F0|\\u79CD\\u540D\\u79F0)$\"\r\n          - var: \"column.comment\"\r\n- desc: \"NLP 识别\"\r\n  name: \"NlpAnalysis\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.FindColumnNameList\"\r\n      - true\r\n      - var: \"$.FindColumnComment\"\r\n      - true\r\n      - var: \"$.ReverseFindColumnComment\"\r\n      - false\r\n      - '>=':\r\n          - array_hit_ratio:\r\n              - and:\r\n                  - <=:\r\n                      - length:\r\n                          var: \"ArrayElementValue\"\r\n                      - 5\r\n                  - validator:\r\n                      - \"cls.pii::name\"\r\n                      - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 30\r\n- desc: \"表名和列名组合规则\"\r\n  name: \"TableAndColumnCombination\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.NlpAnalysis\"\r\n      - and:\r\n          - regexp_find:\r\n              - \"(?i)(erinfo|yeeinfo)$\"\r\n              - var: \"table.name\"\r\n          - regexp_find:\r\n              - \"(?i)(name)\"\r\n              - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n    - var: \"$.TableAndColumnCombination\"\r\n    - 100\r\n    - 0", "type": "rule"}], "name": "xing<PERSON>", "description": null, "levelName": null, "id": "ddfbe81c-3604-4c7c-97a6-c49e6acb361a", "category": null}, {"level": null, "dataTag": "1.qian<PERSON><PERSON><PERSON>i.wans<PERSON><PERSON>a", "builtin": true, "patterns": [{"expr": "^5[1-5][0-9]{2}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[ ]*$", "type": "data"}], "name": "wans<PERSON><PERSON>a", "description": null, "levelName": null, "id": "e9e065ab-6fa4-4b0f-904d-9e1069471000", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.shoujihao_enc_DW", "builtin": false, "patterns": [{"expr": "login_name_enc", "type": "catalog"}], "name": "shou<PERSON>hao_enc_DW", "description": "", "levelName": null, "id": "09f9c99a-9ff6-4336-af40-be29dffdf32e", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.c_email", "builtin": false, "patterns": [{"expr": "c_email", "type": "catalog"}], "name": "c_email", "description": "", "levelName": null, "id": "c8c8ea3a-d61f-40a6-9065-96fb949bd7b3", "category": null}, {"level": null, "dataTag": "1.qianyi<PERSON><PERSON>i.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "(^(86|\\+86|0086)?[-]?((0\\d{2}[-]?\\d{8}([-]?\\d{1,4})?)|(0\\d{3}[-]?\\d{7,8}([-]?\\d{1,4})?))|(^((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(16[2|5-7])|(17[0-8])|(18[0,5-9])|(19[0-3|5-9]))\\d{8}))[ ]*$", "type": "data"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "ce13fd02-3ebe-450d-b8ee-17b607b4f2bd", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.weizhi", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"位置（结构化）\"\r\nscores:\r\n- desc: \"列注释以 位置、经度、纬度、经纬、经纬度 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u4F4D\\u7F6E\\\\s*$)|(\\u7ECF[\\u7EAC]?\\u5EA6\\\\s*$)|(\\u7ECF\\u7EAC\\\\s*$)|(\\u7EAC\\u5EA6\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则精确匹配location、latitude、longitude、lng、lat\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(^(?i)location$)|(^(?i)latitude$)|(^(?i)longitude$)|(^(?i)lng$)|(^(?i)lat$)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "we<PERSON>hi", "description": null, "levelName": null, "id": "a3523f7b-f9c8-4c8d-afe7-a6cb09b770cf", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.youxiang_new", "builtin": false, "patterns": [{"expr": "{   \"model\" : \"1.0\",   \"desc\" : \"邮箱\",   \"scores\" : [ {     \"desc\" : \"匹配邮箱； 内容正则匹配 && 内容校验\",     \"name\" : \"HitRatio\",     \"rule\" : {       \"array_hit_ratio\" : [ {         \"and\" : [ {           \"regexp_find\" : [ \"^[a-zA-Z0-9._%-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,4}[ ]*$\", {             \"var\" : \"ArrayElementValue\"           }]         }, {           \"validator\" : [ \"cls.pii::email\", {             \"var\" : \"ArrayElementValue\"           } ]         } ]       }, {         \"var\" : \"column.values\"       } ]     },     \"output\" : true   }, {     \"desc\" : \"命中率必须大于等于 40\",     \"name\" : \"OverallScore\",     \"rule\" : {       \">=\" : [ {         \"var\" : \"$.HitRatio\"       }, 40 ]     },     \"output\" : true   } ] }", "type": "rule"}], "name": "youxiang_new", "description": "", "levelName": null, "id": "2c3601eb-70e1-41e1-99c2-fcf7c935b10e", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.youxiang", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"邮箱（结构化）\"\r\nscores:\r\n- desc: \"匹配列注释，以邮箱结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n    - \"\\u90AE[\\u4EF6\\u7BB1](\\u5730\\u5740)?\\\\s*$\"\r\n    - var: \"column.comment\"\r\n- desc: \"匹配列名为 email\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"^(?i)(email|email[_]?address)$\"\r\n      - var: \"column.name\"\r\n- desc: \"匹配 validator\"\r\n  name: \"ColumnValueValidator\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.FindColumnComment\"\r\n      - 100\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - array_hit_ratio:\r\n          - and:\r\n              - regexp_find:\r\n                  - \"^\\\\s*[a-zA-Z0-9.-]+@[a-zA-Z0-9-]+(\\\\.[a-zA-Z0-9-]+)*\\\\.[a-zA-Z0-9]{2,6}\\\\s*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - validator:\r\n                  - \"cls.pii::email\"\r\n                  - var: \"ArrayElementValue\"\r\n          - var: \"column.values\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n    - '>=':\r\n        - var: \"$.ColumnValueValidator\"\r\n        - 30\r\n    - 100\r\n    - 0", "type": "rule"}], "name": "youxiang", "description": null, "levelName": null, "id": "52ba1f37-6b3d-466a-94b5-b61d6ddca594", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.huanzheID", "builtin": true, "patterns": [{"expr": "(?i)patient.?id", "type": "catalog"}], "name": "huanzheID", "description": null, "levelName": null, "id": "5eb011c9-6394-4e24-86ff-b7ceccd126a0", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.xinyongka", "builtin": true, "patterns": [{"expr": "^[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[ ]*$", "type": "data"}], "name": "xinyongka", "description": null, "levelName": null, "id": "d315a8c7-38a9-4396-8601-bfb539743cc8", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.zhiye", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"职业（结构化）\"\r\nscores:\r\n- desc: \"列注释以  职业、工作 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u804C\\u4E1A\\\\s*$)|(\\u5DE5\\u4F5C\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"数据字典(命中率90%)\"\r\n  name: \"FindDataDict\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - \">=\":\r\n          - array_hit_ratio:\r\n              - \">\":\r\n                - search_in_dictionary:\r\n                    - \"professions\"\r\n                    - var: \"ArrayElementValue\"\r\n                - 0\r\n              - var: \"column.values\"\r\n          - 90\r\n- desc: \"[字段名]正则精确匹配job、work、occupation、profession\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindDataDict\"\r\n      - regexp_find:\r\n          - \"(^(?i)job$)|(^(?i)work$)|(^(?i)occupation$)|(^(?i)profession[sS]?$)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "zhiye", "description": null, "levelName": null, "id": "f7ef611b-a203-4cb2-8c5f-771b051e965b", "category": null}, {"level": null, "dataTag": "1.qianyi<PERSON>lei.c_figure_shifting", "builtin": false, "patterns": [{"expr": "c_figure_shifting", "type": "catalog"}], "name": "c_figure_shifting", "description": "", "levelName": null, "id": "c7f490c5-419a-4743-8684-73c25bc6f9cf", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.riqi", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"日期（结构化）\"\r\nscores:\r\n- desc: \"列注释以 日期、时间 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u65E5\\u671F\\\\s*$)|(\\u65F6\\u95F4\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则匹配date\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(?i)date\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "riqi", "description": null, "levelName": null, "id": "20cd24b5-dd75-463b-ba2a-1957b83994b2", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.yong<PERSON>ing", "builtin": true, "patterns": [{"expr": "(?i)user.?name", "type": "catalog"}], "name": "yonghuming", "description": null, "levelName": null, "id": "627b4eac-b1e5-40f1-a44c-26b5df9d9729", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.zhaopian", "builtin": true, "patterns": [{"expr": "(?i)photo", "type": "catalog"}], "name": "zhao<PERSON>", "description": null, "levelName": null, "id": "1510f191-4236-4cfa-a3c0-6f5dae004455", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.yonghuID", "builtin": true, "patterns": [{"expr": "(?i)user.?id", "type": "catalog"}], "name": "yonghu<PERSON>", "description": null, "levelName": null, "id": "d115700e-c35f-467b-ae81-3729139318ae", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.chengshi", "builtin": true, "patterns": [{"expr": "(?i)city", "type": "catalog"}], "name": "ch<PERSON><PERSON>", "description": null, "levelName": null, "id": "0096a4c6-07a5-480d-881f-************", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.c_shelter_all", "builtin": false, "patterns": [{"expr": "c_shelter_all", "type": "catalog"}], "name": "c_shelter_all", "description": "", "levelName": null, "id": "34d56243-f6cd-492d-99e3-6de07592798d", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.chash", "builtin": false, "patterns": [{"expr": "c_hash", "type": "catalog"}], "name": "chash", "description": "", "levelName": null, "id": "aeaadc3b-cf3f-45e0-89fb-285f078450d1", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.guoji", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"国籍（结构化）\"\r\nscores:\r\n- desc: \"列注释以 国籍 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u56FD\\u7C4D\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则模糊匹配nationality、citizenship\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"((?i)nationality)|((?i)citizenship)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "guoji", "description": null, "levelName": null, "id": "b3e50887-7406-48b5-bbce-b9a6625e361f", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.s<PERSON><PERSON><PERSON>_mask_DW", "builtin": false, "patterns": [{"expr": "login_name_mask", "type": "catalog"}], "name": "shou<PERSON><PERSON>_mask_DW", "description": "", "levelName": null, "id": "71215576-a01d-467e-b353-482508ca7b73", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.cfigurerounding", "builtin": false, "patterns": [{"expr": "c_figure_rounding", "type": "catalog"}], "name": "cfigurerounding", "description": "", "levelName": null, "id": "b023cd21-5862-4136-9df9-beb610ef16fc", "category": null}, {"level": null, "dataTag": "1.q<PERSON><PERSON><PERSON><PERSON><PERSON>.xuexing", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"血型（结构化）\"\r\nscores:\r\n- desc: \"列注释以 血型 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u8840\\u578B\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则模糊匹配blood(允许包含_)type\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"((?i)blood[_]?type)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "xuexing", "description": null, "levelName": null, "id": "91c11406-8280-43f6-9c75-594bd3aaa63b", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.s<PERSON>ji<PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"手机号（中国内地)（结构化）\"\r\nscores:\r\n- desc: \"字段注释\"\r\n  name: \"MobilePhoneColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u624B\\u673A\\u53F7[\\u7801]?\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"字段名匹配 以 id 结尾\"\r\n  name: \"ColumnNameEndWithId\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(?i)id$\"\r\n      - var: \"column.name\"\r\n- desc: \"计算列的手机号正则命中率\"\r\n  name: \"ColumnValueRegexpFind\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.MobilePhoneColumnComment\"\r\n      - 100\r\n      - var: \"$.ColumnNameEndWithId\"\r\n      - 0\r\n      - array_hit_ratio:\r\n          - and:\r\n              - <=:\r\n                  - length:\r\n                      var: \"ArrayElementValue\"\r\n                  - 20\r\n              - regexp_find:\r\n                  - \"^\\\\s*([+]?\\\\s*86|0086)?\\\\s*[-]?\\\\s*((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(16[2|5-7])|(17[0-8])|(18[0-9])|(19[0-3|5-9]))\\\\d{8}\\\\s*$\"\r\n                  - var: \"ArrayElementValue\"\r\n          - var: \"column.values\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    'condition':\r\n      - '>=':\r\n          - var: \"$.ColumnValueRegexpFind\"\r\n          - 30\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "670d4b27-7858-4126-bb81-a381655efa1d", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.c_unified_credit_cod", "builtin": false, "patterns": [{"expr": "c_unified_credit_code", "type": "catalog"}], "name": "c_unified_credit_cod", "description": "", "levelName": null, "id": "7be1914f-58c2-4520-ade0-a1687976cdd9", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.URL", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"URL（结构化）\"\r\nscores:\r\n  - desc: \"列注释精确匹配url\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"^\\\\s*(?i)url\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]精确匹配url\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"^(?i)url$\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 满足正则匹配规则\"\r\n    name: \"FindColumnValue\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - regexp_find:\r\n                  - \"(?i)^(ht|f)tp(s?)\\\\:\\\\/\\\\/[0-9a-zA-Z]([-.\\\\w]*[0-9a-zA-Z])*(:(0-9)*)*(\\\\/?)([a-zA-Z0-9\\\\-\\\\.\\\\?\\\\,\\\\'\\\\/\\\\\\\\\\\\+&amp;%\\\\$#_]*)?[ ]*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnValue\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "URL", "description": "Update on 2024.01.30", "levelName": null, "id": "53c2edbd-5311-49e8-8b48-012d38e8d24c", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.mask_column", "builtin": false, "patterns": [{"expr": "^mask_column$", "type": "catalog"}], "name": "mask_column", "description": "", "levelName": null, "id": "2ba693fc-a640-415b-938c-88d700a461a2", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.guojia", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"国家（结构化）\"\r\nscores:\r\n- desc: \"列注释以 国家 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u56FD\\u5BB6\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"数据字典(命中率90%)\"\r\n  name: \"FindDataDict\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - \">=\":\r\n          - array_hit_ratio:\r\n              - \">\":\r\n                - search_in_dictionary:\r\n                    - \"countries\"\r\n                    - var: \"ArrayElementValue\"\r\n                - 0\r\n              - var: \"column.values\"\r\n          - 90\r\n- desc: \"[字段名]精确匹配country、nation 或 以 country(允许包含_)of、nation(允许包含_)of\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindDataDict\"\r\n      - regexp_find:\r\n          - \"(^(?i)country$)|(^(?i)nation$)|((?i)country[_]?of)|((?i)nation[_]?of)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "guojia", "description": null, "levelName": null, "id": "14420dfc-04be-40af-a038-3f640cb6f2ec", "category": null}, {"level": null, "dataTag": "1.qianyi<PERSON><PERSON>i.c_keep_front_n_rear_", "builtin": false, "patterns": [{"expr": "c_keep_front_n_rear_m", "type": "catalog"}], "name": "c_keep_front_n_rear_", "description": "", "levelName": null, "id": "ac351d68-ac5f-4379-951d-9c9b16cccda3", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.cshelterparatition", "builtin": false, "patterns": [{"expr": "c_shelter_paratition", "type": "catalog"}], "name": "cshelterparatition", "description": "", "levelName": null, "id": "84f7d7f5-a5a7-4af0-943f-5b60074a9a4b", "category": null}, {"level": null, "dataTag": "1.q<PERSON><PERSON><PERSON>lei.IPv6", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"IPV6（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 ipv6 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"(?i)ipv6\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]模糊匹配ipv6\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"(?i)ipv6\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 满足正则匹配规则\"\r\n    name: \"FindColumnValue\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - regexp_find:\r\n                  - \"^(:{2}(/0)?)|((([a-fA-F0-9]{1,4}|):){3,7}([a-fA-F0-9]{1,4}|:)[ ]*)$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnValue\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "IPv6", "description": "Update on 2024.01.22", "levelName": null, "id": "b095d3ef-dd52-40e6-a8fa-dc1638fd7952", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.dianhuahaomazhongguo", "builtin": true, "patterns": [{"expr": "(^(0\\d{2}-\\d{8}(-\\d{1,4})?)|(0\\d{3}-\\d{7,8}(-\\d{1,4})?))|(^((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(18[0,5-9]))\\d{8})[ ]*$", "type": "data"}], "name": "dianhuahaomazhong<PERSON>o", "description": null, "levelName": null, "id": "5202ba39-c01a-4d6b-ad33-c7b43258bb1c", "category": null}], "scanJobHistoryId": 96610, "taskId": 92803}