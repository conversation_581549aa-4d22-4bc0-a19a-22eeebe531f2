{"taskParam": {"dcapProbeId": 1, "dbInstAddressId": 89038, "dbInstAccountId": 88880, "viewDefinitionSampling": false, "sampleCount": 100, "sampleSqlTimeout": 15, "minimumLength": 0, "maximumLength": 256, "hitPercentage": 60, "tableType": "TABLE,VIEW,SYNONYM", "dataType": "TEXT,NUMBER", "excludeSchema": "", "excludeTable": "", "excludeColumn": "", "tableRowCountEnabled": false, "tableRowCount": 100, "tableRowCountUnit": 1, "excludeEmptyValues": false, "emptyPercentage": 90, "scanRange": {"excludedDatabase": [], "excludedSchema": [], "excludedSynonym": [], "excludedTable": [], "excludedView": [], "selectedDatabase": [], "selectedSchema": [], "selectedSynonym": [], "selectedTable": ["joe.field_data", "joe.encrypt_test01"], "selectedView": ["aaaa"]}}, "datasource": {"dbNameType": null, "sourceType": "mysql", "port": "3306", "name": "mysql80", "host": "rm-2zenwhx374y6os41s8o.mysql.rds.aliyuncs.com", "authCfg": {"password": "first@YD", "username": "dcap_test"}, "id": 88951, "extraCfg": "joe"}, "tenantId": 31, "name": "default-db-88951", "policies": [{"level": null, "dataTag": "31.tongyongmobanv2.she<PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"生日（结构化）\"\r\nscores:\r\n- desc: \"列注释 以 生日 、 出生日期 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"([\\u51FA]?\\u751F\\u65E5[\\u671F]?\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则精确匹配 birth、模糊匹配birthday、date(允许包含_)of(允许包含_) birth\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(^(?i)birth$)|((?i)birthday)|((?i)date[_]?of[_]?birth)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "0a79d887-27f8-4d16-8df0-da8bd4b477f6", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.dizhi", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"地址（结构化）\"\r\nscores:\r\n- desc: \"列名匹配 address 或者 addr\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(?i)address|(?i)addr\"\r\n      - var: \"column.name\"\r\n- desc: \"列定义长度 大于 20\"\r\n  name: \"ColumnDefLength\"\r\n  output: true\r\n  rule:\r\n    and:\r\n    - var: \"$.FindColumnName\"\r\n    - '>=':\r\n      - var: \"column.size\"\r\n      - 20\r\n- desc: \"字段注释 包含 住址 or 家庭地址 or 办公地址 or 联系地址\"\r\n  name: \"FieldCommentsContains\"\r\n  output: true\r\n  rule:\r\n    and:\r\n    - var: \"$.ColumnDefLength\"\r\n    - regexp_find:\r\n        - \"\\u4f4f\\u5740|\\u5bb6\\u5ead\\u5730\\u5740|\\u529e\\u516c\\u5730\\u5740|\\u8054\\u7cfb\\u5730\\u5740\"\r\n        - var: \"column.comment\"\r\n- desc: \"NLP 识别地址。以上条件都满足或者是 NLP 识别率达到 30%\"\r\n  name: \"NlpAnalysis\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FieldCommentsContains\"\r\n      - and:\r\n        - var: \"$.ColumnDefLength\"\r\n        - '>=':\r\n            - array_hit_ratio:\r\n                - validator:\r\n                    - \"cls.piilinkable::address\"\r\n                    - var: \"ArrayElementValue\"\r\n                - var: \"column.values\"\r\n            - 30\r\n- desc: \"NLP 如果不满足，继续 字段内容 匹配规则列表\"\r\n  name: \"RegexpListFind\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.NlpAnalysis\"\r\n      - and:\r\n          - var: \"$.ColumnDefLength\"\r\n          - '>=':\r\n              - array_hit_ratio:\r\n                  - or: # 包含 小区|单元|号楼|大厦|家园|新区|村委会|公安局|派出所|街道办|公寓|厂区\r\n                      - regexp_find:\r\n                          - \"\\u5c0f\\u533a|\\u5355\\u5143|\\u53f7\\u697c|\\u5927\\u53a6|\\u5bb6\\u56ed|\\u65b0\\u533a|\\u6751\\u59d4\\u4f1a|\\u516c\\u5b89\\u5c40|\\u6d3e\\u51fa\\u6240|\\u8857\\u9053\\u529e|\\u516c\\u5bd3|\\u5382\\u533a\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 以 3位数字+室or房 结尾\r\n                          - \"\\\\d{1,3}\\\\s*[\\u5BA4\\u623F]$\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 市 and 区 and （1位数字+号），市区两字不可紧相邻\r\n                          - \"\\u5E02(\\\\s*[^\\\\s\\u5E02\\u533A\\\\d])+\\\\s*\\u533A(\\\\s*[^\\\\s\\u5E02\\u533A\\u53F7])+\\\\d*\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 村 and 号，之间有至少一位值\r\n                          - \"\\u6751(\\\\s*[^\\\\s\\u6751\\u53F7])+\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 市 and 村，之间有至少一位值\r\n                          - \"\\u5E02(\\\\s*[^\\\\s\\u5E02\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 区 and 村，之间有至少一位值\r\n                          - \"\\u533A(\\\\s*[^\\\\s\\u533A\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 镇 and 村，之间有至少一位值\r\n                          - \"\\u9547(\\\\s*[^\\\\s\\u9547\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 县 and 村，之间有至少一位值\r\n                          - \"\\u53BF(\\\\s*[^\\\\s\\u53BF\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 巷 and 号，之间有至少一位值\r\n                          - \"\\u5DF7(\\\\s*[^\\\\s\\u5DF7\\u53F7])+\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 包含 任意位数字+栋+任意位数字\r\n                          - \"\\\\d+\\\\s*\\u680B\\\\s*\\\\d+\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 社区 and 号，之间有至少一位值\r\n                          - \"\\u793E\\u533A(\\\\s*[^\\\\s\\u793E\\u533A\\u53F7])+\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 以 局 结尾\r\n                          - \"\\u5C40\\\\s*$\"\r\n                          - var: \"ArrayElementValue\"\r\n                  - var: \"column.values\"\r\n              - 30\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.RegexpListFind\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "dizhi", "description": "Updated on 2024.05.25", "levelName": null, "id": "ed4efdaa-6161-4d3c-9db3-9d657b580ade", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.<PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"邮编（结构化）\"\r\nscores:\r\n- desc: \"列注释以 邮编 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u90AE\\u7F16\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则精确匹配postal、postcode、postalcode、zipcode 模糊匹配 (允许包含_)of\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(^(?i)postal$)|(^(?i)postcode$)|(^(?i)postalcode$)|(^(?i)zipcode$)|((?i)postal[_]?of)|((?i)postcode[_]?of)|((?i)postalcode[_]?of)|((?i)zipcode[_]?of)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "c1162524-141c-42e7-b653-575f1b862044", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.IdentityHK", "builtin": false, "patterns": [{"expr": "^((\\\\s?[A-Za-z])|([A-Za-z]{2}))\\\\d{6}((\\\\([0−9aA]\\\\))|(\\\\([0-9aA]\\\\)))$", "type": "data"}], "name": "IdentityHK", "description": "IdentityHK", "levelName": null, "id": "915bdc2a-91e9-4010-aee3-88daae915e4c", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.MAC", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"MAC（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 mac 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"(?i)mac\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]精确匹配mac\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"^(?i)mac$\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 满足正则匹配规则\"\r\n    name: \"FindColumnValue\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - regexp_find:\r\n                  - \"^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})|([0-9a-fA-F]{4}\\\\\\\\.[0-9a-fA-F]{4}\\\\\\\\.[0-9a-fA-F]{4})[ ]*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnValue\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "MAC", "description": "Updated on 2024.05.25", "levelName": null, "id": "97967a1b-d218-4e1a-81af-5bd708906112", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.SocialCode", "builtin": false, "patterns": [{"expr": "socialcode", "type": "catalog"}], "name": "SocialCode", "description": "SocialCode", "levelName": null, "id": "d5d71edb-2dfd-4c04-95d7-69df2b9af686", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.x<PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"性别（结构化）\"\r\nscores:\r\n- desc: \"列注释匹配以 性别 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u6027\\u522B\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"数据字典(命中率90%)\"\r\n  name: \"FindDataDict\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - \">=\":\r\n          - array_hit_ratio:\r\n              - \">\":\r\n                - search_in_dictionary:\r\n                    - \"gender\"\r\n                    - var: \"ArrayElementValue\"\r\n                - 0\r\n              - var: \"column.values\"\r\n          - 90\r\n- desc: \"[字段名]正则精确匹配gender、sexual、sex\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindDataDict\"\r\n      - regexp_find:\r\n          - \"(^(?i)gender$)|(^(?i)sexual$)|(^(?i)sex$)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "58422a00-e13c-4061-9b09-9465ffd26ece", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.mima", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"密码（结构化）\"\r\nscores:\r\n  - desc: \"列注释匹配以 密码 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"\\u5BC6\\u7801\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]正则精确匹配password、passcode、passphrase\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"(^(?i)password$)|(^(?i)passcode$)|(^(?i)passphrase$)\"\r\n            - var: \"column.name\"\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnName\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "mima", "description": "Updated on 2024.05.25", "levelName": null, "id": "dad95240-2643-494d-a6cd-4fb35873008c", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.xueya", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"血压（结构化）\"\r\nscores:\r\n- desc: \"列注释匹配 以 血压 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u8840\\u538B\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则模糊匹配blood(允许包含_)pressure 且 内容校验在 20-220 之间的整数(命中率90%)\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - and:\r\n        - regexp_find:\r\n          - \"(?i)blood[_]?pressure\"\r\n          - var: \"column.name\"\r\n        - \">=\":\r\n            - array_hit_ratio:\r\n                - and:\r\n                    - \">=\":\r\n                        - var: \"ArrayElementValue\"\r\n                        - 20\r\n                    - \"<=\":\r\n                        - var: \"ArrayElementValue\"\r\n                        - 220\r\n                - var: \"column.values\"\r\n            - 90\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "x<PERSON>ya", "description": "Updated on 2024.05.25", "levelName": null, "id": "394f7c7c-caf4-4d1c-a140-4fda2d36885c", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.IPv4", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"IPV4（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 ip、ipv4 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"((?i)ipv4\\\\s*$)|((?i)ip\\\\s*$)\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]精确匹配ip、ipv4\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"(^(?i)ip$)|(^(?i)ipv4$)\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 满足正则匹配规则\"\r\n    name: \"FindColumnValue\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - regexp_find:\r\n                  - \"^(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\\\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])[ ]*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnValue\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "IPv4", "description": "Updated on 2024.05.25", "levelName": null, "id": "0c574a63-f1b5-4d9c-8048-ea703ec7726c", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.IdentityMacao", "builtin": false, "patterns": [{"expr": "^[1|5|7][0-9]{6}\\\\([0-9Aa]\\\\)", "type": "data"}], "name": "IdentityMacao", "description": "IdentityMacao", "levelName": null, "id": "36ffec9c-aab7-40b2-8211-db12ede2319b", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"身份证（中国内地）（结构化）\"\r\nscores:\r\n- desc: \"内容长度限制必须大于等于 18\"\r\n  name: \"IdCardLength\"\r\n  output: true\r\n  rule:\r\n    \">=\":\r\n      - var: \"column.size\"\r\n      - 18\r\n- desc: \"注释以身份证号结尾\"\r\n  name: \"ColumnComment\"\r\n  output: true\r\n  rule:\r\n    and:\r\n      - var: \"$.IdCardLength\"\r\n      - regexp_find:\r\n          - \"\\u8EAB\\u4EFD\\u8BC1\\u53F7$\"\r\n          - var: \"column.comment\"\r\n- desc: \"如果注释匹配，直接返回，否则计算命中率。\"\r\n  name: \"RegexpFindAndValidator\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.ColumnComment\"\r\n      - 100\r\n      - array_hit_ratio:\r\n          - and:\r\n            - '>=':\r\n                - length:\r\n                  - var: \"ArrayElementValue\"\r\n                - 18\r\n            - regexp_find:\r\n                - \"(([1][1-5])|([2][1-3])|([3][1-7])|([4][1-6])|([5][0-4])|([6][1-5])|([7][1])|([8][1-2]))\\\\d{4}(([1][9]\\\\d{2})|([2]\\\\d{3}))(([0][1-9])|([1][0-2]))(([0][1-9])|([1-2][0-9])|([3][0-1]))\\\\d{3}[0-9xX]\\\\s*\"\r\n                - var: \"ArrayElementValue\"\r\n            - validator:\r\n                - \"cls.pii::idcard\"\r\n                - var: \"ArrayElementValue\"\r\n          - var: \"column.values\"\r\n- desc: \"命中率必须大于等于 30\"\r\n  name: \"CalculateHitRatio\"\r\n  output: true\r\n  rule:\r\n    '>=':\r\n      - var: \"$.RegexpFindAndValidator\"\r\n      - 30\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    'condition':\r\n    - var: \"$.CalculateHitRatio\"\r\n    - 100\r\n    - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "140cb96b-ed94-4b00-853b-a75739fa2a85", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.<PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"宗教（结构化）\"\r\nscores:\r\n- desc: \"列注释以 宗教、信仰 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u5B97\\u6559\\\\s*$)|(\\u4FE1\\u4EF0\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"数据字典(命中率90%)\"\r\n  name: \"FindDataDict\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - \">=\":\r\n          - array_hit_ratio:\r\n              - \">\":\r\n                - search_in_dictionary:\r\n                    - \"religions\"\r\n                    - var: \"ArrayElementValue\"\r\n                - 0\r\n              - var: \"column.values\"\r\n          - 90\r\n- desc: \"[字段名]正则精确匹配 faith 或 精确匹配religion 或 模糊匹配religion(允许包含_)of \"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindDataDict\"\r\n      - regexp_find:\r\n          - \"(^(?i)faith$)|(^(?i)religion$)|((?i)religion[_]?of)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON>jiao", "description": "Updated on 2024.05.25", "levelName": null, "id": "d0ba8970-9473-45c0-8e78-2345d4cc112d", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.xinlv", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"心率（结构化）\"\r\nscores:\r\n  - desc: \"列注释匹配 以 心率 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"\\u5FC3\\u7387\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]正则模糊匹配heart (允许包含_)rate 且 内容校验是在40-180之间的整数(命中率90%)\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - and:\r\n            - regexp_find:\r\n                - \"(?i)heart[_]?rate\"\r\n                - var: \"column.name\"\r\n            - \">=\":\r\n                - array_hit_ratio:\r\n                    - and:\r\n                        - \">=\":\r\n                            - var: \"ArrayElementValue\"\r\n                            - 40\r\n                        - \"<=\":\r\n                            - var: \"ArrayElementValue\"\r\n                            - 180\r\n                    - var: \"column.values\"\r\n                - 90\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnName\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "xinlv", "description": "Updated on 2024.05.25", "levelName": null, "id": "16cd77a1-0e74-4a60-85fe-4d0908c6d292", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"银行卡（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 银行卡号 或 银行账号 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"\\u94F6\\u884C[\\u5361\\u8D26][\\u53F7]?\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"字段名匹配\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"^(?i)bank[_-]?accno$|^(?i)bank[_-]?account$|^(?i)bank[_-]?accno$|^(?i)bank[_-]?card[_-]?no$|^(?i)bank[_-]?card[_-]?number$\"\r\n        - var: \"column.name\"\r\n  - desc: \"正则+校验\"\r\n    name: \"ColumnValuesRegexpAndValidator\"\r\n    output: true\r\n    rule:\r\n      condition:\r\n        - var: \"$.FindColumnComment\"\r\n        - 100\r\n        - var: \"$.FindColumnName\"\r\n        - 100\r\n        - array_hit_ratio:\r\n            - and:\r\n                - regexp_find:\r\n                    - \"^\\\\s*\\\\d{10,20}\\\\s*$\"\r\n                    - var: \"ArrayElementValue\"\r\n                - regexp_find:\r\n                    - \"^\\\\s*(?!(\\\\d)\\\\1{5}).*$\"\r\n                    - var: \"ArrayElementValue\"\r\n                - or:\r\n                    - regexp_find:\r\n                        - \"^5[1-5][0-9]{2}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[ ]*$\"\r\n                        - var: \"ArrayElementValue\"\r\n                    - regexp_find:\r\n                        - \"^[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[ ]*$\"\r\n                        - var: \"ArrayElementValue\"\r\n                    - regexp_find:\r\n                        - \"^62[0-5][0-9]{13,16}[ ]*$\"\r\n                        - var: \"ArrayElementValue\"\r\n                    - regexp_find:\r\n                        - \"^4[0-9]{3}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[ ]*$\"\r\n                        - var: \"ArrayElementValue\"\r\n                - validator:\r\n                    - \"cls.pii::unionpay\"\r\n                    - var: \"ArrayElementValue\"\r\n            - var: \"column.values\"\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - '>=':\r\n            - var: \"$.ColumnValuesRegexpAndValidator\"\r\n            - 60\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "yin<PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "c6c1b731-a813-4402-8617-5e7e4c6ce56d", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.nian<PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"年龄（结构化）\"\r\nscores:\r\n- desc: \"列注释匹配 以 年龄 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u5E74\\u9F84\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"列名精确匹配 age，且值 0-100 之间，90% 命中率\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - and:\r\n        - regexp_find:\r\n          - \"^(?i)age$\"\r\n          - var: \"column.name\"\r\n        - \">=\":\r\n            - array_hit_ratio:\r\n                - and:\r\n                    - \">=\":\r\n                        - var: \"ArrayElementValue\"\r\n                        - 0\r\n                    - \"<=\":\r\n                        - var: \"ArrayElementValue\"\r\n                        - 100\r\n                - var: \"column.values\"\r\n            - 90\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "d137b65b-871c-4c54-8df3-cd7fb870ba71", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"车牌号(大陆)（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 车牌、车牌号 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"\\u8F66\\u724C[\\u53F7]?\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]正则模糊匹配license(允许包含_)plate、plate(允许包含_)number\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"((?i)license[_]?plate)|((?i)plate[_]?number)\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 内置车牌号匹配规则 cls.pii::cardnum (命中率80%)\"\r\n    name: \"FindValidator\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - validator:\r\n                  - \"cls.pii::cardnum\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindValidator\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "ch<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "e599d648-42d5-4d4c-b98e-8e060c9117f8", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.xingming", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"姓名（结构化）\"\r\nscores:\r\n- desc: \"字段名规则列表\"\r\n  name: \"FindColumnNameList\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - regexp_find:\r\n          - \"^(?i)(insured|insured[_-]?name|receiver|receiver[_-]?name|sender|sender[_-]?name|recipient|recipient[-_]?name|full[_-]?name|first[_-]?name|last[_-]?name|real[_-]?name|staff[_-]?name|candidate|cardholder|cardholder[_-]?name|member[_-]?name|payer[_-]?name|payee[_-]?name|purchaser[_-]?name|trustee[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(bank[_-]?account[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(customer[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(holder[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(contact[s]?[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(patient[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(spouse[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(kin[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(student[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"^(?i)(name[_-]?of|name[_-]?on)\"\r\n          - var: \"column.name\"\r\n- desc: \"列名匹配\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"^(?!.*(org|medicine))(.*(name|er|yee))$|^(insured|initial)$\"\r\n      - var: \"column.name\"\r\n- desc: \"字段注释\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    and:\r\n      - var: \"$.FindColumnName\"\r\n      - regexp_find: # （字符串中包含 姓名 或者 账户名称）同时（不能包含 企 或者 公 或者 人 或者 修改 或者 编辑 或者 变更 或者 创建 或者 操作)\r\n          - \"^(?=.*(\\u59D3\\u540D|\\u8D26\\u6237\\u540D\\u79F0))(?!.*(\\u4F01|\\u516C|\\u4EBA|\\u4FEE\\u6539|\\u7F16\\u8F91|\\u53D8\\u66F4|\\u521B\\u5EFA|\\u64CD\\u4F5C)).*$\"\r\n          - var: \"column.comment\"\r\n- desc: \"反向字段注释\"\r\n  name: \"ReverseFindColumnComment\"\r\n  output: true\r\n  rule:\r\n    and:\r\n      - var: \"$.FindColumnName\"\r\n      - regexp_find: # 品名 或 品名称 或 司名称 或 业名称 或 厂名称 或 商名称 或 社名称 或 机构名称 或 种名称 结尾\r\n          - \"(\\u54C1\\u540D|\\u54C1\\u540D\\u79F0|\\u53F8\\u540D\\u79F0|\\u4E1A\\u540D\\u79F0|\\u5382\\u540D\\u79F0|\\u5546\\u540D\\u79F0|\\u793E\\u540D\\u79F0|\\u673A\\u6784\\u540D\\u79F0|\\u79CD\\u540D\\u79F0)$\"\r\n          - var: \"column.comment\"\r\n- desc: \"NLP 识别\"\r\n  name: \"NlpAnalysis\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.FindColumnNameList\"\r\n      - true\r\n      - var: \"$.FindColumnComment\"\r\n      - true\r\n      - var: \"$.ReverseFindColumnComment\"\r\n      - false\r\n      - '>=':\r\n          - array_hit_ratio:\r\n              - and:\r\n                  - <=:\r\n                      - length:\r\n                          var: \"ArrayElementValue\"\r\n                      - 5\r\n                  - validator:\r\n                      - \"cls.pii::name\"\r\n                      - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 30\r\n- desc: \"表名和列名组合规则\"\r\n  name: \"TableAndColumnCombination\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.NlpAnalysis\"\r\n      - and:\r\n          - regexp_find:\r\n              - \"(?i)(erinfo|yeeinfo)$\"\r\n              - var: \"table.name\"\r\n          - regexp_find:\r\n              - \"(?i)(name)\"\r\n              - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n    - var: \"$.TableAndColumnCombination\"\r\n    - 100\r\n    - 0", "type": "rule"}], "name": "xing<PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "d82c318b-870b-4c7c-a46f-372cf567e2ac", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "(?i)(fax|phone|landline)", "type": "catalog"}, {"expr": "^\\s*(86|\\+86|0086)?\\s*-?\\s*(0\\d{2}\\s*-?\\s*\\d{8}(\\s*-?\\s*\\d{1,4})?)\\s*$|^\\s*(0\\d{3}\\s*-?\\s*\\d{7,8}(\\s*-?\\s*\\d{1,4})?)\\s*$", "type": "data"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "090735fb-fdfb-4d3a-92d3-a708a8a207d4", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.weizhi", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"位置（结构化）\"\r\nscores:\r\n- desc: \"列注释以 位置、经度、纬度、经纬、经纬度 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u4F4D\\u7F6E\\\\s*$)|(\\u7ECF[\\u7EAC]?\\u5EA6\\\\s*$)|(\\u7ECF\\u7EAC\\\\s*$)|(\\u7EAC\\u5EA6\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则精确匹配location、latitude、longitude、lng、lat\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(^(?i)location$)|(^(?i)latitude$)|(^(?i)longitude$)|(^(?i)lng$)|(^(?i)lat$)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "we<PERSON>hi", "description": "Updated on 2024.05.25", "levelName": null, "id": "fee344a9-f73a-4970-9b08-6eff3be6758b", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.youxiang", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"邮箱（结构化）\"\r\nscores:\r\n- desc: \"匹配列注释，以邮箱结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n    - \"\\u90AE[\\u4EF6\\u7BB1](\\u5730\\u5740)?\\\\s*$\"\r\n    - var: \"column.comment\"\r\n- desc: \"匹配列名为 email\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"^(?i)(email|email[_]?address)$\"\r\n      - var: \"column.name\"\r\n- desc: \"匹配 validator\"\r\n  name: \"ColumnValueValidator\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.FindColumnComment\"\r\n      - 100\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - array_hit_ratio:\r\n          - and:\r\n              - regexp_find:\r\n                  - \"^\\\\s*[a-zA-Z0-9.-]+@[a-zA-Z0-9-]+(\\\\.[a-zA-Z0-9-]+)*\\\\.[a-zA-Z0-9]{2,6}\\\\s*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - validator:\r\n                  - \"cls.pii::email\"\r\n                  - var: \"ArrayElementValue\"\r\n          - var: \"column.values\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n    - '>=':\r\n        - var: \"$.ColumnValueValidator\"\r\n        - 30\r\n    - 100\r\n    - 0", "type": "rule"}], "name": "youxiang", "description": "Updated on 2024.05.25", "levelName": null, "id": "26396c2b-aaba-4a77-a456-98b637e9d35d", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.z<PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"职业（结构化）\"\r\nscores:\r\n- desc: \"列注释以  职业、工作 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u804C\\u4E1A\\\\s*$)|(\\u5DE5\\u4F5C\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"数据字典(命中率90%)\"\r\n  name: \"FindDataDict\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - \">=\":\r\n          - array_hit_ratio:\r\n              - \">\":\r\n                - search_in_dictionary:\r\n                    - \"professions\"\r\n                    - var: \"ArrayElementValue\"\r\n                - 0\r\n              - var: \"column.values\"\r\n          - 90\r\n- desc: \"[字段名]正则精确匹配job、work、occupation、profession\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindDataDict\"\r\n      - regexp_find:\r\n          - \"(^(?i)job$)|(^(?i)work$)|(^(?i)occupation$)|(^(?i)profession[sS]?$)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "zhiye", "description": "Updated on 2024.05.25", "levelName": null, "id": "6396154a-47bf-4f2b-b3e7-41643bfa74cb", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.riqi", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"日期（结构化）\"\r\nscores:\r\n- desc: \"列注释以 日期、时间 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u65E5\\u671F\\\\s*$)|(\\u65F6\\u95F4\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则匹配date\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(?i)date\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "riqi", "description": "Updated on 2024.05.25", "levelName": null, "id": "9fd452e1-84bb-45e3-8436-a58c634e6a3b", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.guoji", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"国籍（结构化）\"\r\nscores:\r\n- desc: \"列注释以 国籍 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u56FD\\u7C4D\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则模糊匹配nationality、citizenship\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"((?i)nationality)|((?i)citizenship)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "guoji", "description": "Updated on 2024.05.25", "levelName": null, "id": "32393e74-f5d4-462e-97a7-6f4844fa573c", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.xuexing", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"血型（结构化）\"\r\nscores:\r\n- desc: \"列注释以 血型 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u8840\\u578B\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则模糊匹配blood(允许包含_)type\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"((?i)blood[_]?type)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "xuexing", "description": "Updated on 2024.05.25", "levelName": null, "id": "6ca12d1f-3dc9-4be1-aa3f-2bde8fc2f08e", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"手机号（中国内地)（结构化）\"\r\nscores:\r\n- desc: \"字段注释\"\r\n  name: \"MobilePhoneColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u624B\\u673A\\u53F7[\\u7801]?\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"字段名匹配 以 id 结尾\"\r\n  name: \"ColumnNameEndWithId\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(?i)id$\"\r\n      - var: \"column.name\"\r\n- desc: \"计算列的手机号正则命中率\"\r\n  name: \"ColumnValueRegexpFind\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.MobilePhoneColumnComment\"\r\n      - 100\r\n      - var: \"$.ColumnNameEndWithId\"\r\n      - 0\r\n      - array_hit_ratio:\r\n          - and:\r\n              - <=:\r\n                  - length:\r\n                      var: \"ArrayElementValue\"\r\n                  - 20\r\n              - regexp_find:\r\n                  - \"^\\\\s*([+]?\\\\s*86|0086)?\\\\s*[-]?\\\\s*((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(16[2|5-7])|(17[0-8])|(18[0-9])|(19[0-3|5-9]))\\\\d{8}\\\\s*$\"\r\n                  - var: \"ArrayElementValue\"\r\n          - var: \"column.values\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    'condition':\r\n      - '>=':\r\n          - var: \"$.ColumnValueRegexpFind\"\r\n          - 30\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "a94e26b0-a50f-4a36-a5a3-28fd9dfeca98", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.URL", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"URL（结构化）\"\r\nscores:\r\n  - desc: \"列注释精确匹配url\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"^\\\\s*(?i)url\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]精确匹配url\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"^(?i)url$\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 满足正则匹配规则\"\r\n    name: \"FindColumnValue\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - regexp_find:\r\n                  - \"(?i)^(ht|f)tp(s?)\\\\:\\\\/\\\\/[0-9a-zA-Z]([-.\\\\w]*[0-9a-zA-Z])*(:(0-9)*)*(\\\\/?)([a-zA-Z0-9\\\\-\\\\.\\\\?\\\\,\\\\'\\\\/\\\\\\\\\\\\+&amp;%\\\\$#_]*)?[ ]*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnValue\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "URL", "description": "Updated on 2024.05.25", "levelName": null, "id": "1beb8b37-a0be-467e-8313-8cd374002f61", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.guojia", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"国家（结构化）\"\r\nscores:\r\n- desc: \"列注释以 国家 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u56FD\\u5BB6\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"数据字典(命中率90%)\"\r\n  name: \"FindDataDict\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - \">=\":\r\n          - array_hit_ratio:\r\n              - \">\":\r\n                - search_in_dictionary:\r\n                    - \"countries\"\r\n                    - var: \"ArrayElementValue\"\r\n                - 0\r\n              - var: \"column.values\"\r\n          - 90\r\n- desc: \"[字段名]精确匹配country、nation 或 以 country(允许包含_)of、nation(允许包含_)of\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindDataDict\"\r\n      - regexp_find:\r\n          - \"(^(?i)country$)|(^(?i)nation$)|((?i)country[_]?of)|((?i)nation[_]?of)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "guojia", "description": "Updated on 2024.05.25", "levelName": null, "id": "15d99b38-c9de-4aa4-85a7-166f1f789634", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.IdentityTai", "builtin": false, "patterns": [{"expr": "^[A-KM-QT-XZ]{1}[0-9]{9}$", "type": "data"}], "name": "IdentityTai", "description": "IdentityTai", "levelName": null, "id": "db3e1ca5-a5f4-4d32-bc38-4a549abe0993", "category": null}, {"level": null, "dataTag": "31.tongyongmobanv2.IPv6", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"IPV6（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 ipv6 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"(?i)ipv6\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]模糊匹配ipv6\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"(?i)ipv6\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 满足正则匹配规则\"\r\n    name: \"FindColumnValue\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - regexp_find:\r\n                  - \"^(:{2}(/0)?)|((([a-fA-F0-9]{1,4}|):){3,7}([a-fA-F0-9]{1,4}|:)[ ]*)$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnValue\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "IPv6", "description": "Updated on 2024.05.25", "levelName": null, "id": "e02ff752-b284-4299-a962-c7ec213bc81d", "category": null}], "scanJobHistoryId": 93953, "taskId": 88034}