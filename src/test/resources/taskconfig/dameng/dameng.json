{"taskParam": {"dcapProbeId": 1, "dbInstAddressId": 103981, "dbInstAccountId": 102991, "viewDefinitionSampling": false, "sampleCount": 100, "sampleSqlTimeout": 15, "minimumLength": 10, "maximumLength": 100, "hitPercentage": 60, "tableType": "TABLE", "dataType": "TEXT,NUMBER", "excludeSchema": "", "excludeTable": "", "excludeColumn": "", "tableRowCountEnabled": false, "tableRowCount": 100, "tableRowCountUnit": 1, "excludeEmptyValues": false, "emptyPercentage": 90, "incrementalEvaluationEnabled": false, "samplingReverseOrder": false, "permitsPerSecond": 1000, "scanRange": {"excludedDatabase": [], "excludedSchema": [], "excludedSynonym": [], "excludedTable": [], "excludedView": [], "selectedDatabase": [], "selectedSchema": [], "selectedSynonym": [], "selectedTable": [], "selectedView": []}}, "datasource": {"dbNameType": 1, "sourceType": "dameng", "port": "30102", "encryptionSwitch": 1, "name": "liuhan测试达梦8", "host": "app-alpha.yuandiansec.net", "authCfg": {"password": "first@YD", "username": "SYSDBA"}, "id": 103094, "extraCfg": ""}, "tenantId": 1, "name": "default-db-103094", "policies": [{"level": null, "dataTag": "1.tttt.fabaof<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "FBFMC", "type": "catalog"}], "name": "fabaofangmingcheng", "description": "", "levelName": null, "id": "dc51a04f-7899-48ce-8892-c58ec3f73ca6", "category": null}, {"level": null, "dataTag": "1.tttt.ji<PERSON><PERSON><PERSON><PERSON>u", "builtin": false, "patterns": [{"expr": "jfxsq", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "8170be31-0758-41f6-8653-afe25ec9eb65", "category": null}, {"level": null, "dataTag": "1.tttt.biyexuexiao", "builtin": false, "patterns": [{"expr": "byxx", "type": "catalog"}], "name": "biyexuexia<PERSON>", "description": "", "levelName": null, "id": "8c969520-f7cc-4840-9afc-033a6b8338f5", "category": null}, {"level": null, "dataTag": "1.tttt.quanzhengyinshuaxuli", "builtin": false, "patterns": [{"expr": "QZYSXLH", "type": "catalog"}], "name": "quanzhengyinshuaxuli", "description": "", "levelName": null, "id": "f367b559-3200-47a4-b75b-b591dae7eb98", "category": null}, {"level": null, "dataTag": "1.tttt.s<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "sjh", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "6a2aa437-2333-400d-9747-006c773f5e23", "category": null}, {"level": null, "dataTag": "1.tttt.re<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "RYBM", "type": "catalog"}], "name": "re<PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "56ea9c7a-2bc0-4548-a536-9f02eaaf5e21", "category": null}, {"level": null, "dataTag": "1.tttt.jingyingzheyidongdia", "builtin": false, "patterns": [{"expr": "JYZYDDH", "type": "catalog"}], "name": "jingyingzheyidongdia", "description": "", "levelName": null, "id": "7ea74ece-381a-43a6-b461-ce9e6ef839c9", "category": null}, {"level": null, "dataTag": "1.tttt.huk<PERSON>izhimenpaihaom", "builtin": false, "patterns": [{"expr": "HJDZMPHM", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ao<PERSON>", "description": "", "levelName": null, "id": "40d00d22-3d62-4ec2-abde-030cdeb3d532", "category": null}, {"level": null, "dataTag": "1.tttt.gong<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "GZDWDZC", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "f48eda7a-01ad-4613-aaee-61e3f02126cc", "category": null}, {"level": null, "dataTag": "1.tttt.gongzuodanwei", "builtin": false, "patterns": [{"expr": "GZDW", "type": "catalog"}], "name": "gongzuodanwei", "description": "", "levelName": null, "id": "83ab8797-50ce-4d3e-ba70-adec5814af43", "category": null}, {"level": null, "dataTag": "1.tttt.quan<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "QLRBH", "type": "catalog"}], "name": "quanlirenbianhao", "description": "", "levelName": null, "id": "0c55d07d-ff8c-421e-94f0-4ea9b9e59c9a", "category": null}, {"level": null, "dataTag": "1.tttt.hu<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "HJH", "type": "catalog"}], "name": "hu<PERSON><PERSON>", "description": "", "levelName": null, "id": "92077174-5df7-4d4d-9faa-df948d11a7a5", "category": null}, {"level": null, "dataTag": "1.tttt.s<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "SLBH", "type": "catalog"}], "name": "shou<PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "7004241f-5b64-4d4a-b145-bbf39c29a4f8", "category": null}, {"level": null, "dataTag": "1.tttt.she<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "sfzh", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "4f669149-1ff2-44d7-8750-aee85d8a8f73", "category": null}, {"level": null, "dataTag": "1.tttt.she<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "IDcard", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "d55edf0c-330b-4ea3-bdf9-d89263c375f5", "category": null}, {"level": null, "dataTag": "1.tttt.z<PERSON>en<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "NAME", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "feecc958-e29d-4372-9075-9a9bf583beca", "category": null}, {"level": null, "dataTag": "1.tttt.yuan<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "YNZLKH", "type": "catalog"}], "name": "yuan<PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "a77d12db-773a-493d-b7b5-1c07c3799f16", "category": null}, {"level": null, "dataTag": "1.tttt.huzhu<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "hzxm", "type": "catalog"}], "name": "huz<PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "292863f4-0ba4-433e-abc5-10f58e808990", "category": null}, {"level": null, "dataTag": "1.tttt.sheq<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "SQMC", "type": "catalog"}], "name": "shequmingcheng", "description": "", "levelName": null, "id": "9220f7fb-21cb-4178-ac8a-eba8c02695ae", "category": null}, {"level": null, "dataTag": "1.tttt.z<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "ZJH", "type": "catalog"}], "name": "<PERSON>hengjianhao", "description": "", "levelName": null, "id": "7ea2107a-7c2d-4c4e-8df8-3b749d088b01", "category": null}, {"level": null, "dataTag": "1.tttt.dizhi", "builtin": false, "patterns": [{"expr": "dz", "type": "catalog"}], "name": "dizhi", "description": "", "levelName": null, "id": "dd7922cb-a365-491b-91fa-769c8bb0657a", "category": null}, {"level": null, "dataTag": "1.tttt.he<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "agree_name", "type": "catalog"}], "name": "hetongmingcheng", "description": "", "levelName": null, "id": "4903388a-3039-4cb3-97dd-5bc911828d6b", "category": null}, {"level": null, "dataTag": "1.tttt.RHDxuexingdaima", "builtin": false, "patterns": [{"expr": "RH_D_XXDM", "type": "catalog"}], "name": "RHDxuexingdaima", "description": "", "levelName": null, "id": "a94c1abe-9c00-4004-abaf-84b4be7f461d", "category": null}, {"level": null, "dataTag": "1.tttt.zu<PERSON>uo", "builtin": false, "patterns": [{"expr": "ZL", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "31215da3-3bbd-4bea-b3a2-2eec17c72cf1", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "XZZSZ", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "be1475b1-05e7-4dcb-bd87-42db0dbba46e", "category": null}, {"level": null, "dataTag": "1.tttt.chizhen<PERSON>cheng", "builtin": false, "patterns": [{"expr": "ISCZRMC", "type": "catalog"}], "name": "chizhengrenmingcheng", "description": "", "levelName": null, "id": "1ea8a383-fe37-48ee-b48f-ee74291be51b", "category": null}, {"level": null, "dataTag": "1.tttt.gong<PERSON><PERSON>fen<PERSON>", "builtin": false, "patterns": [{"expr": "AAC002", "type": "catalog"}], "name": "gong<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "18d07eb7-bcf1-455e-8615-d3ebe019b5c1", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "SBKH", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "b01cfffc-373e-4db8-80b5-f851e90edd94", "category": null}, {"level": null, "dataTag": "1.tttt.ka<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "kfgsmc", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>ingcheng", "description": "", "levelName": null, "id": "bc879122-59b7-46e4-99b3-baa160a377aa", "category": null}, {"level": null, "dataTag": "1.tttt.ban<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "bjmc", "type": "catalog"}], "name": "banjimingcheng", "description": "", "levelName": null, "id": "094cccda-6fb2-4f85-abd4-70bd484adcbe", "category": null}, {"level": null, "dataTag": "1.tttt.x<PERSON><PERSON><PERSON>zhixiangzhenj", "builtin": false, "patterns": [{"expr": "XZZXZ", "type": "catalog"}], "name": "xianzhuzhixiangzhenj", "description": "", "levelName": null, "id": "8218ba4d-2ce6-4216-9bb0-c73051189635", "category": null}, {"level": null, "dataTag": "1.tttt.xian<PERSON>", "builtin": false, "patterns": [{"expr": "address", "type": "catalog"}], "name": "xiangmuxiangxidizhi", "description": "", "levelName": null, "id": "af078557-61d6-43c4-bdb2-a2d35096047a", "category": null}, {"level": null, "dataTag": "1.tttt.bin<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "XM", "type": "catalog"}], "name": "bingrenxingming", "description": "", "levelName": null, "id": "36f0f885-8b04-40ca-bdb0-0cfa9d8c6274", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "JZLSH", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "942fb415-d362-4b94-b940-9c687d745d6e", "category": null}, {"level": null, "dataTag": "1.tttt.huk<PERSON>", "builtin": false, "patterns": [{"expr": "hkxz", "type": "catalog"}], "name": "huk<PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "e14e9712-449d-49f4-a0e3-be9df594a891", "category": null}, {"level": null, "dataTag": "1.tttt.jing<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "JYCS", "type": "catalog"}], "name": "jing<PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "613ff123-4119-43c6-b68e-12549b5b98f1", "category": null}, {"level": null, "dataTag": "1.tttt.yuanzhangxingming", "builtin": false, "patterns": [{"expr": "NAME", "type": "catalog"}], "name": "yuanzhangxingming", "description": "", "levelName": null, "id": "e1fe8aa1-da81-4cf4-96c4-9365ddb87d58", "category": null}, {"level": null, "dataTag": "1.tttt.su<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "prj_id", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "774bfaa6-938c-4334-a3c0-e04c184c92af", "category": null}, {"level": null, "dataTag": "1.tttt.zhen<PERSON>", "builtin": false, "patterns": [{"expr": "BDCQZH_NO", "type": "catalog"}], "name": "zhengbian<PERSON>", "description": "", "levelName": null, "id": "577b81f5-6ccc-43bd-b0ac-7bbf6eb92a48", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "xxbsm", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "35aaa021-ef17-4034-ae6d-535ffa392cc8", "category": null}, {"level": null, "dataTag": "1.tttt.gongzuodanweidizhi", "builtin": false, "patterns": [{"expr": "GZDWDZ", "type": "catalog"}], "name": "gongzuodanweidizhi", "description": "", "levelName": null, "id": "9d383410-2b18-42be-88b3-53e60a97649a", "category": null}, {"level": null, "dataTag": "1.tttt.xianz<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "XZZ", "type": "catalog"}], "name": "x<PERSON>zhu<PERSON>", "description": "", "levelName": null, "id": "c5a5aaae-89d0-4a3a-873c-1c281d891382", "category": null}, {"level": null, "dataTag": "1.tttt.y<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "ysxkzh", "type": "catalog"}], "name": "y<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "3c38bf0a-f0f7-4b79-9b79-9451105e0237", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "legal_name", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "0d505530-32e2-44ac-8fc9-161a965704cb", "category": null}, {"level": null, "dataTag": "1.tttt.jiankan<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "JKKH", "type": "catalog"}], "name": "jiankangkahao", "description": "", "levelName": null, "id": "c5774bee-4aae-4393-8789-6bea8c1bb746", "category": null}, {"level": null, "dataTag": "1.tttt.cun", "builtin": false, "patterns": [{"expr": "VILLAGE", "type": "catalog"}], "name": "cun", "description": "", "levelName": null, "id": "02be49ac-6af6-4f63-8d04-abbd185585e6", "category": null}, {"level": null, "dataTag": "1.tttt.yidongdian<PERSON>", "builtin": false, "patterns": [{"expr": "MOBILEPHONE", "type": "catalog"}], "name": "yidongdianhua", "description": "", "levelName": null, "id": "acea9fe7-4e72-46c5-8f23-bedb8befa44b", "category": null}, {"level": null, "dataTag": "1.tttt.x<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "xxmc", "type": "catalog"}], "name": "xuexiaomingcheng", "description": "", "levelName": null, "id": "edc426d0-19a0-45e3-9fd2-3c26af0a42a3", "category": null}, {"level": null, "dataTag": "1.tttt.xing<PERSON>", "builtin": false, "patterns": [{"expr": "xb", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "2f5d8b30-6e6e-4d22-b34d-e83a3094411a", "category": null}, {"level": null, "dataTag": "1.tttt.ton<PERSON><PERSON><PERSON><PERSON><PERSON>d", "builtin": false, "patterns": [{"expr": "uniform_code", "type": "catalog"}], "name": "ton<PERSON><PERSON><PERSON><PERSON>xinyon<PERSON>d", "description": "", "levelName": null, "id": "a6d894d2-68b1-40df-97dd-d83978009125", "category": null}, {"level": null, "dataTag": "1.tttt.lianxirenyuhuanzhede", "builtin": false, "patterns": [{"expr": "LXRYHZGXMC", "type": "catalog"}], "name": "lianxirenyuhuanzhede", "description": "", "levelName": null, "id": "80cb9679-9341-4b11-b438-c1e4022c4028", "category": null}, {"level": null, "dataTag": "1.tttt.h<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "HKDZS", "type": "catalog"}], "name": "h<PERSON>oudizhisheng<PERSON>zhi", "description": "", "levelName": null, "id": "e1635cda-9ee4-4b28-a963-80d83edd3990", "category": null}, {"level": null, "dataTag": "1.tttt.mingcheng", "builtin": false, "patterns": [{"expr": "MC", "type": "catalog"}], "name": "mingcheng", "description": "", "levelName": null, "id": "5637906f-0b16-4490-9c18-d3e4e4d115a4", "category": null}, {"level": null, "dataTag": "1.tttt.chush<PERSON>", "builtin": false, "patterns": [{"expr": "CSDS", "type": "catalog"}], "name": "chushengdishengzizhi", "description": "", "levelName": null, "id": "28bad85a-453f-48c4-89fa-6a29139ee827", "category": null}, {"level": null, "dataTag": "1.tttt.z<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "ZS", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "6918950e-3ae1-4492-a9a5-43aae76ab70c", "category": null}, {"level": null, "dataTag": "1.tttt.yiliaojigoumingcheng", "builtin": false, "patterns": [{"expr": "ms_name", "type": "catalog"}], "name": "yiliaojigoumingcheng", "description": "", "levelName": null, "id": "21f98384-6847-473e-a861-f3260b785de6", "category": null}, {"level": null, "dataTag": "1.tttt.gongzuodanweixiangxi", "builtin": false, "patterns": [{"expr": "GZDWXXDZ", "type": "catalog"}], "name": "gongzuodanweixiangxi", "description": "", "levelName": null, "id": "e19276bc-96b4-470b-ab60-1f8867346bed", "category": null}, {"level": null, "dataTag": "1.tttt.lianxirendizhishengz", "builtin": false, "patterns": [{"expr": "LXRDZS", "type": "catalog"}], "name": "lianxirendizhishengz", "description": "", "levelName": null, "id": "08abdaa6-c312-48cd-b528-cd60c72c457b", "category": null}, {"level": null, "dataTag": "1.tttt.he<PERSON>", "builtin": false, "patterns": [{"expr": "agree_code", "type": "catalog"}], "name": "he<PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "e6d4c078-493b-4754-a147-516e986072b3", "category": null}, {"level": null, "dataTag": "1.tttt.huk<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "AAC010", "type": "catalog"}], "name": "hukousuozaidizhi", "description": "", "levelName": null, "id": "1e21e71c-535c-41fc-953f-a57877c11d45", "category": null}, {"level": null, "dataTag": "1.tttt.ji<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "JYZZJHM", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "adb35348-ad33-4db8-99a5-f3e5b41c1847", "category": null}, {"level": null, "dataTag": "1.tttt.canbaoxianshiqu", "builtin": false, "patterns": [{"expr": "cbxsq", "type": "catalog"}], "name": "canbaoxia<PERSON><PERSON>q<PERSON>", "description": "", "levelName": null, "id": "98eb2b47-2393-4e32-8f0b-ac501b923f8c", "category": null}, {"level": null, "dataTag": "1.tttt.yi<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "YWRMC", "type": "catalog"}], "name": "yi<PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "82e6c7b2-585e-440d-83d0-386e0edeb26c", "category": null}, {"level": null, "dataTag": "1.tttt.shiz<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "SZXM", "type": "catalog"}], "name": "shiz<PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "a6ec540e-7991-4f29-a877-9b7f8e5f746c", "category": null}, {"level": null, "dataTag": "1.tttt.hunyinzhuangkuangmin", "builtin": false, "patterns": [{"expr": "HYZKMC", "type": "catalog"}], "name": "hunyinzhuangkuangmin", "description": "", "levelName": null, "id": "65ea7206-d637-4b25-98c4-caa41c69538e", "category": null}, {"level": null, "dataTag": "1.tttt.x<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "XZZXQ_XZQHDM", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON>hu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "5abd7207-ae7c-4fcb-840b-4a48864977a5", "category": null}, {"level": null, "dataTag": "1.tttt.lianxirendizhixiangz", "builtin": false, "patterns": [{"expr": "LXRDZXZ", "type": "catalog"}], "name": "lianxirendizhixiangz", "description": "", "levelName": null, "id": "ef207657-0717-4a68-ac74-d9dbcbbd835f", "category": null}, {"level": null, "dataTag": "1.tttt.z<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "BDCQZH_NO", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "da62b652-3dfe-41c3-b573-20bd8888cd01", "category": null}, {"level": null, "dataTag": "1.tttt.h<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "HZDHHM", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "e6fde868-086a-46e0-bff5-e4f6597ad3aa", "category": null}, {"level": null, "dataTag": "1.tttt.zhuguanjiaoyuju", "builtin": false, "patterns": [{"expr": "zgjyj", "type": "catalog"}], "name": "zhuguanjiaoyuju", "description": "", "levelName": null, "id": "04b91471-f4e8-4ce1-9af4-cc1d8f22c1c0", "category": null}, {"level": null, "dataTag": "1.tttt.louceng", "builtin": false, "patterns": [{"expr": "lc", "type": "catalog"}], "name": "louceng", "description": "", "levelName": null, "id": "78f41e23-52bb-4711-83c6-a5ada2e79e26", "category": null}, {"level": null, "dataTag": "1.tttt.gongzuodanweimingche", "builtin": false, "patterns": [{"expr": "GZDWMC", "type": "catalog"}], "name": "gongzuodanweimingche", "description": "", "levelName": null, "id": "7274cbfe-60d9-4f79-9842-c33ba6dab750", "category": null}, {"level": null, "dataTag": "1.tttt.yiwurenleixing", "builtin": false, "patterns": [{"expr": "YWRLX", "type": "catalog"}], "name": "yiwurenleixing", "description": "", "levelName": null, "id": "05cc424f-30ea-423c-b3f2-28584ab801b5", "category": null}, {"level": null, "dataTag": "1.tttt.tongy<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "TYXYDM", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "00bb3d51-40ee-48a5-82bb-c23ead1fc5a7", "category": null}, {"level": null, "dataTag": "1.tttt.di<PERSON><PERSON><PERSON><PERSON><PERSON>den", "builtin": false, "patterns": [{"expr": "DZC", "type": "catalog"}], "name": "dizhi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "e0b8dbb8-f7ca-4cb3-bdc2-3f2db6726823", "category": null}, {"level": null, "dataTag": "1.tttt.lianluoyuandianhua", "builtin": false, "patterns": [{"expr": "LLYDH", "type": "catalog"}], "name": "lianluoyuandianhua", "description": "", "levelName": null, "id": "971d5a74-ec92-4a9f-a9ff-c070ba708126", "category": null}, {"level": null, "dataTag": "1.tttt.butieren<PERSON><PERSON><PERSON><PERSON>f", "builtin": false, "patterns": [{"expr": "btrzjh", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "05e1cc19-d5ab-492b-b68d-1003728468cb", "category": null}, {"level": null, "dataTag": "1.tttt.qiyelianxidian<PERSON>", "builtin": false, "patterns": [{"expr": "QYLXDH", "type": "catalog"}], "name": "qiyelianxidianhua", "description": "", "levelName": null, "id": "1070b148-06cb-4d09-8a42-a50c885e28fd", "category": null}, {"level": null, "dataTag": "1.tttt.ka<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "gys_bank_number", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "ad8d1365-529f-4955-b394-eb96b7b76d69", "category": null}, {"level": null, "dataTag": "1.tttt.den<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "DJBH", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "10ba0fe7-ebcc-42cf-992d-35bb9bfb2e77", "category": null}, {"level": null, "dataTag": "1.tttt.canbaoxiangzhenjieda", "builtin": false, "patterns": [{"expr": "cbxzjd", "type": "catalog"}], "name": "canbaoxiangzhenjieda", "description": "", "levelName": null, "id": "8adceaac-4c94-4d00-85dd-41a20e6a43d8", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "ZYLSH", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "25634b41-c202-4530-8a63-37e98d881c7b", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "MZLSH", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "b46191af-5c4b-4239-a903-a4897a5ce248", "category": null}, {"level": null, "dataTag": "1.tttt.xian<PERSON>hu<PERSON>sheng<PERSON>zhi", "builtin": false, "patterns": [{"expr": "XZZS", "type": "catalog"}], "name": "xianzhuzhishengzizhi", "description": "", "levelName": null, "id": "fc3259e4-0037-4bc7-8aec-c288b0e3227d", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "ID_CERT_NO", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "60327122-b68c-43eb-a486-505f92d0ee84", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "btrmc", "type": "catalog"}], "name": "but<PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "99927100-94ca-4272-9e3c-dcbababdecc6", "category": null}, {"level": null, "dataTag": "1.tttt.xian<PERSON>", "builtin": false, "patterns": [{"expr": "XZZDH", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "b794111c-3f73-406b-b1bf-905ac7e28b7d", "category": null}, {"level": null, "dataTag": "1.tttt.lianxirendizhicunjie", "builtin": false, "patterns": [{"expr": "LXRDZC", "type": "catalog"}], "name": "lianxirendizhicunjie", "description": "", "levelName": null, "id": "8cd49186-5a18-4e56-b13d-bc4634e3efcc", "category": null}, {"level": null, "dataTag": "1.tttt.b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "BDCQZH", "type": "catalog"}], "name": "budong<PERSON><PERSON>nzhen<PERSON>", "description": "", "levelName": null, "id": "bccd8d5a-6c94-47b7-be55-8cefbdefa9e2", "category": null}, {"level": null, "dataTag": "1.tttt.budongchanID", "builtin": false, "patterns": [{"expr": "BDCID", "type": "catalog"}], "name": "budongchanID", "description": "", "levelName": null, "id": "37545d1c-6076-4bb9-86aa-cf1122f8cb3b", "category": null}, {"level": null, "dataTag": "1.tttt.xian<PERSON>", "builtin": false, "patterns": [{"expr": "prj_name", "type": "catalog"}], "name": "xiang<PERSON>ingcheng", "description": "", "levelName": null, "id": "e0ef5bdd-4d58-4616-b779-cce61390b431", "category": null}, {"level": null, "dataTag": "1.tttt.gudong<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "GDXM", "type": "catalog"}], "name": "gudongxingming", "description": "", "levelName": null, "id": "c0a7d19d-a5b5-4871-a06c-5488e266b455", "category": null}, {"level": null, "dataTag": "1.tttt.lianxirendizhimenpai", "builtin": false, "patterns": [{"expr": "LXRDZMPHM", "type": "catalog"}], "name": "lianxirendizhimenpai", "description": "", "levelName": null, "id": "147bf19f-4318-4350-b7eb-3ce0ff7bc4de", "category": null}, {"level": null, "dataTag": "1.tttt.ji<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "JGDZ", "type": "catalog"}], "name": "jigo<PERSON><PERSON>", "description": "", "levelName": null, "id": "0e9a8f75-7bbc-48da-bd1e-3130fd0466f6", "category": null}, {"level": null, "dataTag": "1.tttt.guotubudongchandengj", "builtin": false, "patterns": [{"expr": "real_estate_num", "type": "catalog"}], "name": "guotubudongchandengj", "description": "", "levelName": null, "id": "4c133d97-3a4c-4eb6-9ff4-5001c67f2a11", "category": null}, {"level": null, "dataTag": "1.tttt.gongzuodanweidizhime", "builtin": false, "patterns": [{"expr": "GZDWDZMPHM", "type": "catalog"}], "name": "gongzuodanweidizhime", "description": "", "levelName": null, "id": "ecdc506b-9a6c-418d-8fc5-8d2995c52787", "category": null}, {"level": null, "dataTag": "1.tttt.shoudaobutienashuire", "builtin": false, "patterns": [{"expr": "sdbtnsrmc", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "46f9e4b9-8f68-41d4-af84-6886609ce6c0", "category": null}, {"level": null, "dataTag": "1.tttt.su<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "SZC", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "5756ed4d-bc44-48fa-a9ba-bae8689f7bc2", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "fddbrxm", "type": "catalog"}], "name": "fading<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "b73938c5-96e2-40b2-a990-99252f90f648", "category": null}, {"level": null, "dataTag": "1.tttt.qiye<PERSON>", "builtin": false, "patterns": [{"expr": "QYMC", "type": "catalog"}], "name": "qiye<PERSON><PERSON>", "description": "", "levelName": null, "id": "b60a3780-d47c-4ddf-aba9-1d5cfa8d4067", "category": null}, {"level": null, "dataTag": "1.tttt.fangh<PERSON>", "builtin": false, "patterns": [{"expr": "FH", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "4ab0b7be-6a37-4670-b5c5-7253e16f81e6", "category": null}, {"level": null, "dataTag": "1.tttt.gongzuodanweidianhua", "builtin": false, "patterns": [{"expr": "GZDWDH", "type": "catalog"}], "name": "gongzuodanweidianhua", "description": "", "levelName": null, "id": "964a589b-6f62-436b-b948-97ced8245e0b", "category": null}, {"level": null, "dataTag": "1.tttt.zongcengshu", "builtin": false, "patterns": [{"expr": "ZCS", "type": "catalog"}], "name": "zongcengshu", "description": "", "levelName": null, "id": "238bb10d-f76a-49d5-a57e-2dcb9419d013", "category": null}, {"level": null, "dataTag": "1.tttt.lian<PERSON><PERSON><PERSON>hi", "builtin": false, "patterns": [{"expr": "LXFS", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "36982ae2-767c-419c-b6ca-772c6c545632", "category": null}, {"level": null, "dataTag": "1.tttt.fang<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "FWZL", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "a45a8230-d3f0-43a9-b306-a3957408b2df", "category": null}, {"level": null, "dataTag": "1.tttt.tudishiyongquanren", "builtin": false, "patterns": [{"expr": "TDSYQR", "type": "catalog"}], "name": "tudishiyongquanren", "description": "", "levelName": null, "id": "e11dbca1-aad0-44b9-9ade-a02b64102dd9", "category": null}, {"level": null, "dataTag": "1.tttt.ye<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "YWH", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "df680577-66e7-4230-a644-25c7bde08905", "category": null}, {"level": null, "dataTag": "1.tttt.juzhudixianzhuzhi", "builtin": false, "patterns": [{"expr": "JZD", "type": "catalog"}], "name": "juzhu<PERSON>xianz<PERSON><PERSON>", "description": "", "levelName": null, "id": "abb9efe3-6325-4f1b-8302-cdcebbe4fbd4", "category": null}, {"level": null, "dataTag": "1.tttt.di<PERSON><PERSON>ngzizhiquz<PERSON>", "builtin": false, "patterns": [{"expr": "DZS", "type": "catalog"}], "name": "dizhishengzizhiquzhi", "description": "", "levelName": null, "id": "05c671b9-f4fc-4887-b510-bc0c8d07378c", "category": null}, {"level": null, "dataTag": "1.tttt.lian<PERSON>oyuan<PERSON>", "builtin": false, "patterns": [{"expr": "LLYXM", "type": "catalog"}], "name": "lian<PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "84523bdf-f202-49d1-938a-3dd765f52326", "category": null}, {"level": null, "dataTag": "1.tttt.juzhu<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "JZDZ", "type": "catalog"}], "name": "juzhu<PERSON>zhi", "description": "", "levelName": null, "id": "3cfed465-82dd-4e84-9275-ef7d1fcd1ca7", "category": null}, {"level": null, "dataTag": "1.tttt.ch<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "cs_date", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "fe6badd2-0880-4e8e-94cf-293dde1c6e58", "category": null}, {"level": null, "dataTag": "1.tttt.h<PERSON><PERSON><PERSON>j", "builtin": false, "patterns": [{"expr": "HJDZXZ", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>xiangzhenj", "description": "", "levelName": null, "id": "c9f95a4e-f685-4247-8631-f3ab33781e14", "category": null}, {"level": null, "dataTag": "1.tttt.tong<PERSON>", "builtin": false, "patterns": [{"expr": "CONTACT_ADDRESS", "type": "catalog"}], "name": "tongxindizhi", "description": "", "levelName": null, "id": "c3cc4523-5014-4852-8b3b-414199a8f6cf", "category": null}, {"level": null, "dataTag": "1.tttt.gongzuodanweidizhixi", "builtin": false, "patterns": [{"expr": "GZDWDZXQ", "type": "catalog"}], "name": "gongzuodanweidizhixi", "description": "", "levelName": null, "id": "ef719623-38fc-44a7-84ac-7560630c3475", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "HKDZSZ", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "926c04d1-1bb8-4cc0-bb91-18018955c207", "category": null}, {"level": null, "dataTag": "1.tttt.xue<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "XXMC", "type": "catalog"}], "name": "xue<PERSON><PERSON>", "description": "", "levelName": null, "id": "ea205f7b-fa12-494f-b6f9-46213daf3755", "category": null}, {"level": null, "dataTag": "1.tttt.dangweifuzeren", "builtin": false, "patterns": [{"expr": "dwfzr", "type": "catalog"}], "name": "dangweifuzeren", "description": "", "levelName": null, "id": "d3b27f23-edc9-4cac-b6ec-3ced331e43bd", "category": null}, {"level": null, "dataTag": "1.tttt.xingming", "builtin": false, "patterns": [{"expr": "PersonName", "type": "catalog"}], "name": "xing<PERSON>", "description": "", "levelName": null, "id": "a9a7c059-c1a0-4889-befd-ca04f71035a6", "category": null}, {"level": null, "dataTag": "1.tttt.jianshedanweimingche", "builtin": false, "patterns": [{"expr": "build_corp_name", "type": "catalog"}], "name": "jianshedanweimingche", "description": "", "levelName": null, "id": "31a31c9d-a184-4da5-b031-aa60b86b1eb8", "category": null}, {"level": null, "dataTag": "1.tttt.yiwuren", "builtin": false, "patterns": [{"expr": "YWRMC", "type": "catalog"}], "name": "yi<PERSON><PERSON>", "description": "", "levelName": null, "id": "4149e18a-7cbd-4192-90ff-190e7a4d973d", "category": null}, {"level": null, "dataTag": "1.tttt.kaihumingcheng", "builtin": false, "patterns": [{"expr": "gys_bank_people", "type": "catalog"}], "name": "kaihumingcheng", "description": "", "levelName": null, "id": "72871f39-7e44-431d-91d7-7a91257c0536", "category": null}, {"level": null, "dataTag": "1.tttt.x<PERSON><PERSON><PERSON><PERSON><PERSON>u", "builtin": false, "patterns": [{"expr": "XZZX", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u", "description": "", "levelName": null, "id": "d01d7643-206f-4fe6-9616-49a7122eda94", "category": null}, {"level": null, "dataTag": "1.tttt.fang<PERSON><PERSON><PERSON><PERSON>yijiag", "builtin": false, "patterns": [{"expr": "FDCJYJG", "type": "catalog"}], "name": "fang<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ag", "description": "", "levelName": null, "id": "835ca534-2731-4d3e-acf2-ee42a1eafdee", "category": null}, {"level": null, "dataTag": "1.tttt.diyaren", "builtin": false, "patterns": [{"expr": "DYR", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "42cc4337-abe8-401e-a25b-d75dc29f0c5b", "category": null}, {"level": null, "dataTag": "1.tttt.x<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "XXDM", "type": "catalog"}], "name": "x<PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "603985e8-0c7e-4e4e-8b90-3f86c171257d", "category": null}, {"level": null, "dataTag": "1.tttt.zu<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "FDZL", "type": "catalog"}], "name": "zuoluoweizhi", "description": "", "levelName": null, "id": "2402eaa9-37f7-4454-bc7a-e0dbf3f56a1a", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "jfcsq", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "f0678af2-90d7-4212-83ab-b0ad156eb3b3", "category": null}, {"level": null, "dataTag": "1.tttt.g<PERSON><PERSON>zhengji<PERSON>", "builtin": false, "patterns": [{"expr": "GDZJHM", "type": "catalog"}], "name": "g<PERSON><PERSON>zhen<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "4cf782de-c1ac-4cdb-abbc-2cf661c07a96", "category": null}, {"level": null, "dataTag": "1.tttt.tijianzhejigouneiwei", "builtin": false, "patterns": [{"expr": "TJZJGNWYID", "type": "catalog"}], "name": "tijianzhejigouneiwei", "description": "", "levelName": null, "id": "e951d287-0dcd-4ffd-9d6c-d6b9e921e42a", "category": null}, {"level": null, "dataTag": "1.tttt.xiang<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "XXDZ", "type": "catalog"}], "name": "xiangxidizhi", "description": "", "levelName": null, "id": "8f5657ca-703d-4710-bd2e-5f0842a198d1", "category": null}, {"level": null, "dataTag": "1.tttt.hub<PERSON>", "builtin": false, "patterns": [{"expr": "hbh", "type": "catalog"}], "name": "hub<PERSON><PERSON>", "description": "", "levelName": null, "id": "ddd3f31f-a536-4c3a-83e5-198da40ada11", "category": null}, {"level": null, "dataTag": "1.tttt.h<PERSON><PERSON><PERSON><PERSON>n<PERSON>u", "builtin": false, "patterns": [{"expr": "HJDZXQ", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "072bf9b8-e9ee-4a48-a202-deec0581f8ac", "category": null}, {"level": null, "dataTag": "1.tttt.kesh<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "KSMC", "type": "catalog"}], "name": "kesh<PERSON>ing<PERSON>", "description": "", "levelName": null, "id": "1ab8bf0b-fbc1-4ee4-ba1c-50ac1e36bc61", "category": null}, {"level": null, "dataTag": "1.tttt.b<PERSON>ng<PERSON>den<PERSON>", "builtin": false, "patterns": [{"expr": "BDCDJZMH", "type": "catalog"}], "name": "budong<PERSON>deng<PERSON>zhen", "description": "", "levelName": null, "id": "a7cd8463-290e-4755-b699-c4cc0a181793", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "XNHKH", "type": "catalog"}], "name": "xinnonghekahao", "description": "", "levelName": null, "id": "cc83184a-777d-4452-b5bb-f6582ba77725", "category": null}, {"level": null, "dataTag": "1.tttt.s<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "SJHM", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "826a1f61-0fee-4e2c-8cc0-a4186b4ba9e6", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "zzbh", "type": "catalog"}], "name": "zhengzhaobianhao", "description": "", "levelName": null, "id": "ff0ecaec-2139-4770-9008-11feeb318713", "category": null}, {"level": null, "dataTag": "1.tttt.fuzeren", "builtin": false, "patterns": [{"expr": "FZR", "type": "catalog"}], "name": "fuzeren", "description": "", "levelName": null, "id": "698ad694-e8e4-471d-91f0-41c5828465ad", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "xjfh", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "23d50323-e0aa-46c2-a910-3640720ab5e9", "category": null}, {"level": null, "dataTag": "1.tttt.y<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "AccounCode", "type": "catalog"}], "name": "y<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "b6a3ef2a-720b-465d-b985-23a555f004ce", "category": null}, {"level": null, "dataTag": "1.tttt.s<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "skzh", "type": "catalog"}], "name": "s<PERSON><PERSON><PERSON>z<PERSON><PERSON>", "description": "", "levelName": null, "id": "206916aa-34f1-4a6b-8422-c73adfe2c680", "category": null}, {"level": null, "dataTag": "1.tttt.b<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "BDCQZH", "type": "catalog"}], "name": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "9be58bc4-5e8a-421e-bccb-673d5f10dde4", "category": null}, {"level": null, "dataTag": "1.tttt.gong<PERSON><PERSON>nwei<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "GZDWDZS", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "5dab20d7-336d-4e4c-831f-1bb7cf5b3ea3", "category": null}, {"level": null, "dataTag": "1.tttt.jiansheyongdiguihuax", "builtin": false, "patterns": [{"expr": "build_plan_num", "type": "catalog"}], "name": "jiansheyongdiguihuax", "description": "", "levelName": null, "id": "111b7590-f83e-44d8-a19f-801001d813c1", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "kfstyshxydm", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "670abc47-1bce-4b96-a105-c5c09eff2e38", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "xjh", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "188ab5d1-c487-412f-b5f9-3e63f7c92ab3", "category": null}, {"level": null, "dataTag": "1.tttt.z<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "ZDDM", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "8f719893-fa84-4100-b7ae-ca965ff49b7f", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "XJZC", "type": "catalog"}], "name": "xianzhu<PERSON><PERSON><PERSON>eluno", "description": "", "levelName": null, "id": "0ab74813-bd34-4183-846a-d449f0d57a60", "category": null}, {"level": null, "dataTag": "1.tttt.dizhimenpaihaoma", "builtin": false, "patterns": [{"expr": "DZMPH", "type": "catalog"}], "name": "dizhimenpaihaoma", "description": "", "levelName": null, "id": "0779c3af-b2ff-47ba-9fc7-eb03a1100ce3", "category": null}, {"level": null, "dataTag": "1.tttt.kahao", "builtin": false, "patterns": [{"expr": "KH", "type": "catalog"}], "name": "ka<PERSON>", "description": "", "levelName": null, "id": "4c3592be-f099-4fad-a354-73bbaf5a32cd", "category": null}, {"level": null, "dataTag": "1.tttt.fang<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "FDZL", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "c320b4b6-5ee1-4115-90a7-4f3647519dec", "category": null}, {"level": null, "dataTag": "1.tttt.youxiang", "builtin": false, "patterns": [{"expr": "EMAIL", "type": "catalog"}], "name": "youxiang", "description": "", "levelName": null, "id": "fcec5051-ed24-4d01-8ee2-b6023f9757de", "category": null}, {"level": null, "dataTag": "1.tttt.xian<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "xmdz", "type": "catalog"}], "name": "xiangmudizhi", "description": "", "levelName": null, "id": "943d98a0-e42e-4088-960b-b0c00aae78c5", "category": null}, {"level": null, "dataTag": "1.tttt.quanliren", "builtin": false, "patterns": [{"expr": "QLRMC", "type": "catalog"}], "name": "quanliren", "description": "", "levelName": null, "id": "6671ef4f-0f07-4c49-8e6f-10de41f60fd6", "category": null}, {"level": null, "dataTag": "1.tttt.farendanweimingcheng", "builtin": false, "patterns": [{"expr": "legal_comp_name", "type": "catalog"}], "name": "farendanweimingcheng", "description": "", "levelName": null, "id": "d37a2727-996b-4692-b4df-d14561ae5a1d", "category": null}, {"level": null, "dataTag": "1.tttt.ji<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "JTZZ", "type": "catalog"}], "name": "ji<PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "4417ee72-d2ac-47e8-ae5e-10c505b054cc", "category": null}, {"level": null, "dataTag": "1.tttt.lianxirentongxindizh", "builtin": false, "patterns": [{"expr": "LXRTXDZ", "type": "catalog"}], "name": "lianxirentongxindizh", "description": "", "levelName": null, "id": "0b62c204-90e3-4c98-b31f-d78573c29910", "category": null}, {"level": null, "dataTag": "1.tttt.lianxirenxingming", "builtin": false, "patterns": [{"expr": "LXRXM", "type": "catalog"}], "name": "lianxirenxingming", "description": "", "levelName": null, "id": "5e21ce34-0d17-4dc7-a4e4-1415e34bd9f0", "category": null}, {"level": null, "dataTag": "1.tttt.lianxirendizhixianqu", "builtin": false, "patterns": [{"expr": "LXRDZXQ", "type": "catalog"}], "name": "lianxirendizhixianqu", "description": "", "levelName": null, "id": "0236f68b-ce58-4807-8005-3467dbf0ebf9", "category": null}, {"level": null, "dataTag": "1.tttt.quan<PERSON>ren<PERSON>gji<PERSON>", "builtin": false, "patterns": [{"expr": "QLRZJH", "type": "catalog"}], "name": "quanlirenzhengjianha", "description": "", "levelName": null, "id": "1664e5a4-e012-4327-b2e4-230de8<PERSON>dac", "category": null}, {"level": null, "dataTag": "1.tttt.bangongdizhi", "builtin": false, "patterns": [{"expr": "BGDZ", "type": "catalog"}], "name": "bangongdizhi", "description": "", "levelName": null, "id": "8ab2c177-7c10-4ffa-a121-17e441c46e05", "category": null}, {"level": null, "dataTag": "1.tttt.s<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "SYDWMC", "type": "catalog"}], "name": "s<PERSON>yedanweimingcheng", "description": "", "levelName": null, "id": "23706d33-da4a-41f2-95ef-7bcda2079ba0", "category": null}, {"level": null, "dataTag": "1.tttt.y<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "YBKH", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "84724679-81c6-4f2d-98b6-af002d6a430d", "category": null}, {"level": null, "dataTag": "1.tttt.b<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "BDCDYH", "type": "catalog"}], "name": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "4a263118-39d0-4501-a13f-245e6f8a7926", "category": null}, {"level": null, "dataTag": "1.tttt.huanz<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "HZXM", "type": "catalog"}], "name": "huanz<PERSON><PERSON>ming", "description": "", "levelName": null, "id": "21a43b41-ed0c-41f1-ab15-4972e20528a5", "category": null}, {"level": null, "dataTag": "1.tttt.ID", "builtin": false, "patterns": [{"expr": "ID", "type": "catalog"}], "name": "ID", "description": "", "levelName": null, "id": "5db76151-f633-4f8c-b574-7dc51030fb48", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "jfxzjd", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "e1c35824-a91f-4ba2-b4c7-70d4fdba6db1", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "xxxc", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "224eae5d-7ce1-4358-a58f-5dc1291bf231", "category": null}, {"level": null, "dataTag": "1.tttt.yi<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "YWRZJH", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "5f31d08b-f1f4-4af9-9e3f-677fb059b229", "category": null}, {"level": null, "dataTag": "1.tttt.y<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "QZYSXLH", "type": "catalog"}], "name": "yins<PERSON>x<PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "cc73fd29-0f30-4e02-abb9-7e2965abdbad", "category": null}, {"level": null, "dataTag": "1.tttt.shen<PERSON>zheng", "builtin": false, "patterns": [{"expr": "sfz", "type": "catalog"}], "name": "<PERSON>n<PERSON>zheng", "description": "", "levelName": null, "id": "6cc98d99-af69-49c6-8b7c-b1c35442ecbf", "category": null}, {"level": null, "dataTag": "1.tttt.shen<PERSON>ren", "builtin": false, "patterns": [{"expr": "SQR", "type": "catalog"}], "name": "shen<PERSON><PERSON>", "description": "", "levelName": null, "id": "4a3ea355-23b2-4f90-a09c-1c12fc7ab48f", "category": null}, {"level": null, "dataTag": "1.tttt.di<PERSON><PERSON><PERSON><PERSON>u", "builtin": false, "patterns": [{"expr": "DZX", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "32c163b6-9802-4364-998a-f3071b45d33e", "category": null}, {"level": null, "dataTag": "1.tttt.goufangrenshenfenzhe", "builtin": false, "patterns": [{"expr": "gfrsfz", "type": "catalog"}], "name": "goufangrenshenfenzhe", "description": "", "levelName": null, "id": "d251d346-2451-4c42-940a-aa4f2966920a", "category": null}, {"level": null, "dataTag": "1.tttt.z<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "ZWRMC", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "d335075c-84aa-4d5f-bceb-0910f1e4ca45", "category": null}, {"level": null, "dataTag": "1.tttt.lianxirendianhuahaom", "builtin": false, "patterns": [{"expr": "LXRDHHM", "type": "catalog"}], "name": "lianxirendianhuahaom", "description": "", "levelName": null, "id": "484cf464-8303-491c-af70-b5bc37f1e3f2", "category": null}, {"level": null, "dataTag": "1.tttt.dianhua", "builtin": false, "patterns": [{"expr": "dh", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "0c9660cf-0e63-446e-9d38-51453aafcf57", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON>zhengji<PERSON>", "builtin": false, "patterns": [{"expr": "ZWRZJH", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON>zhengjian<PERSON>", "description": "", "levelName": null, "id": "c2a5ac2e-a032-4c7f-be6f-9c60a51aabd1", "category": null}, {"level": null, "dataTag": "1.tttt.huanzhejigouneiweiyi", "builtin": false, "patterns": [{"expr": "HZJGNWYID", "type": "catalog"}], "name": "huanzhejigouneiweiyi", "description": "", "levelName": null, "id": "32ffe96b-1c9d-4b65-8c31-faefb47c3964", "category": null}, {"level": null, "dataTag": "1.tttt.lianxirendizhishidiq", "builtin": false, "patterns": [{"expr": "LXRDZSZ", "type": "catalog"}], "name": "lianxirendizhishidiq", "description": "", "levelName": null, "id": "477ced2b-2a5e-4e83-9f85-db0eac760299", "category": null}, {"level": null, "dataTag": "1.tttt.lianxidianhua", "builtin": false, "patterns": [{"expr": "TELEPHONE", "type": "catalog"}], "name": "lianxidianhua", "description": "", "levelName": null, "id": "81b4e44d-73b7-4da0-aa66-f08bbd43c708", "category": null}, {"level": null, "dataTag": "1.tttt.renyuanID", "builtin": false, "patterns": [{"expr": "PERSONNEL_ID", "type": "catalog"}], "name": "renyuanID", "description": "", "levelName": null, "id": "d17e8f27-9e71-4bd5-b39c-aca0246e0ca1", "category": null}, {"level": null, "dataTag": "1.tttt.z<PERSON>jiaoxinyang", "builtin": false, "patterns": [{"expr": "RELIGION_FAITH", "type": "catalog"}], "name": "zongjiaoxinyang", "description": "", "levelName": null, "id": "54a82c65-b34b-4f76-9486-d1932da38a57", "category": null}, {"level": null, "dataTag": "1.tttt.zhuzhi", "builtin": false, "patterns": [{"expr": "HOME_ADDRESS", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "12c40329-f2bd-44ca-96c0-100f6b13c228", "category": null}, {"level": null, "dataTag": "1.tttt.x<PERSON><PERSON><PERSON>hon", "builtin": false, "patterns": [{"expr": "XZZXQ_MC", "type": "catalog"}], "name": "xianzhuzhixianquzhon", "description": "", "levelName": null, "id": "e21bbee2-1ff8-4c60-9f47-731d040acf03", "category": null}, {"level": null, "dataTag": "1.tttt.xiang<PERSON>", "builtin": false, "patterns": [{"expr": "TOWN", "type": "catalog"}], "name": "xiangzhen", "description": "", "levelName": null, "id": "c8c66695-6f3e-4b51-ac84-605bfbb0387d", "category": null}, {"level": null, "dataTag": "1.tttt.jubanz<PERSON>", "builtin": false, "patterns": [{"expr": "jbz", "type": "catalog"}], "name": "jubanzhe", "description": "", "levelName": null, "id": "25db45f7-f78a-4baf-992c-fde7420ca3cc", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "djzh", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "5b1dab3c-65ca-4552-a77b-4b98c6392c3d", "category": null}, {"level": null, "dataTag": "1.tttt.goufangrenxingming", "builtin": false, "patterns": [{"expr": "gfrxm", "type": "catalog"}], "name": "goufangrenxingming", "description": "", "levelName": null, "id": "ca465416-4518-4d5a-a165-740e625031b3", "category": null}, {"level": null, "dataTag": "1.tttt.h<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "HHZBH", "type": "catalog"}], "name": "<PERSON>uo<PERSON>zhen<PERSON><PERSON>", "description": "", "levelName": null, "id": "30bd80c3-80e5-4d0e-853e-f723e14b2b36", "category": null}, {"level": null, "dataTag": "1.tttt.jigoumingcheng", "builtin": false, "patterns": [{"expr": "JGMC", "type": "catalog"}], "name": "jigoumingcheng", "description": "", "levelName": null, "id": "e57aa6a1-f69c-4de1-8749-933de1596969", "category": null}, {"level": null, "dataTag": "1.tttt.donghao", "builtin": false, "patterns": [{"expr": "dh", "type": "catalog"}], "name": "donghao", "description": "", "levelName": null, "id": "940f8312-05b3-4ed9-b740-11b6614b5b1a", "category": null}, {"level": null, "dataTag": "1.tttt.lianxiren", "builtin": false, "patterns": [{"expr": "lxr", "type": "catalog"}], "name": "lianxiren", "description": "", "levelName": null, "id": "57bda0d4-fee4-48aa-9b7f-bf58fb7794ef", "category": null}, {"level": null, "dataTag": "1.tttt.zhu<PERSON>", "builtin": false, "patterns": [{"expr": "ZCH", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "6e7d2bbb-1838-4853-b3f5-8e5990334c66", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON><PERSON>uzhou", "builtin": false, "patterns": [{"expr": "DZSQ", "type": "catalog"}], "name": "dizhishidiquzhou", "description": "", "levelName": null, "id": "5c1de7db-7b48-4761-8a48-5b8bfb524a81", "category": null}, {"level": null, "dataTag": "1.tttt.jiguanshidiquzhou", "builtin": false, "patterns": [{"expr": "JGSZ", "type": "catalog"}], "name": "jiguanshidiquzhou", "description": "", "levelName": null, "id": "0ba2284c-000b-4b5e-b316-3ab2de77450f", "category": null}, {"level": null, "dataTag": "1.tttt.zaiji<PERSON>ji<PERSON>z<PERSON>wudiya", "builtin": false, "patterns": [{"expr": "ZJJZWDYFW", "type": "catalog"}], "name": "z<PERSON>ji<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ya", "description": "", "levelName": null, "id": "792d813a-f211-4e8c-848d-a4664e1ba075", "category": null}, {"level": null, "dataTag": "1.tttt.hu<PERSON><PERSON>hi", "builtin": false, "patterns": [{"expr": "HJDZ", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "f06299e6-d79e-4cb2-8a7a-1a52ea91a50a", "category": null}, {"level": null, "dataTag": "1.tttt.quanli<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "QLRMC", "type": "catalog"}], "name": "quanlirenmingcheng", "description": "", "levelName": null, "id": "c442863a-53a7-434d-81ba-47b5284eda56", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "SBH", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "33d7a04b-e287-43d9-806d-9186f409b2c9", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "SFZHM_JS", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "3f8216ae-78fc-4339-98c4-159ffe88caae", "category": null}, {"level": null, "dataTag": "1.tttt.jiguanshengzizhiquzh", "builtin": false, "patterns": [{"expr": "JGS", "type": "catalog"}], "name": "jiguanshengzizhiquzh", "description": "", "levelName": null, "id": "d7723a11-50a6-4491-9af8-463469f38754", "category": null}, {"level": null, "dataTag": "1.tttt.hunyinzhuangkuangdai", "builtin": false, "patterns": [{"expr": "HYZKDM", "type": "catalog"}], "name": "hunyinzhuangkuangdai", "description": "", "levelName": null, "id": "fd2a12e1-15a4-4c8e-bb30-d9b7baa97dbe", "category": null}, {"level": null, "dataTag": "1.tttt.suozaiji<PERSON>u", "builtin": false, "patterns": [{"expr": "szjg", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "3abad1ce-2d81-4b2d-acd7-387c254f9b61", "category": null}, {"level": null, "dataTag": "1.tttt.lianxirendianhua", "builtin": false, "patterns": [{"expr": "LXRDH", "type": "catalog"}], "name": "lianxirendianhua", "description": "", "levelName": null, "id": "f95349a4-dd51-4376-9503-7ed080544d7c", "category": null}, {"level": null, "dataTag": "1.tttt.changzhusuozaididizh", "builtin": false, "patterns": [{"expr": "AAE006", "type": "catalog"}], "name": "changzhusuozaididizh", "description": "", "levelName": null, "id": "3883a396-b8aa-45a6-bcef-5afdedb642c1", "category": null}, {"level": null, "dataTag": "1.tttt.jing<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "JBJS", "type": "catalog"}], "name": "jing<PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "08b2584d-8f43-46a8-b3e2-825b532f7f2a", "category": null}, {"level": null, "dataTag": "1.tttt.chush<PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "CSDMC", "type": "catalog"}], "name": "chush<PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "c7919196-cfec-4cac-b4cd-a14ae5fb9c39", "category": null}, {"level": null, "dataTag": "1.tttt.chush<PERSON><PERSON>u", "builtin": false, "patterns": [{"expr": "CSDX", "type": "catalog"}], "name": "chush<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "2c04e086-9fdb-4985-9588-be814dc579d3", "category": null}, {"level": null, "dataTag": "1.tttt.jing<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "JYZ", "type": "catalog"}], "name": "jing<PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "913dc1a5-1ef8-4d49-8fe7-6a90b253bf93", "category": null}, {"level": null, "dataTag": "1.tttt.z<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "ZWRMC", "type": "catalog"}], "name": "zhai<PERSON>renmingcheng", "description": "", "levelName": null, "id": "7c72a6d4-09f2-4ea5-9427-7735ac27deed", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "corp_id", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "bb295a54-6090-4800-b5e9-05a7c56b3fbb", "category": null}, {"level": null, "dataTag": "1.tttt.h<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "HJDZC", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "b5392e92-6ac5-4683-a01f-f808e52fe72e", "category": null}, {"level": null, "dataTag": "1.tttt.lianxirendizhi", "builtin": false, "patterns": [{"expr": "LXRDZ", "type": "catalog"}], "name": "lianxirendizhi", "description": "", "levelName": null, "id": "19660a63-7348-41d3-8152-a95f1f34d520", "category": null}, {"level": null, "dataTag": "1.tttt.sen<PERSON><PERSON><PERSON>u", "builtin": false, "patterns": [{"expr": "SLLMSYQR2", "type": "catalog"}], "name": "sen<PERSON><PERSON><PERSON><PERSON><PERSON>u", "description": "", "levelName": null, "id": "be34a811-fa49-4a08-b344-5f9c7d4552df", "category": null}, {"level": null, "dataTag": "1.tttt.xianzhuzhimenpaihaom", "builtin": false, "patterns": [{"expr": "XJZMPHM", "type": "catalog"}], "name": "xianzhuzhimenpaihaom", "description": "", "levelName": null, "id": "ccecef06-8096-4543-91aa-4c25c4cfa896", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "shzzmc", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "ddebb25a-4036-4c88-874e-1001a3bccb54", "category": null}, {"level": null, "dataTag": "1.tttt.gerenjiankan<PERSON>danganb", "builtin": false, "patterns": [{"expr": "GRJKDABM", "type": "catalog"}], "name": "gere<PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "1dd7d324-da71-4d2a-bc26-3ae30ff52cfe", "category": null}, {"level": null, "dataTag": "1.tttt.y<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "QZYSXLH", "type": "catalog"}], "name": "yins<PERSON>hao", "description": "", "levelName": null, "id": "8f4cda04-447e-48ee-8aa1-d1c6958e05ca", "category": null}, {"level": null, "dataTag": "1.tttt.ch<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "ISCZR", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "612afa43-a112-4548-bfbb-b0b43be064f6", "category": null}, {"level": null, "dataTag": "1.tttt.tudi<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "TDZL", "type": "catalog"}], "name": "t<PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "04813e57-1f69-470d-8453-8f5e727e8dcd", "category": null}, {"level": null, "dataTag": "1.tttt.canbaocunshequ", "builtin": false, "patterns": [{"expr": "cbcsq", "type": "catalog"}], "name": "canbao<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "6128ccef-eaba-4cc8-8ae5-781fdca89635", "category": null}, {"level": null, "dataTag": "1.tttt.ji<PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "JYDZ", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "c1eb415c-d973-4731-952d-33d9cb4876de", "category": null}, {"level": null, "dataTag": "1.tttt.di<PERSON><PERSON><PERSON><PERSON><PERSON>o", "builtin": false, "patterns": [{"expr": "DZXZ", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "7f7d7204-c6e2-4771-a50f-a6efe4233576", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "REG_NO", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "7884eb34-6205-43e5-96a7-387aecb0d8c8", "category": null}, {"level": null, "dataTag": "1.tttt.minzu", "builtin": false, "patterns": [{"expr": "mz", "type": "catalog"}], "name": "minzu", "description": "", "levelName": null, "id": "ecb66dab-5857-4cfc-875b-0af3d63fb5e0", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "FRZJHM", "type": "catalog"}], "name": "farenzhengjianhaoma", "description": "", "levelName": null, "id": "19649d4e-b427-4313-ba1a-a89c7816d08c", "category": null}, {"level": null, "dataTag": "1.tttt.juminjiankang<PERSON>", "builtin": false, "patterns": [{"expr": "JMJKDABM", "type": "catalog"}], "name": "juminjiankangkahao", "description": "", "levelName": null, "id": "b864de46-b0ed-49db-bba1-3be53d471da7", "category": null}, {"level": null, "dataTag": "1.tttt.chaf<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "CFFW", "type": "catalog"}], "name": "chaf<PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "511cc8e7-5d6d-428b-a7c6-e3f7fb8e73f6", "category": null}, {"level": null, "dataTag": "1.tttt.jiguan", "builtin": false, "patterns": [{"expr": "jg", "type": "catalog"}], "name": "jiguan", "description": "", "levelName": null, "id": "178f7926-12a4-4fe9-8a97-ab91703f1432", "category": null}, {"level": null, "dataTag": "1.tttt.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "CSDSZ", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "1583345b-c687-4465-b079-4017c8f02006", "category": null}, {"level": null, "dataTag": "1.tttt.zaijianjianzhuwuzuol", "builtin": false, "patterns": [{"expr": "ZJJZWZL", "type": "catalog"}], "name": "zaijianjianzhuwuzuol", "description": "", "levelName": null, "id": "15f25273-ab2d-4351-8eb5-c82d7a3d18d6", "category": null}, {"level": null, "dataTag": "1.5555.<PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"生日（结构化）\"\r\nscores:\r\n- desc: \"列注释 以 生日 、 出生日期 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"([\\u51FA]?\\u751F\\u65E5[\\u671F]?\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则精确匹配 birth、模糊匹配birthday、date(允许包含_)of(允许包含_) birth\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(^(?i)birth$)|((?i)birthday)|((?i)date[_]?of[_]?birth)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "e7bad2bb-cf4c-4a17-8833-1d18418cdd49", "category": null}, {"level": null, "dataTag": "1.5555.dizhi", "builtin": false, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"地址（结构化）\"\r\nscores:\r\n- desc: \"列名匹配 address 或者 addr\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(?i)address|(?i)addr\"\r\n      - var: \"column.name\"\r\n- desc: \"列定义长度 大于 20\"\r\n  name: \"ColumnDefLength\"\r\n  output: true\r\n  rule:\r\n    and:\r\n    - var: \"$.FindColumnName\"\r\n    - '>=':\r\n      - var: \"column.size\"\r\n      - 20\r\n- desc: \"字段注释 包含 住址 or 家庭地址 or 办公地址 or 联系地址\"\r\n  name: \"FieldCommentsContains\"\r\n  output: true\r\n  rule:\r\n    and:\r\n    - var: \"$.ColumnDefLength\"\r\n    - regexp_find:\r\n        - \"\\u4f4f\\u5740|\\u5bb6\\u5ead\\u5730\\u5740|\\u529e\\u516c\\u5730\\u5740|\\u8054\\u7cfb\\u5730\\u5740\"\r\n        - var: \"column.comment\"\r\n- desc: \"NLP 识别地址。以上条件都满足或者是 NLP 识别率达到 30%\"\r\n  name: \"NlpAnalysis\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FieldCommentsContains\"\r\n      - and:\r\n        - var: \"$.ColumnDefLength\"\r\n        - '>=':\r\n            - array_hit_ratio:\r\n                - validator:\r\n                    - \"cls.piilinkable::address\"\r\n                    - var: \"ArrayElementValue\"\r\n                - var: \"column.values\"\r\n            - 30\r\n- desc: \"NLP 如果不满足，继续 字段内容 匹配规则列表\"\r\n  name: \"RegexpListFind\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.NlpAnalysis\"\r\n      - and:\r\n          - var: \"$.ColumnDefLength\"\r\n          - '>=':\r\n              - array_hit_ratio:\r\n                  - or: # 包含 小区|单元|号楼|大厦|家园|新区|村委会|公安局|派出所|街道办|公寓|厂区\r\n                      - regexp_find:\r\n                          - \"\\u5c0f\\u533a|\\u5355\\u5143|\\u53f7\\u697c|\\u5927\\u53a6|\\u5bb6\\u56ed|\\u65b0\\u533a|\\u6751\\u59d4\\u4f1a|\\u516c\\u5b89\\u5c40|\\u6d3e\\u51fa\\u6240|\\u8857\\u9053\\u529e|\\u516c\\u5bd3|\\u5382\\u533a\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 以 3位数字+室or房 结尾\r\n                          - \"\\\\d{1,3}\\\\s*[\\u5BA4\\u623F]$\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 市 and 区 and （1位数字+号），市区两字不可紧相邻\r\n                          - \"\\u5E02(\\\\s*[^\\\\s\\u5E02\\u533A\\\\d])+\\\\s*\\u533A(\\\\s*[^\\\\s\\u5E02\\u533A\\u53F7])+\\\\d*\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 村 and 号，之间有至少一位值\r\n                          - \"\\u6751(\\\\s*[^\\\\s\\u6751\\u53F7])+\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 市 and 村，之间有至少一位值\r\n                          - \"\\u5E02(\\\\s*[^\\\\s\\u5E02\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 区 and 村，之间有至少一位值\r\n                          - \"\\u533A(\\\\s*[^\\\\s\\u533A\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 镇 and 村，之间有至少一位值\r\n                          - \"\\u9547(\\\\s*[^\\\\s\\u9547\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 县 and 村，之间有至少一位值\r\n                          - \"\\u53BF(\\\\s*[^\\\\s\\u53BF\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 巷 and 号，之间有至少一位值\r\n                          - \"\\u5DF7(\\\\s*[^\\\\s\\u5DF7\\u53F7])+\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 包含 任意位数字+栋+任意位数字\r\n                          - \"\\\\d+\\\\s*\\u680B\\\\s*\\\\d+\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 社区 and 号，之间有至少一位值\r\n                          - \"\\u793E\\u533A(\\\\s*[^\\\\s\\u793E\\u533A\\u53F7])+\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 以 局 结尾\r\n                          - \"\\u5C40\\\\s*$\"\r\n                          - var: \"ArrayElementValue\"\r\n                  - var: \"column.values\"\r\n              - 30\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.RegexpListFind\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "dizhi", "description": "Updated on 2024.05.25", "levelName": null, "id": "94860c5d-cd9d-4d01-a0b5-d83c40f3fe0f", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.shengri", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"生日（结构化）\"\r\nscores:\r\n- desc: \"列注释 以 生日 、 出生日期 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"([\\u51FA]?\\u751F\\u65E5[\\u671F]?\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则精确匹配 birth、模糊匹配birthday、date(允许包含_)of(允许包含_) birth\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(^(?i)birth$)|((?i)birthday)|((?i)date[_]?of[_]?birth)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "29f71136-0359-4a52-b22d-de2b9595bdec", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.dizhi", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"地址（结构化）\"\r\nscores:\r\n- desc: \"列名匹配 address 或者 addr\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(?i)address|(?i)addr\"\r\n      - var: \"column.name\"\r\n- desc: \"列定义长度 大于 20\"\r\n  name: \"ColumnDefLength\"\r\n  output: true\r\n  rule:\r\n    and:\r\n    - var: \"$.FindColumnName\"\r\n    - '>=':\r\n      - var: \"column.size\"\r\n      - 20\r\n- desc: \"字段注释 包含 住址 or 家庭地址 or 办公地址 or 联系地址\"\r\n  name: \"FieldCommentsContains\"\r\n  output: true\r\n  rule:\r\n    and:\r\n    - var: \"$.ColumnDefLength\"\r\n    - regexp_find:\r\n        - \"\\u4f4f\\u5740|\\u5bb6\\u5ead\\u5730\\u5740|\\u529e\\u516c\\u5730\\u5740|\\u8054\\u7cfb\\u5730\\u5740\"\r\n        - var: \"column.comment\"\r\n- desc: \"NLP 识别地址。以上条件都满足或者是 NLP 识别率达到 30%\"\r\n  name: \"NlpAnalysis\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FieldCommentsContains\"\r\n      - and:\r\n        - var: \"$.ColumnDefLength\"\r\n        - '>=':\r\n            - array_hit_ratio:\r\n                - validator:\r\n                    - \"cls.piilinkable::address\"\r\n                    - var: \"ArrayElementValue\"\r\n                - var: \"column.values\"\r\n            - 30\r\n- desc: \"NLP 如果不满足，继续 字段内容 匹配规则列表\"\r\n  name: \"RegexpListFind\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.NlpAnalysis\"\r\n      - and:\r\n          - var: \"$.ColumnDefLength\"\r\n          - '>=':\r\n              - array_hit_ratio:\r\n                  - or: # 包含 小区|单元|号楼|大厦|家园|新区|村委会|公安局|派出所|街道办|公寓|厂区\r\n                      - regexp_find:\r\n                          - \"\\u5c0f\\u533a|\\u5355\\u5143|\\u53f7\\u697c|\\u5927\\u53a6|\\u5bb6\\u56ed|\\u65b0\\u533a|\\u6751\\u59d4\\u4f1a|\\u516c\\u5b89\\u5c40|\\u6d3e\\u51fa\\u6240|\\u8857\\u9053\\u529e|\\u516c\\u5bd3|\\u5382\\u533a\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 以 3位数字+室or房 结尾\r\n                          - \"\\\\d{1,3}\\\\s*[\\u5BA4\\u623F]$\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 市 and 区 and （1位数字+号），市区两字不可紧相邻\r\n                          - \"\\u5E02(\\\\s*[^\\\\s\\u5E02\\u533A\\\\d])+\\\\s*\\u533A(\\\\s*[^\\\\s\\u5E02\\u533A\\u53F7])+\\\\d*\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 村 and 号，之间有至少一位值\r\n                          - \"\\u6751(\\\\s*[^\\\\s\\u6751\\u53F7])+\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 市 and 村，之间有至少一位值\r\n                          - \"\\u5E02(\\\\s*[^\\\\s\\u5E02\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 区 and 村，之间有至少一位值\r\n                          - \"\\u533A(\\\\s*[^\\\\s\\u533A\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 镇 and 村，之间有至少一位值\r\n                          - \"\\u9547(\\\\s*[^\\\\s\\u9547\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 县 and 村，之间有至少一位值\r\n                          - \"\\u53BF(\\\\s*[^\\\\s\\u53BF\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 巷 and 号，之间有至少一位值\r\n                          - \"\\u5DF7(\\\\s*[^\\\\s\\u5DF7\\u53F7])+\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 包含 任意位数字+栋+任意位数字\r\n                          - \"\\\\d+\\\\s*\\u680B\\\\s*\\\\d+\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 社区 and 号，之间有至少一位值\r\n                          - \"\\u793E\\u533A(\\\\s*[^\\\\s\\u793E\\u533A\\u53F7])+\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 以 局 结尾\r\n                          - \"\\u5C40\\\\s*$\"\r\n                          - var: \"ArrayElementValue\"\r\n                  - var: \"column.values\"\r\n              - 30\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.RegexpListFind\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "dizhi", "description": "Updated on 2024.05.25", "levelName": null, "id": "ac0c52d0-838d-4cd8-b0a8-3c3976f985c5", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.youbiandalu", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"邮编（结构化）\"\r\nscores:\r\n- desc: \"列注释以 邮编 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u90AE\\u7F16\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则精确匹配postal、postcode、postalcode、zipcode 模糊匹配 (允许包含_)of\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(^(?i)postal$)|(^(?i)postcode$)|(^(?i)postalcode$)|(^(?i)zipcode$)|((?i)postal[_]?of)|((?i)postcode[_]?of)|((?i)postalcode[_]?of)|((?i)zipcode[_]?of)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "e34866c9-c730-4bea-818f-2ff0df58243f", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.MAC", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"MAC（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 mac 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"(?i)mac\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]精确匹配mac\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"^(?i)mac$\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 满足正则匹配规则\"\r\n    name: \"FindColumnValue\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - regexp_find:\r\n                  - \"^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})|([0-9a-fA-F]{4}\\\\\\\\.[0-9a-fA-F]{4}\\\\\\\\.[0-9a-fA-F]{4})[ ]*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnValue\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "MAC", "description": "Updated on 2024.05.25", "levelName": null, "id": "385600c9-2e58-44e9-8f27-248a741668dc", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.xing<PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"性别（结构化）\"\r\nscores:\r\n- desc: \"列注释匹配以 性别 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u6027\\u522B\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"数据字典(命中率90%)\"\r\n  name: \"FindDataDict\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - \">=\":\r\n          - array_hit_ratio:\r\n              - \">\":\r\n                - search_in_dictionary:\r\n                    - \"gender\"\r\n                    - var: \"ArrayElementValue\"\r\n                - 0\r\n              - var: \"column.values\"\r\n          - 90\r\n- desc: \"[字段名]正则精确匹配gender、sexual、sex\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindDataDict\"\r\n      - regexp_find:\r\n          - \"(^(?i)gender$)|(^(?i)sexual$)|(^(?i)sex$)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "d1ebb618-4c46-4fb3-bdee-78337ce3d2e7", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.mima", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"密码（结构化）\"\r\nscores:\r\n  - desc: \"列注释匹配以 密码 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"\\u5BC6\\u7801\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]正则精确匹配password、passcode、passphrase\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"(^(?i)password$)|(^(?i)passcode$)|(^(?i)passphrase$)\"\r\n            - var: \"column.name\"\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnName\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "mima", "description": "Updated on 2024.05.25", "levelName": null, "id": "50e822eb-ae28-45e5-8397-041d4ed02635", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.xueya", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"血压（结构化）\"\r\nscores:\r\n- desc: \"列注释匹配 以 血压 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u8840\\u538B\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则模糊匹配blood(允许包含_)pressure 且 内容校验在 20-220 之间的整数(命中率90%)\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - and:\r\n        - regexp_find:\r\n          - \"(?i)blood[_]?pressure\"\r\n          - var: \"column.name\"\r\n        - \">=\":\r\n            - array_hit_ratio:\r\n                - and:\r\n                    - \">=\":\r\n                        - var: \"ArrayElementValue\"\r\n                        - 20\r\n                    - \"<=\":\r\n                        - var: \"ArrayElementValue\"\r\n                        - 220\r\n                - var: \"column.values\"\r\n            - 90\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "x<PERSON>ya", "description": "Updated on 2024.05.25", "levelName": null, "id": "a06dcb52-2e52-46d2-ae17-7f5a7fe3059d", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.IPv4", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"IPV4（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 ip、ipv4 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"((?i)ipv4\\\\s*$)|((?i)ip\\\\s*$)\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]精确匹配ip、ipv4\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"(^(?i)ip$)|(^(?i)ipv4$)\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 满足正则匹配规则\"\r\n    name: \"FindColumnValue\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - regexp_find:\r\n                  - \"^(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\\\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])[ ]*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnValue\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "IPv4", "description": "Updated on 2024.05.25", "levelName": null, "id": "720ac1f4-20ad-4425-b2bc-1cfe9511466c", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"身份证（中国内地）（结构化）\"\r\nscores:\r\n- desc: \"内容长度限制必须大于等于 18\"\r\n  name: \"IdCardLength\"\r\n  output: true\r\n  rule:\r\n    \">=\":\r\n      - var: \"column.size\"\r\n      - 18\r\n- desc: \"注释以身份证号结尾\"\r\n  name: \"ColumnComment\"\r\n  output: true\r\n  rule:\r\n    and:\r\n      - var: \"$.IdCardLength\"\r\n      - regexp_find:\r\n          - \"\\u8EAB\\u4EFD\\u8BC1\\u53F7$\"\r\n          - var: \"column.comment\"\r\n- desc: \"如果注释匹配，直接返回，否则计算命中率。\"\r\n  name: \"RegexpFindAndValidator\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.ColumnComment\"\r\n      - 100\r\n      - array_hit_ratio:\r\n          - and:\r\n            - '>=':\r\n                - length:\r\n                  - var: \"ArrayElementValue\"\r\n                - 18\r\n            - regexp_find:\r\n                - \"(([1][1-5])|([2][1-3])|([3][1-7])|([4][1-6])|([5][0-4])|([6][1-5])|([7][1])|([8][1-2]))\\\\d{4}(([1][9]\\\\d{2})|([2]\\\\d{3}))(([0][1-9])|([1][0-2]))(([0][1-9])|([1-2][0-9])|([3][0-1]))\\\\d{3}[0-9xX]\\\\s*\"\r\n                - var: \"ArrayElementValue\"\r\n            - validator:\r\n                - \"cls.pii::idcard\"\r\n                - var: \"ArrayElementValue\"\r\n          - var: \"column.values\"\r\n- desc: \"命中率必须大于等于 30\"\r\n  name: \"CalculateHitRatio\"\r\n  output: true\r\n  rule:\r\n    '>=':\r\n      - var: \"$.RegexpFindAndValidator\"\r\n      - 30\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    'condition':\r\n    - var: \"$.CalculateHitRatio\"\r\n    - 100\r\n    - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "9677e7ab-0d08-43e8-93bd-8e7572d3ec11", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.zong<PERSON>ao", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"宗教（结构化）\"\r\nscores:\r\n- desc: \"列注释以 宗教、信仰 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u5B97\\u6559\\\\s*$)|(\\u4FE1\\u4EF0\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"数据字典(命中率90%)\"\r\n  name: \"FindDataDict\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - \">=\":\r\n          - array_hit_ratio:\r\n              - \">\":\r\n                - search_in_dictionary:\r\n                    - \"religions\"\r\n                    - var: \"ArrayElementValue\"\r\n                - 0\r\n              - var: \"column.values\"\r\n          - 90\r\n- desc: \"[字段名]正则精确匹配 faith 或 精确匹配religion 或 模糊匹配religion(允许包含_)of \"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindDataDict\"\r\n      - regexp_find:\r\n          - \"(^(?i)faith$)|(^(?i)religion$)|((?i)religion[_]?of)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON>jiao", "description": "Updated on 2024.05.25", "levelName": null, "id": "9104875a-ff61-4d9d-bccf-fc14919884b5", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.xinlv", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"心率（结构化）\"\r\nscores:\r\n  - desc: \"列注释匹配 以 心率 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"\\u5FC3\\u7387\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]正则模糊匹配heart (允许包含_)rate 且 内容校验是在40-180之间的整数(命中率90%)\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - and:\r\n            - regexp_find:\r\n                - \"(?i)heart[_]?rate\"\r\n                - var: \"column.name\"\r\n            - \">=\":\r\n                - array_hit_ratio:\r\n                    - and:\r\n                        - \">=\":\r\n                            - var: \"ArrayElementValue\"\r\n                            - 40\r\n                        - \"<=\":\r\n                            - var: \"ArrayElementValue\"\r\n                            - 180\r\n                    - var: \"column.values\"\r\n                - 90\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnName\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "xinlv", "description": "Updated on 2024.05.25", "levelName": null, "id": "7ccd1998-f868-4cc5-a7e8-fea86a48d1ae", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.y<PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"银行卡（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 银行卡号 或 银行账号 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"\\u94F6\\u884C[\\u5361\\u8D26][\\u53F7]?\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"字段名匹配\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"^(?i)bank[_-]?accno$|^(?i)bank[_-]?account$|^(?i)bank[_-]?accno$|^(?i)bank[_-]?card[_-]?no$|^(?i)bank[_-]?card[_-]?number$\"\r\n        - var: \"column.name\"\r\n  - desc: \"正则+校验\"\r\n    name: \"ColumnValuesRegexpAndValidator\"\r\n    output: true\r\n    rule:\r\n      condition:\r\n        - var: \"$.FindColumnComment\"\r\n        - 100\r\n        - var: \"$.FindColumnName\"\r\n        - 100\r\n        - array_hit_ratio:\r\n            - and:\r\n                - regexp_find:\r\n                    - \"^\\\\s*\\\\d{10,20}\\\\s*$\"\r\n                    - var: \"ArrayElementValue\"\r\n                - regexp_find:\r\n                    - \"^\\\\s*(?!(\\\\d)\\\\1{5}).*$\"\r\n                    - var: \"ArrayElementValue\"\r\n                - or:\r\n                    - regexp_find:\r\n                        - \"^5[1-5][0-9]{2}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[ ]*$\"\r\n                        - var: \"ArrayElementValue\"\r\n                    - regexp_find:\r\n                        - \"^[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[ ]*$\"\r\n                        - var: \"ArrayElementValue\"\r\n                    - regexp_find:\r\n                        - \"^62[0-5][0-9]{13,16}[ ]*$\"\r\n                        - var: \"ArrayElementValue\"\r\n                    - regexp_find:\r\n                        - \"^4[0-9]{3}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[ ]*$\"\r\n                        - var: \"ArrayElementValue\"\r\n                - validator:\r\n                    - \"cls.pii::unionpay\"\r\n                    - var: \"ArrayElementValue\"\r\n            - var: \"column.values\"\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - '>=':\r\n            - var: \"$.ColumnValuesRegexpAndValidator\"\r\n            - 60\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "yin<PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "8bde1f94-d603-45f4-a3c9-dc3f7185df6c", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.nianling", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"年龄（结构化）\"\r\nscores:\r\n- desc: \"列注释匹配 以 年龄 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u5E74\\u9F84\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"列名精确匹配 age，且值 0-100 之间，90% 命中率\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - and:\r\n        - regexp_find:\r\n          - \"^(?i)age$\"\r\n          - var: \"column.name\"\r\n        - \">=\":\r\n            - array_hit_ratio:\r\n                - and:\r\n                    - \">=\":\r\n                        - var: \"ArrayElementValue\"\r\n                        - 0\r\n                    - \"<=\":\r\n                        - var: \"ArrayElementValue\"\r\n                        - 100\r\n                - var: \"column.values\"\r\n            - 90\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "2f0f2abf-b50a-4709-8d8c-09f4e65e174b", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.ch<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"车牌号(大陆)（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 车牌、车牌号 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"\\u8F66\\u724C[\\u53F7]?\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]正则模糊匹配license(允许包含_)plate、plate(允许包含_)number\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"((?i)license[_]?plate)|((?i)plate[_]?number)\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 内置车牌号匹配规则 cls.pii::cardnum (命中率80%)\"\r\n    name: \"FindValidator\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - validator:\r\n                  - \"cls.pii::cardnum\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindValidator\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "ch<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "0607", "levelName": null, "id": "e3211787-c112-4d8b-a315-205f878572b7", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.xingming", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"姓名（结构化）\"\r\nscores:\r\n- desc: \"字段名规则列表\"\r\n  name: \"FindColumnNameList\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - regexp_find:\r\n          - \"^(?i)(insured|insured[_-]?name|receiver|receiver[_-]?name|sender|sender[_-]?name|recipient|recipient[-_]?name|full[_-]?name|first[_-]?name|last[_-]?name|real[_-]?name|staff[_-]?name|candidate|cardholder|cardholder[_-]?name|member[_-]?name|payer[_-]?name|payee[_-]?name|purchaser[_-]?name|trustee[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(bank[_-]?account[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(customer[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(holder[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(contact[s]?[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(patient[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(spouse[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(kin[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(student[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"^(?i)(name[_-]?of|name[_-]?on)\"\r\n          - var: \"column.name\"\r\n- desc: \"列名匹配\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"^(?!.*(org|medicine))(.*(name|er|yee))$|^(insured|initial)$\"\r\n      - var: \"column.name\"\r\n- desc: \"字段注释\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    and:\r\n      - var: \"$.FindColumnName\"\r\n      - regexp_find: # （字符串中包含 姓名 或者 账户名称）同时（不能包含 企 或者 公 或者 人 或者 修改 或者 编辑 或者 变更 或者 创建 或者 操作)\r\n          - \"^(?=.*(\\u59D3\\u540D|\\u8D26\\u6237\\u540D\\u79F0))(?!.*(\\u4F01|\\u516C|\\u4EBA|\\u4FEE\\u6539|\\u7F16\\u8F91|\\u53D8\\u66F4|\\u521B\\u5EFA|\\u64CD\\u4F5C)).*$\"\r\n          - var: \"column.comment\"\r\n- desc: \"反向字段注释\"\r\n  name: \"ReverseFindColumnComment\"\r\n  output: true\r\n  rule:\r\n    and:\r\n      - var: \"$.FindColumnName\"\r\n      - regexp_find: # 品名 或 品名称 或 司名称 或 业名称 或 厂名称 或 商名称 或 社名称 或 机构名称 或 种名称 结尾\r\n          - \"(\\u54C1\\u540D|\\u54C1\\u540D\\u79F0|\\u53F8\\u540D\\u79F0|\\u4E1A\\u540D\\u79F0|\\u5382\\u540D\\u79F0|\\u5546\\u540D\\u79F0|\\u793E\\u540D\\u79F0|\\u673A\\u6784\\u540D\\u79F0|\\u79CD\\u540D\\u79F0)$\"\r\n          - var: \"column.comment\"\r\n- desc: \"NLP 识别\"\r\n  name: \"NlpAnalysis\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.FindColumnNameList\"\r\n      - true\r\n      - var: \"$.FindColumnComment\"\r\n      - true\r\n      - var: \"$.ReverseFindColumnComment\"\r\n      - false\r\n      - '>=':\r\n          - array_hit_ratio:\r\n              - and:\r\n                  - <=:\r\n                      - length:\r\n                          var: \"ArrayElementValue\"\r\n                      - 5\r\n                  - validator:\r\n                      - \"cls.pii::name\"\r\n                      - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 30\r\n- desc: \"表名和列名组合规则\"\r\n  name: \"TableAndColumnCombination\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.NlpAnalysis\"\r\n      - and:\r\n          - regexp_find:\r\n              - \"(?i)(erinfo|yeeinfo)$\"\r\n              - var: \"table.name\"\r\n          - regexp_find:\r\n              - \"(?i)(name)\"\r\n              - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n    - var: \"$.TableAndColumnCombination\"\r\n    - 100\r\n    - 0", "type": "rule"}], "name": "xing<PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "64ed3af4-092f-4728-af78-09bfb15f85aa", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "(?i)(fax|phone|landline)", "type": "catalog"}, {"expr": "^\\s*(86|\\+86|0086)?\\s*-?\\s*(0\\d{2}\\s*-?\\s*\\d{8}(\\s*-?\\s*\\d{1,4})?)\\s*$|^\\s*(0\\d{3}\\s*-?\\s*\\d{7,8}(\\s*-?\\s*\\d{1,4})?)\\s*$", "type": "data"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "e88e721e-4caf-4c70-9790-d96f1de40623", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.weizhi", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"位置（结构化）\"\r\nscores:\r\n- desc: \"列注释以 位置、经度、纬度、经纬、经纬度 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u4F4D\\u7F6E\\\\s*$)|(\\u7ECF[\\u7EAC]?\\u5EA6\\\\s*$)|(\\u7ECF\\u7EAC\\\\s*$)|(\\u7EAC\\u5EA6\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则精确匹配location、latitude、longitude、lng、lat\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(^(?i)location$)|(^(?i)latitude$)|(^(?i)longitude$)|(^(?i)lng$)|(^(?i)lat$)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "we<PERSON>hi", "description": "Updated on 2024.05.25", "levelName": null, "id": "64425bdc-0721-4ca8-becd-fee84162e841", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.youxiang", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"邮箱（结构化）\"\r\nscores:\r\n- desc: \"匹配列注释，以邮箱结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n    - \"\\u90AE[\\u4EF6\\u7BB1](\\u5730\\u5740)?\\\\s*$\"\r\n    - var: \"column.comment\"\r\n- desc: \"匹配列名为 email\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"^(?i)(email|email[_]?address)$\"\r\n      - var: \"column.name\"\r\n- desc: \"匹配 validator\"\r\n  name: \"ColumnValueValidator\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.FindColumnComment\"\r\n      - 100\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - array_hit_ratio:\r\n          - and:\r\n              - regexp_find:\r\n                  - \"^\\\\s*[a-zA-Z0-9.-]+@[a-zA-Z0-9-]+(\\\\.[a-zA-Z0-9-]+)*\\\\.[a-zA-Z0-9]{2,6}\\\\s*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - validator:\r\n                  - \"cls.pii::email\"\r\n                  - var: \"ArrayElementValue\"\r\n          - var: \"column.values\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n    - '>=':\r\n        - var: \"$.ColumnValueValidator\"\r\n        - 30\r\n    - 100\r\n    - 0", "type": "rule"}], "name": "youxiang", "description": "Updated on 2024.05.25", "levelName": null, "id": "97810fb1-7b09-4b36-9606-969ec86734cb", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.zhiye", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"职业（结构化）\"\r\nscores:\r\n- desc: \"列注释以  职业、工作 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u804C\\u4E1A\\\\s*$)|(\\u5DE5\\u4F5C\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"数据字典(命中率90%)\"\r\n  name: \"FindDataDict\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - \">=\":\r\n          - array_hit_ratio:\r\n              - \">\":\r\n                - search_in_dictionary:\r\n                    - \"professions\"\r\n                    - var: \"ArrayElementValue\"\r\n                - 0\r\n              - var: \"column.values\"\r\n          - 90\r\n- desc: \"[字段名]正则精确匹配job、work、occupation、profession\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindDataDict\"\r\n      - regexp_find:\r\n          - \"(^(?i)job$)|(^(?i)work$)|(^(?i)occupation$)|(^(?i)profession[sS]?$)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "zhiye", "description": "Updated on 2024.05.25", "levelName": null, "id": "a3606be5-8162-4fc5-a935-cd773dd1319b", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.riqi", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"日期（结构化）\"\r\nscores:\r\n- desc: \"列注释以 日期、时间 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u65E5\\u671F\\\\s*$)|(\\u65F6\\u95F4\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则匹配date\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(?i)date\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "riqi", "description": "Updated on 2024.05.25", "levelName": null, "id": "288dca19-28c1-43d3-b306-6c054a991764", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.guoji", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"国籍（结构化）\"\r\nscores:\r\n- desc: \"列注释以 国籍 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u56FD\\u7C4D\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则模糊匹配nationality、citizenship\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"((?i)nationality)|((?i)citizenship)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "guoji", "description": "Updated on 2024.05.25", "levelName": null, "id": "947fdf66-8a5a-4650-908d-8d50616586f3", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.xuexing", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"血型（结构化）\"\r\nscores:\r\n- desc: \"列注释以 血型 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u8840\\u578B\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则模糊匹配blood(允许包含_)type\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"((?i)blood[_]?type)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "xuexing", "description": "Updated on 2024.05.25", "levelName": null, "id": "12afb006-8dca-47fd-97e8-a16ff70a7919", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"手机号（中国内地)（结构化）\"\r\nscores:\r\n- desc: \"字段注释\"\r\n  name: \"MobilePhoneColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u624B\\u673A\\u53F7[\\u7801]?\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"字段名匹配 以 id 结尾\"\r\n  name: \"ColumnNameEndWithId\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(?i)id$\"\r\n      - var: \"column.name\"\r\n- desc: \"计算列的手机号正则命中率\"\r\n  name: \"ColumnValueRegexpFind\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.MobilePhoneColumnComment\"\r\n      - 100\r\n      - var: \"$.ColumnNameEndWithId\"\r\n      - 0\r\n      - array_hit_ratio:\r\n          - and:\r\n              - <=:\r\n                  - length:\r\n                      var: \"ArrayElementValue\"\r\n                  - 20\r\n              - regexp_find:\r\n                  - \"^\\\\s*([+]?\\\\s*86|0086)?\\\\s*[-]?\\\\s*((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(16[2|5-7])|(17[0-8])|(18[0-9])|(19[0-3|5-9]))\\\\d{8}\\\\s*$\"\r\n                  - var: \"ArrayElementValue\"\r\n          - var: \"column.values\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    'condition':\r\n      - '>=':\r\n          - var: \"$.ColumnValueRegexpFind\"\r\n          - 30\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "4f8b469b-719d-4e37-9289-c64a51bf2bb7", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.URL", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"URL（结构化）\"\r\nscores:\r\n  - desc: \"列注释精确匹配url\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"^\\\\s*(?i)url\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]精确匹配url\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"^(?i)url$\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 满足正则匹配规则\"\r\n    name: \"FindColumnValue\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - regexp_find:\r\n                  - \"(?i)^(ht|f)tp(s?)\\\\:\\\\/\\\\/[0-9a-zA-Z]([-.\\\\w]*[0-9a-zA-Z])*(:(0-9)*)*(\\\\/?)([a-zA-Z0-9\\\\-\\\\.\\\\?\\\\,\\\\'\\\\/\\\\\\\\\\\\+&amp;%\\\\$#_]*)?[ ]*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnValue\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "URL", "description": "Updated on 2024.05.25", "levelName": null, "id": "31b7d4f2-adfc-4002-abf5-58c84114a5c5", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.guojia", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"国家（结构化）\"\r\nscores:\r\n- desc: \"列注释以 国家 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u56FD\\u5BB6\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"数据字典(命中率90%)\"\r\n  name: \"FindDataDict\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - \">=\":\r\n          - array_hit_ratio:\r\n              - \">\":\r\n                - search_in_dictionary:\r\n                    - \"countries\"\r\n                    - var: \"ArrayElementValue\"\r\n                - 0\r\n              - var: \"column.values\"\r\n          - 90\r\n- desc: \"[字段名]精确匹配country、nation 或 以 country(允许包含_)of、nation(允许包含_)of\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindDataDict\"\r\n      - regexp_find:\r\n          - \"(^(?i)country$)|(^(?i)nation$)|((?i)country[_]?of)|((?i)nation[_]?of)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "guojia", "description": "Updated on 2024.05.25", "levelName": null, "id": "0c87ef1d-57ab-41d1-8766-0fc89cf92900", "category": null}, {"level": null, "dataTag": "1.tongyongmobanv2.IPv6", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"IPV6（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 ipv6 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"(?i)ipv6\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]模糊匹配ipv6\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"(?i)ipv6\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 满足正则匹配规则\"\r\n    name: \"FindColumnValue\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - regexp_find:\r\n                  - \"^(:{2}(/0)?)|((([a-fA-F0-9]{1,4}|):){3,7}([a-fA-F0-9]{1,4}|:)[ ]*)$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnValue\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "IPv6", "description": "Updated on 2024.05.25", "levelName": null, "id": "396449f2-56fc-4d8b-aa1d-be023b30462a", "category": null}, {"level": null, "dataTag": "1.mingan<PERSON><PERSON><PERSON><PERSON>qi<PERSON>.shengri", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"生日（结构化）\"\r\nscores:\r\n- desc: \"列注释 以 生日 、 出生日期 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"([\\u51FA]?\\u751F\\u65E5[\\u671F]?\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则精确匹配 birth、模糊匹配birthday、date(允许包含_)of(允许包含_) birth\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(^(?i)birth$)|((?i)birthday)|((?i)date[_]?of[_]?birth)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "891c334f-f3f1-44c4-8559-cc6d2e57d4d2", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.dizhi", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"地址（结构化）\"\r\nscores:\r\n- desc: \"列名匹配 address 或者 addr\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(?i)address|(?i)addr\"\r\n      - var: \"column.name\"\r\n- desc: \"列定义长度 大于 20\"\r\n  name: \"ColumnDefLength\"\r\n  output: true\r\n  rule:\r\n    and:\r\n    - var: \"$.FindColumnName\"\r\n    - '>=':\r\n      - var: \"column.size\"\r\n      - 20\r\n- desc: \"字段注释 包含 住址 or 家庭地址 or 办公地址 or 联系地址\"\r\n  name: \"FieldCommentsContains\"\r\n  output: true\r\n  rule:\r\n    and:\r\n    - var: \"$.ColumnDefLength\"\r\n    - regexp_find:\r\n        - \"\\u4f4f\\u5740|\\u5bb6\\u5ead\\u5730\\u5740|\\u529e\\u516c\\u5730\\u5740|\\u8054\\u7cfb\\u5730\\u5740\"\r\n        - var: \"column.comment\"\r\n- desc: \"NLP 识别地址。以上条件都满足或者是 NLP 识别率达到 30%\"\r\n  name: \"NlpAnalysis\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FieldCommentsContains\"\r\n      - and:\r\n        - var: \"$.ColumnDefLength\"\r\n        - '>=':\r\n            - array_hit_ratio:\r\n                - validator:\r\n                    - \"cls.piilinkable::address\"\r\n                    - var: \"ArrayElementValue\"\r\n                - var: \"column.values\"\r\n            - 30\r\n- desc: \"NLP 如果不满足，继续 字段内容 匹配规则列表\"\r\n  name: \"RegexpListFind\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.NlpAnalysis\"\r\n      - and:\r\n          - var: \"$.ColumnDefLength\"\r\n          - '>=':\r\n              - array_hit_ratio:\r\n                  - or: # 包含 小区|单元|号楼|大厦|家园|新区|村委会|公安局|派出所|街道办|公寓|厂区\r\n                      - regexp_find:\r\n                          - \"\\u5c0f\\u533a|\\u5355\\u5143|\\u53f7\\u697c|\\u5927\\u53a6|\\u5bb6\\u56ed|\\u65b0\\u533a|\\u6751\\u59d4\\u4f1a|\\u516c\\u5b89\\u5c40|\\u6d3e\\u51fa\\u6240|\\u8857\\u9053\\u529e|\\u516c\\u5bd3|\\u5382\\u533a\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 以 3位数字+室or房 结尾\r\n                          - \"\\\\d{1,3}\\\\s*[\\u5BA4\\u623F]$\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 市 and 区 and （1位数字+号），市区两字不可紧相邻\r\n                          - \"\\u5E02(\\\\s*[^\\\\s\\u5E02\\u533A\\\\d])+\\\\s*\\u533A(\\\\s*[^\\\\s\\u5E02\\u533A\\u53F7])+\\\\d*\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 村 and 号，之间有至少一位值\r\n                          - \"\\u6751(\\\\s*[^\\\\s\\u6751\\u53F7])+\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 市 and 村，之间有至少一位值\r\n                          - \"\\u5E02(\\\\s*[^\\\\s\\u5E02\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 区 and 村，之间有至少一位值\r\n                          - \"\\u533A(\\\\s*[^\\\\s\\u533A\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 镇 and 村，之间有至少一位值\r\n                          - \"\\u9547(\\\\s*[^\\\\s\\u9547\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 县 and 村，之间有至少一位值\r\n                          - \"\\u53BF(\\\\s*[^\\\\s\\u53BF\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 巷 and 号，之间有至少一位值\r\n                          - \"\\u5DF7(\\\\s*[^\\\\s\\u5DF7\\u53F7])+\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 包含 任意位数字+栋+任意位数字\r\n                          - \"\\\\d+\\\\s*\\u680B\\\\s*\\\\d+\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 社区 and 号，之间有至少一位值\r\n                          - \"\\u793E\\u533A(\\\\s*[^\\\\s\\u793E\\u533A\\u53F7])+\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 以 局 结尾\r\n                          - \"\\u5C40\\\\s*$\"\r\n                          - var: \"ArrayElementValue\"\r\n                  - var: \"column.values\"\r\n              - 30\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.RegexpListFind\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "dizhi", "description": null, "levelName": null, "id": "b8ee29ac-e82c-4935-8f38-31143ecc96bb", "category": null}, {"level": null, "dataTag": "1.minganshu<PERSON>biaoqian.cfigureshifting", "builtin": false, "patterns": [{"expr": "c_figure_shifting", "type": "catalog"}], "name": "cfigureshifting", "description": "", "levelName": null, "id": "c52cb968-6543-4a1d-975d-ddc8ef8ce0b8", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.cidcard", "builtin": false, "patterns": [{"expr": "c_id_card", "type": "catalog"}], "name": "cidcard", "description": "", "levelName": null, "id": "0b67fa42-6834-46c6-b6b9-3912b3e118f6", "category": null}, {"level": null, "dataTag": "1.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": false, "patterns": [{"expr": "{     \"model\": \"1.0\",     \"desc\": \"座机号码（中国内地)（结构化）\",     \"scores\": [         {             \"desc\": \"匹配座机号码（中国内地)； 列名匹配 && 内容正则匹配 && 内容长度限制\",             \"name\": \"LandlineHitRatio\",             \"output\": true,             \"rule\": {                 \"array_hit_ratio\": [                     {                         \"and\": [                             {                                 \"<=\": [                                     {                                         \"length\": {                                             \"var\": \"ArrayElementValue\"                                         }                                     },                                     20                                 ]                             },                             {                                 \"regexp_find\": [                                     \"(?i)(fax|phone|tel)\",                                     {                                         \"var\": \"column.name\"                                     }                                 ]                             },                             {                                 \"regexp_find\": [                                     \"((^0\\\\d{2}[-]?\\\\d{8}([-]?\\\\d{1,4})?)|(^0\\\\d{3}[-]?\\\\d{7,8}([-]?\\\\d{1,4})?))[ ]*$\",                                     {                                         \"var\": \"ArrayElementValue\"                                     }                                 ]                             }                         ]                     },                     {                         \"var\": \"column.values\"                     }                 ]             }         },         {             \"desc\": \"命中率必须大于等于 30\",             \"name\": \"CalculateHitRatio\",             \"output\": true,             \"rule\": {                 \">=\": [                     {                         \"var\": \"$.LandlineHitRatio\"                     },                     30                 ]             }         },         {             \"desc\": \"计算得分\",             \"name\": \"OverallScore\",             \"output\": true,             \"rule\": {                 \"condition\": [                     {                         \"var\": \"$.CalculateHitRatio\"                     },                     80,                     0                 ]             }         }     ] }", "type": "rule"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "20549da9-94c7-4b73-90f1-cb0d21676e90", "category": null}, {"level": null, "dataTag": "1.min<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.you<PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"邮编（结构化）\"\r\nscores:\r\n- desc: \"列注释以 邮编 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u90AE\\u7F16\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则精确匹配postal、postcode、postalcode、zipcode 模糊匹配 (允许包含_)of\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(^(?i)postal$)|(^(?i)postcode$)|(^(?i)postalcode$)|(^(?i)zipcode$)|((?i)postal[_]?of)|((?i)postcode[_]?of)|((?i)postalcode[_]?of)|((?i)zipcode[_]?of)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "57bab41a-1944-4bb0-bcde-ccce24210189", "category": null}, {"level": null, "dataTag": "1.mingan<PERSON><PERSON><PERSON><PERSON>qi<PERSON>.SwiftCode", "builtin": true, "patterns": [{"expr": "^[A-Za-z]{4}(?:GB|US|DE|RU|CA|JP|CN)[0-9a-zA-Z]{2,5}[ ]*$", "type": "data"}], "name": "SwiftCode", "description": null, "levelName": null, "id": "9285e1f7-5fb1-4ddd-a91e-a9d381ea6460", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.WebURL", "builtin": true, "patterns": [{"expr": "(?i)web.?url", "type": "catalog"}], "name": "WebURL", "description": null, "levelName": null, "id": "48eb61a8-ca03-416a-b126-830a90ad057e", "category": null}, {"level": null, "dataTag": "1.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.salary", "builtin": false, "patterns": [{"expr": "salary", "type": "catalog"}], "name": "salary", "description": "", "levelName": null, "id": "b0f820a6-2213-4668-9fb2-fe757b144d7e", "category": null}, {"level": null, "dataTag": "1.minganshu<PERSON>biaoqian.cshelterspeccharbef", "builtin": false, "patterns": [{"expr": "c_shelter_spec_char_bef", "type": "catalog"}], "name": "cshelterspeccharbef", "description": "", "levelName": null, "id": "edbcae56-1628-4204-8478-4ba12dd4a583", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.MAC", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"MAC（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 mac 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"(?i)mac\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]精确匹配mac\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"^(?i)mac$\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 满足正则匹配规则\"\r\n    name: \"FindColumnValue\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - regexp_find:\r\n                  - \"^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})|([0-9a-fA-F]{4}\\\\\\\\.[0-9a-fA-F]{4}\\\\\\\\.[0-9a-fA-F]{4})[ ]*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnValue\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "MAC", "description": null, "levelName": null, "id": "22465fe0-d273-451b-b913-8de38ea0ae21", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.ckeepfromntom", "builtin": false, "patterns": [{"expr": "c_keep_from_n_to_m", "type": "catalog"}], "name": "ckeepfromntom", "description": "", "levelName": null, "id": "8963e551-e87c-42b1-80d9-186df51b8c28", "category": null}, {"level": null, "dataTag": "1.min<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.xingbie", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"性别（结构化）\"\r\nscores:\r\n- desc: \"列注释匹配以 性别 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u6027\\u522B\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"数据字典(命中率90%)\"\r\n  name: \"FindDataDict\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - \">=\":\r\n          - array_hit_ratio:\r\n              - \">\":\r\n                - search_in_dictionary:\r\n                    - \"gender\"\r\n                    - var: \"ArrayElementValue\"\r\n                - 0\r\n              - var: \"column.values\"\r\n          - 90\r\n- desc: \"[字段名]正则精确匹配gender、sexual、sex\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindDataDict\"\r\n      - regexp_find:\r\n          - \"(^(?i)gender$)|(^(?i)sexual$)|(^(?i)sex$)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "3dae0bcf-ff7b-44eb-8165-26e71673b493", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.jiedao", "builtin": true, "patterns": [{"expr": "(?i)street", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "a0a61644-4186-4898-bf36-1ea86811b59a", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.zidingyicolumn", "builtin": false, "patterns": [{"expr": "^column_\\d+$", "type": "catalog"}], "name": "zidingyicolumn", "description": "", "levelName": null, "id": "75067fd6-7c91-47fc-9eb8-8d62ade6dc99", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.mima", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"密码（结构化）\"\r\nscores:\r\n  - desc: \"列注释匹配以 密码 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"\\u5BC6\\u7801\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]正则精确匹配password、passcode、passphrase\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"(^(?i)password$)|(^(?i)passcode$)|(^(?i)passphrase$)\"\r\n            - var: \"column.name\"\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnName\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "mima", "description": null, "levelName": null, "id": "57b9d901-7753-49d4-86fe-16f1cba1c230", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.shoujihaoencDW", "builtin": false, "patterns": [{"expr": "login_name_enc", "type": "catalog"}], "name": "shoujihaoencDW", "description": "", "levelName": null, "id": "e338568e-8a14-497e-ad82-0542ce460daf", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.xueya", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"血压（结构化）\"\r\nscores:\r\n- desc: \"列注释匹配 以 血压 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u8840\\u538B\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则模糊匹配blood(允许包含_)pressure 且 内容校验在 20-220 之间的整数(命中率90%)\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - and:\r\n        - regexp_find:\r\n          - \"(?i)blood[_]?pressure\"\r\n          - var: \"column.name\"\r\n        - \">=\":\r\n            - array_hit_ratio:\r\n                - and:\r\n                    - \">=\":\r\n                        - var: \"ArrayElementValue\"\r\n                        - 20\r\n                    - \"<=\":\r\n                        - var: \"ArrayElementValue\"\r\n                        - 220\r\n                - var: \"column.values\"\r\n            - 90\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "x<PERSON>ya", "description": null, "levelName": null, "id": "e0253c4d-e8d1-4f12-86f2-8b1e6d350c82", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.IPv4", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"IPV4（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 ip、ipv4 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"((?i)ipv4\\\\s*$)|((?i)ip\\\\s*$)\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]精确匹配ip、ipv4\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"(^(?i)ip$)|(^(?i)ipv4$)\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 满足正则匹配规则\"\r\n    name: \"FindColumnValue\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - regexp_find:\r\n                  - \"^(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\\\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])[ ]*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnValue\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "IPv4", "description": null, "levelName": null, "id": "2c3d22c9-14ea-415d-98b2-86dbcbe159b0", "category": null}, {"level": null, "dataTag": "1.mingan<PERSON><PERSON><PERSON><PERSON>qi<PERSON>.maskcolumn", "builtin": false, "patterns": [{"expr": "^mask_column$", "type": "catalog"}], "name": "maskcolumn", "description": "", "levelName": null, "id": "d1e0bd5a-ab39-4453-b818-151f9815e5fe", "category": null}, {"level": null, "dataTag": "1.minganshu<PERSON>biaoqian.cregexreplace", "builtin": false, "patterns": [{"expr": "c_regex_replace", "type": "catalog"}], "name": "cregexre<PERSON>", "description": "", "levelName": null, "id": "27860f06-e975-4261-a27b-acff8c70c662", "category": null}, {"level": null, "dataTag": "1.minganshu<PERSON><PERSON><PERSON>qi<PERSON>.youxiangnew", "builtin": false, "patterns": [{"expr": "{   \"model\" : \"1.0\",   \"desc\" : \"邮箱\",   \"scores\" : [ {     \"desc\" : \"匹配邮箱； 内容正则匹配 && 内容校验\",     \"name\" : \"HitRatio\",     \"rule\" : {       \"array_hit_ratio\" : [ {         \"and\" : [ {           \"regexp_find\" : [ \"^[a-zA-Z0-9._%-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,4}[ ]*$\", {             \"var\" : \"ArrayElementValue\"           }]         }, {           \"validator\" : [ \"cls.pii::email\", {             \"var\" : \"ArrayElementValue\"           } ]         } ]       }, {         \"var\" : \"column.values\"       } ]     },     \"output\" : true   }, {     \"desc\" : \"命中率必须大于等于 40\",     \"name\" : \"OverallScore\",     \"rule\" : {       \">=\" : [ {         \"var\" : \"$.HitRatio\"       }, 40 ]     },     \"output\" : true   } ] }", "type": "rule"}], "name": "youxiangnew", "description": "", "levelName": null, "id": "a9c7d3fb-1d0d-432e-a08c-2a1434bf927e", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.ddddccc", "builtin": false, "patterns": [{"expr": "(?i)city", "type": "data"}], "name": "ddddccc", "description": "dddddccc", "levelName": null, "id": "9d1bed12-dad1-4d3b-a6fb-d79c3d4b33bb", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.cbankcard", "builtin": false, "patterns": [{"expr": "c_bank_card", "type": "catalog"}], "name": "cbankcard", "description": "", "levelName": null, "id": "2ab64308-4173-47d3-bfa3-510d6d7a42c6", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.yinlianka", "builtin": true, "patterns": [{"expr": "^62[0-5][0-9]{13,16}[ ]*$", "type": "data"}], "name": "yin<PERSON><PERSON>", "description": null, "levelName": null, "id": "********-460d-464e-abba-3c32810c6f44", "category": null}, {"level": null, "dataTag": "1.mingan<PERSON><PERSON><PERSON><PERSON>qi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"身份证（中国内地）（结构化）\"\r\nscores:\r\n- desc: \"内容长度限制必须大于等于 18\"\r\n  name: \"IdCardLength\"\r\n  output: true\r\n  rule:\r\n    \">=\":\r\n      - var: \"column.size\"\r\n      - 18\r\n- desc: \"注释以身份证号结尾\"\r\n  name: \"ColumnComment\"\r\n  output: true\r\n  rule:\r\n    and:\r\n      - var: \"$.IdCardLength\"\r\n      - regexp_find:\r\n          - \"\\u8EAB\\u4EFD\\u8BC1\\u53F7$\"\r\n          - var: \"column.comment\"\r\n- desc: \"如果注释匹配，直接返回，否则计算命中率。\"\r\n  name: \"RegexpFindAndValidator\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.ColumnComment\"\r\n      - 100\r\n      - array_hit_ratio:\r\n          - and:\r\n            - '>=':\r\n                - length:\r\n                  - var: \"ArrayElementValue\"\r\n                - 18\r\n            - regexp_find:\r\n                - \"(([1][1-5])|([2][1-3])|([3][1-7])|([4][1-6])|([5][0-4])|([6][1-5])|([7][1])|([8][1-2]))\\\\d{4}(([1][9]\\\\d{2})|([2]\\\\d{3}))(([0][1-9])|([1][0-2]))(([0][1-9])|([1-2][0-9])|([3][0-1]))\\\\d{3}[0-9xX]\\\\s*\"\r\n                - var: \"ArrayElementValue\"\r\n            - validator:\r\n                - \"cls.pii::idcard\"\r\n                - var: \"ArrayElementValue\"\r\n          - var: \"column.values\"\r\n- desc: \"命中率必须大于等于 30\"\r\n  name: \"CalculateHitRatio\"\r\n  output: true\r\n  rule:\r\n    '>=':\r\n      - var: \"$.RegexpFindAndValidator\"\r\n      - 30\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    'condition':\r\n    - var: \"$.CalculateHitRatio\"\r\n    - 100\r\n    - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "d722efdf-fbcd-474b-a80e-91ac2f7c31ff", "category": null}, {"level": null, "dataTag": "1.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.cdonothing", "builtin": false, "patterns": [{"expr": "c_do_nothing", "type": "catalog"}], "name": "cdonothing", "description": "", "levelName": null, "id": "cab187f6-2b1a-4556-b514-7937ee5c2203", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.ckeepfrontnrearm", "builtin": false, "patterns": [{"expr": "c_keep_front_n_rear_m", "type": "catalog"}], "name": "ckeepfrontnrearm", "description": "", "levelName": null, "id": "ca205109-4cc1-466b-99bc-1dc028815d2f", "category": null}, {"level": null, "dataTag": "1.mingan<PERSON><PERSON><PERSON><PERSON>qi<PERSON>.VISAka", "builtin": true, "patterns": [{"expr": "^4[0-9]{3}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[ ]*$", "type": "data"}], "name": "VISAka", "description": null, "levelName": null, "id": "e8518254-bbda-46e1-9216-6854162d20c4", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.zongjiao", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"宗教（结构化）\"\r\nscores:\r\n- desc: \"列注释以 宗教、信仰 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u5B97\\u6559\\\\s*$)|(\\u4FE1\\u4EF0\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"数据字典(命中率90%)\"\r\n  name: \"FindDataDict\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - \">=\":\r\n          - array_hit_ratio:\r\n              - \">\":\r\n                - search_in_dictionary:\r\n                    - \"religions\"\r\n                    - var: \"ArrayElementValue\"\r\n                - 0\r\n              - var: \"column.values\"\r\n          - 90\r\n- desc: \"[字段名]正则精确匹配 faith 或 精确匹配religion 或 模糊匹配religion(允许包含_)of \"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindDataDict\"\r\n      - regexp_find:\r\n          - \"(^(?i)faith$)|(^(?i)religion$)|((?i)religion[_]?of)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON>jiao", "description": null, "levelName": null, "id": "a8a927b4-3598-4670-a9f4-b1d7c598571e", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.guojiyinxingzhanghao", "builtin": true, "patterns": [{"expr": "^[a-zA-Z]{2}[0-9]{2}[a-zA-Z0-9]{4}[0-9]{7}([a-zA-Z0-9]?){0,16}[ ]*$", "type": "data"}], "name": "guojiyinxingzhanghao", "description": null, "levelName": null, "id": "ca9d6500-99a0-48fa-8e6c-ff2ab4610dd5", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.cchinesename", "builtin": false, "patterns": [{"expr": "c_chinese_name", "type": "catalog"}], "name": "cchinesename", "description": "", "levelName": null, "id": "08a91241-815c-4cef-9db1-ce3c26c3cb3c", "category": null}, {"level": null, "dataTag": "1.minganshu<PERSON>biaoqi<PERSON>.danwei", "builtin": true, "patterns": [{"expr": "(?i)affiliation", "type": "catalog"}], "name": "danwei", "description": null, "levelName": null, "id": "c519e925-7a3e-4cde-b210-8da374eb0ebb", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.xinlv", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"心率（结构化）\"\r\nscores:\r\n  - desc: \"列注释匹配 以 心率 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"\\u5FC3\\u7387\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]正则模糊匹配heart (允许包含_)rate 且 内容校验是在40-180之间的整数(命中率90%)\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - and:\r\n            - regexp_find:\r\n                - \"(?i)heart[_]?rate\"\r\n                - var: \"column.name\"\r\n            - \">=\":\r\n                - array_hit_ratio:\r\n                    - and:\r\n                        - \">=\":\r\n                            - var: \"ArrayElementValue\"\r\n                            - 40\r\n                        - \"<=\":\r\n                            - var: \"ArrayElementValue\"\r\n                            - 180\r\n                    - var: \"column.values\"\r\n                - 90\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnName\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "xinlv", "description": null, "levelName": null, "id": "c119a9aa-893a-4bfc-b7d4-9163590f2b73", "category": null}, {"level": null, "dataTag": "1.mingan<PERSON><PERSON><PERSON><PERSON>qi<PERSON>.y<PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"银行卡（结构化）\"\r\nscores:\r\n- desc: \"列注释以 银行卡号 或 银行账号 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u94F6\\u884C[\\u5361\\u8D26][\\u53F7]?\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"字段名匹配\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"^(?i)bank[_-]?accno$|^(?i)bank[_-]?account$|^(?i)bank[_-]?accno$|^(?i)bank[_-]?card[_-]?no$|^(?i)bank[_-]?card[_-]?number$\"\r\n      - var: \"column.name\"\r\n- desc: \"正则+校验\"\r\n  name: \"ColumnValuesRegexpAndValidator\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.FindColumnComment\"\r\n      - 100\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - array_hit_ratio:\r\n          - and:\r\n              - regexp_find:\r\n                  - \"^\\\\s*\\\\d{10,20}\\\\s*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - regexp_find:\r\n                  - \"^\\\\s*(?!(\\\\d)\\\\1{5}).*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - validator:\r\n                  - \"cls.pii::unionpay\"\r\n                  - var: \"ArrayElementValue\"\r\n          - var: \"column.values\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - '>=':\r\n          - var: \"$.ColumnValuesRegexpAndValidator\"\r\n          - 80\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "yin<PERSON><PERSON><PERSON>", "description": "Update on 2024.04.19", "levelName": null, "id": "70c94511-dfe3-4375-acb4-6b27a1c5ceb0", "category": null}, {"level": null, "dataTag": "1.minganshu<PERSON>biaoqian.nianling", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"年龄（结构化）\"\r\nscores:\r\n- desc: \"列注释匹配 以 年龄 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u5E74\\u9F84\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"列名精确匹配 age，且值 0-100 之间，90% 命中率\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - and:\r\n        - regexp_find:\r\n          - \"^(?i)age$\"\r\n          - var: \"column.name\"\r\n        - \">=\":\r\n            - array_hit_ratio:\r\n                - and:\r\n                    - \">=\":\r\n                        - var: \"ArrayElementValue\"\r\n                        - 0\r\n                    - \"<=\":\r\n                        - var: \"ArrayElementValue\"\r\n                        - 100\r\n                - var: \"column.values\"\r\n            - 90\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "dcf76854-6765-4e41-b111-23e7dea61671", "category": null}, {"level": null, "dataTag": "1.mingan<PERSON><PERSON><PERSON><PERSON>qi<PERSON>.chushengriqi", "builtin": true, "patterns": [{"expr": "(?i)date.?of.?birth", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "a82eb786-fff1-4554-bc8e-6c19014c7387", "category": null}, {"level": null, "dataTag": "1.mingan<PERSON><PERSON><PERSON><PERSON>qi<PERSON>.chepai<PERSON>dalu", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"车牌号(大陆)（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 车牌、车牌号 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"\\u8F66\\u724C[\\u53F7]?\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]正则模糊匹配license(允许包含_)plate、plate(允许包含_)number\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"((?i)license[_]?plate)|((?i)plate[_]?number)\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 内置车牌号匹配规则 cls.pii::cardnum (命中率80%)\"\r\n    name: \"FindValidator\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - validator:\r\n                  - \"cls.pii::cardnum\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindValidator\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "ch<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "e4b5d6d6-64fd-4013-8231-a733db931706", "category": null}, {"level": null, "dataTag": "1.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.xingming", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"姓名（结构化）\"\r\nscores:\r\n- desc: \"字段名规则列表\"\r\n  name: \"FindColumnNameList\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - regexp_find:\r\n          - \"^(?i)(insured|insured[_-]?name|receiver|receiver[_-]?name|sender|sender[_-]?name|recipient|recipient[-_]?name|full[_-]?name|first[_-]?name|last[_-]?name|real[_-]?name|staff[_-]?name|candidate|cardholder|cardholder[_-]?name|member[_-]?name|payer[_-]?name|payee[_-]?name|purchaser[_-]?name|trustee[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(bank[_-]?account[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(customer[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(holder[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(contact[s]?[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(patient[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(spouse[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(kin[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(student[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"^(?i)(name[_-]?of|name[_-]?on)\"\r\n          - var: \"column.name\"\r\n- desc: \"列名匹配\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"^(?!.*(org|medicine))(.*(name|er|yee))$|^(insured|initial)$\"\r\n      - var: \"column.name\"\r\n- desc: \"字段注释\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    and:\r\n      - var: \"$.FindColumnName\"\r\n      - regexp_find: # （字符串中包含 姓名 或者 账户名称）同时（不能包含 企 或者 公 或者 人 或者 修改 或者 编辑 或者 变更 或者 创建 或者 操作)\r\n          - \"^(?=.*(\\u59D3\\u540D|\\u8D26\\u6237\\u540D\\u79F0))(?!.*(\\u4F01|\\u516C|\\u4EBA|\\u4FEE\\u6539|\\u7F16\\u8F91|\\u53D8\\u66F4|\\u521B\\u5EFA|\\u64CD\\u4F5C)).*$\"\r\n          - var: \"column.comment\"\r\n- desc: \"反向字段注释\"\r\n  name: \"ReverseFindColumnComment\"\r\n  output: true\r\n  rule:\r\n    and:\r\n      - var: \"$.FindColumnName\"\r\n      - regexp_find: # 品名 或 品名称 或 司名称 或 业名称 或 厂名称 或 商名称 或 社名称 或 机构名称 或 种名称 结尾\r\n          - \"(\\u54C1\\u540D|\\u54C1\\u540D\\u79F0|\\u53F8\\u540D\\u79F0|\\u4E1A\\u540D\\u79F0|\\u5382\\u540D\\u79F0|\\u5546\\u540D\\u79F0|\\u793E\\u540D\\u79F0|\\u673A\\u6784\\u540D\\u79F0|\\u79CD\\u540D\\u79F0)$\"\r\n          - var: \"column.comment\"\r\n- desc: \"NLP 识别\"\r\n  name: \"NlpAnalysis\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.FindColumnNameList\"\r\n      - true\r\n      - var: \"$.FindColumnComment\"\r\n      - true\r\n      - var: \"$.ReverseFindColumnComment\"\r\n      - false\r\n      - '>=':\r\n          - array_hit_ratio:\r\n              - and:\r\n                  - <=:\r\n                      - length:\r\n                          var: \"ArrayElementValue\"\r\n                      - 5\r\n                  - validator:\r\n                      - \"cls.pii::name\"\r\n                      - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 30\r\n- desc: \"表名和列名组合规则\"\r\n  name: \"TableAndColumnCombination\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.NlpAnalysis\"\r\n      - and:\r\n          - regexp_find:\r\n              - \"(?i)(erinfo|yeeinfo)$\"\r\n              - var: \"table.name\"\r\n          - regexp_find:\r\n              - \"(?i)(name)\"\r\n              - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n    - var: \"$.TableAndColumnCombination\"\r\n    - 100\r\n    - 0", "type": "rule"}], "name": "xing<PERSON>", "description": null, "levelName": null, "id": "56699149-e1df-4459-8d2b-401ba5453bd7", "category": null}, {"level": null, "dataTag": "1.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.wans<PERSON><PERSON>a", "builtin": true, "patterns": [{"expr": "^5[1-5][0-9]{2}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[ ]*$", "type": "data"}], "name": "wans<PERSON><PERSON>a", "description": null, "levelName": null, "id": "cf5e0346-3a14-4639-b4b9-dd78db866e8c", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.shoujihaomaskDW", "builtin": false, "patterns": [{"expr": "login_name_mask", "type": "catalog"}], "name": "shoujihaomaskDW", "description": "", "levelName": null, "id": "969c2f5b-e65b-477c-8c83-4bcfc1cb9a70", "category": null}, {"level": null, "dataTag": "1.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "(^(86|\\+86|0086)?[-]?((0\\d{2}[-]?\\d{8}([-]?\\d{1,4})?)|(0\\d{3}[-]?\\d{7,8}([-]?\\d{1,4})?))|(^((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(16[2|5-7])|(17[0-8])|(18[0,5-9])|(19[0-3|5-9]))\\d{8}))[ ]*$", "type": "data"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "60f8309f-00ff-4852-acd2-c7f9e1801176", "category": null}, {"level": null, "dataTag": "1.minganshu<PERSON>biaoqian.weizhi", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"位置（结构化）\"\r\nscores:\r\n- desc: \"列注释以 位置、经度、纬度、经纬、经纬度 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u4F4D\\u7F6E\\\\s*$)|(\\u7ECF[\\u7EAC]?\\u5EA6\\\\s*$)|(\\u7ECF\\u7EAC\\\\s*$)|(\\u7EAC\\u5EA6\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则精确匹配location、latitude、longitude、lng、lat\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(^(?i)location$)|(^(?i)latitude$)|(^(?i)longitude$)|(^(?i)lng$)|(^(?i)lat$)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "we<PERSON>hi", "description": null, "levelName": null, "id": "a01fdac6-aab8-4a72-a58c-26f3c83c6c41", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.cemail", "builtin": false, "patterns": [{"expr": "c_email", "type": "catalog"}], "name": "cemail", "description": "", "levelName": null, "id": "919598a7-d5a5-4c51-b965-286df8ad1721", "category": null}, {"level": null, "dataTag": "1.mingan<PERSON><PERSON><PERSON><PERSON>qi<PERSON>.youxiang", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"邮箱（结构化）\"\r\nscores:\r\n- desc: \"匹配列注释，以邮箱结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n    - \"\\u90AE[\\u4EF6\\u7BB1](\\u5730\\u5740)?\\\\s*$\"\r\n    - var: \"column.comment\"\r\n- desc: \"匹配列名为 email\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"^(?i)(email|email[_]?address)$\"\r\n      - var: \"column.name\"\r\n- desc: \"匹配 validator\"\r\n  name: \"ColumnValueValidator\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.FindColumnComment\"\r\n      - 100\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - array_hit_ratio:\r\n          - and:\r\n              - regexp_find:\r\n                  - \"^\\\\s*[a-zA-Z0-9.-]+@[a-zA-Z0-9-]+(\\\\.[a-zA-Z0-9-]+)*\\\\.[a-zA-Z0-9]{2,6}\\\\s*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - validator:\r\n                  - \"cls.pii::email\"\r\n                  - var: \"ArrayElementValue\"\r\n          - var: \"column.values\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n    - '>=':\r\n        - var: \"$.ColumnValueValidator\"\r\n        - 30\r\n    - 100\r\n    - 0", "type": "rule"}], "name": "youxiang", "description": null, "levelName": null, "id": "b42987ab-7cc0-4a38-9971-f8e02979c152", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.huanzheID", "builtin": true, "patterns": [{"expr": "(?i)patient.?id", "type": "catalog"}], "name": "huanzheID", "description": null, "levelName": null, "id": "8461385b-ebe4-4f82-a827-2112908a2980", "category": null}, {"level": null, "dataTag": "1.mingan<PERSON><PERSON><PERSON><PERSON>qi<PERSON>.cshelterfromntom", "builtin": false, "patterns": [{"expr": "c_shelter_from_n_to_m", "type": "catalog"}], "name": "cshelterfromntom", "description": "", "levelName": null, "id": "723435ba-eb2b-4783-81f7-165545a45a9c", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.xinyongka", "builtin": true, "patterns": [{"expr": "^[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[ ]*$", "type": "data"}], "name": "xinyongka", "description": null, "levelName": null, "id": "77266c17-c490-4cbc-bb4f-de46e0426343", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.zhiye", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"职业（结构化）\"\r\nscores:\r\n- desc: \"列注释以  职业、工作 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u804C\\u4E1A\\\\s*$)|(\\u5DE5\\u4F5C\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"数据字典(命中率90%)\"\r\n  name: \"FindDataDict\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - \">=\":\r\n          - array_hit_ratio:\r\n              - \">\":\r\n                - search_in_dictionary:\r\n                    - \"professions\"\r\n                    - var: \"ArrayElementValue\"\r\n                - 0\r\n              - var: \"column.values\"\r\n          - 90\r\n- desc: \"[字段名]正则精确匹配job、work、occupation、profession\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindDataDict\"\r\n      - regexp_find:\r\n          - \"(^(?i)job$)|(^(?i)work$)|(^(?i)occupation$)|(^(?i)profession[sS]?$)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "zhiye", "description": null, "levelName": null, "id": "c9f4ef6c-bc6c-4f4f-a0b7-e3e493c0ad1d", "category": null}, {"level": null, "dataTag": "1.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.cmobile", "builtin": false, "patterns": [{"expr": "c_mobile", "type": "catalog"}], "name": "cmobile", "description": "", "levelName": null, "id": "ae3d14a6-5957-4e3f-9868-1b344db986dd", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.riqi", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"日期（结构化）\"\r\nscores:\r\n- desc: \"列注释以 日期、时间 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u65E5\\u671F\\\\s*$)|(\\u65F6\\u95F4\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则匹配date\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(?i)date\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "riqi", "description": null, "levelName": null, "id": "dcf4f531-a90c-4457-98d1-f68102b712b0", "category": null}, {"level": null, "dataTag": "1.mingan<PERSON><PERSON><PERSON><PERSON>qi<PERSON>.yong<PERSON>ing", "builtin": true, "patterns": [{"expr": "(?i)user.?name", "type": "catalog"}], "name": "yonghuming", "description": null, "levelName": null, "id": "06b03b46-fcee-407c-bc53-91779c8dd212", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.zhaopian", "builtin": true, "patterns": [{"expr": "(?i)photo", "type": "catalog"}], "name": "zhao<PERSON>", "description": null, "levelName": null, "id": "81cb194c-196a-4545-991f-12304e0e62dd", "category": null}, {"level": null, "dataTag": "1.mingan<PERSON><PERSON><PERSON><PERSON>qi<PERSON>.cshelterfrontnrearm", "builtin": false, "patterns": [{"expr": "c_shelter_front_n_rear_m", "type": "catalog"}], "name": "cshelterfrontnrearm", "description": "", "levelName": null, "id": "7427606b-31f6-436f-ace3-266b4a10d99d", "category": null}, {"level": null, "dataTag": "1.mingan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.cshelterrelatedfield", "builtin": false, "patterns": [{"expr": "c_shelter_related_field", "type": "catalog"}], "name": "cshelterrelatedfield", "description": "", "levelName": null, "id": "7222cb10-82d9-4957-87e1-21c94075688b", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.yonghuID", "builtin": true, "patterns": [{"expr": "(?i)user.?id", "type": "catalog"}], "name": "yonghu<PERSON>", "description": null, "levelName": null, "id": "30cd1c90-556e-4d22-9a42-1f44745d8050", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.chengshi", "builtin": true, "patterns": [{"expr": "(?i)city", "type": "catalog"}], "name": "ch<PERSON><PERSON>", "description": null, "levelName": null, "id": "7dd7618f-fc75-4f2d-bf50-a9a8320e5c3d", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.cunifiedcreditcode", "builtin": false, "patterns": [{"expr": "c_unified_credit_code", "type": "catalog"}], "name": "cunifiedcreditcode", "description": "", "levelName": null, "id": "55570ab1-9e70-4155-99a4-8df3c2bae8c1", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.guoji", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"国籍（结构化）\"\r\nscores:\r\n- desc: \"列注释以 国籍 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u56FD\\u7C4D\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则模糊匹配nationality、citizenship\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"((?i)nationality)|((?i)citizenship)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "guoji", "description": null, "levelName": null, "id": "c978f5f4-41cb-45bb-8e75-52698b4c0b17", "category": null}, {"level": null, "dataTag": "1.min<PERSON><PERSON><PERSON><PERSON><PERSON>qi<PERSON>.xuexing", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"血型（结构化）\"\r\nscores:\r\n- desc: \"列注释以 血型 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u8840\\u578B\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则模糊匹配blood(允许包含_)type\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"((?i)blood[_]?type)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "xuexing", "description": null, "levelName": null, "id": "e6bb6261-bdf4-47b0-b00d-df9fa027a415", "category": null}, {"level": null, "dataTag": "1.mingan<PERSON><PERSON><PERSON><PERSON>qi<PERSON>.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"手机号（中国内地)（结构化）\"\r\nscores:\r\n- desc: \"字段注释\"\r\n  name: \"MobilePhoneColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u624B\\u673A\\u53F7[\\u7801]?\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"字段名匹配 以 id 结尾\"\r\n  name: \"ColumnNameEndWithId\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(?i)id$\"\r\n      - var: \"column.name\"\r\n- desc: \"计算列的手机号正则命中率\"\r\n  name: \"ColumnValueRegexpFind\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.MobilePhoneColumnComment\"\r\n      - 100\r\n      - var: \"$.ColumnNameEndWithId\"\r\n      - 0\r\n      - array_hit_ratio:\r\n          - and:\r\n              - <=:\r\n                  - length:\r\n                      var: \"ArrayElementValue\"\r\n                  - 20\r\n              - regexp_find:\r\n                  - \"^\\\\s*([+]?\\\\s*86|0086)?\\\\s*[-]?\\\\s*((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(16[2|5-7])|(17[0-8])|(18[0-9])|(19[0-3|5-9]))\\\\d{8}\\\\s*$\"\r\n                  - var: \"ArrayElementValue\"\r\n          - var: \"column.values\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    'condition':\r\n      - '>=':\r\n          - var: \"$.ColumnValueRegexpFind\"\r\n          - 30\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "582cc185-2c92-4bc9-bbb0-f8b1fda5e694", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.URL", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"URL（结构化）\"\r\nscores:\r\n  - desc: \"列注释精确匹配url\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"^\\\\s*(?i)url\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]精确匹配url\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"^(?i)url$\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 满足正则匹配规则\"\r\n    name: \"FindColumnValue\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - regexp_find:\r\n                  - \"(?i)^(ht|f)tp(s?)\\\\:\\\\/\\\\/[0-9a-zA-Z]([-.\\\\w]*[0-9a-zA-Z])*(:(0-9)*)*(\\\\/?)([a-zA-Z0-9\\\\-\\\\.\\\\?\\\\,\\\\'\\\\/\\\\\\\\\\\\+&amp;%\\\\$#_]*)?[ ]*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnValue\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "URL", "description": "Update on 2024.01.30", "levelName": null, "id": "1487a5d8-8ec3-4a6e-80d4-3b002d9013c0", "category": null}, {"level": null, "dataTag": "1.minganshujubiaoqian.guojia", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"国家（结构化）\"\r\nscores:\r\n- desc: \"列注释以 国家 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u56FD\\u5BB6\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"数据字典(命中率90%)\"\r\n  name: \"FindDataDict\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - \">=\":\r\n          - array_hit_ratio:\r\n              - \">\":\r\n                - search_in_dictionary:\r\n                    - \"countries\"\r\n                    - var: \"ArrayElementValue\"\r\n                - 0\r\n              - var: \"column.values\"\r\n          - 90\r\n- desc: \"[字段名]精确匹配country、nation 或 以 country(允许包含_)of、nation(允许包含_)of\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindDataDict\"\r\n      - regexp_find:\r\n          - \"(^(?i)country$)|(^(?i)nation$)|((?i)country[_]?of)|((?i)nation[_]?of)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "guojia", "description": null, "levelName": null, "id": "733d04e3-0aee-45e6-be63-45f332bbdfd7", "category": null}, {"level": null, "dataTag": "1.min<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.cshelterall", "builtin": false, "patterns": [{"expr": "c_shelter_all", "type": "catalog"}], "name": "cshe<PERSON><PERSON>", "description": "", "levelName": null, "id": "7c38f984-b595-4f13-b5c2-2c333b0b29e8", "category": null}, {"level": null, "dataTag": "1.mingan<PERSON><PERSON>biaoqian.IPv6", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"IPV6（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 ipv6 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"(?i)ipv6\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]模糊匹配ipv6\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"(?i)ipv6\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 满足正则匹配规则\"\r\n    name: \"FindColumnValue\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - regexp_find:\r\n                  - \"^(:{2}(/0)?)|((([a-fA-F0-9]{1,4}|):){3,7}([a-fA-F0-9]{1,4}|:)[ ]*)$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnValue\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "IPv6", "description": "Update on 2024.01.22", "levelName": null, "id": "1ca16fe2-c72a-4a34-b6ea-ebb2a8e55ac3", "category": null}, {"level": null, "dataTag": "1.minganshu<PERSON>biaoqian.dianhuahaomazhongguo", "builtin": true, "patterns": [{"expr": "(^(0\\d{2}-\\d{8}(-\\d{1,4})?)|(0\\d{3}-\\d{7,8}(-\\d{1,4})?))|(^((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(18[0,5-9]))\\d{8})[ ]*$", "type": "data"}], "name": "dianhuahaomazhong<PERSON>o", "description": null, "levelName": null, "id": "3527cb39-9b93-4bdf-a350-ab4b286afecd", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.shengri", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"生日（结构化）\"\r\nscores:\r\n- desc: \"列注释 以 生日 、 出生日期 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"([\\u51FA]?\\u751F\\u65E5[\\u671F]?\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则精确匹配 birth、模糊匹配birthday、date(允许包含_)of(允许包含_) birth\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(^(?i)birth$)|((?i)birthday)|((?i)date[_]?of[_]?birth)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "1d685df5-e8e2-47ae-8b44-a8a0ac8e0028", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.dizhi", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"地址（结构化）\"\r\nscores:\r\n- desc: \"列名匹配 address 或者 addr\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(?i)addr|(?i)street|(?i)location|(?i)dizhi|(?i)jiedao|(?i)weizhi\"\r\n      - var: \"column.name\"\r\n- desc: \"列定义长度 大于 20\"\r\n  name: \"ColumnDefLength\"\r\n  output: true\r\n  rule:\r\n    and:\r\n    - var: \"$.FindColumnName\"\r\n    - '>=':\r\n      - var: \"column.size\"\r\n      - 20\r\n- desc: \"字段注释 包含 住址 or 家庭地址 or 办公地址 or 联系地址\"\r\n  name: \"FieldCommentsContains\"\r\n  output: true\r\n  rule:\r\n    and:\r\n    - var: \"$.ColumnDefLength\"\r\n    - regexp_find:\r\n        - \"\\u4f4f\\u5740|\\u5bb6\\u5ead\\u5730\\u5740|\\u529e\\u516c\\u5730\\u5740|\\u8054\\u7cfb\\u5730\\u5740\"\r\n        - var: \"column.comment\"\r\n- desc: \"NLP 识别地址。以上条件都满足或者是 NLP 识别率达到 30%\"\r\n  name: \"NlpAnalysis\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FieldCommentsContains\"\r\n      - and:\r\n        - var: \"$.ColumnDefLength\"\r\n        - '>=':\r\n            - array_hit_ratio:\r\n                - validator:\r\n                    - \"cls.piilinkable::address\"\r\n                    - var: \"ArrayElementValue\"\r\n                - var: \"column.values\"\r\n            - 30\r\n- desc: \"NLP 如果不满足，继续 字段内容 匹配规则列表\"\r\n  name: \"RegexpListFind\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.NlpAnalysis\"\r\n      - and:\r\n          - var: \"$.ColumnDefLength\"\r\n          - '>=':\r\n              - array_hit_ratio:\r\n                  - or: # 包含 小区|单元|号楼|大厦|家园|新区|村委会|公安局|派出所|街道办|公寓|厂区\r\n                      - regexp_find:\r\n                          - \"\\u5c0f\\u533a|\\u5355\\u5143|\\u53f7\\u697c|\\u5927\\u53a6|\\u5bb6\\u56ed|\\u65b0\\u533a|\\u6751\\u59d4\\u4f1a|\\u516c\\u5b89\\u5c40|\\u6d3e\\u51fa\\u6240|\\u8857\\u9053\\u529e|\\u516c\\u5bd3|\\u5382\\u533a\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 以 3位数字+室or房 结尾\r\n                          - \"\\\\d{1,3}\\\\s*[\\u5BA4\\u623F]$\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 市 and 区 and （1位数字+号），市区两字不可紧相邻\r\n                          - \"\\u5E02(\\\\s*[^\\\\s\\u5E02\\u533A\\\\d])+\\\\s*\\u533A(\\\\s*[^\\\\s\\u5E02\\u533A\\u53F7])+\\\\d*\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 村 and 号，之间有至少一位值\r\n                          - \"\\u6751(\\\\s*[^\\\\s\\u6751\\u53F7])+\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 市 and 村，之间有至少一位值\r\n                          - \"\\u5E02(\\\\s*[^\\\\s\\u5E02\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 区 and 村，之间有至少一位值\r\n                          - \"\\u533A(\\\\s*[^\\\\s\\u533A\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 镇 and 村，之间有至少一位值\r\n                          - \"\\u9547(\\\\s*[^\\\\s\\u9547\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 县 and 村，之间有至少一位值\r\n                          - \"\\u53BF(\\\\s*[^\\\\s\\u53BF\\u6751])+\\\\s*\\u6751\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 巷 and 号，之间有至少一位值\r\n                          - \"\\u5DF7(\\\\s*[^\\\\s\\u5DF7\\u53F7])+\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 包含 任意位数字+栋+任意位数字\r\n                          - \"\\\\d+\\\\s*\\u680B\\\\s*\\\\d+\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 按前后顺序包含 社区 and 号，之间有至少一位值\r\n                          - \"\\u793E\\u533A(\\\\s*[^\\\\s\\u793E\\u533A\\u53F7])+\\\\s*\\u53F7\"\r\n                          - var: \"ArrayElementValue\"\r\n                      - regexp_find: # 以 局 结尾\r\n                          - \"\\u5C40\\\\s*$\"\r\n                          - var: \"ArrayElementValue\"\r\n                  - var: \"column.values\"\r\n              - 30\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.RegexpListFind\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "dizhi", "description": "Updated on 2024.05.25", "levelName": null, "id": "a5845e47-bc11-479f-addf-d967ed541cef", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.youbiandalu", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"邮编（结构化）\"\r\nscores:\r\n- desc: \"列注释以 邮编 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u90AE\\u7F16\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则精确匹配postal、postcode、postalcode、zipcode 模糊匹配 (允许包含_)of\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(^(?i)postal$)|(^(?i)postcode$)|(^(?i)postalcode$)|(^(?i)zipcode$)|((?i)postal[_]?of)|((?i)postcode[_]?of)|((?i)postalcode[_]?of)|((?i)zipcode[_]?of)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "4aeaf466-0eb9-4c7b-b6c6-413b2c991466", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.MAC", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"MAC（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 mac 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"(?i)mac\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]精确匹配mac\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"^(?i)mac$\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 满足正则匹配规则\"\r\n    name: \"FindColumnValue\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - regexp_find:\r\n                  - \"^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})|([0-9a-fA-F]{4}\\\\\\\\.[0-9a-fA-F]{4}\\\\\\\\.[0-9a-fA-F]{4})[ ]*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnValue\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "MAC", "description": "Updated on 2024.05.25", "levelName": null, "id": "95f1d1b1-6a34-4134-a4bd-ea0c0ddf9c96", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.xingbie", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"性别（结构化）\"\r\nscores:\r\n- desc: \"列注释匹配以 性别 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u6027\\u522B\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"数据字典(命中率90%)\"\r\n  name: \"FindDataDict\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - \">=\":\r\n          - array_hit_ratio:\r\n              - \">\":\r\n                - search_in_dictionary:\r\n                    - \"gender\"\r\n                    - var: \"ArrayElementValue\"\r\n                - 0\r\n              - var: \"column.values\"\r\n          - 90\r\n- desc: \"[字段名]正则精确匹配gender、sexual、sex\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindDataDict\"\r\n      - regexp_find:\r\n          - \"(^(?i)gender$)|(^(?i)sexual$)|(^(?i)sex$)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "5d93fe4e-bcc1-4b9e-a214-31c3383482d5", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.mima", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"密码（结构化）\"\r\nscores:\r\n  - desc: \"列注释匹配以 密码 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"\\u5BC6\\u7801\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]正则精确匹配password、passcode、passphrase\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"(^(?i)password$)|(^(?i)passcode$)|(^(?i)passphrase$)\"\r\n            - var: \"column.name\"\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnName\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "mima", "description": "Updated on 2024.05.25", "levelName": null, "id": "11263ec8-72af-422c-9475-78c792c6d1cf", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.xueya", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"血压（结构化）\"\r\nscores:\r\n- desc: \"列注释匹配 以 血压 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u8840\\u538B\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则模糊匹配blood(允许包含_)pressure 且 内容校验在 20-220 之间的整数(命中率90%)\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - and:\r\n        - regexp_find:\r\n          - \"(?i)blood[_]?pressure\"\r\n          - var: \"column.name\"\r\n        - \">=\":\r\n            - array_hit_ratio:\r\n                - and:\r\n                    - \">=\":\r\n                        - var: \"ArrayElementValue\"\r\n                        - 20\r\n                    - \"<=\":\r\n                        - var: \"ArrayElementValue\"\r\n                        - 220\r\n                - var: \"column.values\"\r\n            - 90\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "x<PERSON>ya", "description": "Updated on 2024.05.25", "levelName": null, "id": "bc0336d6-4f07-4264-b741-71fae7bd32eb", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.IPv4", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"IPV4（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 ip、ipv4 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"((?i)ipv4\\\\s*$)|((?i)ip\\\\s*$)\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]精确匹配ip、ipv4\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"(^(?i)ip$)|(^(?i)ipv4$)\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 满足正则匹配规则\"\r\n    name: \"FindColumnValue\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - regexp_find:\r\n                  - \"^(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\\\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])[ ]*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnValue\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "IPv4", "description": "Updated on 2024.05.25", "levelName": null, "id": "67a44e99-11ad-4193-b05e-1586663fffc8", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"身份证（中国内地）（结构化）\"\r\nscores:\r\n- desc: \"内容长度限制必须大于等于 18\"\r\n  name: \"IdCardLength\"\r\n  output: true\r\n  rule:\r\n    \">=\":\r\n      - var: \"column.size\"\r\n      - 18\r\n- desc: \"注释以身份证号结尾\"\r\n  name: \"ColumnComment\"\r\n  output: true\r\n  rule:\r\n    and:\r\n      - var: \"$.IdCardLength\"\r\n      - regexp_find:\r\n          - \"\\u8EAB\\u4EFD\\u8BC1\\u53F7$\"\r\n          - var: \"column.comment\"\r\n- desc: \"如果注释匹配，直接返回，否则计算命中率。\"\r\n  name: \"RegexpFindAndValidator\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.ColumnComment\"\r\n      - 100\r\n      - array_hit_ratio:\r\n          - and:\r\n            - '>=':\r\n                - length:\r\n                  - var: \"ArrayElementValue\"\r\n                - 18\r\n            - regexp_find:\r\n                - \"(([1][1-5])|([2][1-3])|([3][1-7])|([4][1-6])|([5][0-4])|([6][1-5])|([7][1])|([8][1-2]))\\\\d{4}(([1][9]\\\\d{2})|([2]\\\\d{3}))(([0][1-9])|([1][0-2]))(([0][1-9])|([1-2][0-9])|([3][0-1]))\\\\d{3}[0-9xX]\\\\s*\"\r\n                - var: \"ArrayElementValue\"\r\n            - validator:\r\n                - \"cls.pii::idcard\"\r\n                - var: \"ArrayElementValue\"\r\n          - var: \"column.values\"\r\n- desc: \"命中率必须大于等于 30\"\r\n  name: \"CalculateHitRatio\"\r\n  output: true\r\n  rule:\r\n    '>=':\r\n      - var: \"$.RegexpFindAndValidator\"\r\n      - 30\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    'condition':\r\n    - var: \"$.CalculateHitRatio\"\r\n    - 100\r\n    - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "2a2fd09e-b83a-4c79-8d5c-ff8bb8e4c35a", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.zongjiao", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"宗教（结构化）\"\r\nscores:\r\n- desc: \"列注释以 宗教、信仰 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u5B97\\u6559\\\\s*$)|(\\u4FE1\\u4EF0\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"数据字典(命中率90%)\"\r\n  name: \"FindDataDict\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - \">=\":\r\n          - array_hit_ratio:\r\n              - \">\":\r\n                - search_in_dictionary:\r\n                    - \"religions\"\r\n                    - var: \"ArrayElementValue\"\r\n                - 0\r\n              - var: \"column.values\"\r\n          - 90\r\n- desc: \"[字段名]正则精确匹配 faith 或 精确匹配religion 或 模糊匹配religion(允许包含_)of \"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindDataDict\"\r\n      - regexp_find:\r\n          - \"(^(?i)faith$)|(^(?i)religion$)|((?i)religion[_]?of)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON>jiao", "description": "Updated on 2024.05.25", "levelName": null, "id": "5937d5de-906d-4b12-a3d0-4148079f3bf6", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.xinlv", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"心率（结构化）\"\r\nscores:\r\n  - desc: \"列注释匹配 以 心率 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"\\u5FC3\\u7387\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]正则模糊匹配heart (允许包含_)rate 且 内容校验是在40-180之间的整数(命中率90%)\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - and:\r\n            - regexp_find:\r\n                - \"(?i)heart[_]?rate\"\r\n                - var: \"column.name\"\r\n            - \">=\":\r\n                - array_hit_ratio:\r\n                    - and:\r\n                        - \">=\":\r\n                            - var: \"ArrayElementValue\"\r\n                            - 40\r\n                        - \"<=\":\r\n                            - var: \"ArrayElementValue\"\r\n                            - 180\r\n                    - var: \"column.values\"\r\n                - 90\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnName\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "xinlv", "description": "Updated on 2024.05.25", "levelName": null, "id": "44568279-b092-4427-bd94-37932f7e0a56", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.y<PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"银行卡（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 银行卡号 或 银行账号 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"\\u94F6\\u884C[\\u5361\\u8D26][\\u53F7]?\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"字段名匹配\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"^(?i)bank[_-]?accno$|^(?i)bank[_-]?account$|^(?i)bank[_-]?accno$|^(?i)bank[_-]?card[_-]?no$|^(?i)bank[_-]?card[_-]?number$\"\r\n        - var: \"column.name\"\r\n  - desc: \"正则+校验\"\r\n    name: \"ColumnValuesRegexpAndValidator\"\r\n    output: true\r\n    rule:\r\n      condition:\r\n        - var: \"$.FindColumnComment\"\r\n        - 100\r\n        - var: \"$.FindColumnName\"\r\n        - 100\r\n        - array_hit_ratio:\r\n            - and:\r\n                - regexp_find:\r\n                    - \"^\\\\s*\\\\d{10,20}\\\\s*$\"\r\n                    - var: \"ArrayElementValue\"\r\n                - regexp_find:\r\n                    - \"^\\\\s*(?!(\\\\d)\\\\1{5}).*$\"\r\n                    - var: \"ArrayElementValue\"\r\n                - or:\r\n                    - regexp_find:\r\n                        - \"^5[1-5][0-9]{2}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[ ]*$\"\r\n                        - var: \"ArrayElementValue\"\r\n                    - regexp_find:\r\n                        - \"^[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[ ]*$\"\r\n                        - var: \"ArrayElementValue\"\r\n                    - regexp_find:\r\n                        - \"^62[0-5][0-9]{13,16}[ ]*$\"\r\n                        - var: \"ArrayElementValue\"\r\n                    - regexp_find:\r\n                        - \"^4[0-9]{3}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[ ]*$\"\r\n                        - var: \"ArrayElementValue\"\r\n                - validator:\r\n                    - \"cls.pii::unionpay\"\r\n                    - var: \"ArrayElementValue\"\r\n            - var: \"column.values\"\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - '>=':\r\n            - var: \"$.ColumnValuesRegexpAndValidator\"\r\n            - 60\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "yin<PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "81621f73-6df8-4931-8bc1-62660b335d7c", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.nianling", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"年龄（结构化）\"\r\nscores:\r\n- desc: \"列注释匹配 以 年龄 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u5E74\\u9F84\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"列名精确匹配 age，且值 0-100 之间，90% 命中率\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - and:\r\n        - regexp_find:\r\n          - \"^(?i)age$\"\r\n          - var: \"column.name\"\r\n        - \">=\":\r\n            - array_hit_ratio:\r\n                - and:\r\n                    - \">=\":\r\n                        - var: \"ArrayElementValue\"\r\n                        - 0\r\n                    - \"<=\":\r\n                        - var: \"ArrayElementValue\"\r\n                        - 100\r\n                - var: \"column.values\"\r\n            - 90\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "59d3f9c2-a6cb-4ec8-bd80-295a733aade0", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.ch<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"车牌号(大陆)（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 车牌、车牌号 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"\\u8F66\\u724C[\\u53F7]?\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]正则模糊匹配license(允许包含_)plate、plate(允许包含_)number\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"((?i)license[_]?plate)|((?i)plate[_]?number)\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 内置车牌号匹配规则 cls.pii::cardnum (命中率80%)\"\r\n    name: \"FindValidator\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - validator:\r\n                  - \"cls.pii::cardnum\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindValidator\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "ch<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "c5e336cd-c4f7-41cd-8642-0cbcd7e8e540", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.xingming", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"姓名（结构化）\"\r\nscores:\r\n- desc: \"字段名规则列表\"\r\n  name: \"FindColumnNameList\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - regexp_find:\r\n          - \"^(?i)(insured|insured[_-]?name|receiver|receiver[_-]?name|sender|sender[_-]?name|recipient|recipient[-_]?name|full[_-]?name|first[_-]?name|last[_-]?name|real[_-]?name|staff[_-]?name|candidate|cardholder|cardholder[_-]?name|member[_-]?name|payer[_-]?name|payee[_-]?name|purchaser[_-]?name|trustee[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(bank[_-]?account[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(customer[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(holder[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(contact[s]?[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(patient[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(spouse[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(kin[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(student[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"^(?i)(name[_-]?of|name[_-]?on)\"\r\n          - var: \"column.name\"\r\n- desc: \"列名匹配\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"^(?!.*(org|medicine))(.*(name|er|yee))$|^(insured|initial)$\"\r\n      - var: \"column.name\"\r\n- desc: \"字段注释\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    and:\r\n      - var: \"$.FindColumnName\"\r\n      - regexp_find: # （字符串中包含 姓名 或者 账户名称）同时（不能包含 企 或者 公 或者 人 或者 修改 或者 编辑 或者 变更 或者 创建 或者 操作)\r\n          - \"^(?=.*(\\u59D3\\u540D|\\u8D26\\u6237\\u540D\\u79F0))(?!.*(\\u4F01|\\u516C|\\u4EBA|\\u4FEE\\u6539|\\u7F16\\u8F91|\\u53D8\\u66F4|\\u521B\\u5EFA|\\u64CD\\u4F5C)).*$\"\r\n          - var: \"column.comment\"\r\n- desc: \"反向字段注释\"\r\n  name: \"ReverseFindColumnComment\"\r\n  output: true\r\n  rule:\r\n    and:\r\n      - var: \"$.FindColumnName\"\r\n      - regexp_find: # 品名 或 品名称 或 司名称 或 业名称 或 厂名称 或 商名称 或 社名称 或 机构名称 或 种名称 结尾\r\n          - \"(\\u54C1\\u540D|\\u54C1\\u540D\\u79F0|\\u53F8\\u540D\\u79F0|\\u4E1A\\u540D\\u79F0|\\u5382\\u540D\\u79F0|\\u5546\\u540D\\u79F0|\\u793E\\u540D\\u79F0|\\u673A\\u6784\\u540D\\u79F0|\\u79CD\\u540D\\u79F0)$\"\r\n          - var: \"column.comment\"\r\n- desc: \"NLP 识别\"\r\n  name: \"NlpAnalysis\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.FindColumnNameList\"\r\n      - true\r\n      - var: \"$.FindColumnComment\"\r\n      - true\r\n      - var: \"$.ReverseFindColumnComment\"\r\n      - false\r\n      - '>=':\r\n          - array_hit_ratio:\r\n              - and:\r\n                  - <=:\r\n                      - length:\r\n                          var: \"ArrayElementValue\"\r\n                      - 5\r\n                  - validator:\r\n                      - \"cls.pii::name\"\r\n                      - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 30\r\n- desc: \"表名和列名组合规则\"\r\n  name: \"TableAndColumnCombination\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.NlpAnalysis\"\r\n      - and:\r\n          - regexp_find:\r\n              - \"(?i)(erinfo|yeeinfo)$\"\r\n              - var: \"table.name\"\r\n          - regexp_find:\r\n              - \"(?i)(name)\"\r\n              - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n    - var: \"$.TableAndColumnCombination\"\r\n    - 100\r\n    - 0", "type": "rule"}], "name": "xing<PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "93f81d66-cbf6-4f74-9c46-79641621e2e4", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "(?i)(fax|phone|landline)", "type": "catalog"}, {"expr": "^\\s*(86|\\+86|0086)?\\s*-?\\s*(0\\d{2}\\s*-?\\s*\\d{8}(\\s*-?\\s*\\d{1,4})?)\\s*$|^\\s*(0\\d{3}\\s*-?\\s*\\d{7,8}(\\s*-?\\s*\\d{1,4})?)\\s*$", "type": "data"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "0fb63b93-a969-4a78-b439-780b6441d768", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.weizhi", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"位置（结构化）\"\r\nscores:\r\n- desc: \"列注释以 位置、经度、纬度、经纬、经纬度 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u4F4D\\u7F6E\\\\s*$)|(\\u7ECF[\\u7EAC]?\\u5EA6\\\\s*$)|(\\u7ECF\\u7EAC\\\\s*$)|(\\u7EAC\\u5EA6\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则精确匹配location、latitude、longitude、lng、lat\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(^(?i)location$)|(^(?i)latitude$)|(^(?i)longitude$)|(^(?i)lng$)|(^(?i)lat$)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "we<PERSON>hi", "description": "Updated on 2024.05.25", "levelName": null, "id": "9063c44f-7691-455c-b7d0-8dd448d7e699", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.youxiang", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"邮箱（结构化）\"\r\nscores:\r\n- desc: \"匹配列注释，以邮箱结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n    - \"\\u90AE[\\u4EF6\\u7BB1](\\u5730\\u5740)?\\\\s*$\"\r\n    - var: \"column.comment\"\r\n- desc: \"匹配列名为 email\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"^(?i)(email|email[_]?address)$\"\r\n      - var: \"column.name\"\r\n- desc: \"匹配 validator\"\r\n  name: \"ColumnValueValidator\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.FindColumnComment\"\r\n      - 100\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - array_hit_ratio:\r\n          - and:\r\n              - regexp_find:\r\n                  - \"^\\\\s*[a-zA-Z0-9.-]+@[a-zA-Z0-9-]+(\\\\.[a-zA-Z0-9-]+)*\\\\.[a-zA-Z0-9]{2,6}\\\\s*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - validator:\r\n                  - \"cls.pii::email\"\r\n                  - var: \"ArrayElementValue\"\r\n          - var: \"column.values\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n    - '>=':\r\n        - var: \"$.ColumnValueValidator\"\r\n        - 30\r\n    - 100\r\n    - 0", "type": "rule"}], "name": "youxiang", "description": "Updated on 2024.05.25", "levelName": null, "id": "b322d4d3-9085-42b9-862e-9ab88216aa68", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.zhiye", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"职业（结构化）\"\r\nscores:\r\n- desc: \"列注释以  职业、工作 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u804C\\u4E1A\\\\s*$)|(\\u5DE5\\u4F5C\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"数据字典(命中率90%)\"\r\n  name: \"FindDataDict\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - \">=\":\r\n          - array_hit_ratio:\r\n              - \">\":\r\n                - search_in_dictionary:\r\n                    - \"professions\"\r\n                    - var: \"ArrayElementValue\"\r\n                - 0\r\n              - var: \"column.values\"\r\n          - 90\r\n- desc: \"[字段名]正则精确匹配job、work、occupation、profession\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindDataDict\"\r\n      - regexp_find:\r\n          - \"(^(?i)job$)|(^(?i)work$)|(^(?i)occupation$)|(^(?i)profession[sS]?$)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "zhiye", "description": "Updated on 2024.05.25", "levelName": null, "id": "d12daf9f-3aa4-4df0-86b8-f11b665ca368", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.riqi", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"日期（结构化）\"\r\nscores:\r\n- desc: \"列注释以 日期、时间 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u65E5\\u671F\\\\s*$)|(\\u65F6\\u95F4\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则匹配date\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(?i)date\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "riqi", "description": "Updated on 2024.05.25", "levelName": null, "id": "25179ac1-d100-4eaf-b3d9-d37d53cb802f", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.guoji", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"国籍（结构化）\"\r\nscores:\r\n- desc: \"列注释以 国籍 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u56FD\\u7C4D\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则模糊匹配nationality、citizenship\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"((?i)nationality)|((?i)citizenship)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "guoji", "description": "Updated on 2024.05.25", "levelName": null, "id": "b310bd75-d10e-4e7c-b979-e030185b2d1b", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.xuexing", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"血型（结构化）\"\r\nscores:\r\n- desc: \"列注释以 血型 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u8840\\u578B\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则模糊匹配blood(允许包含_)type\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"((?i)blood[_]?type)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "xuexing", "description": "Updated on 2024.05.25", "levelName": null, "id": "fa300c71-e726-4785-bf08-bc5555a781b0", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"手机号（中国内地)（结构化）\"\r\nscores:\r\n- desc: \"字段注释\"\r\n  name: \"MobilePhoneColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u624B\\u673A\\u53F7[\\u7801]?\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"字段名匹配 以 id 结尾\"\r\n  name: \"ColumnNameEndWithId\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(?i)id$\"\r\n      - var: \"column.name\"\r\n- desc: \"计算列的手机号正则命中率\"\r\n  name: \"ColumnValueRegexpFind\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.MobilePhoneColumnComment\"\r\n      - 100\r\n      - var: \"$.ColumnNameEndWithId\"\r\n      - 0\r\n      - array_hit_ratio:\r\n          - and:\r\n              - <=:\r\n                  - length:\r\n                      var: \"ArrayElementValue\"\r\n                  - 20\r\n              - regexp_find:\r\n                  - \"^\\\\s*([+]?\\\\s*86|0086)?\\\\s*[-]?\\\\s*((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(16[2|5-7])|(17[0-8])|(18[0-9])|(19[0-3|5-9]))\\\\d{8}\\\\s*$\"\r\n                  - var: \"ArrayElementValue\"\r\n          - var: \"column.values\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    'condition':\r\n      - '>=':\r\n          - var: \"$.ColumnValueRegexpFind\"\r\n          - 30\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Updated on 2024.05.25", "levelName": null, "id": "a657c89a-6c77-41fc-88e5-dbdde66db333", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.URL", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"URL（结构化）\"\r\nscores:\r\n  - desc: \"列注释精确匹配url\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"^\\\\s*(?i)url\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]精确匹配url\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"^(?i)url$\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 满足正则匹配规则\"\r\n    name: \"FindColumnValue\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - regexp_find:\r\n                  - \"(?i)^(ht|f)tp(s?)\\\\:\\\\/\\\\/[0-9a-zA-Z]([-.\\\\w]*[0-9a-zA-Z])*(:(0-9)*)*(\\\\/?)([a-zA-Z0-9\\\\-\\\\.\\\\?\\\\,\\\\'\\\\/\\\\\\\\\\\\+&amp;%\\\\$#_=]*)?[ ]*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnValue\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "URL", "description": "Updated on 2024.05.25", "levelName": null, "id": "91ad8175-33b6-4c0f-9e77-baaad6146cf7", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.guojia", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"国家（结构化）\"\r\nscores:\r\n- desc: \"列注释以 国家 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u56FD\\u5BB6\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"数据字典(命中率90%)\"\r\n  name: \"FindDataDict\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - \">=\":\r\n          - array_hit_ratio:\r\n              - \">\":\r\n                - search_in_dictionary:\r\n                    - \"countries\"\r\n                    - var: \"ArrayElementValue\"\r\n                - 0\r\n              - var: \"column.values\"\r\n          - 90\r\n- desc: \"[字段名]精确匹配country、nation 或 以 country(允许包含_)of、nation(允许包含_)of\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindDataDict\"\r\n      - regexp_find:\r\n          - \"(^(?i)country$)|(^(?i)nation$)|((?i)country[_]?of)|((?i)nation[_]?of)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "guojia", "description": "Updated on 2024.05.25", "levelName": null, "id": "1e39136a-5d18-4e2a-9d8c-0ae0a3bc862c", "category": null}, {"level": null, "dataTag": "1.tongyonggerenminganxinxiv2.IPv6", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"IPV6（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 ipv6 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"(?i)ipv6\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]模糊匹配ipv6\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"(?i)ipv6\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 满足正则匹配规则\"\r\n    name: \"FindColumnValue\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - regexp_find:\r\n                  - \"^(:{2}(/0)?)|((([a-fA-F0-9]{1,4}|):){3,7}([a-fA-F0-9]{1,4}|:)[ ]*)$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnValue\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "IPv6", "description": "Updated on 2024.05.25", "levelName": null, "id": "cdd961d0-b474-4b35-91cc-33cb308bb802", "category": null}, {"level": null, "dataTag": "1.ningdeshidaiceshi.ningdeceshishenfenzh", "builtin": false, "patterns": [{"expr": "idcard", "type": "catalog"}], "name": "ningdeceshishenfenzh", "description": "", "levelName": null, "id": "f577246c-eb44-48d0-965d-ac435c7057f0", "category": null}, {"level": null, "dataTag": "1.ningdeshidaiceshi.ningdeceshinianling", "builtin": false, "patterns": [{"expr": "age", "type": "catalog"}], "name": "ning<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "3b5021b3-b56c-4e2e-8bed-47a51b8512d1", "category": null}, {"level": null, "dataTag": "1.ningdeshidaiceshi.ningdeceshishoujihao", "builtin": false, "patterns": [{"expr": "phone_number", "type": "catalog"}], "name": "ning<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "levelName": null, "id": "4e6b13ee-f7d5-44ab-8e2d-f0c4fa1b5e91", "category": null}, {"level": null, "dataTag": "1.ningdeshidaiceshi.ningdeceshixingming", "builtin": false, "patterns": [{"expr": "name1", "type": "catalog"}], "name": "ning<PERSON><PERSON><PERSON><PERSON>ming", "description": "", "levelName": null, "id": "712a45ef-d3b9-4554-9caf-60471616691d", "category": null}, {"level": null, "dataTag": "1.ningdeshidaiceshi.ningdeceshidizhi", "builtin": false, "patterns": [{"expr": "address", "type": "catalog"}], "name": "ningdeceshidizhi", "description": "", "levelName": null, "id": "5b3e53d6-4385-441e-9db0-a4c214e6b2e8", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.zyl-test1", "builtin": false, "patterns": [{"expr": "zyl-test1", "type": "catalog"}, {"expr": "(?i)^.*(or|and)\\s*('|\")*(-?\\d+)(\"|')*\\s*=\\s*('|\")*\\3('|\")*.*", "type": "catalog"}], "name": "zyl-test1", "description": "zyl-test1", "levelName": null, "id": "72df66f6-062a-4869-9cd3-d1e435d29d1f", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.danwei", "builtin": true, "patterns": [{"expr": "(?i)affiliation", "type": "catalog"}], "name": "danwei", "description": null, "levelName": null, "id": "5aa3380f-5791-4732-9c3f-bc1f5d1f5118", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.dianhuahaoma_kuanfan", "builtin": false, "patterns": [{"expr": "^1[3-9]\\d{9}$", "type": "data"}, {"expr": "df", "type": "catalog"}, {"expr": "sa<PERSON><PERSON>a", "type": "data"}], "name": "dianhuahaoma_kuanfan", "description": "", "levelName": null, "id": "2cbbf12c-96e8-4fb7-93d6-61b9ddaa6f00", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.xinlv", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"心率（结构化）\"\r\nscores:\r\n  - desc: \"列注释匹配 以 心率 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"\\u5FC3\\u7387\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]正则模糊匹配heart (允许包含_)rate 且 内容校验是在40-180之间的整数(命中率90%)\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - and:\r\n            - regexp_find:\r\n                - \"(?i)heart[_]?rate\"\r\n                - var: \"column.name\"\r\n            - \">=\":\r\n                - array_hit_ratio:\r\n                    - and:\r\n                        - \">=\":\r\n                            - var: \"ArrayElementValue\"\r\n                            - 40\r\n                        - \"<=\":\r\n                            - var: \"ArrayElementValue\"\r\n                            - 180\r\n                    - var: \"column.values\"\r\n                - 90\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnName\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "xinlv", "description": null, "levelName": null, "id": "1484c971-3d37-4996-b8b4-e6b981677b6e", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.y<PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"银行卡（结构化）\"\r\nscores:\r\n- desc: \"列注释以 银行卡号 或 银行账号 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u94F6\\u884C[\\u5361\\u8D26][\\u53F7]?\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"字段名匹配\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"^(?i)bank[_-]?accno$|^(?i)bank[_-]?account$|^(?i)bank[_-]?accno$|^(?i)bank[_-]?card[_-]?no$|^(?i)bank[_-]?card[_-]?number$\"\r\n      - var: \"column.name\"\r\n- desc: \"正则+校验\"\r\n  name: \"ColumnValuesRegexpAndValidator\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.FindColumnComment\"\r\n      - 100\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - array_hit_ratio:\r\n          - and:\r\n              - regexp_find:\r\n                  - \"^\\\\s*\\\\d{10,20}\\\\s*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - regexp_find:\r\n                  - \"^\\\\s*(?!(\\\\d)\\\\1{5}).*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - validator:\r\n                  - \"cls.pii::unionpay\"\r\n                  - var: \"ArrayElementValue\"\r\n          - var: \"column.values\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - '>=':\r\n          - var: \"$.ColumnValuesRegexpAndValidator\"\r\n          - 80\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "yin<PERSON><PERSON><PERSON>", "description": "Update on 2024.04.19", "levelName": null, "id": "f7c3dbad-c8f9-4971-b925-dbe734636f89", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.nianling", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"年龄（结构化）\"\r\nscores:\r\n- desc: \"列注释匹配 以 年龄 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u5E74\\u9F84\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"列名精确匹配 age，且值 0-100 之间，90% 命中率\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - and:\r\n        - regexp_find:\r\n          - \"^(?i)age$\"\r\n          - var: \"column.name\"\r\n        - \">=\":\r\n            - array_hit_ratio:\r\n                - and:\r\n                    - \">=\":\r\n                        - var: \"ArrayElementValue\"\r\n                        - 0\r\n                    - \"<=\":\r\n                        - var: \"ArrayElementValue\"\r\n                        - 100\r\n                - var: \"column.values\"\r\n            - 90\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "117ed049-e491-4d86-86e0-0f2a22da668e", "category": null}, {"level": null, "dataTag": "1.qianyi<PERSON><PERSON>i.c_keep_from_n_to_m", "builtin": false, "patterns": [{"expr": "c_keep_from_n_to_m", "type": "catalog"}], "name": "c_keep_from_n_to_m", "description": "", "levelName": null, "id": "bf6aa1d1-2bd5-4d5f-968e-d08ac448f971", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.chushengriqi", "builtin": true, "patterns": [{"expr": "(?i)date.?of.?birth", "type": "catalog"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "09c412cd-fc5c-4d67-bada-ebc53f1eb966", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.chepaihaodalu", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"车牌号(大陆)（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 车牌、车牌号 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"\\u8F66\\u724C[\\u53F7]?\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]正则模糊匹配license(允许包含_)plate、plate(允许包含_)number\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"((?i)license[_]?plate)|((?i)plate[_]?number)\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 内置车牌号匹配规则 cls.pii::cardnum (命中率80%)\"\r\n    name: \"FindValidator\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - validator:\r\n                  - \"cls.pii::cardnum\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindValidator\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "ch<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "1da035a5-25c8-487d-b976-ad16150595b2", "category": null}, {"level": null, "dataTag": "1.q<PERSON>yifenlei.xingming-zhongwences", "builtin": false, "patterns": [{"expr": "[姓名]", "type": "catalog"}], "name": "xingming-zhongwences", "description": "", "levelName": null, "id": "71174d13-4c4a-4387-a5b5-7f13e4fef731", "category": null}, {"level": null, "dataTag": "1.q<PERSON><PERSON><PERSON><PERSON><PERSON>.xingming", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"姓名（结构化）\"\r\nscores:\r\n- desc: \"字段名规则列表\"\r\n  name: \"FindColumnNameList\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - regexp_find:\r\n          - \"^(?i)(insured|insured[_-]?name|receiver|receiver[_-]?name|sender|sender[_-]?name|recipient|recipient[-_]?name|full[_-]?name|first[_-]?name|last[_-]?name|real[_-]?name|staff[_-]?name|candidate|cardholder|cardholder[_-]?name|member[_-]?name|payer[_-]?name|payee[_-]?name|purchaser[_-]?name|trustee[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(bank[_-]?account[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(customer[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(holder[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(contact[s]?[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(patient[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(spouse[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(kin[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"(?i)(student[_-]?name)$\"\r\n          - var: \"column.name\"\r\n      - regexp_find:\r\n          - \"^(?i)(name[_-]?of|name[_-]?on)\"\r\n          - var: \"column.name\"\r\n- desc: \"列名匹配\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"^(?!.*(org|medicine))(.*(name|er|yee))$|^(insured|initial)$\"\r\n      - var: \"column.name\"\r\n- desc: \"字段注释\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    and:\r\n      - var: \"$.FindColumnName\"\r\n      - regexp_find: # （字符串中包含 姓名 或者 账户名称）同时（不能包含 企 或者 公 或者 人 或者 修改 或者 编辑 或者 变更 或者 创建 或者 操作)\r\n          - \"^(?=.*(\\u59D3\\u540D|\\u8D26\\u6237\\u540D\\u79F0))(?!.*(\\u4F01|\\u516C|\\u4EBA|\\u4FEE\\u6539|\\u7F16\\u8F91|\\u53D8\\u66F4|\\u521B\\u5EFA|\\u64CD\\u4F5C)).*$\"\r\n          - var: \"column.comment\"\r\n- desc: \"反向字段注释\"\r\n  name: \"ReverseFindColumnComment\"\r\n  output: true\r\n  rule:\r\n    and:\r\n      - var: \"$.FindColumnName\"\r\n      - regexp_find: # 品名 或 品名称 或 司名称 或 业名称 或 厂名称 或 商名称 或 社名称 或 机构名称 或 种名称 结尾\r\n          - \"(\\u54C1\\u540D|\\u54C1\\u540D\\u79F0|\\u53F8\\u540D\\u79F0|\\u4E1A\\u540D\\u79F0|\\u5382\\u540D\\u79F0|\\u5546\\u540D\\u79F0|\\u793E\\u540D\\u79F0|\\u673A\\u6784\\u540D\\u79F0|\\u79CD\\u540D\\u79F0)$\"\r\n          - var: \"column.comment\"\r\n- desc: \"NLP 识别\"\r\n  name: \"NlpAnalysis\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.FindColumnNameList\"\r\n      - true\r\n      - var: \"$.FindColumnComment\"\r\n      - true\r\n      - var: \"$.ReverseFindColumnComment\"\r\n      - false\r\n      - '>=':\r\n          - array_hit_ratio:\r\n              - and:\r\n                  - <=:\r\n                      - length:\r\n                          var: \"ArrayElementValue\"\r\n                      - 5\r\n                  - validator:\r\n                      - \"cls.pii::name\"\r\n                      - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 30\r\n- desc: \"表名和列名组合规则\"\r\n  name: \"TableAndColumnCombination\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.NlpAnalysis\"\r\n      - and:\r\n          - regexp_find:\r\n              - \"(?i)(erinfo|yeeinfo)$\"\r\n              - var: \"table.name\"\r\n          - regexp_find:\r\n              - \"(?i)(name)\"\r\n              - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n    - var: \"$.TableAndColumnCombination\"\r\n    - 100\r\n    - 0", "type": "rule"}], "name": "xing<PERSON>", "description": null, "levelName": null, "id": "ddfbe81c-3604-4c7c-97a6-c49e6acb361a", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.address", "builtin": false, "patterns": [{"expr": "address", "type": "catalog"}, {"expr": "ADDRESS", "type": "catalog"}], "name": "address", "description": "", "levelName": null, "id": "c4678e96-b93f-45a9-a192-340138effd28", "category": null}, {"level": null, "dataTag": "1.qian<PERSON><PERSON><PERSON>i.wans<PERSON><PERSON>a", "builtin": true, "patterns": [{"expr": "^5[1-5][0-9]{2}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[ ]*$", "type": "data"}], "name": "wans<PERSON><PERSON>a", "description": null, "levelName": null, "id": "e9e065ab-6fa4-4b0f-904d-9e1069471000", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.shoujihao_enc_DW", "builtin": false, "patterns": [{"expr": "login_name_enc", "type": "catalog"}], "name": "shou<PERSON>hao_enc_DW", "description": "", "levelName": null, "id": "09f9c99a-9ff6-4336-af40-be29dffdf32e", "category": null}, {"level": null, "dataTag": "1.qianyi<PERSON><PERSON><PERSON>.xing<PERSON>z", "builtin": false, "patterns": [{"expr": "\\w*name\\w*", "type": "catalog"}, {"expr": "\\w*NAME\\w*", "type": "catalog"}, {"expr": "CUST_NM", "type": "catalog"}, {"expr": "TRUST_ACC_NAME", "type": "catalog"}, {"expr": "ENT_ADDRESS", "type": "catalog"}, {"expr": "AUTHOR", "type": "catalog"}, {"expr": "C_PROFITCLASSNAME", "type": "catalog"}, {"expr": "mobility_res", "type": "catalog"}, {"expr": "CREATE_BY", "type": "catalog"}, {"expr": "C_CUSTOMERNAME", "type": "catalog"}, {"expr": "VC_GLR", "type": "catalog"}, {"expr": "C_CONTACT", "type": "catalog"}, {"expr": "C_OPERATUSER", "type": "catalog"}], "name": "xing<PERSON>z", "description": "", "levelName": null, "id": "6ab6b1e6-ef27-4acd-ada0-f69cf54e6fc5", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.c_email", "builtin": false, "patterns": [{"expr": "c_email", "type": "catalog"}], "name": "c_email", "description": "", "levelName": null, "id": "c8c8ea3a-d61f-40a6-9065-96fb949bd7b3", "category": null}, {"level": null, "dataTag": "1.qianyi<PERSON><PERSON>i.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "(^(86|\\+86|0086)?[-]?((0\\d{2}[-]?\\d{8}([-]?\\d{1,4})?)|(0\\d{3}[-]?\\d{7,8}([-]?\\d{1,4})?))|(^((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(16[2|5-7])|(17[0-8])|(18[0,5-9])|(19[0-3|5-9]))\\d{8}))[ ]*$", "type": "data"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "ce13fd02-3ebe-450d-b8ee-17b607b4f2bd", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.weizhi", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"位置（结构化）\"\r\nscores:\r\n- desc: \"列注释以 位置、经度、纬度、经纬、经纬度 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u4F4D\\u7F6E\\\\s*$)|(\\u7ECF[\\u7EAC]?\\u5EA6\\\\s*$)|(\\u7ECF\\u7EAC\\\\s*$)|(\\u7EAC\\u5EA6\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则精确匹配location、latitude、longitude、lng、lat\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(^(?i)location$)|(^(?i)latitude$)|(^(?i)longitude$)|(^(?i)lng$)|(^(?i)lat$)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "we<PERSON>hi", "description": null, "levelName": null, "id": "a3523f7b-f9c8-4c8d-afe7-a6cb09b770cf", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.testc", "builtin": false, "patterns": [{"expr": "a", "type": "data"}, {"expr": "a", "type": "catalog"}], "name": "testc", "description": "tests desc", "levelName": null, "id": "0c23fd33-f6d4-41e1-9ec9-9b462b6a7c9a", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.youxiang_new", "builtin": false, "patterns": [{"expr": "{   \"model\" : \"1.0\",   \"desc\" : \"邮箱\",   \"scores\" : [ {     \"desc\" : \"匹配邮箱； 内容正则匹配 && 内容校验\",     \"name\" : \"HitRatio\",     \"rule\" : {       \"array_hit_ratio\" : [ {         \"and\" : [ {           \"regexp_find\" : [ \"^[a-zA-Z0-9._%-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,4}[ ]*$\", {             \"var\" : \"ArrayElementValue\"           }]         }, {           \"validator\" : [ \"cls.pii::email\", {             \"var\" : \"ArrayElementValue\"           } ]         } ]       }, {         \"var\" : \"column.values\"       } ]     },     \"output\" : true   }, {     \"desc\" : \"命中率必须大于等于 40\",     \"name\" : \"OverallScore\",     \"rule\" : {       \">=\" : [ {         \"var\" : \"$.HitRatio\"       }, 40 ]     },     \"output\" : true   } ] }", "type": "rule"}], "name": "youxiang_new", "description": "", "levelName": null, "id": "2c3601eb-70e1-41e1-99c2-fcf7c935b10e", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.youxiang", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"邮箱（结构化）\"\r\nscores:\r\n- desc: \"匹配列注释，以邮箱结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n    - \"\\u90AE[\\u4EF6\\u7BB1](\\u5730\\u5740)?\\\\s*$\"\r\n    - var: \"column.comment\"\r\n- desc: \"匹配列名为 email\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"^(?i)(email|email[_]?address)$\"\r\n      - var: \"column.name\"\r\n- desc: \"匹配 validator\"\r\n  name: \"ColumnValueValidator\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.FindColumnComment\"\r\n      - 100\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - array_hit_ratio:\r\n          - and:\r\n              - regexp_find:\r\n                  - \"^\\\\s*[a-zA-Z0-9.-]+@[a-zA-Z0-9-]+(\\\\.[a-zA-Z0-9-]+)*\\\\.[a-zA-Z0-9]{2,6}\\\\s*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - validator:\r\n                  - \"cls.pii::email\"\r\n                  - var: \"ArrayElementValue\"\r\n          - var: \"column.values\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n    - '>=':\r\n        - var: \"$.ColumnValueValidator\"\r\n        - 30\r\n    - 100\r\n    - 0", "type": "rule"}], "name": "youxiang", "description": null, "levelName": null, "id": "52ba1f37-6b3d-466a-94b5-b61d6ddca594", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.huanzheID", "builtin": true, "patterns": [{"expr": "(?i)patient.?id", "type": "catalog"}], "name": "huanzheID", "description": null, "levelName": null, "id": "5eb011c9-6394-4e24-86ff-b7ceccd126a0", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.xinyongka", "builtin": true, "patterns": [{"expr": "^[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[-, ]?[0-9]{4}[ ]*$", "type": "data"}], "name": "xinyongka", "description": null, "levelName": null, "id": "d315a8c7-38a9-4396-8601-bfb539743cc8", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.zhiye", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"职业（结构化）\"\r\nscores:\r\n- desc: \"列注释以  职业、工作 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u804C\\u4E1A\\\\s*$)|(\\u5DE5\\u4F5C\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"数据字典(命中率90%)\"\r\n  name: \"FindDataDict\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - \">=\":\r\n          - array_hit_ratio:\r\n              - \">\":\r\n                - search_in_dictionary:\r\n                    - \"professions\"\r\n                    - var: \"ArrayElementValue\"\r\n                - 0\r\n              - var: \"column.values\"\r\n          - 90\r\n- desc: \"[字段名]正则精确匹配job、work、occupation、profession\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindDataDict\"\r\n      - regexp_find:\r\n          - \"(^(?i)job$)|(^(?i)work$)|(^(?i)occupation$)|(^(?i)profession[sS]?$)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "zhiye", "description": null, "levelName": null, "id": "f7ef611b-a203-4cb2-8c5f-771b051e965b", "category": null}, {"level": null, "dataTag": "1.qianyi<PERSON>lei.c_figure_shifting", "builtin": false, "patterns": [{"expr": "c_figure_shifting", "type": "catalog"}], "name": "c_figure_shifting", "description": "", "levelName": null, "id": "c7f490c5-419a-4743-8684-73c25bc6f9cf", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.riqi", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"日期（结构化）\"\r\nscores:\r\n- desc: \"列注释以 日期、时间 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u65E5\\u671F\\\\s*$)|(\\u65F6\\u95F4\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则匹配date\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"(?i)date\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "riqi", "description": null, "levelName": null, "id": "20cd24b5-dd75-463b-ba2a-1957b83994b2", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.yong<PERSON>ing", "builtin": true, "patterns": [{"expr": "(?i)user.?name", "type": "catalog"}], "name": "yonghuming", "description": null, "levelName": null, "id": "627b4eac-b1e5-40f1-a44c-26b5df9d9729", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.zhaopian", "builtin": true, "patterns": [{"expr": "(?i)photo", "type": "catalog"}], "name": "zhao<PERSON>", "description": null, "levelName": null, "id": "1510f191-4236-4cfa-a3c0-6f5dae004455", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.yonghuID", "builtin": true, "patterns": [{"expr": "(?i)user.?id", "type": "catalog"}], "name": "yonghu<PERSON>", "description": null, "levelName": null, "id": "d115700e-c35f-467b-ae81-3729139318ae", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.chengshi", "builtin": true, "patterns": [{"expr": "(?i)city", "type": "catalog"}], "name": "ch<PERSON><PERSON>", "description": null, "levelName": null, "id": "0096a4c6-07a5-480d-881f-************", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.c_shelter_all", "builtin": false, "patterns": [{"expr": "c_shelter_all", "type": "catalog"}], "name": "c_shelter_all", "description": "", "levelName": null, "id": "34d56243-f6cd-492d-99e3-6de07592798d", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.chash", "builtin": false, "patterns": [{"expr": "c_hash", "type": "catalog"}], "name": "chash", "description": "", "levelName": null, "id": "aeaadc3b-cf3f-45e0-89fb-285f078450d1", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.guoji", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"国籍（结构化）\"\r\nscores:\r\n- desc: \"列注释以 国籍 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u56FD\\u7C4D\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则模糊匹配nationality、citizenship\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"((?i)nationality)|((?i)citizenship)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "guoji", "description": null, "levelName": null, "id": "b3e50887-7406-48b5-bbce-b9a6625e361f", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.s<PERSON><PERSON><PERSON>_mask_DW", "builtin": false, "patterns": [{"expr": "login_name_mask", "type": "catalog"}], "name": "shou<PERSON><PERSON>_mask_DW", "description": "", "levelName": null, "id": "71215576-a01d-467e-b353-482508ca7b73", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.cfigurerounding", "builtin": false, "patterns": [{"expr": "c_figure_rounding", "type": "catalog"}], "name": "cfigurerounding", "description": "", "levelName": null, "id": "b023cd21-5862-4136-9df9-beb610ef16fc", "category": null}, {"level": null, "dataTag": "1.q<PERSON><PERSON><PERSON><PERSON><PERSON>.xuexing", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"血型（结构化）\"\r\nscores:\r\n- desc: \"列注释以 血型 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u8840\\u578B\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"[字段名]正则模糊匹配blood(允许包含_)type\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - regexp_find:\r\n          - \"((?i)blood[_]?type)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "xuexing", "description": null, "levelName": null, "id": "91c11406-8280-43f6-9c75-594bd3aaa63b", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.s<PERSON>ji<PERSON><PERSON><PERSON>", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"手机号（中国内地)（结构化）\"\r\nscores:\r\n- desc: \"字段注释\"\r\n  name: \"MobilePhoneColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"\\u624B\\u673A\\u53F7[\\u7801]?\\\\s*$\"\r\n      - var: \"column.comment\"\r\n- desc: \"字段名匹配 以 id 结尾\"\r\n  name: \"ColumnNameEndWithId\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(?i)id$\"\r\n      - var: \"column.name\"\r\n- desc: \"计算列的手机号正则命中率\"\r\n  name: \"ColumnValueRegexpFind\"\r\n  output: true\r\n  rule:\r\n    condition:\r\n      - var: \"$.MobilePhoneColumnComment\"\r\n      - 100\r\n      - var: \"$.ColumnNameEndWithId\"\r\n      - 0\r\n      - array_hit_ratio:\r\n          - and:\r\n              - <=:\r\n                  - length:\r\n                      var: \"ArrayElementValue\"\r\n                  - 20\r\n              - regexp_find:\r\n                  - \"^\\\\s*([+]?\\\\s*86|0086)?\\\\s*[-]?\\\\s*((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(16[2|5-7])|(17[0-8])|(18[0-9])|(19[0-3|5-9]))\\\\d{8}\\\\s*$\"\r\n                  - var: \"ArrayElementValue\"\r\n          - var: \"column.values\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    'condition':\r\n      - '>=':\r\n          - var: \"$.ColumnValueRegexpFind\"\r\n          - 30\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": null, "levelName": null, "id": "670d4b27-7858-4126-bb81-a381655efa1d", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.telnumber", "builtin": false, "patterns": [{"expr": "^1[2-9]\\d{9}$", "type": "data"}], "name": "telnumber", "description": "电话号码 zht测试", "levelName": null, "id": "82c6aa20-27d7-4272-bf3e-e99fb9c90d89", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.c_unified_credit_cod", "builtin": false, "patterns": [{"expr": "c_unified_credit_code", "type": "catalog"}], "name": "c_unified_credit_cod", "description": "", "levelName": null, "id": "7be1914f-58c2-4520-ade0-a1687976cdd9", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.URL", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"URL（结构化）\"\r\nscores:\r\n  - desc: \"列注释精确匹配url\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"^\\\\s*(?i)url\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]精确匹配url\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"^(?i)url$\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 满足正则匹配规则\"\r\n    name: \"FindColumnValue\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - regexp_find:\r\n                  - \"(?i)^(ht|f)tp(s?)\\\\:\\\\/\\\\/[0-9a-zA-Z]([-.\\\\w]*[0-9a-zA-Z])*(:(0-9)*)*(\\\\/?)([a-zA-Z0-9\\\\-\\\\.\\\\?\\\\,\\\\'\\\\/\\\\\\\\\\\\+&amp;%\\\\$#_]*)?[ ]*$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnValue\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "URL", "description": "Update on 2024.01.30", "levelName": null, "id": "53c2edbd-5311-49e8-8b48-012d38e8d24c", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.mask_column", "builtin": false, "patterns": [{"expr": "^mask_column$", "type": "catalog"}], "name": "mask_column", "description": "", "levelName": null, "id": "2ba693fc-a640-415b-938c-88d700a461a2", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.guojia", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"国家（结构化）\"\r\nscores:\r\n- desc: \"列注释以 国家 结尾\"\r\n  name: \"FindColumnComment\"\r\n  output: true\r\n  rule:\r\n    regexp_find:\r\n      - \"(\\u56FD\\u5BB6\\\\s*$)\"\r\n      - var: \"column.comment\"\r\n- desc: \"数据字典(命中率90%)\"\r\n  name: \"FindDataDict\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindColumnComment\"\r\n      - \">=\":\r\n          - array_hit_ratio:\r\n              - \">\":\r\n                - search_in_dictionary:\r\n                    - \"countries\"\r\n                    - var: \"ArrayElementValue\"\r\n                - 0\r\n              - var: \"column.values\"\r\n          - 90\r\n- desc: \"[字段名]精确匹配country、nation 或 以 country(允许包含_)of、nation(允许包含_)of\"\r\n  name: \"FindColumnName\"\r\n  output: true\r\n  rule:\r\n    or:\r\n      - var: \"$.FindDataDict\"\r\n      - regexp_find:\r\n          - \"(^(?i)country$)|(^(?i)nation$)|((?i)country[_]?of)|((?i)nation[_]?of)\"\r\n          - var: \"column.name\"\r\n- desc: \"计算得分\"\r\n  name: \"OverallScore\"\r\n  output: true\r\n  rule:\r\n    \"condition\":\r\n      - var: \"$.FindColumnName\"\r\n      - 100\r\n      - 0", "type": "rule"}], "name": "guojia", "description": null, "levelName": null, "id": "14420dfc-04be-40af-a038-3f640cb6f2ec", "category": null}, {"level": null, "dataTag": "1.qianyi<PERSON><PERSON>i.c_keep_front_n_rear_", "builtin": false, "patterns": [{"expr": "c_keep_front_n_rear_m", "type": "catalog"}], "name": "c_keep_front_n_rear_", "description": "", "levelName": null, "id": "ac351d68-ac5f-4379-951d-9c9b16cccda3", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.cshelterparatition", "builtin": false, "patterns": [{"expr": "c_shelter_paratition", "type": "catalog"}], "name": "cshelterparatition", "description": "", "levelName": null, "id": "84f7d7f5-a5a7-4af0-943f-5b60074a9a4b", "category": null}, {"level": null, "dataTag": "1.q<PERSON><PERSON><PERSON>lei.IPv6", "builtin": true, "patterns": [{"expr": "model: \"1.0\"\r\ndesc: \"IPV6（结构化）\"\r\nscores:\r\n  - desc: \"列注释以 ipv6 结尾\"\r\n    name: \"FindColumnComment\"\r\n    output: true\r\n    rule:\r\n      regexp_find:\r\n        - \"(?i)ipv6\\\\s*$\"\r\n        - var: \"column.comment\"\r\n  - desc: \"[字段名]模糊匹配ipv6\"\r\n    name: \"FindColumnName\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnComment\"\r\n        - regexp_find:\r\n            - \"(?i)ipv6\"\r\n            - var: \"column.name\"\r\n  - desc: \"[数据特征] 满足正则匹配规则\"\r\n    name: \"FindColumnValue\"\r\n    output: true\r\n    rule:\r\n      or:\r\n        - var: \"$.FindColumnName\"\r\n        - '>=':\r\n          - array_hit_ratio:\r\n              - regexp_find:\r\n                  - \"^(:{2}(/0)?)|((([a-fA-F0-9]{1,4}|):){3,7}([a-fA-F0-9]{1,4}|:)[ ]*)$\"\r\n                  - var: \"ArrayElementValue\"\r\n              - var: \"column.values\"\r\n          - 80\r\n  - desc: \"计算得分\"\r\n    name: \"OverallScore\"\r\n    output: true\r\n    rule:\r\n      \"condition\":\r\n        - var: \"$.FindColumnValue\"\r\n        - 100\r\n        - 0", "type": "rule"}], "name": "IPv6", "description": "Update on 2024.01.22", "levelName": null, "id": "b095d3ef-dd52-40e6-a8fa-dc1638fd7952", "category": null}, {"level": null, "dataTag": "1.qianyifenlei.dianhuahaomazhongguo", "builtin": true, "patterns": [{"expr": "(^(0\\d{2}-\\d{8}(-\\d{1,4})?)|(0\\d{3}-\\d{7,8}(-\\d{1,4})?))|(^((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(18[0,5-9]))\\d{8})[ ]*$", "type": "data"}], "name": "dianhuahaomazhong<PERSON>o", "description": null, "levelName": null, "id": "5202ba39-c01a-4d6b-ad33-c7b43258bb1c", "category": null}], "scanJobHistoryId": 3629, "taskId": 99705}