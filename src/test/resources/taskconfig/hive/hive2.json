{"taskId": 9616, "currentVersion": 0, "scanJobHistoryId": 59648, "tenantId": 1, "name": "default-3322", "taskParam": {"excludeEmptyValues": true, "emptyPercentage": 1, "useRandomSampling": false, "sampleCount": 100, "sampleSqlTimeout": 15, "tableRowCountEnabled": false, "tableRowCount": 100, "tableRowCountUnit": 1, "excludeSchema": "", "excludeTable": "", "excludeColumn": "", "tableNameLike": null, "tableType": "TABLE", "columnNameLike": null, "searchValueLike": null, "minimumLength": 1, "maximumLength": 100, "hitPercentage": 60, "dataType": "TEXT,NUMBER", "scanRange": null, "markThresholds": 80, "tableRowCountLimit": 100}, "datasource": {"id": "3322", "name": "hive2", "sourceType": "hive", "host": "app-alpha.yuandiansec.net", "port": "30100", "authCfg": {"username": "admin", "password": "first@YD"}, "extraCfg": ""}, "policies": [{"id": 31409, "name": "电话号码(中国内地)", "builtin": "1", "category": 20296, "dataTag": "cls.pii::phonech", "dataTagTypeForDspm": "BASIC", "dataTagOrderBy": 50, "level": 40, "levelName": "S4", "patterns": [{"type": "data", "expr": "(^(0\\d{2}-\\d{8}(-\\d{1,4})?)|(0\\d{3}-\\d{7,8}(-\\d{1,4})?))|(^((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(18[0,5-9]))\\d{8})[ ]*$"}]}]}