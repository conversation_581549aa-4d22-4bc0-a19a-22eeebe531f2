{"taskParam": {"dcapProbeId": 1, "dbInstAddressId": 83869, "dbInstAccountId": 28223, "viewDefinitionSampling": false, "sampleCount": 100, "sampleSqlTimeout": 15, "minimumLength": 0, "maximumLength": 256, "hitPercentage": 60, "tableType": "TABLE", "dataType": "TEXT,NUMBER", "excludeSchema": "", "excludeTable": "", "excludeColumn": "", "tableRowCountEnabled": false, "tableRowCount": 100, "tableRowCountUnit": 1, "excludeEmptyValues": false, "emptyPercentage": 90, "scanRange": {"excludedDatabase": [], "excludedSchema": [], "excludedSynonym": [], "excludedTable": [], "excludedView": [], "selectedDatabase": [], "selectedSchema": [], "selectedSynonym": [], "selectedTable": [], "selectedView": []}}, "datasource": {"dbNameType": 2, "sourceType": "gaussdb", "port": "30122", "name": "gaussdb", "host": "app-alpha.yuandiansec.net", "authCfg": {"password": "P@ssword123", "username": "gaussdb"}, "id": 28225, "extraCfg": ""}, "tenantId": 1, "name": "default-db-28225", "policies": [], "scanJobHistoryId": 86125, "taskId": 82902}