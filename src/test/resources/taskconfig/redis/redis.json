{"taskId": 2105, "currentVersion": 0, "scanJobHistoryId": 59436, "tenantId": 1, "name": "default-3995", "taskParam": {"excludeEmptyValues": false, "emptyPercentage": 90, "useRandomSampling": false, "sampleCount": 100, "sampleSqlTimeout": 15, "tableRowCountEnabled": false, "tableRowCount": 100, "tableRowCountUnit": 1, "excludeSchema": "", "excludeTable": "", "excludeColumn": "", "tableNameLike": null, "tableType": "TABLE", "columnNameLike": null, "searchValueLike": null, "minimumLength": 1, "maximumLength": 100, "hitPercentage": 60, "dataType": "TEXT,NUMBER", "scanRange": {"selectedSchema": [], "selectedDatabase": [], "selectedTable": [], "selectedView": [], "selectedSynonym": [], "excludedDatabase": [], "excludedSchema": [], "excludedTable": [], "excludedView": [], "excludedSynonym": []}, "markThresholds": 80, "tableRowCountLimit": 100}, "datasource": {"id": "3995", "name": "redis", "sourceType": "redis", "host": "**************", "port": "30813", "authCfg": {"username": "none_user", "password": "1qaz@WSX3edc"}, "extraCfg": ""}, "policies": []}