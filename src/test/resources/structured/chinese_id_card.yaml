model: "1.0"
desc: "身份证（中国内地）（结构化）"
scores:
- desc: "内容长度限制必须大于等于 18"
  name: "IdCardLength"
  output: true
  rule:
    ">=":
      - var: "column.size"
      - 18
- desc: "注释以身份证号结尾"
  name: "ColumnComment"
  output: true
  rule:
    and:
      - var: "$.IdCardLength"
      - regexp_find:
          - "(\u8EAB\u4EFD\u8BC1\u53F7$)|(\u8BC1\u4EF6\u53F7\u7801$)"
          - var: "column.comment"
- desc: "如果注释匹配，直接返回，否则计算命中率。"
  name: "RegexpFindAndValidator"
  output: true
  rule:
    condition:
      - var: "$.ColumnComment"
      - 100
      - array_hit_ratio:
          - and:
            - '>=':
                - length:
                  - var: "ArrayElementValue"
                - 18
            - regexp_find:
                - "(([1][1-5])|([2][1-3])|([3][1-7])|([4][1-6])|([5][0-4])|([6][1-5])|([7][1])|([8][1-2]))\\d{4}(([1][9]\\d{2})|([2]\\d{3}))(([0][1-9])|([1][0-2]))(([0][1-9])|([1-2][0-9])|([3][0-1]))\\d{3}[0-9xX]\\s*"
                - var: "ArrayElementValue"
            - validator:
                - "cls.pii::idcard"
                - var: "ArrayElementValue"
          - var: "column.values"
- desc: "命中率必须大于等于 30"
  name: "CalculateHitRatio"
  output: true
  rule:
    '>=':
      - var: "$.RegexpFindAndValidator"
      - 30
- desc: "计算得分"
  name: "OverallScore"
  output: true
  rule:
    'condition':
    - var: "$.CalculateHitRatio"
    - 100
    - 0