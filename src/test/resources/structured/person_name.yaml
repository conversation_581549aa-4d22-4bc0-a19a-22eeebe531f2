model: "1.0"
desc: "姓名（结构化）"
scores:
- desc: "字段名规则列表"
  name: "FindColumnNameList"
  output: true
  rule:
    or:
      - regexp_find:
          - "^(?i)(insured|insured[_-]?name|receiver|receiver[_-]?name|sender|sender[_-]?name|recipient|recipient[-_]?name|full[_-]?name|first[_-]?name|last[_-]?name|real[_-]?name|staff[_-]?name|candidate|cardholder|cardholder[_-]?name|member[_-]?name|payer[_-]?name|payee[_-]?name|purchaser[_-]?name|trustee[_-]?name)$"
          - var: "column.name"
      - regexp_find:
          - "(?i)(bank[_-]?account[_-]?name)$"
          - var: "column.name"
      - regexp_find:
          - "(?i)(customer[_-]?name)$"
          - var: "column.name"
      - regexp_find:
          - "(?i)(holder[_-]?name)$"
          - var: "column.name"
      - regexp_find:
          - "(?i)(contact[s]?[_-]?name)$"
          - var: "column.name"
      - regexp_find:
          - "(?i)(patient[_-]?name)$"
          - var: "column.name"
      - regexp_find:
          - "(?i)(spouse[_-]?name)$"
          - var: "column.name"
      - regexp_find:
          - "(?i)(kin[_-]?name)$"
          - var: "column.name"
      - regexp_find:
          - "(?i)(student[_-]?name)$"
          - var: "column.name"
      - regexp_find:
          - "^(?i)(name[_-]?of|name[_-]?on)"
          - var: "column.name"
- desc: "列名匹配"
  name: "FindColumnName"
  output: true
  rule:
    regexp_find:
      - "^(?!.*(org|medicine))(.*(name|er|yee))$|^(insured|initial)$"
      - var: "column.name"
- desc: "字段注释"
  name: "FindColumnComment"
  output: true
  rule:
    and:
      - var: "$.FindColumnName"
      - regexp_find: # （字符串中包含 姓名 或者 账户名称）同时（不能包含 企 或者 公 或者 人 或者 修改 或者 编辑 或者 变更 或者 创建 或者 操作)
          - "^(?=.*(\u59D3\u540D|\u8D26\u6237\u540D\u79F0))(?!.*(\u4F01|\u516C|\u4EBA|\u4FEE\u6539|\u7F16\u8F91|\u53D8\u66F4|\u521B\u5EFA|\u64CD\u4F5C)).*$"
          - var: "column.comment"
- desc: "反向字段注释"
  name: "ReverseFindColumnComment"
  output: true
  rule:
    and:
      - var: "$.FindColumnName"
      - regexp_find: # 品名 或 品名称 或 司名称 或 业名称 或 厂名称 或 商名称 或 社名称 或 机构名称 或 种名称 结尾
          - "(\u54C1\u540D|\u54C1\u540D\u79F0|\u53F8\u540D\u79F0|\u4E1A\u540D\u79F0|\u5382\u540D\u79F0|\u5546\u540D\u79F0|\u793E\u540D\u79F0|\u673A\u6784\u540D\u79F0|\u79CD\u540D\u79F0)$"
          - var: "column.comment"
- desc: "NLP 识别"
  name: "NlpAnalysis"
  output: true
  rule:
    condition:
      - var: "$.FindColumnNameList"
      - true
      - var: "$.FindColumnComment"
      - true
      - var: "$.ReverseFindColumnComment"
      - false
      - '>=':
          - array_hit_ratio:
              - and:
                  - <=:
                      - length:
                          var: "ArrayElementValue"
                      - 5
                  - validator:
                      - "cls.pii::name"
                      - var: "ArrayElementValue"
              - var: "column.values"
          - 30
- desc: "表名和列名组合规则"
  name: "TableAndColumnCombination"
  output: true
  rule:
    or:
      - var: "$.NlpAnalysis"
      - and:
          - regexp_find:
              - "(?i)(erinfo|yeeinfo)$"
              - var: "table.name"
          - regexp_find:
              - "(?i)(name)"
              - var: "column.name"
- desc: "计算得分"
  name: "OverallScore"
  output: true
  rule:
    "condition":
    - var: "$.TableAndColumnCombination"
    - 100
    - 0