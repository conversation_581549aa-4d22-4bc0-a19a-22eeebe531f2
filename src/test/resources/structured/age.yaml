model: "1.0"
desc: "年龄（结构化）"
scores:
- desc: "列注释匹配 以 年龄 结尾"
  name: "FindColumnComment"
  output: true
  rule:
    regexp_find:
      - "\u5E74\u9F84\\s*$"
      - var: "column.comment"
- desc: "列名精确匹配 age，且值 0-100 之间，90% 命中率"
  name: "FindColumnName"
  output: true
  rule:
    or:
      - var: "$.FindColumnComment"
      - and:
        - regexp_find:
          - "^(?i)age$"
          - var: "column.name"
        - ">=":
            - array_hit_ratio:
                - and:
                    - ">=":
                        - var: "ArrayElementValue"
                        - 0
                    - "<=":
                        - var: "ArrayElementValue"
                        - 100
                - var: "column.values"
            - 90
- desc: "计算得分"
  name: "OverallScore"
  output: true
  rule:
    "condition":
      - var: "$.FindColumnName"
      - 100
      - 0