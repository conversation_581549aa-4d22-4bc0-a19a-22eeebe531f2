model: "1.0"
desc: "邮编（结构化）"
scores:
- desc: "列注释以 邮编 结尾"
  name: "FindColumnComment"
  output: true
  rule:
    regexp_find:
      - "\u90AE\u7F16\\s*$"
      - var: "column.comment"
- desc: "[字段名]正则精确匹配postal、postcode、postalcode、zipcode 模糊匹配 (允许包含_)of"
  name: "FindColumnName"
  output: true
  rule:
    or:
      - var: "$.FindColumnComment"
      - regexp_find:
          - "(^(?i)postal$)|(^(?i)postcode$)|(^(?i)postalcode$)|(^(?i)zipcode$)|((?i)postal[_]?of)|((?i)postcode[_]?of)|((?i)postalcode[_]?of)|((?i)zipcode[_]?of)"
          - var: "column.name"
- desc: "计算得分"
  name: "OverallScore"
  output: true
  rule:
    "condition":
      - var: "$.FindColumnName"
      - 100
      - 0