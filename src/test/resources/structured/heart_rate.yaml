model: "1.0"
desc: "心率（结构化）"
scores:
  - desc: "列注释匹配 以 心率 结尾"
    name: "FindColumnComment"
    output: true
    rule:
      regexp_find:
        - "\u5FC3\u7387\\s*$"
        - var: "column.comment"
  - desc: "[字段名]正则模糊匹配heart (允许包含_)rate 且 内容校验是在40-180之间的整数(命中率90%)"
    name: "FindColumnName"
    output: true
    rule:
      or:
        - var: "$.FindColumnComment"
        - and:
            - regexp_find:
                - "(?i)heart[_]?rate"
                - var: "column.name"
            - ">=":
                - array_hit_ratio:
                    - and:
                        - ">=":
                            - var: "ArrayElementValue"
                            - 40
                        - "<=":
                            - var: "ArrayElementValue"
                            - 180
                    - var: "column.values"
                - 90
  - desc: "计算得分"
    name: "OverallScore"
    output: true
    rule:
      "condition":
        - var: "$.FindColumnName"
        - 100
        - 0