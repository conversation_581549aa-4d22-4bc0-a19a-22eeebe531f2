model: "1.0"
desc: "国籍（结构化）"
scores:
- desc: "列注释以 国籍 结尾"
  name: "FindColumnComment"
  output: true
  rule:
    regexp_find:
      - "\u56FD\u7C4D\\s*$"
      - var: "column.comment"
- desc: "[字段名]正则模糊匹配nationality、citizenship"
  name: "FindColumnName"
  output: true
  rule:
    or:
      - var: "$.FindColumnComment"
      - regexp_find:
          - "((?i)nationality)|((?i)citizenship)"
          - var: "column.name"
- desc: "计算得分"
  name: "OverallScore"
  output: true
  rule:
    "condition":
      - var: "$.FindColumnName"
      - 100
      - 0