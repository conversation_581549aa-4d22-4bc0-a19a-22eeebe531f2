model: "1.0"
desc: "IPV4（结构化）"
scores:
  - desc: "列注释以 ip、ipv4 结尾"
    name: "FindColumnComment"
    output: true
    rule:
      regexp_find:
        - "((?i)ipv4\\s*$)|((?i)ip\\s*$)"
        - var: "column.comment"
  - desc: "[字段名]精确匹配ip、ipv4"
    name: "FindColumnName"
    output: true
    rule:
      or:
        - var: "$.FindColumnComment"
        - regexp_find:
            - "(^(?i)ip$)|(^(?i)ipv4$)"
            - var: "column.name"
  - desc: "[数据特征] 满足正则匹配规则"
    name: "FindColumnValue"
    output: true
    rule:
      or:
        - var: "$.FindColumnName"
        - '>=':
          - array_hit_ratio:
              - regexp_find:
                  - "^(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])[ ]*$"
                  - var: "ArrayElementValue"
              - var: "column.values"
          - 80
  - desc: "计算得分"
    name: "OverallScore"
    output: true
    rule:
      "condition":
        - var: "$.FindColumnValue"
        - 100
        - 0