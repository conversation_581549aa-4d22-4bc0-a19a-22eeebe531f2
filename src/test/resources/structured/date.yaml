model: "1.0"
desc: "日期（结构化）"
scores:
- desc: "列注释以 日期、时间 结尾"
  name: "FindColumnComment"
  output: true
  rule:
    regexp_find:
      - "(\u65E5\u671F\\s*$)|(\u65F6\u95F4\\s*$)"
      - var: "column.comment"
- desc: "[字段名]正则匹配date"
  name: "FindColumnName"
  output: true
  rule:
    or:
      - var: "$.FindColumnComment"
      - regexp_find:
          - "(?i)date"
          - var: "column.name"
- desc: "计算得分"
  name: "OverallScore"
  output: true
  rule:
    "condition":
      - var: "$.FindColumnName"
      - 100
      - 0