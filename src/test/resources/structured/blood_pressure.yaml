model: "1.0"
desc: "血压（结构化）"
scores:
- desc: "列注释匹配 以 血压 结尾"
  name: "FindColumnComment"
  output: true
  rule:
    regexp_find:
      - "\u8840\u538B\\s*$"
      - var: "column.comment"
- desc: "[字段名]正则模糊匹配blood(允许包含_)pressure 且 内容校验在 20-220 之间的整数(命中率90%)"
  name: "FindColumnName"
  output: true
  rule:
    or:
      - var: "$.FindColumnComment"
      - and:
        - regexp_find:
          - "(?i)blood[_]?pressure"
          - var: "column.name"
        - ">=":
            - array_hit_ratio:
                - and:
                    - ">=":
                        - var: "ArrayElementValue"
                        - 20
                    - "<=":
                        - var: "ArrayElementValue"
                        - 220
                - var: "column.values"
            - 90
- desc: "计算得分"
  name: "OverallScore"
  output: true
  rule:
    "condition":
      - var: "$.FindColumnName"
      - 100
      - 0