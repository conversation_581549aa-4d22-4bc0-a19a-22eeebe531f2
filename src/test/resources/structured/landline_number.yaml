model: "1.0"
desc: "座机号码（中国内地)（结构化）"
scores:
- desc: "匹配列名称"
  name: "MatchColumnNameAndSize"
  output: true
  rule:
    and:
      - regexp_find:
          - "(?i)(fax|phone|landline)"
          - var: "column.name"
      - ">=":
          - var: "column.size"
          - 10
- desc: "匹配电话号码（中国内地)； 内容正则匹配 && 内容长度限制 && 内容校验"
  name: "PhoneHitRatio"
  output: true
  rule:
    array_hit_ratio:
      - and:
          - var: "$.MatchColumnNameAndSize"
          - '<=':
              - length:
                  var: "ArrayElementValue"
              - 20
          - regexp_find:
              - '\s*(86|\+86|0086)?\s*-?\s*(0\d{2}\s*-?\s*\d{8}(\s*-?\s*\d{1,4})?)|(0\d{3}\s*-?\s*\d{7,8}(\s*-?\s*\d{1,4})?)\s*'
              - var: "ArrayElementValue"
      - var: "column.values"
- desc: "计算得分"
  name: "OverallScore"
  output: true
  rule:
    'condition':
    - '>=':
        - var: "$.PhoneHitRatio"
        - 30
    - 100
    - 0