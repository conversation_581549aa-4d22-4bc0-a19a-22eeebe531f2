model: "1.0"
desc: "IPV6（结构化）"
scores:
  - desc: "列注释以 ipv6 结尾"
    name: "FindColumnComment"
    output: true
    rule:
      regexp_find:
        - "(?i)ipv6\\s*$"
        - var: "column.comment"
  - desc: "[字段名]模糊匹配ipv6"
    name: "FindColumnName"
    output: true
    rule:
      or:
        - var: "$.FindColumnComment"
        - regexp_find:
            - "(?i)ipv6"
            - var: "column.name"
  - desc: "[数据特征] 满足正则匹配规则"
    name: "FindColumnValue"
    output: true
    rule:
      or:
        - var: "$.FindColumnName"
        - '>=':
          - array_hit_ratio:
              - regexp_find:
                  - "^(:{2}(/0)?)|((([a-fA-F0-9]{1,4}|):){3,7}([a-fA-F0-9]{1,4}|:)[ ]*)$"
                  - var: "ArrayElementValue"
              - var: "column.values"
          - 80
  - desc: "计算得分"
    name: "OverallScore"
    output: true
    rule:
      "condition":
        - var: "$.FindColumnValue"
        - 100
        - 0