model: "1.0"
desc: "位置（结构化）"
scores:
- desc: "列注释以 位置、经度、纬度、经纬、经纬度 结尾"
  name: "FindColumnComment"
  output: true
  rule:
    regexp_find:
      - "(\u4F4D\u7F6E\\s*$)|(\u7ECF[\u7EAC]?\u5EA6\\s*$)|(\u7ECF\u7EAC\\s*$)|(\u7EAC\u5EA6\\s*$)"
      - var: "column.comment"
- desc: "[字段名]正则精确匹配location、latitude、longitude、lng、lat"
  name: "FindColumnName"
  output: true
  rule:
    or:
      - var: "$.FindColumnComment"
      - regexp_find:
          - "(^(?i)location$)|(^(?i)latitude$)|(^(?i)longitude$)|(^(?i)lng$)|(^(?i)lat$)"
          - var: "column.name"
- desc: "计算得分"
  name: "OverallScore"
  output: true
  rule:
    "condition":
      - var: "$.FindColumnName"
      - 100
      - 0