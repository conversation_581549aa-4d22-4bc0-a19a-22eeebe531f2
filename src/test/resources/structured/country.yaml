model: "1.0"
desc: "国家（结构化）"
scores:
- desc: "列注释以 国家 结尾"
  name: "FindColumnComment"
  output: true
  rule:
    regexp_find:
      - "(\u56FD\u5BB6\\s*$)"
      - var: "column.comment"
- desc: "数据字典(命中率90%)"
  name: "FindDataDict"
  output: true
  rule:
    or:
      - var: "$.FindColumnComment"
      - ">=":
          - array_hit_ratio:
              - ">":
                - search_in_dictionary:
                    - "countries"
                    - var: "ArrayElementValue"
                - 0
              - var: "column.values"
          - 90
- desc: "[字段名]精确匹配country、nation 或 以 country(允许包含_)of、nation(允许包含_)of"
  name: "FindColumnName"
  output: true
  rule:
    or:
      - var: "$.FindDataDict"
      - regexp_find:
          - "(^(?i)country$)|(^(?i)nation$)|((?i)country[_]?of)|((?i)nation[_]?of)"
          - var: "column.name"
- desc: "计算得分"
  name: "OverallScore"
  output: true
  rule:
    "condition":
      - var: "$.FindColumnName"
      - 100
      - 0