model: "1.0"
desc: "地址（结构化）"
scores:
- desc: "列名匹配 address 或者 addr"
  name: "FindColumnName"
  output: true
  rule:
    regexp_find:
      - "(?i)address|(?i)addr"
      - var: "column.name"
- desc: "列定义长度 大于 20"
  name: "ColumnDefLength"
  output: true
  rule:
    and:
    - var: "$.FindColumnName"
    - '>=':
      - var: "column.size"
      - 20
- desc: "字段注释 包含 住址 or 家庭地址 or 办公地址 or 联系地址"
  name: "FieldCommentsContains"
  output: true
  rule:
    and:
    - var: "$.ColumnDefLength"
    - regexp_find:
        - "\u4f4f\u5740|\u5bb6\u5ead\u5730\u5740|\u529e\u516c\u5730\u5740|\u8054\u7cfb\u5730\u5740"
        - var: "column.comment"
- desc: "NLP 识别地址。以上条件都满足或者是 NLP 识别率达到 30%"
  name: "NlpAnalysis"
  output: true
  rule:
    or:
      - var: "$.FieldCommentsContains"
      - and:
        - var: "$.ColumnDefLength"
        - '>=':
            - array_hit_ratio:
                - validator:
                    - "cls.piilinkable::address"
                    - var: "ArrayElementValue"
                - var: "column.values"
            - 30
- desc: "NLP 如果不满足，继续 字段内容 匹配规则列表"
  name: "RegexpListFind"
  output: true
  rule:
    or:
      - var: "$.NlpAnalysis"
      - and:
          - var: "$.ColumnDefLength"
          - '>=':
              - array_hit_ratio:
                  - or: # 包含 小区|单元|号楼|大厦|家园|新区|村委会|公安局|派出所|街道办|公寓|厂区
                      - regexp_find:
                          - "\u5c0f\u533a|\u5355\u5143|\u53f7\u697c|\u5927\u53a6|\u5bb6\u56ed|\u65b0\u533a|\u6751\u59d4\u4f1a|\u516c\u5b89\u5c40|\u6d3e\u51fa\u6240|\u8857\u9053\u529e|\u516c\u5bd3|\u5382\u533a"
                          - var: "ArrayElementValue"
                      - regexp_find: # 以 3位数字+室or房 结尾
                          - "\\d{1,3}\\s*[\u5BA4\u623F]$"
                          - var: "ArrayElementValue"
                      - regexp_find: # 按前后顺序包含 市 and 区 and （1位数字+号），市区两字不可紧相邻
                          - "\u5E02(\\s*[^\\s\u5E02\u533A\\d])+\\s*\u533A(\\s*[^\\s\u5E02\u533A\u53F7])+\\d*\\s*\u53F7"
                          - var: "ArrayElementValue"
                      - regexp_find: # 按前后顺序包含 村 and 号，之间有至少一位值
                          - "\u6751(\\s*[^\\s\u6751\u53F7])+\\s*\u53F7"
                          - var: "ArrayElementValue"
                      - regexp_find: # 按前后顺序包含 市 and 村，之间有至少一位值
                          - "\u5E02(\\s*[^\\s\u5E02\u6751])+\\s*\u6751"
                          - var: "ArrayElementValue"
                      - regexp_find: # 按前后顺序包含 区 and 村，之间有至少一位值
                          - "\u533A(\\s*[^\\s\u533A\u6751])+\\s*\u6751"
                          - var: "ArrayElementValue"
                      - regexp_find: # 按前后顺序包含 镇 and 村，之间有至少一位值
                          - "\u9547(\\s*[^\\s\u9547\u6751])+\\s*\u6751"
                          - var: "ArrayElementValue"
                      - regexp_find: # 按前后顺序包含 县 and 村，之间有至少一位值
                          - "\u53BF(\\s*[^\\s\u53BF\u6751])+\\s*\u6751"
                          - var: "ArrayElementValue"
                      - regexp_find: # 按前后顺序包含 巷 and 号，之间有至少一位值
                          - "\u5DF7(\\s*[^\\s\u5DF7\u53F7])+\\s*\u53F7"
                          - var: "ArrayElementValue"
                      - regexp_find: # 包含 任意位数字+栋+任意位数字
                          - "\\d+\\s*\u680B\\s*\\d+"
                          - var: "ArrayElementValue"
                      - regexp_find: # 按前后顺序包含 社区 and 号，之间有至少一位值
                          - "\u793E\u533A(\\s*[^\\s\u793E\u533A\u53F7])+\\s*\u53F7"
                          - var: "ArrayElementValue"
                      - regexp_find: # 以 局 结尾
                          - "\u5C40\\s*$"
                          - var: "ArrayElementValue"
                  - var: "column.values"
              - 30
- desc: "计算得分"
  name: "OverallScore"
  output: true
  rule:
    "condition":
      - var: "$.RegexpListFind"
      - 100
      - 0