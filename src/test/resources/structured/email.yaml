model: "1.0"
desc: "邮箱（结构化）"
scores:
- desc: "匹配列注释，以邮箱结尾"
  name: "FindColumnComment"
  output: true
  rule:
    regexp_find:
    - "\u90AE[\u4EF6\u7BB1](\u5730\u5740)?\\s*$"
    - var: "column.comment"
- desc: "匹配列名为 email"
  name: "FindColumnName"
  output: true
  rule:
    regexp_find:
      - "^(?i)(email|email[_]?address)$"
      - var: "column.name"
- desc: "匹配 validator"
  name: "ColumnValueValidator"
  output: true
  rule:
    condition:
      - var: "$.FindColumnComment"
      - 100
      - var: "$.FindColumnName"
      - 100
      - array_hit_ratio:
          - and:
              - regexp_find:
                  - "^\\s*[a-zA-Z0-9.-]+@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*\\.[a-zA-Z0-9]{2,6}\\s*$"
                  - var: "ArrayElementValue"
              - validator:
                  - "cls.pii::email"
                  - var: "ArrayElementValue"
          - var: "column.values"
- desc: "计算得分"
  name: "OverallScore"
  output: true
  rule:
    "condition":
    - '>=':
        - var: "$.ColumnValueValidator"
        - 30
    - 100
    - 0