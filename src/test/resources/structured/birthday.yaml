model: "1.0"
desc: "生日（结构化）"
scores:
- desc: "列注释 以 生日 、 出生日期 结尾"
  name: "FindColumnComment"
  output: true
  rule:
    regexp_find:
      - "([\u51FA]?\u751F\u65E5[\u671F]?\\s*$)"
      - var: "column.comment"
- desc: "[字段名]正则精确匹配 birth、模糊匹配birthday、date(允许包含_)of(允许包含_) birth"
  name: "FindColumnName"
  output: true
  rule:
    or:
      - var: "$.FindColumnComment"
      - regexp_find:
          - "(^(?i)birth$)|((?i)birthday)|((?i)date[_]?of[_]?birth)"
          - var: "column.name"
- desc: "计算得分"
  name: "OverallScore"
  output: true
  rule:
    "condition":
      - var: "$.FindColumnName"
      - 100
      - 0