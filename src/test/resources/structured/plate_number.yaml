model: "1.0"
desc: "车牌号(大陆)（结构化）"
scores:
  - desc: "列注释以 车牌、车牌号 结尾"
    name: "FindColumnComment"
    output: true
    rule:
      regexp_find:
        - "\u8F66\u724C[\u53F7]?\\s*$"
        - var: "column.comment"
  - desc: "[字段名]正则模糊匹配license(允许包含_)plate、plate(允许包含_)number"
    name: "FindColumnName"
    output: true
    rule:
      or:
        - var: "$.FindColumnComment"
        - regexp_find:
            - "((?i)license[_]?plate)|((?i)plate[_]?number)"
            - var: "column.name"
  - desc: "[数据特征] 内置车牌号匹配规则 cls.pii::cardnum (命中率80%)"
    name: "FindValidator"
    output: true
    rule:
      or:
        - var: "$.FindColumnName"
        - '>=':
          - array_hit_ratio:
              - validator:
                  - "cls.pii::cardnum"
                  - var: "ArrayElementValue"
              - var: "column.values"
          - 80
  - desc: "计算得分"
    name: "OverallScore"
    output: true
    rule:
      "condition":
        - var: "$.FindValidator"
        - 100
        - 0