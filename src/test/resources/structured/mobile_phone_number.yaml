model: "1.0"
desc: "手机号（中国内地)（结构化）"
scores:
- desc: "字段注释"
  name: "MobilePhoneColumnComment"
  output: true
  rule:
    regexp_find:
      - "\u624B\u673A\u53F7[\u7801]?\\s*$"
      - var: "column.comment"
- desc: "字段名匹配 以 id 结尾"
  name: "ColumnNameEndWithId"
  output: true
  rule:
    regexp_find:
      - "(?i)id$"
      - var: "column.name"
- desc: "计算列的手机号正则命中率"
  name: "ColumnValueRegexpFind"
  output: true
  rule:
    condition:
      - var: "$.MobilePhoneColumnComment"
      - 100
      - var: "$.ColumnNameEndWithId"
      - 0
      - array_hit_ratio:
          - and:
              - <=:
                  - length:
                      var: "ArrayElementValue"
                  - 20
              - regexp_find:
                  - "^\\s*([+]?\\s*86|0086)?\\s*[-]?\\s*((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(16[2|5-7])|(17[0-8])|(18[0-9])|(19[0-3|5-9]))\\d{8}\\s*$"
                  - var: "ArrayElementValue"
          - var: "column.values"
- desc: "计算得分"
  name: "OverallScore"
  output: true
  rule:
    'condition':
      - '>=':
          - var: "$.ColumnValueRegexpFind"
          - 30
      - 100
      - 0