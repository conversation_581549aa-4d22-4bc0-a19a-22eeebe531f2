model: "1.0"
desc: "宗教（结构化）"
scores:
- desc: "列注释以 宗教、信仰 结尾"
  name: "FindColumnComment"
  output: true
  rule:
    regexp_find:
      - "(\u5B97\u6559\\s*$)|(\u4FE1\u4EF0\\s*$)"
      - var: "column.comment"
- desc: "数据字典(命中率90%)"
  name: "FindDataDict"
  output: true
  rule:
    or:
      - var: "$.FindColumnComment"
      - ">=":
          - array_hit_ratio:
              - ">":
                - search_in_dictionary:
                    - "religions"
                    - var: "ArrayElementValue"
                - 0
              - var: "column.values"
          - 90
- desc: "[字段名]正则精确匹配 faith 或 精确匹配religion 或 模糊匹配religion(允许包含_)of "
  name: "FindColumnName"
  output: true
  rule:
    or:
      - var: "$.FindDataDict"
      - regexp_find:
          - "(^(?i)faith$)|(^(?i)religion$)|((?i)religion[_]?of)"
          - var: "column.name"
- desc: "计算得分"
  name: "OverallScore"
  output: true
  rule:
    "condition":
      - var: "$.FindColumnName"
      - 100
      - 0