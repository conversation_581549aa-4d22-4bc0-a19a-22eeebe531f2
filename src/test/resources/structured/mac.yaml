model: "1.0"
desc: "MAC（结构化）"
scores:
  - desc: "列注释以 mac 结尾"
    name: "FindColumnComment"
    output: true
    rule:
      regexp_find:
        - "(?i)mac\\s*$"
        - var: "column.comment"
  - desc: "[字段名]精确匹配mac"
    name: "FindColumnName"
    output: true
    rule:
      or:
        - var: "$.FindColumnComment"
        - regexp_find:
            - "^(?i)mac$"
            - var: "column.name"
  - desc: "[数据特征] 满足正则匹配规则"
    name: "FindColumnValue"
    output: true
    rule:
      or:
        - var: "$.FindColumnName"
        - '>=':
          - array_hit_ratio:
              - regexp_find:
                  - "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})|([0-9a-fA-F]{4}\\\\.[0-9a-fA-F]{4}\\\\.[0-9a-fA-F]{4})[ ]*$"
                  - var: "ArrayElementValue"
              - var: "column.values"
          - 80
  - desc: "计算得分"
    name: "OverallScore"
    output: true
    rule:
      "condition":
        - var: "$.FindColumnValue"
        - 100
        - 0