model: "1.0"
desc: "职业（结构化）"
scores:
- desc: "列注释以  职业、工作 结尾"
  name: "FindColumnComment"
  output: true
  rule:
    regexp_find:
      - "(\u804C\u4E1A\\s*$)|(\u5DE5\u4F5C\\s*$)"
      - var: "column.comment"
- desc: "数据字典(命中率90%)"
  name: "FindDataDict"
  output: true
  rule:
    or:
      - var: "$.FindColumnComment"
      - ">=":
          - array_hit_ratio:
              - ">":
                - search_in_dictionary:
                    - "professions"
                    - var: "ArrayElementValue"
                - 0
              - var: "column.values"
          - 90
- desc: "[字段名]正则精确匹配job、work、occupation、profession"
  name: "FindColumnName"
  output: true
  rule:
    or:
      - var: "$.FindDataDict"
      - regexp_find:
          - "(^(?i)job$)|(^(?i)work$)|(^(?i)occupation$)|(^(?i)profession[sS]?$)"
          - var: "column.name"
- desc: "计算得分"
  name: "OverallScore"
  output: true
  rule:
    "condition":
      - var: "$.FindColumnName"
      - 100
      - 0