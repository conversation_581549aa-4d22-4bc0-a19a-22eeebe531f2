model: "1.0"
desc: "血型（结构化）"
scores:
- desc: "列注释以 血型 结尾"
  name: "FindColumnComment"
  output: true
  rule:
    regexp_find:
      - "\u8840\u578B\\s*$"
      - var: "column.comment"
- desc: "[字段名]正则模糊匹配blood(允许包含_)type"
  name: "FindColumnName"
  output: true
  rule:
    or:
      - var: "$.FindColumnComment"
      - regexp_find:
          - "((?i)blood[_]?type)"
          - var: "column.name"
- desc: "计算得分"
  name: "OverallScore"
  output: true
  rule:
    "condition":
      - var: "$.FindColumnName"
      - 100
      - 0