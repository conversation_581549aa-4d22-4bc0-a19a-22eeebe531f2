model: "1.0"
desc: "URL（结构化）"
scores:
  - desc: "列注释精确匹配url"
    name: "FindColumnComment"
    output: true
    rule:
      regexp_find:
        - "^\\s*(?i)url\\s*$"
        - var: "column.comment"
  - desc: "[字段名]精确匹配url"
    name: "FindColumnName"
    output: true
    rule:
      or:
        - var: "$.FindColumnComment"
        - regexp_find:
            - "^(?i)url$"
            - var: "column.name"
  - desc: "[数据特征] 满足正则匹配规则"
    name: "FindColumnValue"
    output: true
    rule:
      or:
        - var: "$.FindColumnName"
        - '>=':
          - array_hit_ratio:
              - regexp_find:
                  - "(?i)^(ht|f)tp(s?)\\:\\/\\/[0-9a-zA-Z]([-.\\w]*[0-9a-zA-Z])*(:(0-9)*)*(\\/?)([a-zA-Z0-9\\-\\.\\?\\,\\'\\/\\\\\\+&amp;%\\$#_]*)?[ ]*$"
                  - var: "ArrayElementValue"
              - var: "column.values"
          - 80
  - desc: "计算得分"
    name: "OverallScore"
    output: true
    rule:
      "condition":
        - var: "$.FindColumnValue"
        - 100
        - 0