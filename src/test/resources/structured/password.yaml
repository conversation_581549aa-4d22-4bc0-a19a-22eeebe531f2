model: "1.0"
desc: "密码（结构化）"
scores:
  - desc: "列注释匹配以 密码 结尾"
    name: "FindColumnComment"
    output: true
    rule:
      regexp_find:
        - "\u5BC6\u7801\\s*$"
        - var: "column.comment"
  - desc: "[字段名]正则精确匹配password、passcode、passphrase"
    name: "FindColumnName"
    output: true
    rule:
      or:
        - var: "$.FindColumnComment"
        - regexp_find:
            - "(^(?i)password$)|(^(?i)passcode$)|(^(?i)passphrase$)"
            - var: "column.name"
  - desc: "计算得分"
    name: "OverallScore"
    output: true
    rule:
      "condition":
        - var: "$.FindColumnName"
        - 100
        - 0