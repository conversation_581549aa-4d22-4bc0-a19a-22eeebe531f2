model: "1.0"
desc: "银行卡（结构化）"
scores:
- desc: "列注释以 银行卡号 或 银行账号 结尾"
  name: "FindColumnComment"
  output: true
  rule:
    regexp_find:
      - "\u94F6\u884C[\u5361\u8D26][\u53F7]?\\s*$"
      - var: "column.comment"
- desc: "字段名匹配"
  name: "FindColumnName"
  output: true
  rule:
    regexp_find:
      - "^(?i)bank[_-]?accno$|^(?i)bank[_-]?account$|^(?i)bank[_-]?accno$|^(?i)bank[_-]?card[_-]?no$|^(?i)bank[_-]?card[_-]?number$"
      - var: "column.name"
- desc: "正则+校验"
  name: "ColumnValuesRegexpAndValidator"
  output: true
  rule:
    condition:
      - var: "$.FindColumnComment"
      - 100
      - var: "$.FindColumnName"
      - 100
      - array_hit_ratio:
          - and:
              - regexp_find:
                  - "^\\s*\\d{10,20}\\s*$"
                  - var: "ArrayElementValue"
              - regexp_find:
                  - "^\\s*(?!(\\d)\\1{5}).*$"
                  - var: "ArrayElementValue"
              - validator:
                  - "cls.pii::unionpay"
                  - var: "ArrayElementValue"
          - var: "column.values"
- desc: "计算得分"
  name: "OverallScore"
  output: true
  rule:
    "condition":
      - '>=':
          - var: "$.ColumnValuesRegexpAndValidator"
          - 60
      - 100
      - 0