model: "1.0"
desc: "性别（结构化）"
scores:
- desc: "列注释匹配以 性别 结尾"
  name: "FindColumnComment"
  output: true
  rule:
    regexp_find:
      - "\u6027\u522B\\s*$"
      - var: "column.comment"
- desc: "数据字典(命中率90%)"
  name: "FindDataDict"
  output: true
  rule:
    or:
      - var: "$.FindColumnComment"
      - ">=":
          - array_hit_ratio:
              - ">":
                - search_in_dictionary:
                    - "gender"
                    - var: "ArrayElementValue"
                - 0
              - var: "column.values"
          - 90
- desc: "[字段名]正则精确匹配gender、sexual、sex"
  name: "FindColumnName"
  output: true
  rule:
    or:
      - var: "$.FindDataDict"
      - regexp_find:
          - "(^(?i)gender$)|(^(?i)sexual$)|(^(?i)sex$)"
          - var: "column.name"
- desc: "计算得分"
  name: "OverallScore"
  output: true
  rule:
    "condition":
      - var: "$.FindColumnName"
      - 100
      - 0