-dontoptimize
-dontshrink
-adaptclassstrings
-dontwarn com.google.common.cache.**
# -dontpreverify
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontskipnonpubliclibraryclassmembers
-verbose
-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,*Annotation*,EnclosingMethod
# -libraryjars <java.home>/lib/rt.jar

-libraryjars <java.home>/jmods/java.desktop.jmod(!**.jar;!module-info.class)
-libraryjars <java.home>/jmods/java.base.jmod(!**.jar;!module-info.class)
-libraryjars <java.home>/jmods/java.naming.jmod(!**.jar;!module-info.class)
-libraryjars <java.home>/jmods/java.sql.jmod(!**.jar;!module-info.class)
-libraryjars <java.home>/jmods/java.xml.jmod(!**.jar;!module-info.class)
-libraryjars <java.home>/jmods/java.security.sasl.jmod(!**.jar;!module-info.class)
-libraryjars <java.home>/jmods/java.security.jgss.jmod(!**.jar;!module-info.class)
-printmapping "${project.build.outputDirectory}/mapping.txt"

-keepclassmembers class * extends java.lang.Enum { *; }
-keepclassmembers enum * { *; }
# -keep class * {
#     enum **;
# }

-keep class com.dcap.classifier.Clock{*;}
-keep class com.dcap.cloud.**{*;}
-keep class com.dcap.conntest.**{*;}
-keep class com.dcap.etl.**{*;}
-keep class com.dcap.sampling.**{*;}
-keep class com.dcap.servicediscovery.**{*;}
-keep class com.dcap.utils.**{*;}
-keep class com.yd.dcap.probe.** { *; }
-keep class com.yd.dcap.classifier.ClassifierExecutorConfig { *; }
-keep class com.yd.dcap.classifier.ClassifierService { *; }
-keep class com.yd.dcap.classifier.taskreport.StatusRecord { *; }
-keep class com.yd.dcap.classifier.config.KafkaConfig { *; }
-keep class com.yd.dcap.classifier.config.SpringContextUtil { *; }
-keep class com.yd.dcap.probe.entities.TaskReporter { *; }
-keep class com.yd.dcap.classifier.taskreport.TaskReport { *; }
-keep class com.dcap.classifier.validate.Validator { *; }
-keep class com.yd.dcap.classifier.taskreport.ScanDbReport { *; }
-keep class com.yd.dcap.classifier.taskreport.StatusRecord$Position { *; }
-keep class com.dcap.classifier.rules.engine.result.MatchedResult {*;}
-keep class com.yd.dcap.probe.entities.ProbeClientTaskContext { *; }
-keep class com.yd.dcap.probe.client.ProbeClientProcessor {*;}
-keep class com.yd.dcap.probe.client.ProbeClientTaskUtil {*;}
-keepclassmembers class * extends com.yd.dcap.probe.client.ProbeClientProcessor {
    public <init>(com.yd.dcap.probe.entities.ProbeClientTaskContext);
}
-keep class com.dcap.classifier.write.** {*;}
-keep class com.dcap.classifier.nmap.** {*;}
-keep class com.dcap.classifier.analyzer.** {*;}
-keep class com.google.common.cache.** {*;}



-keep public class com.yd.dcap.probe.ClientApplication{*;}
#-keep interface * extends * { *; }